## [2.2.87]

### Fix

- E-sign draft old issue fix

## [2.2.86]

### Update

- Add salutation field

## [2.2.85]

### Update

- Draft subject change into html content

## [2.2.84]

### Update

- Importing date

## [2.2.83]

### Update

- Importing note on draft

## [2.2.82]

### Update

- Simple text editor added for subject

## [2.2.81]

### Update

- Salutation added in editor

## [2.2.80]

### Fix

- Draft status filter pagination fix

## [2.2.79]

### Fix

- Draft status filter not showing issue fixed

## [2.2.78]

### Fix

- Tag issue fix after save

## [2.2.77]

### Fix

- Draft validation issue fix

## [2.2.76]

### Update

- New Text-editor implementation 
- Draft create design change

## [2.2.75]

### Fix

- Fixed local document preview scrolling issue

## [2.2.74]

### Fix

- Journal and contra entry re direction when finance button active in file summary note card

## [2.2.73]

### Fix

- Journal and contra entry re direction when finance button active in file summary

## [2.2.72]

### Fix

- Reference Add button

## [2.2.71]

### Update

- Address Enclosure new design update

## [2.2.70]

### Fix

- Contra entry and journal entry strip added

## [2.2.69]

### Update

- Address copy-to modal new design update

## [2.2.68]

### Update

- Reference Modal new design

## [2.2.67]

### Update

- Draft filter pagination logic change

## [2.2.66]

### Update

- Draft address design change

## [2.2.65]

### Fix

- Draft sign issue fix

## [2.2.64]

### Update

- Draft list new design

## [2.2.63]

### Fix

- E sign option enabled

## [2.2.62]

### Fix

- E sign route enabled

## [2.2.62]

### Fix

- E sign route enabled

## [2.2.61]

### Fix

- E sign dev uncomment

## [2.2.60]

### Fix

- Fix confirm digital signature with edit

## [2.2.59]

### Fix

- ES Commented

## [2.2.58]

### Fix

- Fix digital sign and confrim DS in draft

## [2.2.57]

### Fix

- Create button condition fix

## [2.2.56]

### Fix

- Confirm DS issues fixed

## [2.2.55]

### Fix

- Uncomment E-sign

## [2.2.54]

### Fix

- Imprest claim and Advance claim re direction when finance button active in note card

## [2.2.53]

### Fix

- Advance claim re direction when finance button active in file summary

## [2.2.52]

### Fix

- Imprest claim re direction when finance button active in file summary

## [2.2.51]

### Fix

- Dev route issue fix

## [2.2.50]

### Fix

- Auditor re direction - reports issue

## [2.2.49]

### Fix

- Auditor re direction - reports

## [2.2.48]

### Fix

- Auditor re direction

## [2.2.47]

### Fix

- eSign position customization flag

## [2.2.46]

### Fix

- Sign position fixed in E Sign

## [2.2.45]

### Fix

- E Sign submit route issue fix

## [2.2.44]

### Update

- E Sign API integration

## [2.2.43]

### Update

- Imprest Disbursement Strip Added - service code changes

## [2.2.42]

### Update

- Imprest Disbursement Strip Added

## [2.2.41]

### Update

- Draft preview design change

## [2.2.40]

### Update

- E sign form api upload

## [2.2.39]

### Update

- E sign form

## [2.2.38]

### Update

- E sign admin requests

## [2.2.37]

### Update

- E sign changes

## [2.2.36]

### Update

- User id added in esign request

## [2.2.35]

### Update

- Updated return url from draft

## [2.2.34]

### Update

- Updated Finace Background Color

## [2.2.33]

### Update

- Updated Finance button in draft view attachments

## [2.2.32]

### Update

- Updated file workflow UI and functionality

## [2.2.31]

### Fix

- Updated Finance status and Icon

## [2.2.30]

### Fix

- Change Finacial Status Values and Removed Notes icon

## [2.2.29]

### Fix

- Update file workflow for forward-plus to get all employees and switch for use functional-group

## [2.2.28]

### Fix

- Unautherize redirect to employee login

## [2.2.27]

### Update

- Update note save button ui
- Enable file abstraction

## [2.2.26]

### Update

- Update the document types in constants

## [2.2.25]

### Fix

- Update the document preview for excel, and docx in localPreview

## [2.2.24]

### Fix

- Update fetchAllNotes api payload form outbox file moreNotes

## [2.2.23]

### Fix

- Fix excel and docs download

## [2.2.22]

### Update

- Disable File abstract card in reports

## [2.2.21]

### Update

- Update the document preview for excel, and docx
- Update document type check and fix the document preview fit in the box

## [2.2.20]

### Update

- Enable the File Abstract card

## [2.2.19]

### Fix

- File type ecxel and document support - summary

## [2.2.18]

### Fix

- File type ecxel and document support

## [2.2.17]

### Fix

- Pan no and gst no showing when individual beneficiary

## [2.2.16]

### Fix

- Note and draft restrict when come from watch file

## [2.2.15]

### Fix

- Draft re direction issue fixed when action taken

## [2.2.14]

### Fix

- Fix the dropdown labal on focused

## [2.2.13]

### Fix

- Esign disable

## [2.2.12]

### Fix

- functional group changes in workflow

## [2.2.11]

### Fix

- Workflow actions set menuPlacement to top
- update `src\pages\file\details\components\notes\style.css` for `.input-rich-container.rich-editor` z-index set to 0
- update `src\index.css` for `.KSmart__control--menu-is-open` z-index set to 9999

## [2.2.10]

### Fix

- Partial note documents showing when loading in note page
- tab reset issue fixed in note page

## [2.2.9]

### Fix

- Home route changes

## [2.2.8]

### Fix

- E sign enable for test

## [2.2.7]

### Fix

- Add GST Number and PAN Number fileds in beneficiary institution form

## [2.2.6]

### Fix

- local otp changes

## [2.2.5]

### Fix

- Validation changes in workflow assignee dropdown

## [2.2.4]

### Fix

- Forward plus changes in workflow

## [2.2.3]

### Fix

- Not found page implementation

## [2.2.2]

### Fix

- Draft pdf url changes

## [2.2.1]

### Fix

- Text editor changes

## [2.1.3]

### Fix

- Salary strip enable issue

## [2.1.2]

### Fix

- Drafts and notes showing issue when in search file case

## [2.1.1]

### Fix

- Salary strip enable for some service codes

## [2.1.0]

### Fix

- Add title column in parked file list and file no column in inward search

## [2.0.269]

### Fix

- document popup changes in note modal

## [2.0.268]

### Fix

- date null issue when click on draft card preview

## [2.0.267]

### Fix

- Remove validation of officerName in beneficiery

## [2.0.266]

### Fix

- Approve and revise button enable issue

## [2.0.265]

### Update

- Update finance urls for button click

## [2.0.264]

### Update

- Enable note and drafts in general feature menu for forward plus

## [2.0.263]

### Update

- Reference date submit issue in counter

## [2.0.262]

### Update

- Expand option for summary document popup

## [2.0.261]

### Update

- Tooltip label added in Notes Expansion button

## [2.0.260]

### Update

- Enable draft actions and general feature menu when route change

## [2.0.259]

### Update

- Documents loading when last note have documents in note page - card selection

## [2.0.258]

### Update

- Notes sections expanded as popup

## [2.0.257]

### Update

- Documents loading when last note have documents in note page

## [2.0.256]

### Update

- Addition Tooltip for title

## [2.0.255]

### Update

- Addition Column of title

## [2.0.254]

### Update

- Addition Column of ref numbers

## [2.0.253]

### Update

- Some changes

## [2.0.252]

### Update

- My files implementation

## [2.0.251]

### Update

- update note collapse

## [2.0.250]

### Update

- update autonote to structured

## [2.0.249]

### Update

- update note docs limit, esign disabled, password protection pdf

## [2.0.248]

### Fix

- Fix the workflow select list option hight limited
- Fix the footer name ksmat to ksuite

## [2.0.247]

### Update

- Date converting to local date time

## [2.0.246]

### Update

- Download and print api changes with esign in draft list

## [2.0.245]

### Update

- E sign separate button and functionality implementation

## [2.0.244]

### Update

- Selected draft active after Esign return

## [2.0.243]

### Update

- Re direct to draft list and active current draft when Es completed - latest changes

## [2.0.242]

### Update

- Re direct to draft list and active current draft when Es completed

## [2.0.241]

### Update

- update preview for print and download whan approved but not signed with watermark

## [2.0.240]

### Update

- Disable digital signature button when esign complete

## [2.0.239]

### Update

- Esign preview showing accept type changes

## [2.0.238]

### Update

- set accept type is pdf in getDocument() fro esign

## [2.0.237]

### Fix

- After ds restrict to click outside of showned alert ok button

## [2.0.236]

### Fix

- Esign return PDF load changes

## [2.0.235]

### Fix

- DS in summary page

## [2.0.234]

### Fix

- Esign return url changes

## [2.0.233]

### Fix

- Esign changes

## [2.0.232]

### Fix

- Fix ds on approve and sudently
- Update approvedMoreStatus for digital signed and e-signed
- Fix alert box after set preview data

## [2.0.231]

### Fix

- Fix ds in summery page

## [2.0.230]

### Fix

- Imprest Claim and Advance Claim strip added

## [2.0.229]

### Fix

- E sign and digital sign flag changes

## [2.0.228]

### Fix

- E sign dragble changes

## [2.0.227]

### Fix

- E sign changes

## [2.0.226]

### Fix

- Fix no roles mapped employee to click the Finance button from summery screen

## [2.0.225]

### Update

- Restrict no rols mapped employee to click the Finance and Note button in summery screen

## [2.0.224]

### Update

- Restrict api's when search file - latest

## [2.0.223]

### Update

- Restrict api's when search file

## [2.0.222]

### Update

- Add finance url against the finance status

## [2.0.221]

### Fix

- Officer name required changes in beneficiary

## [2.0.220]

### Fix

- Disable general feature menu when forward plus case

## [2.0.219]

### Fix

- Re direct issue in out box list

## [2.0.218]

### Fix

- Re direct issue in note and summary

## [2.0.217]

### Fix

- Re direct issue in search file

## [2.0.216]

### Fix

- after approve the draft, watermark free preview

### [2.0.215]

### Fix

- open draft preview model after action taken

## [2.0.214]

### Fix

- Download and print in draft preview

## [2.0.213]

### Fix

- Applicant name missing issue in file search

## [2.0.212]

### Fix

- fix the document print issue.
- update fetch all updated draft item after ds

## [2.0.211]

### Fix

- Enable re open button for operator

## [2.0.210]

### Fix

- Role FORWARD_PLUS_ROLE in summary page changed to FORWARD PLUS

## [2.0.209]

### Fix

- stage FORWARD_PLUS_ROLE in summary page changed to FORWARD PLUS

## [2.0.208]

### Fix

- fix DS in summery screen, file from operator and first time open ds issue.

## [2.0.207]

### Fix

- Applicant name changes in file tracking

## [2.0.206]

### Fix

- Replace route change to forward plus in workflow dropdown and functionalities

## [2.0.205]

### Fix

- Beneficiary field binding issue

## [2.0.204]

### Fix

- Submit when not select any user in counter , arising , legacy

## [2.0.203]

### Fix

- in Create Beneficiary section, fix the brach list exist or not

## [2.0.202]

### Update

- in Create Beneficiary section, IFSC is selectable with filter

## [2.0.201]

### Changes

- Pull file issue with note editor

## [2.0.200]

### Changes

- Documents download issue in counter , arising , legacy

## [2.0.199]

### Changes

- Validation changes in enclosure and copy to form when on change

## [2.0.198]

### Changes

- Remove file stage from Inward search

## [2.0.197]

### Changes

- Enclosure name validation in draft

## [2.0.196]

### Changes

- Copy to button showing issue

## [2.0.195]

### Changes

- Allow special character in designation in Institution
- Arranged local body name in file log

## [2.0.194]

### Changes

- Date duplication issue fixed in file tracking

## [2.0.193]

### Changes

- Inward details added in file log report

## [2.0.192]

### Changes

- Applicant name missing issue fixed in inward search

## [2.0.191]

### Changes

- Name, designation changes in file tracking

## [2.0.190]

### Changes

- subject alignment in draft create page

## [2.0.189]

### Changes

- Documents , notes and drafts api changes when outbox view - latest changes

## [2.0.188]

### Changes

- Documents , notes and drafts api changes when outbox view

## [2.0.187]

### Changes

- preview all documents Api changes when file search

## [2.0.186]

### Changes

- after approve the file, preview api call with new params
- url have null params in from key resolve

## [2.0.185]

### Changes

- Linked file auto note duplication .Auto note and also in note editor

## [2.0.184]

### Changes

- Draft preview closing when approve in summary

## [2.0.183]

### Changes

- Please enter valid draft validation added when space in draft editor
- Please enter valid subject validation added when space in draft subject field

## [2.0.182]

### Changes

- Validation mesages changes

## [2.0.181]

### Changes

- Fix the inital logger
- Fix the note must be enterd in take the file action

## [2.0.180]

### Changes

- Fix the note creation in summery, note, and draft
- Fix the Delete confirmation popup action disable after take the action

## [2.0.179]

### Changes

- New inward Applicant - English fieldil malayalam entry restrict

## [2.0.178]

### Changes

- Hide File Abstract, Personal Register cards in reports

## [2.0.177]

### Changes

- Officer name validation change

## [2.0.176]

### Changes

- Workflow issue fixed

## [2.0.175]

### Changes

- Logo and title changed

## [2.0.174]

### Changes

- Fix Go to button
- Update Park button style

## [2.0.173]

### Changes

- Please enter Note validation added
- Please select Actions validation added
- Please select Functional Group validation added
- Actions label added note editor note page

## [2.0.172]

### Changes

- Functional group validation added in workflow when route change
- no space allowing validation in editor (summary , note, draft)
- Note file last note number change to note file last para number (legacy create)

## [2.0.171]

### Changes

- Workflow validation issue fixed

## [2.0.170]

### Changes

- draft api changes when come from search file , otherwise previous api loading

## [2.0.169]

### Fix

- note card active or inActive when click

## [2.0.168]

### Fix

- Notes api changes from serach files

## [2.0.167]

### Fix

- Merged file not reopen alert

## [2.0.166]

### Fix

- Implement some menus in note and summary text editor

## [2.0.165]

### Fix

- Draft preview update flag added

## [2.0.164]

### Fix

- In draft if the correspondence type is Others then Pull Draft and Pull Note popup is not visible. Popup window listed behind the editor
- Disable Draft action buttons while open a file from Outbox,Child file,link file and merge file view

## [2.0.163]

### Fix

- Change Temporary disposed card name to Parked files

## [2.0.162]

### Fix

- Legacy file document name clearing issue
- Route change issue in particular functional group

## [2.0.161]

### Fix

- Legacy file ui document alignment issue
- Remove draft action button while open a file from outbox
- REOPEN button active when linked file selection time
- route change failed issue

## [2.0.160]

### Fix

- Note card click functionality changes

## [2.0.159]

### Fix

- Land no changes in counter

## [2.0.158]

### Fix

- Draft list design changes
- disable title field when type other than other type
- institution name showing issue when edit

## [2.0.157]

### Fix

- Route key details missing in Legacy file create

## [2.0.156]

### Fix

- Note reference changes

## [2.0.155]

### Fix

- set loader on confirm digital sign in draft DS

## [2.0.154]

### Fix

- Workflow design changes in note

## [2.0.153]

### Fix

- multiple pop-up and loader in draft ds

## [2.0.152]

### Fix

- approved draft edit and show preview
- digital signature show in preview screen

## [2.0.151]

### Fix

- Route key binding issue in general details info

## [2.0.150]

### Fix

- when draft edit, popup issue solved
- when draft ds from summary screen big fixed

## [2.0.149]

### Fix

- Remove auto notes for link, merge, un link , un merge

## [2.0.148]

### Fix

- Proceed and preview issue in draft- After draft edit , edited content not visible in proceed and preview popup
- Draft Copy To not working- Copy To details added in draft and details seen in popup preview. If we close and open again copy to details are
  missing from preview

## [2.0.147]

### Fix

- Remove Draft address delete option when edit page
- While creating a new draft Make inactive button required by default. Currently Make Active is visible as default

## [2.0.146]

### Fix

- resolve the ds issue on approve and ds in draft

## [2.0.145]

### Fix

- Please save note before workflow action' message changes to 'File can be submitted only after saving the note.
- Draft Receiver address missing issue- Draft created by operator and Revise marked by verifier. File returned to operator and click on edit button.
  During that time To address field visible as blank

## [2.0.144]

### Fix

- resolve the ds issue on approve and ds in draft

## [2.0.143]

### Fix

- In Add attachments , if no documents are selected then the error message should be changed to
- Alert for empty selection of users in counter / arising / legacy

## [2.0.142]

### Fix

- In draft if Correspondence type is Governing council then preview label is out of screen
- In note editor, Add Reference -button size should be rearrange
- In note editor Reference delete confirmation message should change
- Add Reference-Error message required while click on Add button without selecting note paragraph number

## [2.0.141]

### Fix

- resolve the ds issue on approve and ds in draft

## [2.0.140]

### Fix

- Route change new issue fixed - 16/10/2024

## [2.0.139]

### Fix

- Route change new issue fixed

## [2.0.138]

### Fix

- In Inward counter- error occurs while edit general information

## [2.0.137]

### Fix

- Route change issue

## [2.0.136]

### Fix

- resolve draft digital signature issue on approved draft

## [2.0.135]

### Fix

- Route key issue
- archived , search file , disposed files back implementation

## [2.0.134]

### Fix

- resolve draft digital signature issue

## [2.0.133]

### Fix

- Bug fix 1

## [2.0.132]

### Fix

- Partial notes issue

## [2.0.131]

### Fix

- DS issue

## [2.0.130]

### Fix

- Route key values added in summary
- Custodian change route keys fields missing

## [2.0.129]

### Fix

- Mandatory field error message not showing in counter applicant form in institution inside and outside country

## [2.0.128]

### Fix

- Partial note issue
- Hide actions buttons when outbox view (note file)

## [2.0.126]

## [2.0.127]

### Fix

- resolve pagination in draft

## [2.0.126]

### Fix

- Previous notes load issue.After click on Go to para 20, notes from 20 loaded. After that select previous notes 10-19 that notes are alos loaded.
  Then again select previous notes 9-18 and that time note screen scroll bar pointed in 20.During that time scroll bar should be pointed to 10

## [2.0.125]

### Fix

- After selecting a Child/Link/Merge file from a main file note file then Go to para should be the selcted Child/Link/Merge file.
  Currently the main file note paragraph is displayed

- Note save issue. Partial note saved and application logout. After login again work flow not working and error message visible.
  Please save note before workflow action ( Partial note visible at editor )

## [2.0.124]

### Fix

- Draft white screen issue when action taken

## [2.0.123]

### Fix

- resolve pagination in draftCount

## [2.0.121]

## [2.0.122]

### Fix

- Document expand option implementation

## [2.0.121]

### Update

- resolve pagination in draftCount

## [2.0.120]

### Update

- update pagination in draftCount

## [2.0.119]

### Fix

- Note order issue fixed

## [2.0.118]

### Fix

- Draft button enable issue when action taken

## [2.0.117]

### Fix

- Re direct appropriate page when click cancel in digital signature popup

## [2.0.116]

### Fix

- Fix the draft count when action occure, default, all status default, ect

## [2.0.115]

### Fix

- Note listing issue in archived file

## [2.0.114]

### Fix

- Note card border bottom color thickness reduced

## [2.0.113]

### Fix

- Hide create draft button when route changed file

## [2.0.112]

### Fix

- Pending draft exists number new api

## [2.0.111]

### Fix

- Pagination issue when draft delete
- Draft re direct when its come from

## [2.0.110]

### Fix

- Draft Count when any action occures on the draftNo, that draft is defaultly set

## [2.0.109]

### Fix

- Pending draft exists and draft no issue with popup and undefined

## [2.0.108]

### Fix

- Pending draft exists and draft no

## [2.0.107]

### Fix

- When take action for active/inactive notes duplicates issue

## [2.0.106]

### Update

- default set the peding section in draft count

## [2.0.105]

### Fix

- create draft button not show when come from archieved , temporary dispose , not user from workflow user
- Inward/Arising - Forwarding to label change Forward To
- Change DISPOSED to Disposed in summary statu
- When select on other documents title become blank , otherwise fill type
- Role first letter capital in summary workflow popup

## [2.0.104]

### Fix

- User label changed to forward to in legacy, counter, arising
- Include work flow in Note file page

## [2.0.103]

- selected note card border color red changes
- Draft pagination reverse order
- expand icon color changes (draft page)
- overlap issue in 3 dots in draft page
- form components gap issue in draft page
- after Draft action in summary page , need to stay in summary page, otherwise move to draft and active curresponding draft no
- If auto note present, then no need to enter manual note in editor

## [2.0.102]

### All Docs Fix

- All Docs fix

## [2.0.101]

### Fix

- After draft delete , preview popup message is not closed

## [2.0.100]

### Fix

- After draft creation re direct to curresponding draft no
- revise button

## [2.0.99]

### Fix

- Add benificiary screen-Mobile no enter fetch call removed
- pending draft exists and number

## [2.0.98]

### Fix

- Documents delete issue
- field clearing issue
- yes/no label changes

## [2.0.97]

### Fix

- Note documents issue fixed

## [2.0.96]

### Fix

- Draft Latest changes

## [2.0.95]

### Fix

- Remove Drag in summary editor
- Document load when notes contents active issue

## [2.0.94]

### Fix

- Draft pagination issue

## [2.0.93]

### Fix

- New version of ui components

## [2.0.92]

### Fix

- Document, notes changes in draft

## [2.0.91]

### Fix

- Document back button showing issue fixed

## [2.0.90]

### Fix

- Draft Pagination

## [2.0.89]

### Fix

- User required message changes in workflow
- Reference remove alert message changes

## [2.0.88]

### Fix

- reset when breadcrumb back button click when Link , merge, child options choose
- Multiple user selection issue in workflow
- create draft button disable when link, merge, child options active
- status first letter capitalize in summary
- Note missing alert in summary submit

## [2.0.87]

### Fix

- Reference edit in draft create

## [2.0.86]

### Fix

- Field clearing issue in note add document section when document close fixed
- Add close button in summary document view

## [2.0.85]

### Fix

- Document clearing issue in note add document section fixed

## [2.0.84]

### Fix

- Remove un wanted general feature menus

## [2.0.83]

### Fix

- not auto save - AI :)

## [2.0.82]

### Fix

- ds signature
- scroll summary

## [2.0.81]

### Fix

- Enable revoke button for custodian
- Parking file label changed to park file
- child , merged , linked selection api call issue fixed

## [2.0.80]

### Fix

- Pending draft exists issue fixed.
- Pending drafts numbers showing on alert
- Changes in next workflow user details
- Back button added in document section (notes)
- Document from para added in document tab (notes)

## [2.0.79]

### Fix

- Disable linked , merged, child options based on selection

## [2.0.78]

### Fix

- Implement new design for linked , merged, child options
- Fix worflow issue

## [2.0.77]

### Fix

- Arising document upload issue
- Note gap in right sroll
- Note card space
- User selection when single user in workflow
- Note card re direct issue when click in summary

## [2.0.76]

### Fix

- Clear field after documents upload, update, some ui changes

## [2.0.75]

### Fix

- Edit buton showing issue fixed in draft popup

## [2.0.74]

### Fix

- Button condition changes in draft preview

## [2.0.73]

### Fix

- Disable edit button on popup when draft page

## [2.0.72]

### Fix

- Pending Draft fix

## [2.0.71]

### Updates

- Remove unwanted call

## [2.0.70]

### Updates

- Formating changes

## [2.0.69]

### Updates

- Remove underline from role in worflow confirmation popup

## [2.0.68]

### Updates

- Remove inbox from breadcrumb

## [2.0.67]

### Fix

- Notes UI fix

## [2.0.66]

### Fix

- Notes UI fix
- Arising Document Update

## [2.0.65]

### Updates

- Notes new api and ui

## [2.0.64]

### fix

- Implement DS button in draft preview popup and pending draft exists alert

## [2.0.63]

### fix

- Draft re direct from summary issue fixed

## [2.0.62]

### fix

- Pending draft exists alert implemented in note save

## [2.0.61]

### fix

- Draft action api changes and draft alignment changes in note card

## [2.0.60]

### fix

- Re direct to documents when click on note list

## [2.0.59]

### fix

- Draft New Popup implementation with functionality

## [2.0.58]

### Update

- Update reference in tag model from table in create-draft

## [2.0.57]

### Update

- Add last-note-number in legacy file create

## [2.0.56]

### fix

- Editor Drag and Resize
- Summary and Note Scrolls

## [2.0.55]

### fix

- Draft filter design changes and inward de link disabling

## [2.0.54]

### fix

- Summary re direction issue fixed

## [2.0.53]

### fix

- notes save fix

## [2.0.52]

### fix

- notes doc fix

## [2.0.51]

### fix

- notes updates

## [2.0.50]

### fix

- draft alert changes

## [2.0.49]

### fix

- Seat name added in note and draft cards

## [2.0.48]

### fix

- Zoom in and zoom out icons in documents

## [2.0.47]

### fix

- Draft design changes

## [2.0.46]

### fix

- Digital signature changes

## [2.0.45]

### fix

- document fix

## [2.0.44]

### Add

- Notes, References and Documents updates

## [2.0.43]

### Add

- Draft loader implementation

## [2.0.42]

### Add

- Doocument Ref updates in note

## [2.0.41]

### Add

- Implement filter and pagination in draft

## [2.0.40]

### Add

- Zoom effect added in documents

## [2.0.39]

### Add

- doc preview notes and inward

## [2.0.38]

### Add

- Draft – Add Reference + button should removed and replaced as Add.If single reference is added no need to click on Add button

## [2.0.37]

### Add

- attachment master and By postal or   email – mobile no validation fixed

## [2.0.36]

### Add

- Document popup view in summary note card

## [2.0.35]

### Add

- Notes Update, Summary editor

## [2.0.34]

### Add

- Draft buttons and color codes changes in draft page

## [2.0.33]

### Add

- Document loading in notes page

## [2.0.32]

### Add

- arising and counter supporting documents changes

## [2.0.31]

### Add

- notes updations

## [2.0.30]

### Add

- Note routes and ui updates

## [2.0.29]

### Add

- Note Reference and scroll load

## [2.0.28]

### Add

- Document , reference, draft showing in notes in summary

## [2.0.27]

### Add

- Notes ref delete
- Notes ref click active note

## [2.0.26]

### Add

- Rename return button to Need Review in draft

## [2.0.25]

### Add

- Notes New ui editor drag
- Notes attachment
- Notes Reference

## [2.0.24]

### Add

- Color code added in draft

## [2.0.23]

### Add

- Delete functionality implemented in draft and note click functionality

## [2.0.22]

### Add

- Document design changes with pagination in note file page

## [2.0.21]

### Add

- style Changes in Draft

## [2.0.20]

### Add

- file status and file role formating in summary

## [2.0.19]

### Add

- Route key dependencies changes in file summary

## [2.0.18]

### Add

- Route key changes in file summary

## [2.0.17]

### Add

- Summary button style changes

## [2.0.16]

### Add

- Summary overall design changes

## [2.0.15]

### Add

- User icon color changes in note card

## [2.0.14]

### Add

- more menu and icon spacing

## [2.0.13]

### Add

- IMplement more menu in note card

## [2.0.12]

### Add

- IMplement new status component design

## [2.0.11]

### Add

- IMplement collapse menu in file summary

## [2.0.10]

### Add

- FE - Route Key changes for IKM file

## [2.0.9]

### Add

- Dialog box removed when choose workflow as approve

## [2.0.8]

### Add

- fixed undefined values in draft

## [2.0.7]

### Add

- Auto note added in verifier / recommending officer approve button

## [2.0.6]

### Add

- Mobile number validation for inward form

## [2.0.5]

### Add

- Changes in Draft , inward and beneficiary

## [2.0.4]

### Add

- Changes in Beneficiary

## [2.0.3]

### Add

- Changes route key in counter and arising

## [2.0.2]

### Add

- Changes in workflow and draft

## [2.0.1]

### Add

- Changes in counter details

## [2.0.0]

### Add

- IKM config

## [1.16.28]

### Add

- dOC SIZE 5 MB

## [1.16.27]

### Add

- KSWIFT BFIF02

## [1.16.26]

### Add

- FE - Despatch - send letter - preview - api response changes

## [1.16.25]

### Add

- FE - Despatch - send letter - preview

## [1.16.24]

### Update

- FE - Record room - inward verify-otp

## [1.16.23]

### Add

- FE - Record room - inward send-otp with API data (mobileNo)

## [1.16.22]

### Add

- FE - Audit login updations

## [1.16.21]

### Update

- FE - counter douments - update with documentName and solve the preview functionality in view-inbox-physical-docs

## [1.16.20]

### Add

- FE - Record room - inbox and physical-docs list

## [1.16.19]

### Add

- FE - Draft creation issue - in approver

## [1.16.18]

### Add

- Kswift new url - PTBSOC

## [1.16.17]

### Add

- FE - Draft creation issue - proceedings

## [1.16.16]

### Add

- FE - Draft dispatch clerk api changes

## [1.16.15]

### Add

- FE - Draft address formating - latest changes

## [1.16.14]

### Add

- FE - Draft address formating

## [1.16.13]

### Update

- Record room for inwards
- draft malayalam/english

## [1.16.12]

### Add

- FE - Draft save enable

## [1.16.11]

### Add

- FE - Draft save response changes

## [1.16.10]

### Add

- FE - Dispose file latest changes

## [1.16.9]

### Updates

- kswift payment url
- draft english checkbox
- recordroom in counter docs

## [1.16.8]

### Add

- FE - Implement re direct in dispose

## [1.16.7]

### Add

- FE - message changes in dispose

## [1.16.6]

### Add

- FE - remove description field in legacy

## [1.16.5]

### Add

- FE - dispose implementation

## [1.16.4]

### Fix

- Efile Doc hadcopy field update

## [1.16.3]

### Add

- Enable floating

## [1.16.2]

### Add

- FE - dispatch - stamp details entry

## [1.16.1]

### Add

- FE - Draft addess modal latest changes

## [1.16.0]

### Add

- FE - Personal Register Report

## [1.15.87]

### Fix

- Ben Dob fix

## [1.15.86]

### Fix

- Remove disptach old folder

## [1.15.85]

### Fix

- Beneficiary radio button issue

## [1.15.84]

### Fix

- Beneficiary Summary List

## [1.15.83]

### Fix

- Datasheet Download

## [1.15.82]

### Fix

- Beneficiary Fix

## [1.15.81]

### Fix

- Beneficiary Cache Institution

## [1.15.80]

### Fix

- Beneficiary Cache

## [1.15.79]

### Update

- Public file search api changes

## [1.15.78]

### Update

- ben fixes
- exe link

## [1.15.77]

### Update

- ben bulk comment

## [1.15.76]

### Update

- ben check fix

## [1.15.75]

### Update

- Despatch - new functionality implementation

## [1.15.74]

### Update

- UI Component update

## [1.15.73]

### Update

- Beneficiary update

## [1.15.72]

### Update

- Beneficiary and Bulkupload Fix

## [1.15.71]

### Update

- Re-arrange the filter options espacially for cashDepartment

## [1.15.70]

### Update

- MultiSelect update the filter option with typing

## [1.15.69]

### Update

- Revert record room

## [1.15.68]

### Update

- Beneficiary Update

## [1.15.67]

### Update

- Draft address fix, Beneficiary Update

## [1.15.66]

### Update

- Draft address fix

## [1.15.65]

### Update

- Beneficiary bulk excel

## [1.15.64]

### Update

- Check floating in origin

## [1.15.63]

### Update

- Excel support for file upload

## [1.15.62]

### Floating

- Floating widget check

## [1.15.61]

### ds

- message update

## [1.15.60]

### ds

- checking added for date - CNONFLICTS

## [1.15.59]

### ds

- checking added for date

## [1.15.58]

### Update

- FE - beneficiary update

## [1.15.57]

### Swift

- BFIF01

## [1.15.56]

### Update

- FE - Cash declaration api issue fixed

## [1.15.55]

### update

- Beneficiary URL FIXES

## [1.15.54]

### update

- Beneficiary URL

## [1.15.53]

### kswift

- BPPA01 url

## [1.15.52]

### kswift

- BPPA02 url

## [1.15.51]

### Update

- floating check

## [1.15.50]

### Update

- kswift fix payment redirect
- draft print fix

## [1.15.49]

### Update

- dispatch layout
- floating disable

## [1.15.48]

### Update

- FE - Legacy file - user listing duplication in workflow

## [1.15.47]

### Fix

- ui fix and kswift ptx url
- and bp url
- draft fix

## [1.15.46]

### Update

- FE - cash declaration report search issue

## [1.15.45]

### Update

- FE - Merge and link displaying

## [1.15.44]

### KSWIFT

- update with redirect and params

## [1.15.43]

### KSWIFT

- update with kswift url

## [1.15.42]

### Update

- New Exe Setup for DS

## [1.15.41]

### fix

- Floating Search url

## [1.15.40]

### fix

- Floating Search Update

## [1.15.39]

### fix

- Floating Search Update

## [1.15.38]

### fix

- Pull Draft Preview Fix

## [1.15.37]

### fix

- Draft Preview Update

## [1.15.36]

### fix

- Summary re direct issue fixed

## [1.15.35]

### Add

- Date filter in note list

## [1.15.34]

### Add

- File de link Message changes

## [1.15.33]

### Fix

- Expire Fix in DS

## [1.15.32]

### Add

- FE - Bill Generate and Bill Send in strip

## [1.15.31]

### Update

- FE - Cash Declaration Report validation message for employee name

## [1.15.30]

### Update

- FE - Cash Declaration Report download changes

## [1.15.29]

### Update

- FE - Cash Declaration Report Filter

## [1.15.28]

### Update

- Disposal file messages changes

## [1.15.27]

### Update

- Un Link listing issue fixed

## [1.15.26]

### Merge

- dev to rel Conflics Solved missed

## [1.15.25]

### Merge

- dev to rel Conflics Solved

## [1.15.24]

### Update

- Cash declaration reports issue fixed (designation)

## [1.15.23]

### Update

- Disposal files api integration completed
- Disable merge and un merge in feature menu for other than custodian

## [1.15.22]

### Update

- Un merge pagination issue fixed and disposal files initial changes

## [1.15.21]

### fix

- draft edit

## [1.15.20]

### fix

- ds draft cache

## [1.15.19]

### fix

- ds draft issue

## [1.15.18]

### fix

- ds save after approve fix

## [1.15.17]

### fix

- enroll response

## [1.15.16]

### fix

- ds draft save loader

## [1.15.15]

### fix

- ds confirmation fix

## [1.15.14]

### Update

- DS update
- Notes update in draft

## [1.15.13]

### Update

- DS enroll ui,
- Ds enroll version block

## [1.15.12]

### Update

- DS enroll password

## [1.15.11]

### Add

- FE - New strip addition for Demand Cancellation

## [1.15.10]

### Fix

- Fix error message ds enroll
- Fix GFM

## [1.15.9]

### Add

- FE - Agenda url change in General feature menu

## [1.15.8]

### Add

- FE - Legacy File workflow user showing

## [1.15.7]

### fix

- Draft DS Enroll validations

## [1.15.6]

### fix

- Draft DS validations

## [1.15.5]

### fix

- kswift

## [1.15.4]

### fix

- Beneficiary type Fix

## [1.15.3]

### feature

- draft ds preview

## [1.15.2]

### feature

- kswift

## [1.15.1]

### Add

- FE - filter adding in admin login for suspended files

## [1.15.0]

### Fix

- Certificate Fix Draft

## [1.14.72]

### Add

- FE - Return Button in Draft in Recommending Officer

## [1.14.71]

### Add

- FE - Add new icon in strip - Agenda

## [1.14.70]

### Add

- FE - Reassign user after delinking in ADMIN login

## [1.14.69]

### Fix

- Ds jAVA plugin name

## [1.14.68]

### Update

- Ds Date

### Fix

- Ds Fix direct and approve

## [1.14.67]

### Update

- Ds Enroll

## [1.14.66]

### Added

- Disable workflow , notes and drafts by Post id

## [1.14.65]

### Added

- Disable General feature menu by Post id

## [1.14.67]

### Add

Application number filter added in common file track

## [1.14.66]

### Add

Place holder added in common file track

## [1.14.65]

### Change

- Remove description field in arising services

- Enable return button for recommonding officer

## [1.14.64]

### Fix

- Routing keys changes (headId to id in fund)
- GF fix

## [1.14.63]

### Fix

- Add Account id in beneficiary

## [1.14.62]

### Fix

- Digital signature url configuration
- Conficts
- Draft dropdown fix

## [1.14.62]

### Added

base64ToBlob added in utils

## [1.14.61]

### Fix

- Draft Esign Configs

## [1.14.60]

### Feature

- General feature menu latest changes

## [1.14.59]

### Update

- Digital Signature Updates

## [1.14.58]

### Feature

- Digital Signature Integrated for Draft
- General feature menu , note , draft hide based on post id

## [1.14.60]

### Fix

- Arising service - dfms

## [1.14.59]

### Fix

- Arising service

## [1.14.58]

### Fix

- Routing keys changes (headId to id in fund)

## [1.14.57]

### Fix

- Address Delete

## [1.14.56]

### Add

- Implement back button functionality

## [1.14.55]

### Update

- New Draft UI

## [1.14.54]

### Add

- New field addition in General Details in beneficiary and child file issue fixed

## [1.14.53]

### Add

- New field addition in General Details

## [1.14.52]

### Add

- Implement current year in footer

## [1.14.51]

### Add

- Clear icon for input

## [1.14.50]

### Add

- Implement public file tracking

## [1.14.49]

### update

- Arising file fix

## [1.14.47]

### update

- Child code fix

## [1.14.46]

### Add

- Table data style changes in reports

## [1.14.45]

### Feature

- Child File

## [1.14.44]

### Add

- Loader for download in file tracking report

## [1.14.43]

### Fix

- Routekey headid

## [1.14.42]

### Update

- Draft Address Update

## [1.14.41]

### Add

- Table data style changes - latest

## [1.14.40]

### Add

- Table data style changes

## [1.14.39]

### fix

- summary blank page issue - latest

## [1.14.38]

### fix

- summary blank page issue

## [1.14.37]

### fix

- route-key-fix summary and custodian

## [1.14.36]

### fix

- route-key-fix

## [1.14.35]

### fix

- Summary link

## [1.14.34]

### fix

- Routing Keys waste Management

## [1.14.33]

### fix

- Abstract Loader

## [1.14.32]

### fix

- Add notes design in draft notes

## [1.14.31]

### fix

- No notes found message added in notes list

## [1.14.30]

### fix

- Note doc fix

## [1.14.29]

### fix

- Draft Correspodence case
- Note Listing
- File Link and Unlink selection issues
- Draft Recommnt Success Message and Auto not Fix
- Parking Stage

## [1.14.28]

### fix

- user selection issue fixed in arising and counter

- Add inward date in general information

## [1.14.27]

### fix

- Fix validation issue in parking

## [1.14.26]

### Add

- Remove forwarded by in scp list

## [1.14.25]

### Add

- Implement Scp List

## [1.14.24]

### Fix

- unlink file

## [1.14.23]

### Fix

- Note UI fix

## [1.14.22]

### Fix

- Note Scroll fix

## [1.14.21]

### Fix

- Document name clear

## [1.14.20]

### Update

- Note Fix

## [1.14.19]

### Update

- Note last note

## [1.14.18]

### Update

- Arising User Issue

## [1.14.17]

### New UI

- Note Done new ui

## [1.14.16]

### Updates

- Draft Updates

## [1.14.15]

### Updates

- Parking Date min

## [1.14.14]

### Fix

- user api response

## [1.14.13]

### Fix

- File Abtract document name
- reject and return not update

## [1.14.12]

### Update

- Parking file

## [1.14.11]

### Update

- Reject Return note for citizen
- summary updateś

## [1.14.10]

### Added

- Note layout and cards design changes

## [1.14.9]

### Added

- Summary design changes

## [1.14.8]

### Added

- Implement new design for file movement

## [1.14.7]

### Fix

- Draft Auto note Fix

## [1.14.6]

### Fix

- Draft Autonote
- Draft Ceertificate sender
- Document Delete clear

## [1.14.5]

### update

- Implement beneficiary bulk upload

## [1.14.4]

### update

- advance search inward fix

## [1.14.3]

### update

- Implement document view in draft
- Implement receipt details for counter and efile(summary)

## [1.14.2]

### search

- remove inward from advanced search

## [1.14.1]

### Update conflict

- Abstract

## [1.14.0]

### Update

- Abstract

## [1.13.26]

### Merge

- Dev to Rel

## [1.13.25]

### Merge

- Rel to Dev

## [1.13.24]

### update

- User ui update ARISING

## [1.13.23]

### update

- User ui update inward

## [1.13.22]

### Feature

- Arising User Selection

## [1.13.21]

### Feature

- Inward User Selection

## [1.13.20]

### update

- Implement Requisition menu in general feature menu

## [1.13.19]

### update

- Received details showing issue fixed in file tracking

## [1.13.18]

### update

- OTP Issues in efile
- trim first, last and middle name

## [1.13.17]

### update

- Applied date showing issue fixed in file tracking

## [1.13.16]

### update

- Beneficiary search - new

## [1.13.15]

### update

- Implement alert popup in legacy file delete

## [1.13.14]

### update

- Enable Standing Demand menu based on service code in General feature menu

## [1.13.13]

### update

- size changes in Standing Demand in General feature menu

## [1.13.12]

### update

- Implement new menu (Standing Demand) in General feature menu

## [1.13.11]

### update

- Implement warning alert in file tracking search

## [1.13.10]

### update

- Inward search api changes

## [1.13.9]

### update

- Document preview showing issue fixed in arising file

## [1.13.8]

### update

- Re open revert

## [1.13.7]

### update

- Distribution register report api update

## [1.13.6]

### update

- New Beneficiary

## [1.13.5]

### update

- File abstract report api changes

## [1.13.4]

### update

- Change no records found design in file tracking

## [1.13.3]

### update

- New beneficiary merge

## [1.13.2]

### update

- Arrow style issue fixed in file tracking

## [1.13.1]

### update

- current user showing in file tracking

## [1.13.0]

### Revert

- DMDM revert

## [1.12.8]

### update

- Latest design changes in file tracking

## [1.12.7]

### update

- inward and report search
- revert re-open

## [1.12.6]

### update

- DMDM revert
- custodian doorNo

## [1.12.5]

### update

- Data showing issue fixed in File tracking

## [1.12.4]

### update

- DMDM Postoffice

## [1.12.3]

### update

- Draft 0

## [1.12.2]

### update

- Delink multiple
- Dispable Dimand for BP

## [1.12.1]

### update

- Re oppen change to common

## [1.12.0]

### change

- Implement Status based message in file log

## [1.11.24]

### change

- General feature menu disable changes

## [1.11.23]

### change

- Receipt details hide in file summary

## [1.11.22]

### change

- File tracking reports style changes

## [1.11.21]

### change

- File abstract report api changes

## [1.11.20]

### Fix

- Postal Fix

## [1.11.19]

### changes

- implement applied status details in File log design

## [1.11.18]

### changes

- File log design changes

## [1.11.17]

### Fix

- Child file module search

## [1.11.16]

### changes

- Search input style and functionality changes in file

## [1.11.15]

### changes

- Distribution Status Report ui and export changes

## [1.11.14]

### Fix

- de-link instilations name

## [1.11.13]

### changes

- File search

## [1.11.12]

### changes

- File log date conversion changes
- File log table data showing issue

## [1.11.11]

### changes

- File log date issue fix

## [1.11.10]

### changes

- File log design and functionality changes

## [1.11.9]

### changes

- Receipt details changes in summary

## [1.11.8]

### Fix

- beneficiary
- route keys

## [1.11.7]

### update

- log style

## [1.11.6]

### changes

- Route key and users design changes

## [1.11.5]

### Route Key and Users

- updated new from file response
- design changed

## [1.11.4]

### fix

- Applicant Fix
- re open error dialog

## [1.11.3]

### fix

- Draft Pagination

## [1.11.2]

### fix

- Button x

## [1.11.1]

### fix

- Spelling mistake in archived and disposed file

## [1.11.0]

### Fix

- Beneficiary Fix

## [1.10.12]

### Fix

- Payment Hide
- Implement receipt details in file summary

## [1.10.11]

### Fix

- De-link

## [1.10.10]

### Added

- Implement payment details in summary

## [1.10.9]

### Update

- Beneficiary

## [1.10.8]

### Update

- De-link

## [1.10.7]

### Added

- Change failure message in counter

## [1.10.6]

### Added

- Implement un link and de link icons on general features menu

## [1.10.5]

### Fix

- Delete Beneficiary efile

## [1.10.4]

### Fix

- File updates, ben organisation

## [1.10.3]

### Fix

- File status params changes in file search api

## [1.10.2]

### Fix

- Dev Fix Enquiry Officer

## [1.10.1]

### Fix

- Dev Fix for Efile Org Beneficiary

## [1.10.0]

### Confics

- Dev rel merge conflics

## [1.9.30]

### Fix

- Drop updates

### Release

## [1.9.29]

### Fix

- Custodian Change Update

## [1.9.28]

### Updated

- Custodian Change File

## [1.9.27]

### Updated

- Remove payment awaiting message from file log popup

## [1.9.26]

### Updated

- certificate fix

## [1.9.25]

### Fix

- counter postal fix

## [1.9.24]

### Fix

- return to citizen workflow

## [1.9.23]

### Fix

- is postal fix ben

## [1.9.22]

### Fix

- is postal fix

## [1.9.21]

### Fix

- is postal

## [1.9.20]

### Fix

- Fetch Custodian

## [1.9.19]

### Fix

- Date in File Popup

## [1.9.18]

### Fix

- Name fix

## [1.9.17]

### fix

- Dev fixes

## [1.9.16]

### fix

- Dev fixes

## [1.9.15]

### update

- url update

## [1.9.14]

### update

- Custodian Change BP

## [1.9.13]

### update

- Workflow changes for route change

## [1.9.12]

### update

- Implement resend OTP count in citizen form

## [1.9.11]

### Route key

- api and payload change

## [1.9.10]

### Update

- Date picker style with icon changes

## [1.9.9]

### Update

- Version of take action api in workflow

## [1.9.8]

### Update

- File routeky users

## [1.9.7]

### Fix

- Efile Fix oRG

## [1.9.6]

### Fix

- Efile Fix

## [1.9.5]

### Feature

- Change next role api in workflow

## [1.9.4]

### Feature

- Add a new field Applicant Name in File Status Report

## [1.9.3]

### Feature

- Workflow user dropdown issue fixed.

## [1.9.2]

### Feature

- Workflow api change

## [1.9.1]

### Update

- Org efile

## [1.9.0]

### Feature

- Cash Declaration updates

## [1.8.10]

### Hot Fix

- Logger Off

## [1.8.9]

### Hot Fix

- General Details Citizen

## [1.8.8]

### Hot Logger

- Hot Fix

## [1.8.7]

### Hot Fix

- efile org

## [1.8.6]

### Feature

- logger
- Workflow changes

## [1.8.5]

### Hot Fix

- efile
- Distribution register report changes

## [1.8.4]

### Fix

- Efile
- conflics

## [1.8.3]

### Release

- conflics solved

## [1.8.2]

### Feature

- organization efile
- rich fontsize and line-height

## [1.8.1]

### Feature

- Implement Distribution register report in reports

## [1.8.0]

### Fix

- efile?

## [1.7.7]

### Fix

- Date

## [1.7.6]

### Fix

- Workflow

## [1.7.5]

### Fix

- Custodian deduction head

## [1.7.4]

### Fix

- Change calendar icon in notes - summary
- Log Report

## [1.7.3]

### Fix

- Custodian Change

## [1.7.2]

### Fix

- Counter Institution General Error

## [1.7.1]

### Fix

- Summary fix

## [1.7.0]

### Fix

- workflow revert

## [1.6.16]

### Fix

- Change Greeting Message session with New Design

## [1.6.15]

### Fix

- Custodian Change user repeat

## [1.6.14]

### Fix

- Styling fixes

## [1.6.13]

### Fix

- Custodian Change routekey 2

## [1.6.12]

### Fix

- Route Change

## [1.6.11]

### Fix

- Summary Fix

## [1.6.10]

### Fix

- Last Name

## [1.6.9]

### TimeZone

- Updated time zone for note and drafts

## [1.6.8]

### Add

- Implement file abstract report in Report

## [1.6.7]

### Fix

- Efile Verify

## [1.6.6]

### Fix

- Fix

## [1.6.5]

### Fix

- release conflicts

## [1.6.4]

### Fix

- General Preview

## [1.6.3]

### Fix

- Routing Keys Preview

## [1.6.2]

### Fix

- Custodian Change Undefined

## [1.6.1]

### Fix

- Routing Ward Null

## [1.6.0]

### update

- Custodian change menu enable

## [1.5.19]

### update

- Custodian change menu disabled

## [1.5.18]

### revert

- Custodian change menu disabled

## [1.5.17]

### revert

- Revert workflow Response

## [1.5.16]

### update

- Hide Custodian change menu

## [1.5.15]

### update

- Hide File log modal (file log reports)

## [1.5.14]

### Update

- Workflow update

## [1.5.13]

### update

- Route changes (Custodian change)

## [1.5.12]

### update

- Route and name changes (Custodian change)

## [1.5.11]

### update

- saga changes (Custodian change)

## [1.5.10]

### update

- workflow seat

## [1.5.9]

### update

- url changes (Custodian change)

## [1.5.8]

### Back CR

- Back button CR

## [1.5.7]

### Beneficiary

- Beneficiary Fix

## [1.5.6]

### Updates

- CR link

## [1.5.5]

### Updates

- Pagination missing issue fixed (Archived file)

## [1.5.4]

### Updates

- Status showing issue in inward search (Search file)

## [1.5.3]

### Updates

- Cash declaration report changes (data loading in page load)

## [1.5.2]

### Fix

- Last Note Popup

## [1.5.1]

### Updates

- Search file department and seat dropdown changes

## [1.5.0]

### Fix Updates - Release ready

- Routing Keys
- Document Fix

## [1.4.25]

### Updates

- Search file changes

## [1.4.24]

### Updates

- Search file column style fix

## [1.4.23]

### Update

- Last Note for Citizen

## [1.4.22]

### Updates

- Search file tab changing issue fixed

## [1.4.21]

### Updates

- Draft fix

## [1.4.20]

### Updates

- Custodian Change user dropdown change

## [1.4.19]

### Updates

- Serial number showing issue in all tables (Reports, search file)

## [1.4.18]

### Updates

- Add status field in search file table

## [1.4.17]

### Updates

- Work flow route change new fields
- route key previews

## [1.4.16]

### Change

- Implement new dropdown (source type) in search file -> advance search

## [1.4.15]

### Change

- Pull draft and pull notes filter changes

## [1.4.14]

### Change

- Add route for recovery payments for finance module

## [1.4.13]

### Update

- General Details Code Common

## [1.4.12]

### Change

- remove unwanted urls and repeated urls

## [1.4.11]

### Update

- General Details Code Common

## [1.4.10]

### Change

- General feature menu - payment recovery menu added

## [1.4.9]

### Change

- pull draft listing

## [1.4.9]

### Fix and Updates

- Legacy File Updates
- Notes Pagination on Expand

## [1.4.8]

### Fix

- Url path

## [1.4.7]

### Fix

- Service Rename

## [1.4.6]

### Fix

- Documents Rename

## [1.4.5]

### Code Refactor

- Counter, Arising, Efile and Legacy

## [1.4.4]

### Fix

- module and sub module alignment in summary

## [1.4.3]

### Fix

- Mandatory documents loader issue (counter, arising,e file)

## [1.4.2]

### Fix

- Custodian details showing in search file

## [1.4.1]

### Fix

- Current user details showing in search file

## [1.4.0]

### Fix

- File log design changes

## [1.3.9]

### Quick Fix

- Custodian Enable for Operator

## [1.3.8]

### Fix

- Benificiary includesfix

## [1.3.7]

### Fix

- Benificiary refresh fix

## [1.3.6]

### Fix

- Draft Correspondence Filter

## [1.3.5]

### Fix

- First name issue

## [1.3.4]

### Fix

- Table Rezie Remove
- Ruturn route change

## [1.3.3]

### Fix

- Counter Gender

## [1.3.2]

### Fix

- Basic changes - latest

## [1.3.1]

### Counter Fix

- Counter Joint Applicant Fix

## [1.3.0]

### Readying Production Release

- Draft Address Changes

## [1.2.30]

### Fix

- Basic changes - maintain constant variables

## [1.2.29]

### Fix

- Basic changes

## [1.2.28]

### Fix

- I18n changes in file summary

## [1.2.27]

### Fix

- Beneficiary Fix
- Route Change Role
- Draft Office Id

## [1.2.26]

### Fix

- Format fetched firstname in counter

## [1.2.25]

### Fix

- pen and designation missing in note card

## [1.2.24]

### Fix

- Child file clearing issue

## [1.2.23]

### Fix

- Pull Draft and custodian changes

## [1.2.22]

### Fix

- Rich Props for image

## [1.2.21]

### Fix

- Pull note changes

## [1.2.20]

### Update

- Document upload for protected files

## [1.2.19]

### Fix

- child file changes

## [1.2.18]

### Added

- Collapse

## [1.2.17]

### Added

- New Rich Update

## [1.2.16]

### Fix

- Add salary menu in strip

## [1.2.15]

### Fix

- table status formating

## [1.2.14]

### Fix

- Summary General Details

## [1.2.13]

### Fix

- custodian user details with route key - changes

## [1.2.12]

### Fix

- Date Preview and Mobile Number Issue in Joint

## [1.2.11]

### Fix

- Menu Handling - draft menu

## [1.2.10]

### Updated

- Menu Handling

## [1.2.9]

### Removed

- SA/QA env

## [1.2.8]

### Fixes

- File Fixes

## [1.2.7]

### Update

- Mapped users implementation in inward search

## [1.2.6]

### Update

- pagination changes in all tables

## [1.2.5]

### Update

- inward search mapped users showing

## [1.2.4]

### Update

- code cleaning

## [1.2.3]

### Fix

- Mobile

## [1.2.2]

### Fix

- File fixes

## [1.2.1]

### Update

- Applicant name showing issue

## [1.2.0]

### Update

- report - functional group filter issue,
- relaese merge

## [1.1.25]

### Update

- receipt option style issue in general feature menu

## [1.1.24]

### Update

- enable receipt option in general feature menu

## [1.1.23]

### Fix

- Comment officeType for citizen

## [1.1.22]

### Update

- remove unwanted codes

## [1.1.21]

### Update

- Workflow changes and disposed file status change

## [1.1.20]

### fix

- New Routing Keys officecode

## [1.1.19]

### Update

- New Routing Keys update

## [1.1.18]

### Update

- New Routing Keys

## [1.1.17]

### Update

- Workflow changes - remove commented codes

## [1.1.16]

### Update

- Workflow changes

## [1.1.15]

### Update

- Hide gender option in beneficiary general details

## [1.1.14]

### Update

- beneficiary name showing in list issue fixed

## [1.1.13]

### Update

- status styling in file details

## [1.1.12]

### Update

- beneficiary for institution
- summary routing information

## [1.1.11]

### Update

- inward number showing issue in search file list

## [1.1.10]

### Update

- file search by application number in search file - advance search

## [1.1.9]

### Updates

- New Routing key Configs

## [1.1.8]

### Fix

- Summary Applicant from efile

## [1.1.7]

### Update

- route changes in service module

## [1.1.6]

### Update

- Notes Date Format
- General Info for multitple inwards
- service section service code fields
- General info date of event issue

## [1.1.5]

### Update

- cash declaration reports date formating issue - fixed

## [1.1.4]

### Update

- add office id in pull file search and update

## [1.1.3]

### Update

- add dynamic serial number in all tables

## [1.1.2]

### Update

- Counter Institution

## [1.1.1]

### Update

- implement current user with pen in inward search

## [1.1.0]

### Update

- Enable custodian menu

- cash declaration reports changes

## [1.0.64]

### HOT Fix

- Legacy Booking Date Fix
- Workflow pending payment check fix
- service code readonly on efile, counter, arising and legacy
- Legacy General Detail Fix

## [1.0.63]

### HOT Fix

- Legacy Booking Date Fix
- Workflow pending payment check fix
- service code readonly on efile, counter, arising and legacy

## [1.0.62]

### Update

- route changes

## [1.0.61]

### HOT FIX

- Efile File Summary Applicant fix

## [1.0.60]

### Date Fix

- general details date fix
- cash declaration reports changes

## [1.0.59]

### Update

- Draft Role Button

## [1.0.58]

### Update

- Draft and Document Preview

## [1.0.57]

### Update

- fix - summary

## [1.0.56]

### Update

- fix - table filter issue

## [1.0.55]

### Fix

- Summary Applicants

## [1.0.54]

### Update

- temporary commented custodian api call details

## [1.0.53]

### Add

- legacy office

## [1.0.52]

### Update

- remove enquiry and custodian menu in general features menu

## [1.0.51]

### Update

- Remove enquiry menu in general feature menu

## [1.0.50]

### Added

- Service Code Search

## [1.0.49]

### Update

- staging configuration

## [1.0.48]

### Update

- rotate in documents preview - note page

## [1.0.47]

### Update

- search file - searching issue fixed

## [1.0.46]

### Update

- rotate in documents - note page

## [1.0.45]

### Update

- description field removed in common route key page

## [1.0.44]

### Update

- download and print option in document preview - note page

## [1.0.43]

### Update

- new keys

## [1.0.42]

### Update

- Custodian change - update

## [1.0.41]

### Update

- Custodian change implementation

## [1.0.40]

### Fix

- Download Fix

## [1.0.39]

### Update

- Routing key Common

## [1.0.38]

### Update

- File upload loader in counter , arising , legacy, efile

## [1.0.37]

### Update

- File upload validation issue fixed in note

## [1.0.36]

### Update

- Route change implementation issue

## [1.0.35]

### Update

- Draft Release Update

## [1.0.34]

### Update

- File Log report download implementation

## [1.0.33]

### update

- return to citizen disposed list

## [1.0.32]

### Update

- General feature menu disable implemetation

## [1.0.31]

### Fix

- Link summary button fix
- return to citizen

## [1.0.30]

### Update

- General info - update in summary

## [1.0.29]

### Feature

- Efile Resubmit

## [1.0.28]

### Update

- Budget menu in general feature menu size

## [1.0.27]

### Update

- Add Budget menu in general feature menu

## [1.0.26]

### Update

- Add url re direction in search file

## [1.0.25]

### Update

- File all general details

## [1.0.24]

### Update

- legacy file year validation

## [1.0.23]

### Update

- search file view block

## [1.0.22]

### Update

- Reports api changes

## [1.0.21]

### Update

- Counter Fix

## [1.0.20]

### Update

- Reports changes

## [1.0.19]

### Arising

- Arising Fix

## [1.0.18]

### Hold

- file unhold

## [1.0.17]

### Update

- Enable reports

## [1.0.16]

### Update

- counter update
- File demand skip for administrator

## [1.0.15]

### Update

- inward search update

## [1.0.14]

### Update

- File search fix

## [1.0.13]

### Update

- General Date Issue

## [1.0.12]

### Update

- Inward search menu added

## [1.0.11]

### Update

- print and download ack

## [1.0.10]

### Update

- Add stage in time line reports

## [1.0.9]

### Update

- links

## [1.0.8]

### Update

- CI name

## [1.0.7]

### Update

- General Details

## [1.0.6]

### Added

- Button styles

## [1.0.5]

### Fix

- general api

## [1.0.4]

### Added

- File Log reports

## [1.0.3]

### Fix

- general details

## [1.0.2]

### Fix

- general details

## [1.0.1]

### Fix

- benificiary fix Nan and Mandatory Remove

## [1.0.0]

### Release

- Prod release
- benificiary fix
- counter route keys

## [0.5.46]

### updates

- Legacy file update

## [0.5.45]

### updates

- Temporary disposal list changes

## [0.5.44]

### updates

- efile beneficiary

## [0.5.43]

### updates

- fix

## [0.5.42]

### updates

- ok updates

## [0.5.41]

### Fixed

- Beneficiary changes

## [0.5.40]

### Fixed

- Removed training env

## [0.5.39]

### Fixed

- URL

## [0.5.38]

### Update

- Beneficiary update

## [0.5.37]

### Update

- Beneficiary

## [0.5.36]

### Update

- General info in summary

## [0.5.35]

### Update

- CICD updates 2

## [0.5.34]

### Update

- CICD updates

## [0.5.33]

### Fix

- efile otp

## [0.5.32]

### Fix

- efile otp and fix

## [0.5.31]

### Fix

- Malayalam letters and special characters allowed in account number screen

## [0.5.30]

### Fix

- efile update

## [0.5.29]

### Fix

- efile preview

## [0.5.28]

### Fix

- efile updates

## [0.5.27]

### Fix

- efile applicant and beneficiary details

## [0.5.26]

### Fix

- efile changes

## [0.5.25]

### Fix

- add dynamic url in archived file

## [0.5.24]

### Fix

- Remove advance search in legacy and arising

## [0.5.23]

### Fix

- income validation in e file

## [0.5.22]

### Fix

- Legacy File - Issues

## [0.5.21]

### Add

- New Efile Fields and Preview Updates

## [0.5.20]

### Fix

- Option to search and open own file through it from search and advanced search menu

## [0.5.19]

### Fix

- Draft - Error while creating draft in legacy file if address is same as applicant.

## [0.5.18]

### Fix

- Inward - No specific message for User while clicking proceed button in Third accordion in the case income entered below 10

## [0.5.17]

### Fix

- Ui updates

## [0.5.16]

### Fix

- Summary button enable

## [0.5.15]

### Add

- file pending notification

## [0.5.14]

### Fix

- file counter and efile

## [0.5.13]

### Fix

- post office update 3

## [0.5.12]

### Fix

- post office update 2

## [0.5.11]

### Fix

- post office update

## [0.5.10]

### Fix

- post office

## [0.5.9]

### Fix

- Inward | Applicant details | joint application | Applicant details view issue regarding

## [0.5.8]

### Fix

- Enable finance button in summary

## [0.5.7]

### Fix

- city

## [0.5.6]

### Fix

- ROUTEKEY

## [0.5.5]

### Fix

- efile postoffice

## [0.5.4]

### Fix

- Show Beneficiary Details in file summary

## [0.5.3]

### Fix

- Efile Updates

## [0.5.2]

### Fix

- Efile Updates

## [0.5.1]

### Fix

- Efile Updates

## [0.5.0]

### Fix

- LB Property Type combo listed items is Function Group Items

## [0.4.9]

### Fix

- Spelling Mistake in FIle Mangement module

## [0.4.8]

### Fix

- File fixes

## [0.4.7]

### Fix

- Merge file search issue

## [0.4.6]

### Fix

- Draft and Note

## [0.4.5]

### Fix

- Re open changes

## [0.4.4]

### Fix

- EFILE

## [0.4.3]

### Fix

- EFILE

## [0.4.2]

### Fix

- Advance search fix

## [0.4.1]

### Fix

- Counter and Efile

## [0.4.0]

### Fix

- Unmerge - No effect while clicking on Cancel button in the Unmerging screen

## [0.3.100]

### Fix

- Inward Applicant details fix

## [0.3.99]

### Fix

- Re open file changes

## [0.3.98]

### Efile Complete

- Efile complete update
- Draft edit fix
- token update

## [0.3.97]

### Fix

- Inward document edit issue fix

## [0.3.96]

### Fix

- Linkfile search fix

## [0.3.95]

### Fix

- Pull - remove cancel Button and place Close button in Pull Success window

## [0.3.94]

### Fix

- Inward | Applicant Details | Outside Local Body | Outside country |Mobile No field |country code

## [0.3.93]

### Fix

- Wrong service name selected as default in Legacy create menu

## [0.3.92]

### Fix

- Draft fix

## [0.3.91]

### UI

- UI and integration of Unlink file

## [0.3.90]

### Fix

- Need to change first letter in Capital in every field of inward . Now Name field is correct

## [0.3.89]

### File

- Draft Enclosure

## [0.3.88]

### Fix

- efile

## [0.3.87]

### Fix

- Secrets

## [0.3.86]

### Efile

- File efile

## [0.3.85]

### Inward

- Inward subno fix

## [0.3.84]

### Efile Link

- link back

## [0.3.83]

### Leagacy fix

- Legacy bug fix

## [0.3.82]

### eFILE fix

- eFILE

## [0.3.81]

### Fix

- Legacy bug fix

### eFILE

- eFILE

## [0.3.80]

### Fix

- Reports changes

## [0.3.79]

### Register

- counter register

## [0.3.78]

### Fix

- counter

## [0.3.77]

### Fix

- Workflow

## [0.3.75]

### Fix

- Applicant Details and general details Accordion shows only the First name of applicant

## [0.3.74]

### Fix

- freeze the back date in “To Date” Calendar from which date is selected in Advanced search menu

## [0.3.73]

### Fix

- Back button issue

## [0.3.72]

### Fix

- Arising file mandatory document issues

## [0.3.71]

### Fix

- Arising document issues

## [0.3.70]

### Fix

- Draft Fix

## [0.3.69]

### Bug

- Disposal -Temporary Disposal Date select issue

## [0.3.68]

### Auto Note

- Draft Auto Note
- workflow modules

## [0.3.67]

### Bug

- Future date issue in calendars of Mandatory and supporting document in inward

## [0.3.66]

### Bug

- Search inward bug fix

## [0.3.65]

### Bug

- Legacy document upload with document name

## [0.3.64]

### file fix

- applicant and beneficiary view issue fix

## [0.3.63]

### file fix

- File Fix

## [0.3.62]

### Bug

- Legacy save bug fix

## [0.3.61]

### Bug

- Beneficiary bug fix

## [0.3.60]

### Bug

- Search file bug fix

## [0.3.59]

### Re-structuring

- Beneficiary Re-structuring

## [0.3.58]

### Fixes and Updates

- file

## [0.3.57]

### Added

- Loader in tables

## [0.3.56]

### Fix

- Pull Draft

## [0.3.55]

### Fix

- fix

## [0.3.54]

### Fix

- Counter and File Note Draft

## [0.3.53]

### Fix

- Scrollbar issue in search file

## [0.3.52]

### Fix

- pull file changes

## [0.3.52]

### WORKFLOW

- Dispose Issue

## [0.3.51]

### Bug

- Search file Validation bug fix

## [0.3.50]

### Update

- Beneficiary updates in summary page

## [0.3.49]

### Fix

- Counter Phone

## [0.3.48]

### Fix

- Preview and Edit

## [0.3.47]

### Fix

- Counter and Note

## [0.3.46]

### Fix

- back file

## [0.3.45]

### Fix

- Add validations in e file page

## [0.3.44]

### Fix

- navigation draft

## [0.3.43]

### Fix

- module and sub module showing in summary

## [0.3.42]

### Fix

- merge link - search

## [0.3.40]

## [0.3.41]

### Fix

- Legacy File | Attachment document | attached document details , Preview not listing

## [0.3.40]

### Fix

- Efile Preview

## [0.3.39]

### Fix

- Efile Docs

## [0.3.37]

### Fix

- Counter loading

## [0.3.36]

### Fix

- Place backbutton in search file

## [0.3.35]

### Fix

- Legacy and inward file fixes

## [0.3.34]

### Fix

- Legacy File Creation | Services |Validation

## [0.3.33]

### Rich Fix

- Rich Fix alignment
- credit remove

## [0.3.32]

### Fix

- Merge and Link

## [0.3.31]

### Fix

- User can upload .Xls, .doc, .txt files in supporting document field, in supporting document field. PDF and Image only required

## [0.3.30]

### Fix

- No Need to List all Citizen Services File Type in Arising File Creation, Now All Citizen Service file types are Listed to Arising File Creation Screen,

## [0.3.29]

### Fix

- Arising and search file fix

## [0.3.28]

### Fix

- Scroll to input

## [0.3.27]

### Merge file

- Bug fix

## [0.3.26]

### Draft

- Draft Type

## [0.3.25]

### counter service

- counter fix

## [0.3.23]

### note doc fix

- documents fix

## [0.3.22]

### note doc updates

- documents added

## [0.3.21]

### Updates

- Services | New Inward | Validations

## [0.3.20]

### Bug

- DoorNo issue fix in inward

## [0.3.19]

### Updates

- Module and Sub Module shows undefined on status when legacy file open- DFCS58

## [0.3.18]

### Updates

- change payload in child file

## [0.3.17]

### Bug

- Inward Bug fixes

## [0.3.16]

### Updates

- There is no way to enter the applicant name who had name have four part

## [0.3.15]

### Updates

- Applicant Detail ല്‍ Whatsapp Phone Number ആദ്യം സീറോ കൊടുത്താലും ആദ്യ നംപര്‍ 1 മുതല്‍ 5 വരെ കൊടുത്താലും മെസ്സേജ് ഒന്നും വരുന്നില്ല ,

## [0.3.14]

### Updates

- In General Information , need to restrict Door No field validation up to 5 digits.No

## [0.3.13]

### draft and noteś

- draft update

## [0.3.12]

### Bug

-Bug fixes

## [0.3.11]

### Added

- File Summary page- There is no option to know the inward number of that file

- Supporting document name കൊടുക്കാതെ upload ചെയ്യാന്‍ കഴിയുന്നു. popup msge needed

- Issue Date Future date selection possible

- Valid up to \_ calendar view year only up to 2023

## [0.3.10]

### fix and update

- draft update
- file updates
- workflow changes

## [0.3.9]

### Updates

- Advance search Updates

## [0.3.8]

### Updates

- Beneficiary modifications

## [0.3.7]

### Added

- Inward| Applicant Details | Institution Details| Reference No | Allow special characters also

## [0.3.6]

### Added

- we cant de select the year from combo in link file search window

## [0.3.5]

### Added

- New Inward | General Information | Label change

## [0.3.4]

### fix

- counter

## [0.3.3]

### Added

- File Search heading label issue

## [0.3.2]

### Added

- Arising File general details Uncheck issue

## [0.3.1]

### counter

- api

## [0.3.0]

### efile

- efile update

## [0.2.251]

### Fixes

- In file summary page status portion not shown submitted by ,created on and Current status details

## [0.2.250]

### Fixes

- Add validations for child file form

## [0.2.248]

### Fixes

- Inward sidebar fix

## [0.2.249]

### Fixes

- Bug fix

## [0.2.247]

### Fixes

- Search menu issue reg

## [0.2.246]

### Fixes

- merge and link

## [0.2.245]

### Fixes

- Pull file Service | Department Selection uncheck not working

## [0.2.244]

### Fixes

- GFM

## [0.2.243]

### Fixes

- Child file - Error while creating child file

## [0.2.242]

### fix

- added disable

## [0.2.241]

### Fixes

- Archived and Temporary Disposed Tables Changes

## [0.2.240]

### New feature

- UI and integration of Beneficiaries

## [0.2.239]

### Fixes

- workflow, pdf and doc preview

## [0.2.238]

### Fixes

- Invalid phone number കൊടുത്താല്‍ യൂസര്‍ ന് warning message വരുന്നില്ല. save fail ആകുന്നു.

## [0.2.237]

### Fixes

- File bug fixes

## [0.2.236]

### Fixes

- In Door No field Malayalam character entry not restricted

## [0.2.235]

### Fixes

- passport issue

## [0.2.234]

### Fixes

- Legacy File - File year combo not working properly

## [0.2.233]

### Fixes

- Legacy File - No specific error message while entering 'last correspondence date' greater than 'File note end date'

## [0.2.232]

### Ward

- issue

## [0.2.231]

### Revert

- service

## [0.2.230]

### Fixes

- Need to show the User name and Designation in all Page

## [0.2.229]

### Fixes

- Header Fixed

## [0.2.228]

### Fixes

- Draft Correspondence

## [0.2.227]

### Fixes

- Legacy File - File year combo not working properly

## [0.2.226]

### Fixes

- No auto note for merging/un merge/link files

## [0.2.224]

### Fixes

- Inward | Applicant Details | Phone no

## [0.2.225]

### Fixes

- blank screen issue arrising file

## [0.2.223]

### Fixes

- note expand issue
- routing action for note
- customtab update

## [0.2.222]

### Fixes

- Post Office Combo Loading issue Corresponding to the district

## [0.2.221]

### Fixes

- Summary page blank issue

## [0.2.220]

### Fixes

- Bug fixes

## [0.2.219]

### Fixes

- delete doc fix

## [0.2.218]

### Fixes

- workflow confirmations
- routekey api removal

## [0.2.217]

### Fix

- Subject should be renamed as " Tittle" in merge/link

## [0.2.216]

### Workflow

- workflow update

## [0.2.215]

### Rich Text

- package update

## [0.2.214]

### Draft

- Draft Address edit bug fix

## [0.2.213]

### workflow

- integration for finance

## [0.2.211]

### Feature

- Add back button functionality in merged file menu

## [0.2.212]

### Fix and updates

- Counter Fix
- file exists

## [0.2.210]

### Fix and updates

- Counter Fix

## [0.2.209]

### Feature

- Implement validation restrictions

## [0.2.208]

### Bug

- fix future date issue in counter

## [0.2.207]

### Bug

- Bug fixes

## [0.2.206]

### Counter Fix

- Counter Fix

## [0.2.205]

### Draft Enclousure

- Draft Enclousure
- functional group
-

## [0.2.204]

### Bug

- Inward and summary bug fix

## [0.2.203]

### Workflow

- functional group

## [0.2.202]

### File and Counter

- Draft Preview
- Counter Fixes

## [0.2.201]

### Draft update

- Draft and existing file

## [0.2.200]

### Update

- Add to existing file api updates

## [0.2.199]

### Update

- Dashboard table params

## [0.2.198]

### Counter and Efile

- Efile and Counter Updates

## [0.2.197]

### Update

- Dashboard table changes

## [0.2.196]

### Update

- Remove cash declaration

## [0.2.195]

### Bug

- Bug fixes

## [0.2.194]

### Counter Ksmart and Aadhar

- Ksmart Integration

## [0.2.193]

### Counter Fix

- Counter Bugs

## [0.2.192]

### Counter Bugs and Draft Updates

- Counter Bugs
- Draft Updates

## [0.2.191]

### Bug

- Date time issue in note

## [0.2.190]

### Bug

- Bug fixes

## [0.2.189]

### File Updates

- ui changes and updates

## [0.2.188]

### Update

- Child file changes

## [0.2.187]

### Bug

- Inward and Arising Bug fixes

## [0.2.186]

### Draft and File Updates

- Draft and File Updates

## [0.2.185]

### Update

- Archived File List - update

## [0.2.184]

### Bug fix

- Inward Bug fix

## [0.2.183]

### Bug fix

- Inward Bug fixes

## [0.2.182]

### Added

- Malayalam regex added in counter - Applicant form
-

## [0.2.181]

### qa config

- qa deploy config

## [0.2.180]

### Added

- Remove unwanted icons from main header

## [0.2.179]

### Bug

- Bug fix

## [0.2.178]

### Added

- Implement back button and functionality in general features menu pages

## [0.2.177]

### fix

- General details Bug fix

## [0.2.176]

### fix

- fix bugs

## [0.2.175]

### qa config

- qa deploy config

## [0.2.174]

### config

- deploy config

## [0.2.173]

### Added

- Add view in archived Table

## [0.2.172]

### Bug

- Bug fix

## [0.2.171]

### certificate

- deploy certificate

## [0.2.170]

### config update

- deploy config

## [0.2.169]

### Bug

- FormModal change

## [0.2.168]

### config

- deploy config

## [0.2.167]

### Draft

- Draft

## [0.2.166]

### Counter and File

- Bug fixes and Updates
- Org Merge

## [0.2.165]

### Added

- Processed File Menu

## [0.2.164]

### Bug

- If multiple offices are mapped against same penNo, primary to be loaded and office selection changed, list have to be changed accrdingly,

## [0.2.163]

### Bug

- Bug fixes in inward listing

## [0.2.161]

### Added

- Employee dashboard changes

## [0.2.160]

### Added

- Archived File Module implementation

## [0.2.159]

### Update

- Arising File mandatory documents changes

## [0.2.158]

### Update

- Loader in draft , Enquiry

## [0.2.162]

### e-file

- e-file updated till preview

## [0.2.157]

### file

- inward postid
- draft pdf viewer update
- blob print and download
-

## [0.2.156]

### draft fix

- Draft loader issue

## [0.2.155]

### draft fix

- Copy to error

## [0.2.153]

### Fix

- # Fix draft copy to

## [0.2.154]

### Integration

- Inward Details documents View in modal

## [0.2.152]

### Integration

- Inward Details View

## [0.2.150]

### Update

- Services Side-menu selection changes

## [0.2.151]

### Fix and Update

- Fix and Update

## [0.2.149]

### Update and fix

- Fix and Update

## [0.2.148]

### Integration

- Integration of pull-note

## [0.2.147]

### Update

- Common configs and fix

## [0.2.146]

### UI updates

- Pull draft UI updates

## [0.2.145]

### Added

- Add scrollbar style

## [0.2.144]

### Added

- Note card update with collapse icon

## [0.2.143]

### Added

- Implement I18n malayalam

## [0.2.142]

### Issue fix

- Update draft issue fix

## [0.2.141]

### Demo Fix

-- fixes

## [0.2.140]

### Demo Fix

- Draft edit options
- counter general details

## [0.2.139]

### Update

- Note list in draft

## [0.2.138]

### Added

- Implement back routes for General Features menu

## [0.2.137]

### Added

- Dashboard style changes

## [0.2.136]

### draft

- draft role

## [0.2.135]

### draft edit

- draft role edit

## [0.2.134]

### draft

- draft role

## [0.2.133]

### Added

- Structure changes

## [0.2.132]

### login

- login

## [0.2.131]

### Fix

- postid

## [0.2.130]

### Fix and Updates

- File
- Counter

## [0.2.129]

### Added

- child file seat response changes
- claim url changes

## [0.2.128]

### Fix

- Custom modal implementation

## [0.2.125]

### Create draft bug Fix

- Create draft bug fix

## [0.2.124]

### Fix

- Dashboard redirect issue

## [0.2.123]

### Fix

- Legacy create issue

## [0.2.127]

### Fix

- Draft Fixes

## [0.2.126]

### Fix Api Integration

- Counter Fixes

## [0.2.122]

### Fix

- Draft Changes preview
- Counter Fixes

## [0.2.121]

### Bug fix

- Bug fixes in draft

## [0.2.120]

### Fix

- Note and arising documents update

## [0.2.119]

### Fix

- Arising Changes

## [0.2.118]

### Fix

- Draft Changes

## [0.2.117]

### Fix

- Notes Changes

## [0.2.115]

### Bug Fix

- UI - Legacy file styling issue fix

## [0.2.114]

### Fix

- UI - Arising file api integrations

## [0.2.112]

### Fix

- UI - Child File UI changes

## [0.2.116]

### Fix

- Preview for counter fix

## [0.2.113]

### Fix

- General Details

## [0.2.111]

### Fix

- Counter api updates
- custom validations

## [0.2.110]

### Bug Fixes

- Bug fixes in draft page

## [0.2.109]

### Fix

- Add General details in arising file

## [0.2.108]

### Fix

- conflics fix user info

## [0.2.107]

### Mdms fixes

- Mdms fixes
- office id

## [0.2.106]

### Integration

- Arising File Changes

## [0.2.105]

### Integration

- Draft exists or not api integrations

## [0.2.104]

### Changes

- Draft- UI changes

## [0.2.103]

### Integration

- Summary page Notes update

## [0.2.102]

### Draft Fix

- Create Draft

## [0.2.101]

### Integration

- Summary page update

## [0.2.100]

### Counter

- num update

## [0.2.99]

### Counter

- fix

## [0.2.98]

### Integration

- Office Id Update

## [0.2.97]

### UI - updates

- Add Expand button in notes

## [0.2.95]

### Integration

- Implement Employee Dropdown data

## [0.2.94]

### Bug fix

- Bug fix in File status.

## [0.2.93]

### Integration

- File status report params API changes

## [0.2.92]

### Integration

- UI - Link and merge issue fixed

## [0.2.91]

### Integration

- UI - File status report API updates

## [0.2.90]

### Integration

- UI - Legacy changes

## [0.2.89]

### Integration

- UI - File status report changes - latest

## [0.2.88]

### Integration

- UI - Cash Declaration Report UI changes - latest

## [0.2.87]

### Integration

- UI - Cash Declaration Report UI changes

## [0.2.86]

### Integration

- File status report Search file API Integration

## [0.2.85]

### Ui fix

- UI - Arising File Changes

## [0.2.84]

### Summary Updates

- Updates in Summary page

## [0.2.83]

### Ui update

- Layout Style
- E file
- Counter
-

## [0.2.82]

### Ui fix

- UI - Integrate Finance Module

## [0.2.81]

### Integration

- Integration of Un-merge files

## [0.2.80]

### Ui fix

- E-file updates

## [0.2.96]

### Ui fix

- UI - Search File validation Changes

## [0.2.79]

### Ui fix

- UI - Search File Changes

## [0.2.78]

### Ui fix

- UI - HoldFile api change Integration

## [0.2.77]

### Bug fix

- Link and Merge Files

## [0.2.76]

### Ui fix

- Legacy file changes

## [0.2.75]

### Ui fix

- UI Updates

## [0.2.74]

### Bug fix

- Bug fixes in draft

## [0.2.73]

### Update

- Search file api integrations - first level

## [0.2.72]

### Bug

- Draft and Summary Bug fixes

## [0.2.71]

### Update

- UI - Applicant Details List

## [0.2.70]

### Update

- I18n changes

## [0.2.69]

### fix

- file preview

## [0.2.68]

### Update

- file preview

## [0.2.67]

### Update

- Sidebar changes

## [0.2.65]

### Update

- file strip menu changes

## [0.2.66]

### Update

- file and inward

## [0.2.64]

### Update

- file and inward

## [0.2.63]

### Update

- Home page alignment and color changes

## [0.2.62]

### Update

- file updates done

## [0.2.61]

### Update

- Side menu JSON update

## [0.2.60]

### Update

- file Update

## [0.2.59]

### Update

- file strip menu changes

## [0.2.58]

### Update

- file Update

## [0.2.57]

### Fix

- Fix

## [0.2.56]

### FIX

- CARD FIX

## [0.2.55]

### Added

- Added QA env

## [0.2.54]

### File

- File Done

## [0.2.53]

### Counter and File

- Counter Module Fix

## [0.2.52]

### UI Updations

- Summary page UI and Validation changes

## [0.2.51]

### Counter and File

- Counter Module FIle

## [0.2.50]

### Bug

- Applicant Name bug fix

## [0.2.49]

### Bug

- Hide Dashboard table's Some field's

## [0.2.48]

### Bug

- Cash declaration issue fixed

## [0.2.47]

### Bug Fix

- Note in Summary bug fix

## [0.2.46]

### Counter Update

- Counter Module Complete

## [0.2.45]

### Bug

- Bug fix in Summary page

## [0.2.44]

### Updated

- Dashboard table changes

## [0.2.43]

### Update

- Summary page,sub-module and module api changes

## [0.2.41]

### Update

- Legacy and arising file service,sub-module and module aip changes

## [0.2.42]

### Counter and File fIX

- Counter and File Update fIX

## [0.2.40]

### Counter and File

- Counter and File Update

## [0.2.39]

### Updated

- Dashboard table API Integration

## [0.2.38]

### UI Update

- Counter UI Update

## [0.2.37]

### UI

- UI for Cash Declaration report

## [0.2.36]

### Updated

- Update Module and Sub modules API's

## [0.2.35]

### UI Update

- Counter UI Update

## [0.2.34]

### Updated

- Pull Draft UI changes

## [0.2.33]

### Updated

- Added Missing Validation in Report Common Filter

## [0.2.32]

### Added

- UI-Pull Notes

## [0.2.31]

### UI Update

- Counter UI Update

## [0.2.30]

### Update

- UI-Address modal changes

## [0.2.29]

### Added

- UI-Pull Draft

## [0.2.28]

### Modification

- Draft Modifications

## [0.2.27]

### Update

- Workflow api

## [0.2.26]

### Added

- change Verification name to login

## [0.2.25]

### Added

- Verification page implementation

## [0.2.24]

### Update

- Change params.id to params.fileNo

## [0.2.23]

### Fix

- Pagination

## [0.2.22]

### Fixed

- Fixed S3 sync issue
- General details, file layout

## [0.2.20]

### Update

- Workflow updates

## [0.2.19]

### Create

- Create workflow as a common module

## [0.2.18]

### fix

- Fix for notes

## [0.2.17]

### Route Change

- Sidebar Route changes

## [0.2.16]

### Changes

- Counter Applicant, General

## [0.2.15]

### Integration

- Add sort option in Legacy list

## [0.2.14]

### Integration

- UI - List Hold files and Unhold file - Api implementation

## [0.2.13]

### UI

- UI - For create child file

## [0.2.12]

### Integration

- UI - Search File in Employee Login - Changes

## [0.2.11]

### Changes

- Counter Applicant Details

## [0.2.10]

### Integration

- UI - Search File in Employee Login

## [0.2.9]

### Integration

- UI - List Hold files and Unhold file
- ## [0.2.8]

### File Updates

- file changes

## [0.2.7]

### Integration

- I18n changes and code refactor

## [0.2.6]

### Integration

- UI - Pull file Integration

## [0.2.5]

### fix

- UI - Hold and Unhold File api integration

## [0.2.8]

### update

- draft and note api updates

## [0.2.4]

### update

- yup form mode for all forms

## [0.2.3]

### Bug fix

- Merge and link file bug fixes

## [0.2.2]

### fix

- side menu heading Changes

## [0.2.1]

### fix

- I18n Changes

## [0.2.0]

### fix

- Implement Day Time Period

## [0.1.59]

### fix

- Demo file fix for merge and link

## [0.1.58]

### UI change

- Add two Additional fields in Summary

## [0.1.57]

### Bug

- Bug fix in Unmerge files

## [0.1.56]

### Bug

- Bug fix

## [0.1.55]

### Update

- File Major Updates, Notes, Draft, Summary UI

## [0.1.54]

### Integration

- Integration of Link and merge files

## [0.1.53]

### Update

- UI - DFMS left side Menu - route changes

## [0.1.52]

### Update

- UI - DFMS left side Menu

## [0.1.51]

### Update

- UI - Employee Home Page (Dashboard) - route changes

## [0.1.50]

### Update

- UI - Employee Home Page (Dashboard)

## [0.1.49]

### Update

- Notes, merged and linked

## [0.1.48]

### Update

- Summary details Modifications

## [0.1.47]

### Update

- Legacy File Bug Fixes

## [0.1.46]

### Update

- File routes, merged and unmerged

## [0.1.45]

### Update

- Notes Documents, Draft

## [0.1.44]

### update

- Add to existing file url change

## [0.1.43]

### update

- Notes Update

## [0.1.42]

### update

- UI - Common left menu for all modules

## [0.1.41]

### update

- Notes File update

## [0.1.40]

### update

- Arising Bug

## [0.1.39]

### update

- Legacy Bug

## [0.1.38]

### update

- New structure for file

## [0.1.37]

### update

- I18n changes

## [0.1.36]

### Bugs and Fix

- counter module bugs and updates

## [0.1.35]

### update

- Arising File Changes

## [0.1.34]

### update

- Route Changes

## [0.1.33]

### Integration

- UI -Integration of Add To Existing File

## [0.1.32]

### update

- UI -Legacy File Backend api Integration
- Arising , cash declaration changes

## [0.1.31]

### Updated

- counter module bugs and updates
- enquiry page completed

## [0.1.30]

### update

- UI - Cash Declaration frontend- backend api Integration

## [0.1.29]

### update

- enquiry page update with document listing

## [0.1.28]

### update

- enquiry page update notes card
- Summary details page updates

## [0.1.27]

### update

- enquiry page update

## [0.1.26]

### Integration

- Summary details Integration

## [0.1.25]

### update

- enquiry page update

## [0.1.24]

### update

- create api
- enquiry page update

## [0.1.22]

### fix

- inbox get api

## [0.1.23]

### Added

- UI-File Abstract Report

## [0.1.22]

### Added

- UI-Pending File Count Report

## [0.1.21]

### UI

- UI for File status and Delayed files

## [0.1.20]

### fix

- Date issue fix

## [0.1.19]

### UI

- UI for Pending files

## [0.1.18]

### updated

- Inward List, Draft, inbox list with filter

## [0.1.17]

### Inward List, Draft

- status fetch

## [0.1.16]

### Added

- UI - Menu Changes

## [0.1.15]

### New Inward Fix

- fix validation issues and messages

## [0.1.14]

### Added

- UI - Arising File backend api integration

## [0.1.13]

### Note Integration

- Summary Details Note Integration

## [0.1.12]

### New Inward Fix

- general details fix

## [0.1.11]

### New Inward Fix

- postoffice

## [0.1.10]

### New Inward Fix

- required fields
- common table

# Changelog

## [0.1.9]

### New Inward Fix

- Updates and fixes

## [0.1.8]

### New Inward Fix

- Updates and fixes payload

## [0.1.7]

### New Inward

- Updates and fixes

## [0.1.6]

### Added

- UI-Distribution Register Report

## [0.1.5]

### Integration

- UI - Draft Integration

## [0.1.4]

### UI - Update

- UI - Note Integration Changes

## [0.1.3]

### UI - File Status Reports

- UI - File Status Reports Ui

## [0.1.2]

### Integration

- Integration changes

## [0.1.1]

### Inbox Listing

- Inbox Listing Ui

## [0.1.0]

### New Inward Completed Integration

- New Inward - Counter Completed

## [0.0.36]

### Added

- UI - HomePage api Integration

## [0.0.35]

### Integration

- Applicant details, General Details Api Integration

## [0.0.34]

### Added

- UI - Employee Home page

## [0.0.33]

### Added

- UI-Modal pop-up for unmerged files

## [0.0.32]

### Added

- UI-Same seat merge files

## [0.0.31]

### Added

- UI-Cash Declaration

## [0.0.30]

### Added

- General Details Ui and Joint Applicant Update

## [0.0.29]

### Added

- UI - Legacy File Modifications - changes

## [0.0.28]

### Update

- UI-Create notes Bug fix

## [0.0.27]

### Added

- UI-Create notes

## [0.0.26]

### TABLE FIX revert

- map error revert

## [0.0.25]

### TABLE FIX TRY

- map error

## [0.0.24]

### Api Integration

- api file creation

## [0.0.23]

### Added

- UI-Un HoldFile - changes

## [0.0.22]

### Fix

- KFM-179 map fix

## [0.0.21]

### Added

- UI-HoldFile - changes

## [0.0.20]

### Fix

- KFM-179 pagination fix

## [0.0.19]

### Api Integration

- KFM-179 Inward list

## [0.0.18]

### Api Integration

- KFM-173 Counter Application service and applicant api integration

## [0.0.17]

### Updated

- UI - Draft new Right-side Menu and pop-ups -Bug fix

## [0.0.16]

### Added

- UI - Draft new Right-side Menu and pop-ups

## [0.0.15]

### Added

- KFM-169 UI draft Institutional and Electoral address modal

## [0.0.14]

### Added

- KFM-156 UI - Custom Component TextEditor Enquiry

## [0.0.13]

### Added

- KFM-28 UI draft individual address modal

## [0.0.12]

### Added

- KFM-160 Image Recorder in Enquiry

## [0.0.11]

### Updated

- KFM-32 Pull File

## [0.0.10]

### Added

- KFM-32 Pull File

## [0.0.9]

### Added

- KFM-158 Inbox Video Recorder in Enquiry

## [0.0.8]

### Added

- KFM-159 Inbox Audio Recorder in Enquiry

## [0.0.7]

### Fix

- Dev intergration ui routes fix

## [0.0.6]

### Fix

- Dev intergration Fix in file name issues

## [0.0.5]

### Fix

- Dev intergration Fix in legacy and arising file name issues

## [0.0.4]

### Fix

- Dev intergration Fix in legacy and arising pages

## [0.0.3]

### Fix

- Dev intergration Route Fix

## [0.0.2]

### Added

- Enquiry page Location Map

## [0.0.1]

### Added

- Pipeline improvements added

## [0.0.0]

### Added

- Project setup completed
- Added required dependencies
