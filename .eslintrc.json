{
    "env": {
        "browser": true,
        "es2021": true,
        "node": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:react/recommended",
        "airbnb"
    ],
    "overrides": [],
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module",
        "ecmaFeatures": {
            "jsx": true
        }
    },
    "plugins": [
        "react",
        "react-hooks"
    ],
    "rules": {
        "linebreak-style": "off",
        "react-hooks/rules-of-hooks": "error", // Checks rules of Hooks
        "react-hooks/exhaustive-deps": "off", // Checks effect dependencies
        "react/jsx-one-expression-per-line": 0,
        "react/react-in-jsx-scope": 0,
        "react/function-component-definition": 0,
        "arrow-body-style": 0,
        "import/prefer-default-export": 0,
        "react/prop-types": 0,
        "no-unused-vars": 1,
        "no-spaced-func": 2,
        "no-trailing-spaces": 2,
        "import/no-unresolved": 0, //TODO: need to fix 
        "comma-dangle": [
            "error",
            "never"
        ],
        "import/extensions": 0,
        "react/jsx-props-no-spreading": 0,
        "react/button-has-type": 0,
        "max-len": 0
    },
    "settings": {
        "react": {
            "version": "detect"
        }
    }
}