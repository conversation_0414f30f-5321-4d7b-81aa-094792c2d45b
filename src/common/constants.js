import ImageIcon from 'assets/Image';
import {
  DocumentSvg,
  ExcelSvg,
  PdfSvg
} from './components/DocumentPreview/Icons';

export const REQUEST_METHOD = {
  GET: 'GET',
  PUT: 'PUT',
  POST: 'POST',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
  MULTIPART: 'MULTIPART'
};
export const CONTENT_TYPE = {
  APPLICATION_JSON: 'application/json'
};

export const HTTP_HEADERS = {
  'Content-Type': CONTENT_TYPE.APPLICATION_JSON,
  Accept: CONTENT_TYPE.APPLICATION_JSON
};

export const DEFAULT_COUNTRY = {
  id: 501077,
  code: 'IND',
  name: 'India',
  nameInLocal: 'ഇന്‍ഡ്യ',
  active: true,
  nationalityName: 'Indian',
  nationalityNameInLocal: 'ഇന്ത്യൻ'
};

export const DEFAULT_STATE = {
  id: 502032,
  code: 'K<PERSON>',
  name: 'Kerala',
  nameInLocal: 'കേരളം',
  active: true,
  countryId: 501077,
  lgdStateCode: 32,
  localCode: 'kl',
  stateType: 'S',
  tcsId: 103,
  tcsCode: 'KL'
};

export const DEFAULT_DISTRICT = {
  id: 503001,
  code: 'TVP',
  name: 'Thiruvananthapuram',
  nameInLocal: 'തിരുവനന്തപുരം',
  active: true,
  districtId: 0,
  stateId: 502032,
  mgrnDistrictId: 1,
  lgdState: 'KERALA(State)',
  lgdDistCodeId: 565,
  tcsId: 2072,
  tcsCode: '"KLTV"'
};

export const STATE = { id: '14', code: 'kl', name: 'Kerala' };
export const COUNTRY = { id: '1', code: 'IND', name: 'India' };

export const REQUEST_STATUS = {
  PROGRESS: 'PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED'
};

export const TOST_CONFIG = {
  duration: 4000,
  isClosable: true,
  status: 'success',
  position: 'top-right',
  variant: 'left-accent'
};

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'kSmart-token',
  USER_DETAILS: 'user-details',
  USER_ROLES: 'roles',
  OFFICE_ID: 'office-id'
};

export const DEVELOPER_SETTINGS = {
  ENABLE_EXAMPLES: false,
  ENABLE_IN_PROGRESS_FEATURES: false,
  ENABLE_LOGGER: {
    RELEASE: true,
    DEV: false
  }
};

export const LOGGER_KEY = {
  RELEASE: 'utxodq/filemanagement',
  DEV: 'utxodq/dev-ksmart'
};

export const USER_TYPE = {
  CITIZEN: 'CITIZEN',
  EMPLOYEE: 'EMPLOYEE',
  ORGANIZATION: 'ORGANIZATION'
};

export const BASE_PATH = '/ui/file-management';
export const HOME_UI_PATH = 'ui';
export const BASE_UI_PATH = `${window.location.origin}/ui/`;
export const MODULE_PATH = `${BASE_PATH}/application`;
export const HOME_PATH = `${window.location.origin}/ui/home/<USER>/dashboard/files`;
export const HOME_CITIZEN_PATH = `${window.location.origin}/ui/home/<USER>/dashboard`;
export const FINANCE_BASE_PATH = `${window.location.origin}/ui/fin`;
export const EMPLOYEE_SERVICE_PATH = `${window?.location?.origin}/ui/home/<USER>/services`;
export const CITIZEN_SERVICE_PATH = `${window?.location?.origin}/ui/home/<USER>/new-services`;
export const MEETING_MANAGEMENT_BASE_PATH = `${window.location.origin}/ui/meeting-management`;
export const AUDIT_APPLICATION_PATH = `${window?.location?.origin}/ui/home/<USER>/application`;
export const EMPLOYEE_LOGIN_PATH = `${HOME_UI_PATH}/home/<USER>/login`;

export const JAR_VERSION = '1.1.2';

export const MODULE_PATHS = {
  CR: 'cr',
  FILE: 'file-management',
  HOME: 'home',
  WEB_PORTAL: 'web-portal',
  PGR: 'pgr',
  BP: 'building-permit',
  FIN: 'fin'
};

export const FINANCE_MODULES = {
  DEMAND: {
    KEY: 'demand',
    URL: `${FINANCE_BASE_PATH}/demand-generation`
  },
  CLAIM: {
    KEY: 'claim',
    URL: `${FINANCE_BASE_PATH}/internal-claim`
  },
  PAYMENT_ORDER: {
    KEY: 'paymentOrder',
    URL: `${FINANCE_BASE_PATH}/payment-order-creation`
  },
  PAYMENT: {
    KEY: 'payment',
    URL: `${FINANCE_BASE_PATH}/payment-inbox`
  },
  BUDGET: {
    KEY: 'budget',
    URL: `${FINANCE_BASE_PATH}/budget-create`
  },
  SALARY: {
    KEY: 'salary',
    URL: `${FINANCE_BASE_PATH}/salary-payment`
  },
  RECEIPT: {
    KEY: 'receipt',
    URL: `${FINANCE_BASE_PATH}/receipt`
  },
  RECOVERY_PAYMENT: {
    KEY: 'recoveryPayment',
    URL: `${FINANCE_BASE_PATH}/payment-recoveries`
  },
  STANDING_DEMAND: {
    KEY: 'standing-demand',
    URL: `${FINANCE_BASE_PATH}/standing-demand`
  },
  REQUISITION: {
    KEY: 'requisition',
    URL: `${FINANCE_BASE_PATH}/requisition-details-creation`
  },
  OWN_FUND_PAYMENT: {
    KEY: 'ownFundpayment',
    URL: `${FINANCE_BASE_PATH}/own-fund-payment-inbox`
  },
  REQUISITION_ALLOTMENT: {
    KEY: 'requisitionAllotment',
    URL: `${FINANCE_BASE_PATH}/requisition-details/allotment`
  },
  DEMAND_CANCELLATION: {
    KEY: 'demandCancellation',
    URL: `${FINANCE_BASE_PATH}/demand-cancellation`
  },
  BILL_GENERATE: {
    KEY: 'billGenerate',
    URL: `${FINANCE_BASE_PATH}/requisition-details/bill-generate`
  },
  BILL_SEND: {
    KEY: 'billSend',
    URL: `${FINANCE_BASE_PATH}/requisition-details/bill-send`
  },
  IMPREST_CLAIM: {
    KEY: 'imprestClaim',
    URL: `${FINANCE_BASE_PATH}/imprest-claim/file`
  },
  ADVANCE_CLAIM: {
    KEY: 'advanceClaim',
    URL: `${FINANCE_BASE_PATH}/advanced-claim/file`
  },
  IMPREST_DISBURSEMENT: {
    KEY: 'imprestDisbursement',
    URL: `${FINANCE_BASE_PATH}/imprest-disbursement/search`
  },
  CONTRA_ENTRY: {
    KEY: 'contraEntry',
    URL: `${FINANCE_BASE_PATH}/contra-entry/search`
  },
  JOURNAL_ENTRY: {
    KEY: 'journalEntry',
    URL: `${FINANCE_BASE_PATH}/journal-voucher/search`
  }
};

export const MEETING_MANAGEMENT_MODULES = {
  AGENDA: {
    KEY: 'agenda',
    URL: `${MEETING_MANAGEMENT_BASE_PATH}/agenda-subject`
  }
};

export const HEADER = {
  SERVICE: 'Service',
  MODULE: 'Module',
  SUBMODULE: 'SubModule'
};
export const DEFAULT_OFFICE_TYPE = 3;

export const EMPLOYEE_ROLES = {
  VERIFIER: 'VERIFIER',
  APPROVER: 'APPROVER',
  OPERATOR: 'OPERATOR',
  ADMINISTRATOR: 'ADMINISTRATOR',
  RECOMMEND: 'RECOMMENDING_OFFICER',
  FORWARD_PLUS_ROLE: 'FORWARD_PLUS_ROLE'
};

export const DOCUMENT_TYPES = {
  PDF: 'application/pdf',
  EXCEL: 'application/excel',
  WORD: 'application/word',
  JAR: 'application/java-archive',
  EXE: 'application/vnd.microsoft.portable-executable',

  DOC: 'application/msword',
  DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',

  XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  CSV: 'text/csv',
  XLS: 'application/vnd.ms-excel',
  ODS: 'application/vnd.oasis.opendocument.spreadsheet',

  PNG: 'image/png',
  JPG: 'image/jpg',
  JPEG: 'image/jpeg',
  GIF: 'image/gif'
};

export const getPreviewFileIcon = (fileType) => {
  switch (fileType) {
    case DOCUMENT_TYPES.PDF:
      return PdfSvg;
    case DOCUMENT_TYPES.EXCEL:
      return ExcelSvg;
    case DOCUMENT_TYPES.WORD:
      return DocumentSvg;
    default:
      return ImageIcon;
  }
};
