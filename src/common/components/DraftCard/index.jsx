import React, { useMemo } from 'react';
import { DRAFT_STATUS, DRAFT_STATUS_CONFIG } from 'pages/common/constants';
import { t } from 'i18next';
import { Delete } from 'assets/Svg';
import { Avatar } from '@ksmartikm/ui-components';
import { convertToLocalTimeZone } from 'utils/date';
import PrinterBoldIcon from 'assets/PrinterBoldIcon';
import DownloadArrowIcon from 'assets/DownloadArrowIcon';

const IconButton = ({ icon, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="flex items-center cursor-pointer justify-center w-9 h-9 rounded-lg border border-[#E7EFF5] bg-white shadow-sm hover:bg-gray-100"
    >
      {icon}
    </button>
  );
};

const DraftCard = (props) => {
  const {
    handlePreview = () => {},
    correspondence = '',
    isDelete = false,
    handleDelete = () => {},
    draftVersion = {},
    isExpanded,
    onClick,
    onMouseEnter,
    onMouseLeave
  } = props;

  const statusInfo = useMemo(
    () => DRAFT_STATUS_CONFIG[draftVersion.status],
    [draftVersion]
  );

  const formatDate = (date) => {
    return convertToLocalTimeZone(date, 'DD MMM YYYY h:mm A');
  };

  const handleNavigate = (type, e) => {
    e.stopPropagation();
    return handlePreview({ item: draftVersion, type });
  };

  const approvedSignatureStatus = (data) => {
    if (draftVersion?.status !== DRAFT_STATUS.APPROVED) {
      return null;
    }

    if (data?.isDigitalSigned) {
      return 'and Digitally Signed';
    }

    if (data?.isESigned) {
      return 'and E-Signed';
    }

    return '';
  };

  return (
    <div
      className={`border border-[#F4F4F4] rounded-[12px] mb-4 transform transition-all duration-500 ease-in-out overflow-hidden ${
        isExpanded ? '' : ''
      }`}
      style={{ boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.08)' }}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick(e);
        }
      }}
    >
      <div className="py-4 px-5">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Avatar
              w={10}
              h={10}
              fontWeight={500}
              name={draftVersion?.updatedByEmployeeName}
            />
            <div className="w-full">
              <span className="font-semibold text-[14px] text-[#3C4449]">
                {draftVersion?.updatedByEmployeeName}
              </span>
              <p className="text-[#5C6E93] text-[12px] font-semibold">
                {draftVersion?.seatName}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <p
              className={`text-[#5C6E93] text-[14px] font-semibold transition-transform duration-500 ease-in-out ${
                isExpanded && isDelete ? '-translate-x-2' : 'translate-x-0'
              }`}
            >
              {formatDate(draftVersion?.date)}
            </p>
            {isDelete && (
              <div
                className={`transition-transform duration-300 ease-in-out absolute ${
                  isExpanded
                    ? 'opacity-100 scale-100 translate-x-0 relative'
                    : 'opacity-0 scale-90 -translate-x-4 pointer-events-none absolute'
                }`}
              >
                <IconButton icon={<Delete />} onClick={handleDelete} />
              </div>
            )}
          </div>
        </div>
        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out transform ${
            isExpanded
              ? 'max-h-96 opacity-100 translate-y-0 mt-4'
              : 'max-h-0 opacity-0 -translate-y-4'
          } `}
        >
          {correspondence === 'certificate' ? (
            <h4 className="capitalize font-medium text-sm">
              {draftVersion?.serviceName}
            </h4>
          ) : (
            <div>
              <div
                className="draft-rich-subject max-w-[100%] line-clamp-3 overflow-hidden"
                /* eslint-disable-next-line react/no-danger */
                dangerouslySetInnerHTML={{ __html: draftVersion?.subject }}
              />
              <div
                className="draft-rich-content mt-3 font-light line-clamp-3"
                /* eslint-disable-next-line react/no-danger */
                dangerouslySetInnerHTML={{ __html: draftVersion?.draftText }}
              />
            </div>
          )}
        </div>
        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out transform ${
            isExpanded
              ? 'max-h-20 opacity-100 mt-3 border-t pt-2 translate-y-0'
              : 'max-h-0 opacity-0 mt-0 border-t-0 pt-0 -translate-y-4'
          }`}
        >
          <div className="flex justify-between items-center pt-2">
            <div className="flex items-center">
              <div className="flex items-center gap-1">
                {statusInfo?.Icon && (
                  <statusInfo.Icon color={statusInfo?.color} />
                )}
                <span
                  style={{ color: statusInfo?.color }}
                  className="font-medium text-[14px]"
                >
                  {t(statusInfo?.label)} {approvedSignatureStatus(draftVersion)}
                </span>
              </div>
              <span className="text-[#5C6E93] text-[14px] font-semibold ml-2 border-l pl-2">
                {correspondence}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {!draftVersion?.isActive && (
                <div className="bg-[#E83A7A33] text-[#E83A7A] px-3 py-1 rounded-[18px] text-xs font-semibold">
                  Inactive
                </div>
              )}
              {draftVersion?.status === DRAFT_STATUS.APPROVED && (
                <>
                  <IconButton
                    icon={<PrinterBoldIcon w="19" h="19" stroke="#848484" />}
                    onClick={(e) => handleNavigate('print', e)}
                  />
                  <IconButton
                    icon={<DownloadArrowIcon w="19" h="19" stroke="#848484" />}
                    onClick={(e) => handleNavigate('download', e)}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DraftCard;
