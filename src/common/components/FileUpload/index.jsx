import {
  Button, IconButton, Input, InputGroup, InputRightElement, ksmThemeStyles
} from '@ksmartikm/ui-components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import CloseOutlineIcon from 'assets/CloseOutline';
import FileUploadIcon from 'assets/FileUpload';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'i18next';
import { MAX_FILE_SIZE_BYTES } from 'pages/common/constants';

import React, { useState, useRef, useEffect } from 'react';
import { formatFileSize } from 'utils/fileSize';
import { DOCUMENT_TYPES } from 'common/constants';

const { colors } = ksmThemeStyles;

const FileUpload = ({
  onChange = () => { },
  disabled = false,
  accept = 'image,pdf',
  required,
  value,
  loading = false,
  error = null,
  setAlertAction,
  isDisabled = false
}) => {
  const styles = {
    customFileInput: {
      width: '100%',
      position: 'relative',
      cursor: 'pointer'
    },
    fileLabel: {
      background: '#fff',
      position: 'absolute',
      marginTop: '-10px',
      fontSize: '14px',
      left: '13px',
      zIndex: '2',
      color: '#09327B',
      fontWeight: '400 !important'
    },
    input: {
      padding: '0 45px 0 18px',
      height: '56px',
      width: '100%',
      background: 'white',
      color: 'gray.700 !important',
      fontStyle: 'normal',
      fontWeight: '400 !important',
      border: `1px solid ${colors.gray[300]}`,
      borderRadius: '8px',
      fontSize: '16px !important',
      lineHeight: '16px',
      outline: '0',
      margin: '0'
    },
    errorinput: {
      padding: '0 45px 0 18px',
      height: '56px',
      width: '100%',
      background: 'white',
      color: 'gray.700 !important',
      fontStyle: 'normal',
      fontWeight: '400 !important',
      border: `1px solid ${colors.secondary[500]}`,
      borderRadius: '8px',
      fontSize: '16px !important',
      lineHeight: '16px',
      outline: '0',
      margin: '0'
    },
    icon: {
      paddingTop: '10px',
      paddingRight: '0px',
      background: 'white',
      marginTop: '2px',
      marginRight: '2px'
    }
  };

  const [fileName, setFileName] = useState('');
  const [acceptType, setAcceptType] = useState('');
  const [showUnsupported, setShowUnsupported] = useState(null);

  const inputRef = useRef(null);
  const inputFileRef = useRef(null);

  useEffect(() => {
    if (!value) {
      setFileName('');
      inputFileRef.current = null;
    }
  }, [value]);

  useEffect(() => {
    switch (accept) {
      case 'image':
        setAcceptType('image/jpeg,image/gif,image/png,image/x-eps');
        break;
      case 'pdf':
        setAcceptType(DOCUMENT_TYPES.PDF);
        break;
      case 'image,pdf':
        setAcceptType('image/jpeg,image/gif,image/png,application/pdf,image/x-eps');
        break;
      case 'excel':
        setAcceptType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv');
        break;
      case 'image,pdf,excel,doc':
        setAcceptType(
          'image/jpeg,image/gif,image/png,application/pdf,image/x-eps,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv,application/vnd.oasis.opendocument.spreadsheet,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        );
        break;

      default:
        setAcceptType('image/jpeg,image/gif,image/png,image/x-eps');
        break;
    }
  }, [accept]);

  const handleClick = () => {
    if (!disabled) {
      inputRef.current.click();
    }
  };

  const showDocumentNotSupported = () => {
    switch (accept) {
      case 'image':
        return `${t('unsupportedDocumentType')}, ${t('useImageOnly')}`;
      case 'pdf':
        return `${t('unsupportedDocumentType')}, ${t('usePDFOnly')}`;
      case 'image,pdf':
        return `${t('unsupportedDocumentType')}, ${t('useImagesAndPdfOnly')}`;
      case 'excel':
        return `${t('unsupportedDocumentType')}, ${t('useExcelorCSVOnly')}`;
      case 'image,pdf,excel,doc':
        return `${t('unsupportedDocumentType')}, ${t('useImagesPdfExelAndWordOnly')}`;
      default:
        return `${t('unsupportedDocumentType')}`;
    }
  };

  const handleDelete = () => {
    if (!disabled) {
      setShowUnsupported(null);
      setFileName('');
      inputFileRef.current = null;
      onChange();
    }
  };

  const validateFileExtension = (type) => {
    return (
      (type && type === DOCUMENT_TYPES.PDF && accept.includes('pdf'))
      || (type && [DOCUMENT_TYPES.PNG, DOCUMENT_TYPES.JPG, DOCUMENT_TYPES.JPEG, DOCUMENT_TYPES.GIF]?.includes(type) && accept.includes('image'))
      || (type && [DOCUMENT_TYPES.EXCEL, DOCUMENT_TYPES.XLSX, DOCUMENT_TYPES.CSV, DOCUMENT_TYPES.XLS, DOCUMENT_TYPES.ODS]?.includes(type) && accept.includes('excel'))
      || (type && [DOCUMENT_TYPES.WORD, DOCUMENT_TYPES.DOC, DOCUMENT_TYPES.DOCX]?.includes(type) && accept.includes('doc'))
    );
  };

  const handleChange = (e) => {
    setShowUnsupported(null);
    if (e?.target?.files) {
      const selectedFile = e?.target?.files[0];
      if (validateFileExtension(e?.target?.files[0]?.type)) {
        if (e?.target?.files[0]?.size > MAX_FILE_SIZE_BYTES) {
          const fileSize = formatFileSize(e?.target?.files[0]?.size);
          setAlertAction({
            open: true,
            variant: 'warning',
            message: `File size should be within 5 MB, ${fileSize} is not allowed`,
            title: t('warning'),
            backwardActionText: t('ok')
          });
        } else {
          const reader = new FileReader();
          reader.readAsArrayBuffer(e?.target?.files[0]);
          reader.onload = () => {
            const files = new Blob([reader.result], { type: e?.target?.files[0]?.type });
            files.text().then((x) => {
              if (x.includes('Encrypt') && !x.includes('Non-Encryption')) {
                setAlertAction({
                  open: true,
                  variant: 'warning',
                  message: 'Password Protected File Not allowed',
                  title: t('warning'),
                  backwardActionText: t('ok')
                });
              } else {
                onChange(selectedFile);
                setFileName(selectedFile?.name);
              }
            });
          };
        }
      } else {
        setShowUnsupported(showDocumentNotSupported());
      }
    } else {
      setFileName('');
    }
  };

  return (
    <div style={styles.customFileInput}>
      <div style={styles.fileLabel}>Attachment {required && <span className="text-lg absolute">*</span>}</div>
      {loading ? (
        <Button isLoading loadingText="Uploading" style={styles.input} />
      ) : (
        <>
          <InputGroup>
            <Input
              style={error || showUnsupported ? styles.errorinput : styles.input}
              placeholder="Click to Select a File"
              readOnly
              value={fileName}
              ref={inputFileRef}
              onClick={handleClick}
              isDisabled={isDisabled}
            />
            <InputRightElement style={styles.icon}>
              {fileName ? (
                <IconButton onClick={handleDelete} variant="unstyled" icon={<CloseOutlineIcon color="#09327B" />} />
              ) : (
                <IconButton
                  ref={inputFileRef}
                  onClick={handleClick}
                  variant="unstyled"
                  isDisabled={isDisabled}
                  icon={<FileUploadIcon />}
                />
              )}
            </InputRightElement>
          </InputGroup>
          {showUnsupported && <div className="text-xs text-red-500 full text-right">{showUnsupported}</div>}
          <div className="text-xs text-gray-500 full text-right absolute w-full">Max File Size Limit to 5 MB</div>
        </>
      )}
      <input
        type="file"
        ref={inputRef}
        onChange={(e) => {
          handleChange(e);
          e.target.value = '';
        }}
        hidden
        accept={acceptType}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(FileUpload);
