.stripp .striped-table th {
    background: #09327B;
    color: #fff;
    text-align: left;
    padding: 16px;
    font-weight: 500;
  }

  .stripp .striped-table th .chakra-checkbox {
    margin-top: 5px;
    margin-left: 0px;
  }
  .stripp .striped-table th .chakra-checkbox span, .stripp .striped-table td .chakra-checkbox span{
    border-color: #ddd !important;
    height: 18px;
  }

  .stripp .striped-table th .chakra-checkbox span[data-checked], .stripp .striped-table td .chakra-checkbox span[data-checked]{
    border-color: #E82C78 !important;
    height: 18px;
  }

  

  .stripp .striped-table tbody tr{
    background: #fff;
  }

  .stripp .striped-table tbody tr td{
    background: #fff;
    padding: 16px;
  }

  .stripp .striped-table tbody tr td .chakra-checkbox{
    margin-top: 5px;
    margin-left: 10px;
  }
  .stripp .striped-table tbody tr td input[type=radio]{
    margin-top: 5px;
    margin-left: 10px;
    width: 18px;
    height: 18px;
  }
  .pagination-item{
    font-size: 14px;
    padding: 10px 20px;
  }
  .pagination{
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
  }
  .pagination .active{
    background: #09327B;
    color: #fff;
  }