import React from 'react';
import { Button } from 'common/components';

const MoreActions = (props) => {
  const { actionData, rowData } = props;
  return (
    actionData?.map(({
      onClick: onItemClick, label, icon, id
    }, index) => (
      <Button
        variant="link"
        key={index || Math.random().toString()}
        onClick={() => {
          onItemClick(rowData || id);
        }}
        className="mr-5"
      >
        {icon || label}
      </Button>
    ))
  );
};

export default MoreActions;
