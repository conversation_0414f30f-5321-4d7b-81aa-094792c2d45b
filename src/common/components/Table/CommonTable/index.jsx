import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { Checkbox } from 'common/components';
import NoNotesIcon from 'assets/NoNotesIcon';
import { Spinner } from '@ksmartikm/ui-components';
import MoreActions from './MoreActions';
import Pagination from '../Pagination';

const getExistingItems = (currentActiveRows, currentPageItems) => {
  const tempSet = new Set();
  const stringifiedActiveRows = currentActiveRows?.map((item) => JSON.stringify(item));
  const activeArr = currentPageItems?.map((item) => stringifiedActiveRows.includes(JSON.stringify(item)));

  _.forEach(currentPageItems, (item) => {
    if (stringifiedActiveRows.includes(JSON.stringify(item))) {
      tempSet.add(item);
    }
  });
  return { checkedItems: activeArr, selectedRows: tempSet };
};

const CommonTable = ({
  tableData = [],
  columns = [],
  onRowClick = () => { },
  onRowCheck = () => { },
  onRowRadioSelected = () => { },
  activeRows = [],
  itemsPerPage,
  currentPage = 1,
  paginationEnabled,
  onPageClick = () => { },
  totalItems,
  variant = 'normal',
  onRowCheckEnabled,
  tableLoader = false,
  noDataText = 'No Records Found',
  numberOfElements
}) => {
  const [checkedItems, setCheckedItems] = useState([]);
  const [selectedRows, setSelectedRows] = useState(new Set());
  const tempArr = selectedRows;
  const currentItems = tableData || [];
  useEffect(() => {
    tempArr.clear();
    const {
      checkedItems: currentCheckedItems,
      selectedRows: currentSelectedRows
    } = getExistingItems(activeRows, currentItems);

    setCheckedItems([...currentCheckedItems]);

    setSelectedRows(currentSelectedRows);
  }, [currentPage, JSON.stringify(activeRows), JSON.stringify(tableData)]);

  const rowValues = (e, row) => {
    if (e.target.checked === true) {
      tempArr.add(row);
    } else if (e.target.checked === false) {
      tempArr.delete(row);
    }

    setSelectedRows(tempArr);
    onRowCheck({
      elementCheck: e.target.checked,
      selectedRow: row,
      selectedRows: tempArr
    });
  };

  const selectAllValues = (e) => {
    currentItems.map((item) => rowValues(e, item));
  };

  const allChecked = checkedItems.every(Boolean);
  const isIndeterminate = checkedItems.some(Boolean) && !allChecked;

  const handleIndividualCheck = (e, index, row) => {
    const items = [...checkedItems];

    items[index] = e.target.checked;

    setCheckedItems(items);
    rowValues(e, row);
  };

  const handleAllCheck = (e) => {
    if (checkedItems.every(Boolean)) {
      setCheckedItems([...Array(itemsPerPage)].map(() => false));
    } else {
      setCheckedItems([...Array(itemsPerPage)].map(() => true));
    }

    selectAllValues(e);
  };

  const radioSelected = (row) => {
    const convertSetToArr = [...selectedRows];
    const stringifiedActiveRow = JSON.stringify(convertSetToArr[0]);
    const stringifiedRow = JSON.stringify(row);

    return stringifiedActiveRow === stringifiedRow;
  };

  const handleRadioSelect = (row) => {
    setSelectedRows(new Set([row]));
    onRowRadioSelected(row);
  };

  return (
    <div className={variant === 'normal' ? '' : 'stripp'}>
      <table className="striped-table">
        <thead>
          <tr>
            {columns?.map((col) => {
              const currentDate = Date.parse(new Date());
              if (col?.type === 'multi-select') {
                return (
                  <th
                    key={`header-${currentDate + columns.indexOf(col)}`}
                    className={`th-${col.alignment}`}
                    aria-label="th"
                  >
                    <Checkbox
                      isChecked={allChecked}
                      isIndeterminate={isIndeterminate}
                      onChange={(e) => handleAllCheck(e)}
                    />
                  </th>
                );
              }

              return (
                <th
                  key={`header-${currentDate + columns.indexOf(col)}`}
                  className={`th-${col.alignment}`}
                >
                  {col?.header}
                </th>
              );
            })}
          </tr>
        </thead>
        {
          tableLoader ? (
            <tbody>
              <tr>
                <td aria-labelledby="js_1" colSpan={columns?.length}>
                  <div className="text-center" style={{ height: '300px', paddingTop: '130px' }}>
                    <Spinner />
                  </div>
                </td>
              </tr>
            </tbody>
          ) : (
            // eslint-disable-next-line react/jsx-no-useless-fragment
            <>
              {
                currentItems.length === 0 ? (
                  <tbody>
                    <tr>
                      <td colSpan={columns?.length}>
                        <div className="p-10 text-center bg-white rounded-lg">
                          <NoNotesIcon width="100px" height="100px" className="mx-auto" />
                          <h4>{noDataText}</h4>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                ) : (
                  <tbody>
                    {currentItems?.map((row, index) => {
                      const newIndex = `tbodyRow-${index}`;
                      return (
                        <tr key={newIndex} onClick={() => onRowClick({ row, index })} className={onRowCheckEnabled ? 'cursor-pointer' : ''}>
                          {columns?.map((col, columnIndex) => {
                            const { cell = ({ field }) => field } = col;
                            const columnKey = `${index - columnIndex}`;
                            if (col?.type === 'multi-select' || col?.type === 'select') {
                              return (
                                <td
                                  key={`select-${columnKey}`}
                                  className={`th-${col.alignment}`}
                                  aria-label="td"
                                >
                                  <Checkbox
                                    isChecked={checkedItems[index]}
                                    onChange={(e) => handleIndividualCheck(e, index, row)}
                                  />
                                </td>
                              );
                            }

                            if (col?.type === 'radio') {
                              return (
                                <td
                                  key={`select-${columnKey}`}
                                  className={`th-${col.alignment}`}
                                  aria-label="td"
                                >
                                  <input
                                    type="radio"
                                    value={index}
                                    checked={radioSelected(row)}
                                    onClick={() => handleRadioSelect(row)}
                                  />
                                </td>
                              );
                            }

                            if (col?.type === 'actions') {
                              const actions = col?.actions;
                              return (
                                <td
                                  key={`action-${columnKey}`}
                                  className={`th-${col.alignment}`}
                                  // onClick={(e) => rowValues(e, row)}
                                  aria-label="td"
                                >
                                  <MoreActions actionData={actions} rowData={row} />
                                </td>
                              );
                            }

                            return (
                              <td
                                key={`normal-${columnKey}`}
                                className={`th-${col.alignment}`}
                                aria-label="td"
                              >
                                {cell({
                                  field: _.get(row, _.get(col, 'field', ''), null),
                                  row,
                                  rowIndex: index,
                                  columnIndex
                                })}
                              </td>
                            );
                          })}
                        </tr>
                      );
                    })}
                  </tbody>
                )
              }
            </>

          )
        }
      </table>
      {paginationEnabled && (
        <Pagination
          className="pagination-bar"
          currentPage={currentPage + 1}
          totalCount={totalItems}
          pageSize={itemsPerPage}
          onPageChange={(page) => onPageClick(page - 1)}
          numberOfElements={numberOfElements}
        />
      )}
    </div>
  );
};

export default CommonTable;
