import {
  Box,
  // Avatar,
  IconButton,
  Tooltip
} from '@ksmartikm/ui-components';
import {
  getCommonConfigSelector,
  getUserInfo,
  getUserOffices
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as commonActions from 'pages/common/actions';
import React, { useEffect } from 'react';

import { connect, useDispatch } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { STORAGE_KEYS, USER_TYPE } from 'common/constants';
import { getDataFromStorage } from 'utils/encryption';
import { useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import UserIcon from 'assets/UserIcon';
import { routeRedirect } from 'utils/common';
import { FormController, TruncatedText } from '.';
// import i18next from 'i18next';

const Logo = () => (
  <svg
    width="100"
    height="40"
    viewBox="0 0 382 166"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1_5)">
      <path
        d="M29.5065 60.7348L86.1519 4.11148C91.6386 -1.36816 100.627 -1.37516 106.114 4.11148L116.342 14.3365L104.864 25.9541L96.1353 17.2292L42.6289 70.7099L94.1099 122.172L82.5588 133.719L29.5065 80.6851C24.0245 75.2055 24.0198 66.2191 29.5065 60.7348Z"
        fill="#E83A7A"
      />
      <path
        d="M29.8111 33.4895C30.1321 31.7805 29.3839 31.4594 28.1021 31.4594H15.7443C13.9279 31.4594 13.3944 32.314 13.1761 33.4895L0.0408489 108.666C-0.17278 109.95 0.468106 110.802 1.74988 110.802H14.1077C15.2832 110.802 16.3513 110.482 16.6759 108.666C23.3474 70.4671 29.6325 34.5075 29.8111 33.4895Z"
        fill="#E83A7A"
      />
      <path
        d="M130.684 161.871L137.416 155.141L169.752 122.82C171.193 121.27 171.976 119.222 171.937 117.107C171.899 114.991 171.041 112.973 169.544 111.477C168.048 109.981 166.029 109.124 163.913 109.086C161.798 109.048 159.75 109.832 158.201 111.274L152.903 116.569L144.015 125.454L132.159 137.305L120.706 148.753L100.283 128.341L88.7318 139.887L110.725 161.871C116.207 167.351 125.196 167.356 130.684 161.871Z"
        fill="#00B2EB"
      />
      <path
        d="M126.562 33.2406C132.865 33.2406 139.379 33.976 145.158 35.4469C146.313 35.7621 146.944 36.0773 146.629 37.8634L145.578 43.852C145.368 45.3229 144.843 46.0583 143.162 45.8482C140.325 45.4279 132.55 44.5874 126.562 44.5874C122.674 44.5874 118.472 45.0077 118.472 48.895C118.472 50.6811 118.892 51.8368 121.624 53.7279L133.916 62.133C140.22 66.4406 141.691 70.7482 141.691 75.371C141.691 88.5039 130.974 94.0722 118.472 94.0722C115.005 94.0722 105.969 93.7571 99.5603 91.971C98.1945 91.5507 97.7742 90.9203 97.9844 89.5545L99.1401 83.2507C99.3502 82.095 99.8755 81.3596 101.662 81.5697C106.6 82.2001 115.32 82.7254 118.472 82.7254C123.62 82.7254 127.507 81.7798 127.507 76.8419C127.507 75.1609 126.562 73.5849 124.04 71.7988L113.008 64.2343C107.02 60.1368 104.393 56.2495 104.393 49.2102C104.393 36.6026 116.476 33.2406 126.562 33.2406ZM197.401 33.976H207.067C208.433 33.976 209.168 34.6064 208.853 36.1824L199.187 91.1305C198.977 92.2862 197.927 93.2317 196.771 93.2317H187.736C186.685 93.2317 185.949 92.3912 185.949 91.3406C185.949 91.3406 185.949 91.2355 185.949 91.1305L186.265 88.9241C181.117 91.8659 175.338 94.1773 169.455 94.1773C161.26 94.1773 153.59 89.8697 153.59 78.2077C153.59 75.7912 153.8 73.1646 154.325 70.433L160.314 36.1824C160.524 35.0267 161.155 33.976 162.836 33.976H172.501C173.867 33.976 174.498 34.6064 174.182 36.1824L168.194 70.433C167.773 73.0596 167.458 75.0558 167.458 76.7368C167.458 81.1495 169.034 82.2001 173.867 82.2001C178.7 82.2001 184.794 79.7836 187.525 78.4178L194.985 36.1824C195.195 35.0267 195.825 33.976 197.401 33.976ZM228.899 12.6482H239.09C240.351 12.6482 241.086 13.3836 240.876 14.7494L239.195 23.995C238.985 25.1507 237.934 26.0963 236.779 26.0963H226.588C225.432 26.0963 224.486 25.1507 224.696 23.995L226.377 14.7494C226.588 13.5938 227.428 12.6482 228.899 12.6482ZM225.327 33.976H234.993C236.148 33.976 237.094 34.3963 236.779 36.0773L227.113 91.0254C226.903 92.1811 226.377 93.2317 224.591 93.2317H214.926C213.665 93.2317 213.034 92.3912 213.245 91.0254L222.91 36.0773C223.121 34.9216 223.856 33.976 225.327 33.976ZM262.265 23.2596L272.351 21.5786C273.822 21.3684 274.452 22.2089 274.242 23.1545L272.246 33.976H281.702C282.857 33.976 283.803 34.2912 283.488 36.0773L282.437 42.276C282.227 43.6419 281.596 44.3773 279.915 44.3773H270.46L264.681 77.3672C264.366 78.9431 264.156 80.2039 264.156 81.0444C264.156 82.9355 265.102 83.2507 267.728 83.2507H273.296C274.347 83.2507 275.398 83.3558 275.188 84.8267L274.032 91.4457C273.822 92.4963 273.086 93.1267 271.721 93.3368C269.094 93.7571 266.783 94.0722 264.471 94.0722C255.226 94.0722 250.183 90.9203 250.183 82.5153C250.183 80.9393 250.393 79.2583 250.708 77.3672L256.486 44.3773L246.82 42.9064C245.35 42.6963 244.929 41.9608 245.139 40.8052L245.98 36.0773C246.19 34.9216 246.715 33.976 248.396 33.976H258.272L259.848 25.3608C260.058 24.2051 261.109 23.4697 262.265 23.2596Z"
        fill="#0C3080"
      />
      <path
        d="M336.808 54.7786C336.808 56.6697 336.598 58.771 336.283 60.7672L335.442 65.8102C335.127 67.4912 334.392 68.4368 332.921 68.4368H298.67C298.46 69.9077 298.145 71.2735 298.145 72.7444C298.145 78.4178 301.087 82.2001 308.861 82.2001C316.531 82.2001 323.78 81.4646 328.298 81.0444C329.664 80.9393 330.504 81.2545 330.294 82.6203L329.033 89.5545C328.718 91.2355 328.403 91.971 326.407 92.2862C319.368 93.5469 314.745 94.0722 305.289 94.0722C295.938 94.0722 284.592 88.8191 284.592 72.9545C284.592 71.0634 284.697 68.8571 285.117 66.6507L286.273 60.1368C289.74 40.49 300.141 33.2406 316.216 33.2406C330.189 33.2406 336.808 42.4862 336.808 54.7786ZM323.15 54.5684C323.15 48.4748 319.893 45.2178 314.114 45.2178C305.709 45.2178 301.822 50.8912 300.456 58.5608H322.73C322.94 57.195 323.15 55.9343 323.15 54.5684Z"
        fill="#00B2EB"
      />
      <path
        d="M333.729 35.5082L328.005 30L307.318 51.4962L313.042 57.0045L333.729 35.5082Z"
        fill="#F6F9FD"
      />
    </g>
    <defs>
      <clipPath id="clip0_1_5">
        <rect width="381.146" height="166" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const Logout = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M9 9.7832L19 9.7832"
      stroke="#232F50"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.5 13.2832L19 9.7832L15.5 6.2832"
      stroke="#232F50"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 18.7832C5.02944 18.7832 1 14.7538 1 9.7832C1 4.81264 5.02944 0.783203 10 0.783203"
      stroke="#232F50"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const MainHeader = (props) => {
  const {
    setUserInfo,
    fetchLocalBodyByOfficeCode
    // setLocale,
    // commonConfig
  } = props;
  // const { locale } = commonConfig;
  const dispatch = useDispatch();
  const location = useLocation();

  const { control, setValue } = useForm({
    mode: 'all',
    defaultValues: {
      offices: ''
    }
  });

  const { offices } = getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true) || {};

  const userRoles = getDataFromStorage(STORAGE_KEYS.USER_ROLES, true) || [];
  const userDetails = getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true) || [];

  const handleLogout = () => {
    dispatch(commonActions.logout());
  };

  const handleOffice = (data) => {
    localStorage.setItem(STORAGE_KEYS.OFFICE_ID, data?.id);
    window.location.reload();
  };

  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const officeId = JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID));
  useEffect(() => {
    if (officeId && userDetails?.user_type !== USER_TYPE.CITIZEN) {
      const info = {
        userRoles,
        userDetails,
        officeId,
        id: officeId,
        assigner: userDetails?.userId,
        postIds: null
      };
      setUserInfo(info);
      fetchLocalBodyByOfficeCode(officeId);
    } else {
      const info = {
        userRoles,
        userDetails
      };
      setUserInfo(info);
    }
  }, [officeId, token]);

  // useEffect(() => {
  //   if (localStorage.getItem('currentLanguage')) {
  //     console.log('currentLanguage', localStorage.getItem('currentLanguage'));
  //     setLocale(localStorage.getItem('currentLanguage'));
  //     i18next.changeLanguage('currentLanguage');
  //   }
  // }, [localStorage.getItem('currentLanguage')]);

  useEffect(() => {
    if (JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID))) {
      setValue('offices', Number(localStorage.getItem(STORAGE_KEYS.OFFICE_ID)));
    }
  }, [JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID))]);

  return (
    <div className="flex justify-between items-center h-[70px] px-[35px] bg-white border border-b-[#DFE5F3] fixed w-full z-50 top-0">
      <div className="flex items-center gap-x-5">
        <div className="">
          <Logo />
        </div>
        {offices?.length > 0 && (
          <div className="min-w-60 max-w-60">
            <FormController
              isClearable={false}
              name="offices"
              type="select"
              className="font-bold text-[12px]"
              control={control}
              options={offices}
              handleChange={(data) => {
                handleOffice(data);
              }}
              optionKey="id"
            />
          </div>
        )}

        {/* {
          !location.pathname.includes('public') && !location.pathname.includes('kswift/login') && (
            <div className="font-[700] text-[16px] text-[rgba(9,_50,_123,_0.3)] flex-none pt-[7px]">
              <span>{userDetails?.name} - {userDetails?.designation}</span>
            </div>
          )
        } */}
      </div>

      <div className="flex justify-end gap-x-4">
        {/* <IconButton
          width={10}
          icon={(
            <Avatar
              name={locale === 'en' ? 'E n' : 'M l'}
              borderRadius={5}
              width={10}
              height={10}
              backgroundColor="transparent"
              textColor="blackAlpha.800"
            />
          )}
          onClick={() => {
            const nextLanguage = locale === 'en' ? 'ml' : 'en';
            localStorage.setItem('currentLanguage', nextLanguage);
            i18next.changeLanguage(nextLanguage);
            setLocale(nextLanguage);
            window.location.reload();
          }}
        /> */}

        {!userDetails?.isAuditor
          && (userDetails?.aadhaarId?.photo?.photo ? (
            <Tooltip label="Profile" aria-label="Profile">
              <img
                aria-hidden
                className="object-fill w-10 h-10 rounded-lg cursor-pointer"
                src={`data:image/png;base64,${userDetails?.aadhaarId?.photo?.photo}`}
                alt="profile"
                onClick={() => routeRedirect(
                  `ui/home/<USER>/my-profile`
                )}
              />
            </Tooltip>
          ) : (
            <Box
              className="flex flex-row gap-2 items-center"
              onClick={() => routeRedirect(
                `ui/home/<USER>/my-profile`
              )}
            >
              <IconButton variant="unstyled" icon={<UserIcon />} />
              <div className="flex flex-col gap-0 !max-w-48">
                <TruncatedText
                  text={userDetails?.name || userDetails?.userName}
                  fontSize={14}
                  fontWeight={700}
                  color="#232F50"
                />
                <TruncatedText
                  maxWidth={['100%', '150px', '250px']}
                  fontWeight={500}
                  text={userDetails?.designation}
                  fontSize={13}
                  color="#5C6E93"
                  mt={-1}
                />
              </div>
            </Box>
          ))}

        {!location.pathname.includes('public')
          && !location.pathname.includes('kswift/login') && (
            <Tooltip label="Logout" aria-label="Logout">
              <IconButton icon={<Logout />} onClick={handleLogout} />
            </Tooltip>
        )}
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userOffices: getUserOffices,
  userInfo: getUserInfo,
  commonConfig: getCommonConfigSelector
});

const mapDispatchToProps = (dispatch) => ({
  setUserInfo: (data) => dispatch(commonSliceActions.setUserInfo(data)),
  fetchLocalBodyByOfficeCode: (data) => dispatch(commonActions.fetchLocalBodyByOfficeCode(data)),
  setLocale: (data) => dispatch(commonSliceActions.setLocale(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(MainHeader);
