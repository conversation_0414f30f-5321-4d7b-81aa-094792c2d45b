import React from 'react';
import {
  <PERSON>u, <PERSON><PERSON><PERSON><PERSON>on, <PERSON>u<PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, But<PERSON>, t
} from 'common/components';
import MoreIcon from 'assets/MoreIcon';
import { dark, primary } from 'utils/color';

const styles = {
  moreButton: {
    borderRadius: '20px',
    color: dark,
    padding: '5px 20px'
  },
  iconButton: {
    borderRadius: '20px',
    background: 'none',
    width: '10px'
  },
  circle: {
    borderRadius: '100%',
    color: dark,
    width: '40px',
    height: '40px',
    padding: '5px 20px 5px 12px'
  },
  solid: {
    width: '40px',
    minWidth: '20px',
    padding: '5px 20px 5px 12px',
    height: '54px'
  },
  noteFile: {
    color: primary,
    padding: '0'
  }
};

const Dropdown = (props) => {
  const {
    label = 'Drop Button', options = [], handleClick = () => { }, optionKey = 'name', variant = 'outline', isDisabled = false, type = 'normal',
    direction = 'right'
  } = props;

  return (
    <Menu placement={direction === 'left' ? 'bottom-end' : 'right-start'}>
      {variant === 'outline'
        && (
          <MenuButton variant="outline" style={type === 'circle' ? styles.circle : styles.moreButton} as={Button} rightIcon={<MoreIcon />} isDisabled={isDisabled}>
            {label}
          </MenuButton>
        )}
      {variant === 'note-file'
        && (
          <MenuButton variant="note-file" style={styles.noteFile} as={Button} isDisabled={isDisabled}>
            {label}
          </MenuButton>
        )}
      {variant === 'solid'
        && (
          <MenuButton variant="outline" style={styles.solid} as={Button} rightIcon={<MoreIcon />} isDisabled={isDisabled}>
            {label}
          </MenuButton>
        )}
      {variant === 'icon'
        && (
          <MenuButton style={styles.iconButton} as={Button} leftIcon={<MoreIcon size={10} />} isDisabled={isDisabled} />
        )}
      <MenuList>
        {options?.length === 0 ? <h4 className="px-3">{t('noItemsToDisplay')}</h4> : (
          options?.map((item) => (
            <MenuItem onClick={() => handleClick(item)} key={item.id}>{item[optionKey]}</MenuItem>
          ))
        )}
      </MenuList>
    </Menu>
  );
};

export default Dropdown;
