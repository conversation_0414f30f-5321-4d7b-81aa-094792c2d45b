import { CustomAlert } from '@ksmartikm/ui-components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import React from 'react';
import { getActionTriggered, getAlertAction } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';

const Alert = ({ setAlertAction, alertAction, actionTriggered }) => {
  const {
    open, variant, message, size = 'md', title, backwardActionText = null, forwardActionText = null, forwardAction = () => { }, backwardAction, closeOnOverlayClick = true, closeOnEsc = true, forwardActionId = '', content = null
  } = alertAction;
  const close = () => {
    setAlertAction(false);
  };

  return (
    <CustomAlert
      open={open}
      close={close}
      variant={variant}
      message={message}
      title={title}
      backwardActionText={backwardActionText}
      forwardActionText={forwardActionText}
      actionForward={forwardAction}
      actionForwardLoading={actionTriggered?.loading && actionTriggered?.id === forwardActionId}
      actionBackward={backwardAction || (() => { close(); })}
      closeOnOverlayClick={closeOnOverlayClick}
      closeOnEsc={closeOnEsc}
      content={content}
      size={size}

    />
  );
};

const mapStateToProps = createStructuredSelector({
  alertAction: getAlertAction,
  actionTriggered: getActionTriggered
});
const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});
export default connect(mapStateToProps, mapDispatchToProps)(Alert);
