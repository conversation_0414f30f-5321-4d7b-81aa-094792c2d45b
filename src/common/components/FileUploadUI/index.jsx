import React, { useState, useRef, useEffect } from 'react';
import { IconButton, ksmThemeStyles } from '@ksmartikm/ui-components';
import { connect } from 'react-redux';
import CloseOutlineIcon from 'assets/CloseOutline';
import FileUploadIcon from 'assets/FileUpload';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'i18next';
import { MAX_FILE_SIZE_BYTES } from 'pages/common/constants';
import { formatFileSize } from 'utils/fileSize';

const FileUploadUI = ({
  onChange = () => {},
  accept = 'image,pdf',
  required,
  value,
  setAlertAction,
  isDisabled = false
}) => {
  const [fileName, setFileName] = useState('');
  const inputRef = useRef(null);

  useEffect(() => {
    if (!value) setFileName('');
  }, [value]);

  const handleClick = () => {
    if (!isDisabled) inputRef.current.click();
  };

  const handleDelete = () => {
    if (!isDisabled) {
      setFileName('');
      onChange(null);
    }
  };

  const handleChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > MAX_FILE_SIZE_BYTES) {
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `File size should be within 5 MB, ${formatFileSize(file.size)} is not allowed`,
          title: t('warning'),
          backwardActionText: t('ok')
        });
        return;
      }
      setFileName(file.name);
      onChange(file);
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        border: `1px solid ${ksmThemeStyles.colors.gray[300]}`,
        borderRadius: '8px',
        padding: '10px',
        width: '100%',
        opacity: isDisabled ? 0.5 : 1
      }}
    >
      <div
        style={{
          width: '56px',
          height: '56px',
          borderRadius: '50%',
          background: '#ddd',
          marginRight: '10px'
        }}
      >
        {value && (
          <img
            src={URL.createObjectURL(value)}
            alt="Preview"
            style={{ width: '100%', height: '100%', borderRadius: '50%' }}
          />
        )}
      </div>
      <div style={{ flex: 1 }}>
        <div style={{ fontSize: '14px', color: '#555' }}>
          Your Photo {required && <span style={{ color: 'red' }}>*</span>}
        </div>
        <div style={{ fontSize: '12px', color: '#777' }}>{fileName || 'No file chosen'}</div>
      </div>
      {fileName ? (
        <IconButton onClick={handleDelete} size="small" disabled={isDisabled}>
          <CloseOutlineIcon />
        </IconButton>
      ) : (
        <IconButton onClick={handleClick} size="small" disabled={isDisabled}>
          <FileUploadIcon />
        </IconButton>
      )}
      <input
        type="file"
        ref={inputRef}
        style={{ display: 'none' }}
        onChange={handleChange}
        accept={accept}
        disabled={isDisabled}
      />
    </div>
  );
};

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(null, mapDispatchToProps)(FileUploadUI);
