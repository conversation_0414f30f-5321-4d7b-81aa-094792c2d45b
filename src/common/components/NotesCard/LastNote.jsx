import React from 'react';
import {
  Modal, ModalOverlay, ModalContent, ModalBody, t
} from 'common/components';
import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>abe<PERSON>
} from '@ksmartikm/ui-components';
import { light, dark } from 'utils/color';

const LastNote = (props) => {
  const {
    open, close, note
  } = props;

  return (
    <div>
      <Modal isOpen={open} size="xl" onClose={close} closeOnOverlayClick={false} className="custom-form-modal">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader className="rounded-t-lg text-center" style={{ background: light, color: dark }}>
            <h4 size="md">
              {t('lastNote')}
            </h4>
          </ModalHeader>
          <ModalBody>
            <RichLabel value={note} />
          </ModalBody>
          <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
            <div className="flex justify-between items-center w-full">
              <div className="flex items-center gap-6" />
              <div className="flex items-center gap-2 cursor-pointer">
                <Button
                  variant="secondary_outline"
                  onClick={close}
                >
                  {t('close')}
                </Button>
              </div>
            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default LastNote;
