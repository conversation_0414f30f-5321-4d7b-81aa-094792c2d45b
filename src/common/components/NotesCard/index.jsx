import React, { useState } from 'react';
import { connect } from 'react-redux';
import {
  Card, CardBody, IconButton, RichLabel
} from 'common/components';
import ProfilePic from 'assets/ProfilePic';
import Edit from 'assets/Edit';
import Delete from 'assets/delete';
import CheckedRadio from 'assets/CheckedRadio';
import UnCheckedRadio from 'assets/UnCheckedRadio';
import { notes } from 'utils/color';
import UnExpand from 'assets/UnExpand';
import { DRAFT_STATUS } from 'pages/common/constants';
import { createStructuredSelector } from 'reselect';
import { useGenerateDocs } from 'hooks/useGenerateDocs';
import * as actions from 'pages/file/details/actions';
import { baseApiURL } from 'utils/http';
import { useParams } from 'react-router-dom';
import { getUserInfo } from 'pages/common/selectors';
import PreviewThumb from '../DocumentPreview/PreviewThump';

const NotesCard = (props) => {
  const {
    item = {}, title = '', description = '', caption = '', action = [], handleEdit = () => { }, handleDelete = () => { },
    index = 0, selectedIndex = 0, handleChecked = () => { }, handleExpandOrCollapse = () => { }, isExpand, deleteDocuments, userInfo, showAttachment = true
  } = props;

  const params = useParams();

  const [flag, setFlag] = useState(false);
  const [url, setUrl] = useState('');
  const [contentType, setContentType] = useState('');
  const [content, setContent] = useState({});

  const { loading, previewData } = useGenerateDocs({
    url, flag, contentType, content
  });

  const edit = () => {
    return handleEdit(item);
  };
  const handleRemove = () => {
    return handleDelete(item);
  };

  const profileColor = (color) => {
    switch (color) {
      case DRAFT_STATUS.CREATED:
        return '#BCE2F7';
      case DRAFT_STATUS.VERIFIED:
        return '#FBE2FF';
      case DRAFT_STATUS.APPROVED:
        return '#CBF2C1';
      case DRAFT_STATUS.PENDING:
        return '#F0D0FF';
      default:
        return '#BCE2F7';
    }
  };

  const profileTextColor = (color) => {
    switch (color) {
      case DRAFT_STATUS.CREATED:
        return '#6596CF';
      case DRAFT_STATUS.VERIFIED:
        return '#CF87BB';
      case DRAFT_STATUS.APPROVED:
        return '#7BBC6A';
      case DRAFT_STATUS.PENDING:
        return '#784B8D';
      default:
        return '#784B8D';
    }
  };

  const handlePreview = (doc) => {
    setUrl(`${baseApiURL}${doc.link}`);
    setContentType(doc.contentType);
    setContent(doc.content);
    setFlag(!flag);
  };

  const onHandleRemove = (data) => {
    const sendData = {
      inwardId: params?.id,
      documentTypeId: data?.documentTypeId,
      fileId: data?.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    deleteDocuments(sendData);
  };

  return (
    <Card className="mt-5" borderRadius="10px" style={{ background: notes }} key={item.id}>
      <CardBody>

        <div className="flex gap-5">
          <div className="flex-none">
            <div className="rounded-full w-12 h-12 p-3" style={{ background: profileColor(item.status) }}><ProfilePic color={profileTextColor(item.status)} /></div>
          </div>
          <div className="flex-grow">
            <div className="flex items-center">
              <div className="flex-grow">
                <h4 className="font-medium text-medium capitalize">{title}</h4>
                <h5 className="font-medium text-xs">{item?.penNo}</h5>
                <h5 className="font-medium text-xs capitalize">{item?.designation}</h5>
              </div>
              <div className="flex-none">
                <span className="ml-auto text-xs pr-5">{caption}</span>
              </div>
              <div className="flex-none">
                {!isExpand && description?.length > 150 && <IconButton onClick={() => handleExpandOrCollapse(item)} icon={<UnExpand />} variant="unstyled" className="mt-3" />}
              </div>
            </div>
            <div className="max-h-[400px] overflow-y-auto">
              <RichLabel value={description?.length > 150 ? `${description?.substring(0, 150)}...` : description} className="whitespace-pre-wrap" />
            </div>
            {showAttachment
              && (
                <div className="flex gap3 w-full flex-wrap">
                  {item?.notesDocsDetails?.map((doc) => (
                    <PreviewThumb
                      key={doc.id}
                      handlePreviewRemove={(data) => onHandleRemove(data)}
                      handlePreview={(data) => handlePreview(data)}
                      item={doc}
                      preview={previewData}
                      fileType={doc?.contentType}
                      loading={loading}
                      from="note"
                    />
                  ))}
                </div>
              )}
            <div className="flex gap3">
              {action.length > 0 && (
                <div className="flex-none">
                  {actions.includes('edit') && <IconButton variant="ghost" className="bg-none" onClick={edit} icon={<Edit />} />}
                  {actions.includes('delete') && <IconButton variant="ghost" onClick={handleRemove} icon={<Delete />} />}
                </div>
              )}
              <div className="flex-grow text-right">
                {index
                  ? <IconButton variant="ghost" className="bg-none" onClick={() => handleChecked(index, item)} icon={selectedIndex === index ? <CheckedRadio /> : <UnCheckedRadio />} /> : ''}
              </div>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo
});
const mapDispatchToProps = (dispatch) => ({
  deleteDocuments: (data) => dispatch(actions.deleteDocuments(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data))
});
export default connect(mapStateToProps, mapDispatchToProps)(NotesCard);
