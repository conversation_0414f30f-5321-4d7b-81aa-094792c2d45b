import React, { useState } from 'react';

function Tabs({ tabs = [], defaultTab = 0, onTabChange }) {
  const [activeIndex, setActiveIndex] = useState(defaultTab);

  const handleTabClick = (index) => {
    setActiveIndex(index);
    onTabChange?.(index);
  };

  return (
    <div className="w-full h-full p-2 flex flex-col gap-3">
      <div className="inline-flex bg-[#E6EFF5] rounded-full w-full">
        {tabs.map((tab, index) => {
          const isActive = index === activeIndex;
          return (
            <button
              key={index?.toString()}
              onClick={() => handleTabClick(index)}
              className={`flex-1 px-6 py-2 rounded-full transition-all text-[12px] font-[700] ${
                isActive
                  ? 'bg-[#00B2EC] text-white shadow'
                  : 'text-gray-600 hover:bg-gray-200'
              }`}
            >
              {tab.title}
            </button>
          );
        })}
      </div>

      <div className="flex-grow overflow-auto h-[90%]">{tabs[activeIndex]?.content}</div>
    </div>
  );
}

export default Tabs;
