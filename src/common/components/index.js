import {
  utils,
  ThemeProvider, theme, ksmThemeStyles, InfoPages, FileUpload, Info, FormLabel, AccordionComponent,
  Button, Checkbox, Select, Switch, Input, Grid, GridItem, Box, SimpleGrid, ErrorText, TextInput, DatePickerComponent,
  Tabs, TabList, TabPanels, Tab, TabPanel, Card, CardHeader, CardBody, CardFooter, Divider, IconButton,
  BasicCard, CardWithHeader, Toaster, VerticalStepper, RadioButton, TextArea, FormModal, InputGroup, InputLeftElement, InputRightElement, Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  SideMenu,
  CustomTable,
  LocationMap,
  MoreActions,
  AudioRecorder,
  VideoRecorder,
  ImageCapture,
  RichText,
  RichLabel,
  Tag,
  TagLabel,
  TagLeftIcon,
  TagRightIcon,
  TagCloseButton,
  Menu,
  <PERSON>uButton,
  MenuList,
  MenuItem,
  MenuItemOption,
  MenuGroup,
  MenuOptionGroup,
  MenuDivider,
  Progress,
  CustomAlert,
  PdfViewer,
  Tooltip,
  CustomTab,
  ToggleSwitch,
  Flex, Text
} from '@ksmartikm/ui-components';

import i18next from 'i18next';
import { TOST_CONFIG } from 'common/constants';
import FormController from './FormController';
import FormWrapper from './FormWrapper';
import Navigator from './Navigator';
import TinyBox from './TinyBox';
import NotesCard from './NotesCard';
import ExpandedNoteCard from './ExpandedNoteCard';
import CustomInfo from './CustomInfo';
import Collapse from './Collapse';
import TruncatedText from './TruncatedText';
import Breadcrumbs from './Breadcrumbs/Breadcrumbs';

const { createTost, ToastContainer } = Toaster;
const Toast = createTost(TOST_CONFIG);

export const { t } = i18next;

export {
  utils,
  ThemeProvider,
  ksmThemeStyles, theme, FormController, Button, InfoPages, FileUpload, FormWrapper, Info, FormLabel, AccordionComponent,
  Checkbox, Select, Switch, Input, Grid, GridItem, Box, SimpleGrid, ErrorText, TextInput, DatePickerComponent, InputGroup, InputLeftElement, InputRightElement,
  Tabs, TabList, TabPanels, Tab, TabPanel, Card, CardHeader, CardBody, CardFooter, Divider, BasicCard, CardWithHeader, Toast, ToastContainer, VerticalStepper, RadioButton, TextArea, FormModal, IconButton, Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  SideMenu,
  CustomTable,
  Navigator,
  Toaster,
  TinyBox,
  LocationMap,
  MoreActions,
  AudioRecorder,
  VideoRecorder,
  ImageCapture,
  RichText,
  RichLabel,
  NotesCard,
  Tag,
  TagLabel,
  TagLeftIcon,
  TagRightIcon,
  TagCloseButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuItemOption,
  MenuGroup,
  MenuOptionGroup,
  MenuDivider,
  Progress,
  CustomAlert,
  ExpandedNoteCard,
  PdfViewer,
  Tooltip,
  CustomTab,
  ToggleSwitch,
  CustomInfo,
  Flex,
  Text,
  Collapse,
  TruncatedText,
  Breadcrumbs
};
