import React from 'react';

import {
  <PERSON><PERSON>,
  <PERSON>dal,
  ModalContent,
  ModalBody,
  ModalOverlay,
  t
} from 'common/components';
import { dateTimeNow } from 'utils/date';

const InwardUserConfirmation = ({
  open, close, closeOnOverlayClick = false, closeOnEsc = false, handleSelect = () => { }, nextUser = {}, returnToCitizen = false, isLoading = false
}) => {
  const {
    inwardNo = '', forwardingTo = '', designation = '', role = '', penNo = '', seat = ''
  } = nextUser;

  return (
    <Modal isOpen={open} size="2xl" closeOnOverlayClick={closeOnOverlayClick} onClose={close} closeOnEsc={closeOnEsc} className="custom-form-modal">
      <ModalOverlay />
      <ModalContent className="form-modal" p={0}>
        <ModalBody p={0}>
          <div className="text-[#09327B] text-[22px] font-[600] text-center mt-[19px]">
            {t('user')}
          </div>
          <div className="text-[#E82C78] text-[14px] font-[600] text-center leading-[25px]">
            {t('inwardNo')} : {inwardNo}
          </div>
          {returnToCitizen ? <div className="bg-[#F2F2F2] rounded-[8px] leading-[20px] text-center p-5 mt-5 mb-2">{t('fileReturningToCitizen')}</div>
            : (
              <table className="w-full mt-[14px]">
                <tbody>
                  <tr className="bg-[#F2F2F2] rounded-[8px] leading-[20px]">
                    <td>
                      <span className="text-[#454545] text-[14px] font-[400] block px-12 pt-[13px] pb-[10px]">{t('forwardTo')}</span>
                      <span className="text-[#09327B] text-[14px] font-[600] block px-12 pt-[6px] pb-[10px]">{forwardingTo}</span>
                    </td>
                    <td>
                      <span className="text-[#454545] text-[14px] font-[400] block px-12 pt-[13px] pb-[10px]">{t('designation')}</span>
                      <span className="text-[#09327B] text-[14px] font-[600] block px-12 pt-[6px] pb-[10px]">{designation}</span>
                    </td>
                  </tr>

                  <tr className="bg-white rounded-[8px] leading-[20px]">
                    <td>
                      <span className="text-[#454545] text-[14px] font-[400] block px-12 pt-[13px] pb-[10px]">{t('role')}</span>
                      <span className="text-[#09327B] text-[14px] font-[600] block px-12 pt-[6px] pb-[10px]">{role}</span>
                    </td>
                    <td>
                      <span className="text-[#454545] text-[14px] font-[400] block px-12 pt-[13px] pb-[10px]">{t('penCap')}</span>
                      <span className="text-[#09327B] text-[14px] font-[600] block px-12 pt-[6px] pb-[10px]">{penNo}</span>
                    </td>
                  </tr>
                  <tr className="bg-[#F2F2F2] rounded-[8px] leading-[20px]">
                    <td>
                      <span className="text-[#454545] text-[14px] font-[400] block px-12 pt-[13px] pb-[10px]">{t('seatCap')}</span>
                      <span className="text-[#09327B] text-[14px] font-[600] block px-12 pt-[6px] pb-[10px]">{seat}</span>
                    </td>
                    <td>
                      <span className="text-[#454545] text-[14px] font-[400] block px-12 pt-[13px] pb-[10px]">{t('dateAndTime')}</span>
                      <span className="text-[#09327B] text-[14px] font-[600] block px-12 pt-[6px] pb-[10px]">{dateTimeNow()}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            )}
        </ModalBody>

        <div className="col-span-12 flex justify-center space-x-2 p-[30px]">
          <Button
            variant="secondary_outline"
            size="xs"
            mr={3}
            onClick={close}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="secondary"
            type="submit"
            size="xs"
            onClick={handleSelect}
            style={{ width: '109.45px' }}
            isLoading={isLoading}
          >
            {t('ok')}
          </Button>
        </div>
      </ModalContent>
    </Modal>
  );
};

export default InwardUserConfirmation;
