import { useState } from 'react';
import { Tooltip, Text } from 'common/components';

const TruncatedText = ({
  text,
  className = '',
  fontSize,
  fontWeight,
  cursor,
  maxWidth = ['100%', '150px', '300px'],
  ...rest
}) => {
  const [isOverflowing, setIsOverflowing] = useState(false);

  const handleTextCheck = (element) => {
    if (element && element.scrollWidth > element.offsetWidth) {
      setIsOverflowing(true);
    }
  };

  return (
    <Tooltip
      label={isOverflowing ? text : ''}
      hasArrow
      bg="gray.700"
      color="white"
    >
      <Text
        ref={handleTextCheck}
        maxWidth={maxWidth}
        fontSize={fontSize}
        fontWeight={fontWeight}
        whiteSpace="nowrap"
        overflow="hidden"
        textOverflow="ellipsis"
        cursor={cursor || isOverflowing ? 'pointer' : 'default'}
        className={`truncate ${className}`}
        {...rest}
      >
        {text}
      </Text>
    </Tooltip>
  );
};

export default TruncatedText;
