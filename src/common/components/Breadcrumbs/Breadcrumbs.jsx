import React from 'react';
import { BreadcrumbItem } from './BreadcrumbItem';

const Breadcrumbs = ({
  items = [],
  className = '',
  onDropdownToggle,
  isDropdownOpen,
  dropdownContent
}) => {
  return (
    <div className="relative w-full overflow-x-auto">
      <nav
        className={`flex items-center space-x-2 sm:space-x-3 ${className} whitespace-nowrap`}
      >
        {items.map((item, index) => (
          <BreadcrumbItem
            key={item?.text}
            item={item}
            isLast={index === items.length - 1}
            onDropdownToggle={onDropdownToggle}
            isDropdownOpen={isDropdownOpen}
          />
        ))}
      </nav>

      {isDropdownOpen && dropdownContent && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-10 py-2 border border-gray-200">
          {dropdownContent}
        </div>
      )}
    </div>
  );
};

export default Breadcrumbs;
