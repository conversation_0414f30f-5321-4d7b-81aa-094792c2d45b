import { IconButton } from '@ksmartikm/ui-components';
import <PERSON><PERSON>eft from 'assets/ArrowLeft';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';
import KeyboardRightArrow from 'assets/KeyboardRightArrow';
import React from 'react';

export const BreadcrumbItem = ({ item, isLast }) => {
  if (item?.isRouteBackAction) {
    return (
      <IconButton
        minW={0}
        variant="link"
        icon={<ArrowLeft />}
        onClick={item?.onClick}
      />
    );
  }

  return (
    <div className="flex items-center gap-2 sm:gap-4">
      <div className="flex items-center">
        <span
          aria-hidden
          onClick={item?.onClick}
          className={`text-[14px] sm:text-[15px] ${
            item?.onClick ? 'hover:underline cursor-pointer' : 'cursor-default'
          }`}
          style={{
            color: isLast ? '#09327B' : '#5C6E93',
            fontWeight: isLast ? 700 : 500
          }}
        >
          {item?.text}
        </span>
        {item?.isDropdownTrigger && <KeyboardDownArrow />}
      </div>
      {!isLast && <KeyboardRightArrow />}
    </div>
  );
};
