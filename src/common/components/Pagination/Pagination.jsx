import PaginationLeftIcon from 'assets/PaginationLeftIcon';
import PaginationRightIcon from 'assets/PaginationRightIcon';
import React from 'react';

const Pagination = ({
  totalPages,
  currentPage,
  onPageChange = () => {}
}) => {
  // const handlePageChange = (page) => {
  //   if (page > currentPage) {
  //     onPageChange('next');
  //   } else if (page < currentPage) {
  //     onPageChange('previous');
  //   } else {
  //     onPageChange(page);
  //   }
  // };

  return (
    <div className="flex items-center justify-center mt-4">
      <button
        aria-label="previous-page"
        onClick={() => onPageChange('previous')}
        disabled={currentPage === 1}
        className="px-3 py-1 mx-1 text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50 h-8 w-8"
      >
        <PaginationLeftIcon className="w-5 h-5" />
      </button>

      {/* First Page */}
      <button
        onClick={() => onPageChange(1)}
        className={`px-3 py-1 mx-1 ${
          currentPage === 1
            ? 'text-white bg-[#E82C78]'
            : 'text-[#454545] bg-[#E8ECEE] hover:bg-gray-200'
        } border border-[#E8ECEE] rounded-md text-[14px]`}
      >
        1
      </button>

      {/* Ellipsis if needed */}
      {totalPages > 2 && currentPage > 3 && (
        <span className="mx-1">...</span>
      )}

      {/* Middle Pages */}
      {Array.from({ length: totalPages - 2 }, (_, index) => index + 2)
        .filter((page) => page > 1 && page < totalPages)
        .slice(
          Math.max(0, currentPage - 3),
          Math.min(currentPage + 1, totalPages - 1)
        )
        .map((page) => {
          return (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`px-3 py-1 mx-1 ${(currentPage) === page
                ? 'text-white bg-[#E82C78]'
                : 'text-[#454545] bg-[#E8ECEE] hover:bg-gray-200'
              } border border-[#E8ECEE] rounded-md text-[14px]`}
            >
              {page}
            </button>
          );
        })}

      {/* Ellipsis if needed */}
      {/* {currentPage < totalPages - 2 && totalPages > 5 && <span className="mx-1">...</span>} */}
      {totalPages > 2 && currentPage < totalPages - 3 && (
        <span className="mx-1">...</span>
      )}

      {/* Last Page */}
      {totalPages > 1 && (
        <button
          onClick={() => onPageChange(totalPages)}
          className={`px-3 py-1 mx-1 ${
            (currentPage) === totalPages
              ? 'text-white bg-[#E82C78]'
              : 'text-[#454545] bg-[#E8ECEE] hover:bg-gray-200'
          } border border-[#E8ECEE] rounded-md text-[14px]`}
        >
          {totalPages}
        </button>
      )}

      <button
        aria-label="next-page"
        onClick={() => onPageChange('next')}
        disabled={(currentPage) === totalPages}
        className="px-3 py-1 mx-1 text-gray-500 bg-white border border-[#E8ECEE] rounded-md hover:bg-[#E8ECEE] disabled:opacity-50 h-8 w-8"
      >
        <PaginationRightIcon className="w-5 h-5" />
      </button>
    </div>
  );
};

export default Pagination;
