import { useState, useEffect, useRef } from 'react';

const Collapse = ({ open, children, className }) => {
  const contentRef = useRef(null);
  const [height, setHeight] = useState(open ? 'auto' : '0px');

  useEffect(() => {
    if (open) {
      setHeight(`${contentRef.current.scrollHeight}px`);
    } else {
      setHeight('0px');
    }
  }, [open]);

  return (
    <div className={`w-full ${className}`}>
      <div
        id="collapseContent"
        ref={contentRef}
        className={`w-full overflow-hidden transition-all duration-300 ease-in-out pt-${
          open ? 2 : 0
        }`}
        style={{ maxHeight: height, opacity: open ? 1 : 0 }}
        aria-hidden={!open}
      >
        <div>{children}</div>
      </div>
    </div>
  );
};

export default Collapse;
