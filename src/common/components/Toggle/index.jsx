const ToggleButton = ({
  label = 'togggle',
  togggle = false,
  setToggle = () => {}
}) => {
  return (
    <div
      className="flex gap-3 items-center"
      role="button"
      onClick={() => setToggle(!togggle)}
      onKeyDown={(event) => (event.key === 'Enter' ? setToggle(!togggle) : null)}
      tabIndex="0"
    >
      {togggle ? (
        <div className="w-[40px] h-[24px] p-[1px] border rounded-full" style={{ background: 'rgba(232, 44, 120, 1)', borderColor: 'rgba(232, 44, 120,.1)' }}>
          <div className="w-[20px] h-[20px] ml-[auto] rounded-full" style={{ background: 'rgba(255, 255, 255, 1)' }} />
        </div>
      ) : (
        <div className="w-[40px] h-[24px] p-[1px] bg-gray-100 border rounded-full">
          <div className="w-[20px] h-[20px] bg-gray-400 rounded-full" />
        </div>
      )}
      <div className="flex-grow">
        <span className="text-sm">{label}</span>
      </div>
    </div>
  );
};

export default ToggleButton;
