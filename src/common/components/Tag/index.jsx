import React from 'react';
import {
  Tag, TagLabel, TagCloseButton, ksmThemeStyles
} from 'common/components';

const DraftRefTag = ({
  from, label, value, handleTag
}) => {
  return (
    from === 'draft-ref' ? (
      <TagLabel className="cursor-pointer" onClick={handleTag}>
        <strong>{label}</strong>{label && ': '}{value}
      </TagLabel>
    ) : (
      <TagLabel>
        {label}: <strong>{value}</strong>
      </TagLabel>
    )
  );
};

const TagLabels = ({
  from, value, label, handleTag
}) => {
  return (
    from === 'note' ? (
      <div className="rounded bg-[#E7EFF5] text-[#454545] text-[14px]">
        File No. {value}
      </div>
    ) : (
      <DraftRefTag from={from} label={label} value={value} handleTag={handleTag} />
    )
  );
};

const Tags = (props) => {
  const { colors } = ksmThemeStyles;

  const styles = {
    tag: {
      border: 0,
      padding: '5px 10px',
      borderRadius: '6px',
      position: 'relative',
      color: colors.tertiary[500]
    },
    closeButton: {
      borderRadius: '100%',
      backgroundColor: 'red',
      color: '#fff',
      marginTop: '-30px',
      marginRight: '-5px',
      fontSize: '10px',
      width: '14px',
      height: '14px',
      opacity: 1,
      position: 'absolute',
      right: '10px'
    }
  };

  const {
    label = 'tag', value = '', handleDelete = () => { }, item = [], from = 'note',
    handleTag = () => { }, disable = false, onClick = () => {}
  } = props;

  return (
    <Tag
      size="md"
      borderRadius="full"
      variant="ghost"
      style={styles.tag}
      background="gray.200"
      cursor={disable ? 'default' : 'pointer'}
      onClick={!disable && onClick}
    >
      {from === 'draft' ? (
        <TagLabel>
          <strong>{label}</strong>{label && ': '}{value}
        </TagLabel>
      ) : (
        <TagLabels from={from} value={value} label={label} handleTag={handleTag} />
      )}
      {!disable && <TagCloseButton style={styles.closeButton} onClick={(e) => { e?.stopPropagation(); handleDelete(item); }} />}
    </Tag>
  );
};
export default Tags;
