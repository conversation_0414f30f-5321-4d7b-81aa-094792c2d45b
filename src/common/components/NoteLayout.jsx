import More from 'assets/More';
import { BASE_PATH } from 'common/constants';
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const NoteLayout = ({
  title = '', content, moreMenu = false, from = 'summary',
  fromValue = ''
}) => {
  const navigate = useNavigate();
  const params = useParams();

  return (
    <div className="bg-[#E7EFF5] rounded-lg overflow-hidden">
      <div className={`px-4 py-2 ${from === 'note-file' ? 'bg-white' : 'bg-[#E7EFF5]'}`}>
        <div className="flex">
          {from === 'summary' && (
            <div className="flex-grow">
              <h2 className="text-[#153171] font-medium text-[16px]">{title}</h2>
            </div>
          )}
          {moreMenu && (
            <div
              className="flex cursor-pointer"
              aria-hidden
              onClick={() => {
                const baseUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=0`;
                const fromParams = new URLSearchParams(fromValue);
                fromParams.delete('show');
                const finalUrl = fromParams.toString() ? `${baseUrl}&${fromParams}` : baseUrl;
                navigate(finalUrl);
              }}
            >
              <div className="flex-none text-[#00B2EB] text-[16px] font-bold">
                All Notes
              </div>
              <div className="flex-none pl-1 pt-[2px]">
                <More />
              </div>
            </div>
          )}

        </div>
        <div>
          {content}
        </div>
      </div>
    </div>
  );
};

export default NoteLayout;
