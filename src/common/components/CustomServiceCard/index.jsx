import React from 'react';

const CustomServiceCard = (props) => {
  const {
    label, leftIcon, onClick, disabled = false, count = 0, colors
  } = props;
  const { bgColor = '', textColor = '' } = colors;

  return (
    <div className="w-full h-[99px] rounded-[24px] mb-4" style={{ background: bgColor }}>
      <button disabled={disabled} onClick={onClick} className="flex items-center w-full h-full p-4 justify-between" aria-label="button">
        <div className="flex justify-start items-center h-full">
          <div className="rounded-2xl shadow-md mr-3 bg-white flex justify-center items-center h-full aspect-square">{leftIcon}</div>
          <div className="text-left font-bold" style={{ color: textColor }}>{label}</div>
        </div>
        <div className="rounded-full w-[30px] h-[30px] text-white flex justify-center font-bold items-center" style={{ background: textColor }}>{count}</div>
      </button>
    </div>
  );
};

export default CustomServiceCard;
