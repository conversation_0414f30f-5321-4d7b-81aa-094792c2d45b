import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';

const ExcelViewer = ({ blobUrl }) => {
  const [excelData, setExcelData] = useState([]);

  useEffect(() => {
    const fetchExcelData = async () => {
      try {
        const response = await fetch(blobUrl);
        const blob = await response.blob();

        const arrayBuffer = await blob.arrayBuffer();

        const workbook = XLSX.read(arrayBuffer, { type: 'array' });

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        const data = XLSX.utils.sheet_to_json(worksheet);

        setExcelData(data);
      } catch (error) {
        setExcelData(null);
      }
    };

    fetchExcelData();
  }, [blobUrl]);

  const currentDate = Date.parse(new Date());

  return (
    <div className="striped relative">
      <table className="striped-table" border="1">
        <thead>
          <tr>
            {excelData[0] && Object.keys(excelData[0]).map((key) => (
              <th className="th-center" aria-label="th" key={key}>{key}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {excelData.map((row) => (
            <tr key={`header-${currentDate + excelData.indexOf(row)}`}>
              {Object.values(row).map((cell, index) => (
                <td className="th-center" aria-label="td" key={`td-${currentDate + Object.values(row).indexOf(cell)}_${index.toString()}`}>{cell}</td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ExcelViewer;
