import React, { useState, useRef } from 'react';
import './Zoom.css';
import { PdfViewer } from '@ksmartikm/ui-components'; // Import your PDF viewer

export const ZoomComponent = ({
  image, type, zoom
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startDrag, setStartDrag] = useState({ x: 0, y: 0 });
  const imageRef = useRef(null);
  const pdfRef = useRef(null);

  // Handle the start of dragging
  const handleMouseDown = (event) => {
    setIsDragging(true);
    setStartDrag({ x: event.clientX - position.x, y: event.clientY - position.y });
  };

  // Handle dragging movement
  const handleMouseMove = (event) => {
    if (isDragging) {
      setPosition({ x: event.clientX - startDrag.x, y: event.clientY - startDrag.y });
    }
  };

  // Stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Adjust the zoom for the PdfViewer component
  const adjustPdfZoom = () => {
    if (pdfRef.current) {
      pdfRef.current.style.transform = `scale(${zoom}) translate(${position.x}px, ${position.y}px)`;
    }
  };

  // Apply zoom effects
  React.useEffect(() => {
    if (type === 'pdf') {
      adjustPdfZoom();
    }
  }, [zoom, position, type]);

  return (
    <div
      className="zoom-container"
      aria-hidden
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      <div className="image-container" aria-hidden>
        {type === 'image' && (
          <img
            ref={imageRef}
            src={image}
            alt="Zoomable content"
            className="zoom-image"
            style={{
              transform: `scale(${zoom}) translate(${position.x}px, ${position.y}px)`,
              cursor: isDragging ? 'grabbing' : 'grab'
            }}
            // onClick={handleZoomIn} // Zoom in on left-click
            // onContextMenu={handleZoomOut} // Zoom out on right-click
            onMouseDown={handleMouseDown} // Start dragging on mouse down
            aria-hidden
          />
        )}
        {type === 'pdf' && (
          <div
            ref={pdfRef}
            className="pdf-wrapper"
            // onClick={handleZoomIn}
            // onContextMenu={handleZoomOut}
            onMouseDown={handleMouseDown}
            style={{
              transform: `scale(${zoom}) translate(${position.x}px, ${position.y}px)`,
              cursor: isDragging ? 'grabbing' : 'grab'
            }}
            aria-hidden
          >
            <PdfViewer
              data={image}
              type="scroll"
              variant="normal"
              alt="Zoomable content"
            />
          </div>
        )}
      </div>
    </div>
  );
};
