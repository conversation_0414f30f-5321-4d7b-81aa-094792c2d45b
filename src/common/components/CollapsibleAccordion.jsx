import {
  Box, IconButton, Flex, Text, Tooltip
} from '@ksmartikm/ui-components';
import { useEffect, useRef, useState } from 'react';
import PlusIconTiny from 'assets/PlusIconTiny';
import MinusIconTiny from 'assets/MinusIconTiny';
import { t } from '.';

const CollapsibleAccordion = ({
  heading = '',
  children,
  boxProps = {},
  flexProps = {},
  collapsibleBoxStyle = {},
  containerProps = {},
  isExpanded = undefined,
  onExpand = undefined,
  hideCollapseButton,
  collapseButtonTooltip = '',
  renderHeaderEnd = () => null
}) => {
  const [isOpen, setIsOpen] = useState(true);
  const contentRef = useRef(null);
  const [maxHeight, setMaxHeight] = useState('0px');

  const flag = isExpanded !== undefined ? isExpanded : isOpen;

  useEffect(() => {
    const updateHeight = () => {
      if (contentRef.current) {
        setMaxHeight(flag ? `${contentRef.current.scrollHeight}px` : '0px');
      }
    };

    // Track height changes inside Collapse
    const observer = new ResizeObserver(updateHeight);
    if (contentRef.current) {
      observer.observe(contentRef.current);
    }

    // Update once on mount and when `isOpen` changes
    updateHeight();

    return () => observer.disconnect();
  }, [isOpen, isExpanded]);

  return (
    <Box
      borderRadius="8px"
      overflow="hidden"
      bg="#fff"
      boxShadow="sm"
      {...containerProps}
    >
      {/* Accordion Header */}
      <Flex
        py={2.5}
        pr={2}
        pl={5}
        align="center"
        justify="space-between"
        borderBottom="1px solid #E6EFF5"
        {...flexProps}
      >
        <Text fontWeight="bold" color="#09327B" fontSize="15px">
          {t(heading)}
        </Text>
        <div className="flex items-center gap-4">
          {renderHeaderEnd()}
          {!hideCollapseButton && (
            <Tooltip fontSize="11px" label={collapseButtonTooltip}>
              <IconButton
                rounded="4px"
                size="xs"
                aria-label="Toggle CollapsibleAccordion"
                px="8px"
                py="10px"
                icon={flag ? <MinusIconTiny /> : <PlusIconTiny />}
                onClick={() => {
                  if (onExpand !== undefined) {
                    onExpand(!isExpanded);
                  } else {
                    setIsOpen(!isOpen);
                  }
                }}
              />
            </Tooltip>
          )}
        </div>
      </Flex>

      {/* Collapsible Content */}
      <Box
        className="overflow-hidden transition-[max-height] duration-300 ease-in-out"
        style={{
          maxHeight,
          opacity: flag ? 1 : 0,
          ...collapsibleBoxStyle
        }}
      >
        <Box p={6} bg="white" {...boxProps} ref={contentRef}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default CollapsibleAccordion;
