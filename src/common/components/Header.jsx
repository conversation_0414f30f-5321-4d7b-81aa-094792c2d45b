import React from 'react';
import { STATE_REDUCER_KEY } from 'pages/common';
import { useSelector } from 'react-redux';
import { dark } from 'utils/color';
import { Info } from 'common/components';
import { HEADER } from 'common/constants';
import { handleFileRole } from 'pages/file/details/components/helper';

const FileHead = () => {
  const { fileHeader } = useSelector((state) => state[STATE_REDUCER_KEY]);

  return (
    <div className="bg-white px-10 py-5 flex rounded-lg">
      {fileHeader?.map((item) => (
        <div key={item.label} className="flex-grow text-center first:text-left last:text-right">
          <h4 className="text-blue-600 text-md font-medium" style={{ color: dark }}>
            {item.label}:
            <strong className="text-stone-950 pl-3">

              {item?.label === 'Role' ? handleFileRole(item?.value) : item?.value}
              {item.label === HEADER.SERVICE
                && item.subValue
                && (item?.subValue?.module !== undefined
                  || item?.subValue?.module !== null
                  || item?.subValue?.module !== '')
                && (item?.subValue?.subModule !== undefined
                  || item?.subValue?.subModule !== null
                  || item?.subValue?.subModule !== '') && (
                  <div className="inline-block -mb-1">
                    <Info
                      text={(
                        <>
                          {item.subValue.module && <div>{`${HEADER.MODULE} : ${item.subValue.module}`}</div>}
                          {item.subValue.subModule && <div>{`${HEADER.SUBMODULE} : ${item.subValue.subModule}`}</div>}
                        </>
                      )}
                    />
                  </div>
              )}
            </strong>
          </h4>
        </div>
      ))}
    </div>
  );
};

export default FileHead;
