import React, { useState, useEffect } from 'react';
import _ from 'lodash';

const DashboardFilter = ({
  data = [], label = '', onChange = () => {}, defaultValue = ''
}) => {
  const [response, setResponse] = useState();

  useEffect(() => {
    if (defaultValue) {
      setResponse(defaultValue);
    }
  }, [defaultValue]);

  return (
    <select
      value={response}
      className="rounded-[5px] bg-white p-[5px]"
      onChange={(event) => {
        setResponse(event.target.value);
        let res;
        onChange(_.find(data, (o) => {
          if (Number(o.id) === Number(event.target.value)) {
            res = o;
          }
          return res;
        }));
      }}
    >
      <option value="" className="text-[15px]">{label}</option>
      {data?.map((item) => {
        return (
          <option className="text-[15px]" value={item.id}>{item.name}</option>
        );
      })}
    </select>
  );
};

export default DashboardFilter;
