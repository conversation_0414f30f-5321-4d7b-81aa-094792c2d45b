import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  t,
  ModalOverlay
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { dark, light } from 'utils/color.js';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getRegisterOpen } from 'pages/common/selectors';

const Registration = (props) => {
  const {
    setRegisterOpen,
    registerOpen
  } = props;

  return (
    <Modal isOpen={registerOpen} size="full" onClose={() => setRegisterOpen(false)}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader p={0} className="text-center">
          <h4 size="md" className="p-5 rounded-t-lg" style={{ background: light, color: dark }}>
            {t('register')}
          </h4>
        </ModalHeader>
        <ModalBody>
          <iframe loading="lazy" title="KMSART-USER-REGISTRATION" style={{ height: 'calc(100vh - 170px)' }} width="100%" src="https://dev.ksmart.live/ui/home/<USER>/dfms/register-other" />
        </ModalBody>
        <ModalFooter style={{ background: light, color: dark }}>
          <div className="w-full text-right space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              onClick={() => setRegisterOpen(false)}
            >
              {t('close')}
            </Button>

          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
  registerOpen: getRegisterOpen
});

const mapDispatchToProps = (dispatch) => ({
  setRegisterOpen: (data) => dispatch(commonSliceActions.setRegisterOpen(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Registration);
