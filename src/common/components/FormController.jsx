import {
  ErrorText,
  Select,
  Switch,
  TextInput,
  DatePickerComponent,
  RadioButton,
  TextArea,
  Checkbox,
  RichText
} from 'common/components';
import { Controller } from 'react-hook-form';
import _ from 'lodash';
import FileUpload from './FileUpload';
import FileUpload<PERSON> from './FileUploadUI';
import MultiSelect from './MultiSelect';

const FormController = (props) => {
  const {
    type, name, control, label, errors, optionKey = 'code', content
  } = props;
  const error = _.get(errors, `${name}.message`, null);

  switch (type) {
    case 'time':
    case 'date':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <DatePickerComponent
                {...field}
                {...props}
                {...{ error }}
                onChange={(data) => {
                  field.onChange(data);
                  props?.handleChange?.(data);
                }}
              />
            )}
          />
        </>
      );

    case 'radio':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <RadioButton
                {...field}
                {...props}
                {...{ error }}
                onChange={(data) => {
                  field.onChange(data);
                  props?.handleChange?.(data);
                }}
              />
            )}
          />
        </>
      );

    case 'check':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <>
                <Checkbox
                  {...field}
                  {...props}
                  onChange={(e) => {
                    field.onChange(e);
                    props?.handleChange?.(e);
                  }}
                  isChecked={field?.value || false}
                >
                  {label}
                </Checkbox>
                {error && <ErrorText error={error} />}
              </>
            )}
          />
        </>
      );

    case 'multi-select':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <>
                <MultiSelect
                  {...field}
                  {...props}
                  {...{ error }}
                  onChange={(e) => {
                    field.onChange(e);
                    props?.handleChange?.(e);
                  }}
                />
                {error && <ErrorText error={error} />}
              </>
            )}
          />
        </>
      );

    case 'select': {
      const { isMulti = false, isClearable = true } = props;
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <Select
              {...field}
              {...props}
              {...{ error }}
              closeMenuOnSelect={!isMulti}
              hideSelectedOptions={false}
              isClearable={isClearable}
              onChange={(data) => {
                // eslint-disable-next-line no-nested-ternary
                const selectedData = isMulti
                  ? _.map(data, (item) => {
                    return item[optionKey];
                  }) || []
                  : data
                    ? data[optionKey]
                    : null;
                field.onChange(selectedData);
                props?.handleChange?.(data);
              }}
            />
          )}
        />
      );
    }
    case 'toggle':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <>
                <Switch {...field} {...props}>
                  {label}
                </Switch>
                {error && <ErrorText error={error} />}
              </>
            )}
          />
        </>
      );
    case 'file':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <FileUpload
                {...field}
                {...props}
                {...{ error }}
                onChange={(e) => {
                  field.onChange(e);
                  props?.handleChange?.(e);
                }}
              />
            )}
          />
        </>
      );
    case 'fileUI':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <FileUploadUI
                {...field}
                {...props}
                {...{ error }}
                onChange={(e) => {
                  field.onChange(e);
                  props?.handleChange?.(e);
                }}
              />
            )}
          />
        </>
      );

    case 'textarea':
      return (
        <>
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <TextArea
                {...field}
                {...props}
                {...{ error }}
                onChange={(data) => {
                  field.onChange(data);
                  props?.handleChange?.(data);
                }}
              />
            )}
          />
          <div id={name} className="mt-[-200px] absolute" />
        </>
      );
    case 'rich':
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <RichText
                toolbarPosition="top"
                {...field}
                {...props}
                {...{ error }}
                onChange={(data) => {
                  field.onChange(data);
                  props?.handleChange?.(data);
                }}
              />
            )}
          />
        </>
      );
    default:
      return (
        <>
          <div id={name} className="mt-[-200px] absolute" />
          <Controller
            name={name}
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                {...props}
                {...{ error }}
                onChange={(data) => {
                  field.onChange(data);
                  props?.handleChange?.(data);
                }}
                content={content}
              />
            )}
          />
        </>
      );
  }
};

export default FormController;
