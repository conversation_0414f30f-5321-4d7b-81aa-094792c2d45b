import React, { useState, useEffect } from 'react';
import KeyboardLeftArrow from 'assets/KeyboardLeftArrow';
import KeyboardRightArrow from 'assets/KeyboardRightArrow';

const PaginationButton = ({
  children,
  onClick,
  isActive,
  pageRole,
  pageNumber,
  getPageColor
}) => {
  const defaultColor = 'bg-gray-300 text-black';
  const colorClass = isActive
    ? defaultColor
    : 'hover:bg-gray-100 border-[#E8ECEE]';

  return (
    <button
      className={`w-9 h-9 flex items-center justify-center rounded-[8px] border transition ${colorClass}`}
      style={getPageColor?.(pageNumber, { isActive, role: pageRole })}
      onClick={onClick}
      disabled={!onClick}
    >
      <span className="text-[13px] font-semibold">{children}</span>
    </button>
  );
};

const NavigationButton = ({ direction, onClick, disabled }) => (
  <button
    className={`w-9 h-9 flex items-center justify-center rounded-[8px] border border-[#E8ECEE] transition 
      ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
    onClick={onClick}
    disabled={disabled}
  >
    {direction === 'left' ? <KeyboardLeftArrow /> : <KeyboardRightArrow />}
  </button>
);

const DraftPagination = ({
  totalPages,
  currentPage,
  onPageChange,
  maxVisibleButtons = 5,
  formatPageNumber = (num) => num,
  getPageColor = () => {},
  showPreviousPermanentButton = false,
  goToLastPageOnGroupChange = false
}) => {
  const firstPage = 1;
  const lastPage = totalPages;

  const [startPage, setStartPage] = useState(1);
  const [endPage, setEndPage] = useState(
    Math.min(maxVisibleButtons, totalPages)
  );

  useEffect(() => {
    if (currentPage > endPage || currentPage < startPage) {
      const newStartPage = Math.max(
        firstPage,
        Math.floor((currentPage - 1) / maxVisibleButtons) * maxVisibleButtons
          + 1
      );

      const newEndPage = Math.min(
        newStartPage + maxVisibleButtons - 1,
        lastPage
      );

      setStartPage(newStartPage);
      setEndPage(newEndPage);
    } else if (endPage > lastPage) {
      setEndPage(lastPage);
    }
  }, [currentPage, maxVisibleButtons, lastPage]);

  const handlePageChange = (page, e) => {
    if (
      ((page < firstPage || page > lastPage) && e?.role !== 'permanent')
      || !onPageChange
    ) {
      return;
    }

    onPageChange(page, e);
  };

  const handleNextGroup = () => {
    const nextStart = endPage + 1;
    if (nextStart <= lastPage) {
      const nextEnd = Math.min(nextStart + maxVisibleButtons - 1, lastPage);

      setStartPage(nextStart);
      setEndPage(nextEnd);

      handlePageChange(goToLastPageOnGroupChange ? nextEnd : nextStart);
    }
  };

  const handlePrevGroup = () => {
    const prevStart = Math.max(startPage - maxVisibleButtons, firstPage);
    if (prevStart >= firstPage) {
      const prevEnd = Math.min(prevStart + maxVisibleButtons - 1, lastPage);

      setStartPage(prevStart);
      setEndPage(prevEnd);

      handlePageChange(goToLastPageOnGroupChange ? prevEnd : prevStart);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <NavigationButton
        direction="left"
        onClick={handlePrevGroup}
        disabled={startPage === firstPage}
      />

      {showPreviousPermanentButton && (
        <PaginationButton
          pageNumber={0}
          pageRole="permanent"
          isActive={currentPage === 0}
          onClick={() => handlePageChange(0, { role: 'permanent' })}
          getPageColor={getPageColor}
        >
          {formatPageNumber(0) || 0}
        </PaginationButton>
      )}

      {[...Array(Math.max(0, endPage - startPage + 1))]?.map((_, index) => {
        const page = startPage + index;
        return (
          <PaginationButton
            key={page}
            onClick={() => handlePageChange(page)}
            isActive={currentPage === page}
            pageNumber={page}
            getPageColor={getPageColor}
          >
            {formatPageNumber(page)}
          </PaginationButton>
        );
      })}

      <NavigationButton
        direction="right"
        onClick={handleNextGroup}
        disabled={endPage >= lastPage}
      />
    </div>
  );
};

export default DraftPagination;
