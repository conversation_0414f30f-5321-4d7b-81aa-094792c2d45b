import { InfoSolid } from 'assets/Svg';
import React from 'react';
import { secondary } from 'utils/color';
import './style.css';
import RightIcon from 'assets/RightIcon';

const CustomInfo = ({
  placement = 'top', content = <div>text</div>
}) => {
  return (
    <div className="custom-info-cover">
      <InfoSolid color={secondary} />

      <div className={`info-content ${placement} text-${placement === 'left' ? 'right' : 'left'}`}>
        {content}
        <RightIcon color="rgba(35, 47, 80, 1)" width="32" height="32" />
      </div>
    </div>
  );
};

export default CustomInfo;
