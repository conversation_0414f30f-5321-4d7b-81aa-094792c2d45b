.custom-info-cover{
    position: relative;
    display: inline-block;
}
.custom-info-cover .info-content{
    visibility: hidden;
    width:100%;
    color: #fff;
    display: block;
    text-align: center;
  /* Position the tooltip text - see examples below! */
    position: absolute;
    z-index: 1;
    background: rgba(35, 47, 80, 1);
    padding: 10px 20px;
    border-radius: 10px;
    min-width: max-content;
}
.custom-info-cover:hover .info-content{
    visibility: visible;
}
.custom-info-cover .info-content.right{
    left: 40px;
    top: -18px;
    
}

.custom-info-cover .info-content.left{
    right: 40px;
    top: -18px;
}

.custom-info-cover .info-content.top{
    bottom: 20px;
}

.custom-info-cover .info-content.bottom{
    top: 20px;
}
.custom-info-cover .info-content svg{
    position: absolute;
    right: -19px;
    top: 15px;
}