/* eslint-disable jsx-a11y/no-noninteractive-element-to-interactive-role */
import React, { useState, useEffect } from 'react';
import {
  FormLabel, InputGroup, InputRightElement, TextInput
} from '@ksmartikm/ui-components';
import './style.css';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import { DownArrow } from 'assets/Svg';

const MultiSelect = (props) => {
  const {
    label = 'select',
    required = false,
    isDisabled = false,
    ellipsis = false,
    name,
    options = [],
    optionKey = 'id',
    onChange = () => {},
    value = []

  } = props;

  const [filter, setFilter] = useState('');
  const [showDrop, setShowDrop] = useState(false);
  const [selected, setSelected] = useState([]);

  document.addEventListener('mouseup', (e) => {
    const container = document.getElementById(`drop-multi-${name}`);
    if (!container.contains(e.target)) {
      setShowDrop(false);
    }
  });

  useEffect(() => {
    if (value && (value !== selected)) {
      setSelected(value);
    }
  }, [value]);

  useEffect(() => {
    onChange(selected);
  }, [JSON.stringify(selected)]);

  const handleSelect = (data) => {
    if (data[optionKey]) {
      const copyArray = JSON.parse(JSON.stringify(selected)) || [];
      const find = selected?.findIndex((item) => item === data[optionKey]);
      if (find === -1) {
        copyArray.push(data[optionKey]);
      } else {
        copyArray.splice(find, 1);
      }
      setSelected(copyArray);
    }
  };

  function selectAll() {
    if (selected?.length > 0 && selected.length === options?.length) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  function checkBoxCheck(data) {
    const find = selected?.findIndex((item) => item === data);
    if (find > -1) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  const handleSelectAll = () => {
    if (selected.length === options?.length) {
      setSelected([]);
    } else {
      setSelected(options.map((item) => item[optionKey]));
    }
  };

  return (
    <div className="dropdown-contain relative" id={`drop-multi-${name}`}>
      <FormLabel label={label} required={required} disabled={isDisabled} ellipsis={ellipsis} />

      <InputGroup onClick={() => setShowDrop(true)}>
        <TextInput
          name={name}
          isDisabled={isDisabled}
          placeHolder={`${selected?.length === 0 ? `Select ${label}` : `${selected?.length} items selected`}`}
          value={filter}
          onChange={(event) => { setFilter(event.target.value); setShowDrop(true); }}
        />
        <InputRightElement width={`${selected?.length > 0 ? '100px' : '50px'}`} pt={4}>
          {selected?.length > 0 && <span className="mr-1">{selected?.length} items</span>} <DownArrow />
        </InputRightElement>
      </InputGroup>
      {showDrop
      && (
      <ul className="multi-drop-ul">
        {options?.filter((item) => item?.name?.toLowerCase().includes(filter?.toLowerCase()))?.length === 0 ? <li>No Options</li> : (
          <li role="button" aria-hidden="true" onClick={() => handleSelectAll()}>
            <div className="flex items-start">
              <div className="flex-none check-icon">
                {selectAll()}
              </div>
              <div className="flex-grow">
                Select All
              </div>
            </div>
          </li>
        )}

        {options?.filter((item) => item?.name?.toLowerCase().includes(filter?.toLowerCase()))?.map((item) => (
          <li key={item.name} role="button" aria-hidden="true" onClick={() => handleSelect(item)}>
            <div className="flex items-start">
              <div className="flex-none check-icon">
                {checkBoxCheck(item[optionKey])}
              </div>
              <div className="flex-grow">
                {item?.name}
              </div>
            </div>

          </li>
        ))}

      </ul>
      )}
    </div>
  );
};

export default MultiSelect;
