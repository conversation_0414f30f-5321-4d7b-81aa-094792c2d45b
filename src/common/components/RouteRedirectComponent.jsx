import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { getProjectPaths } from 'utils/common';
import { actions as commonSliceActions } from 'pages/common/slice';

const RouteRedirectComponent = () => {
  const dispatch = useDispatch();
  const { WEB_PORTAL } = getProjectPaths();
  useEffect(() => {
    dispatch(commonSliceActions.navigateTo({ to: WEB_PORTAL, isSameModule: false }));
  }, []);

  return null;
};

export default RouteRedirectComponent;
