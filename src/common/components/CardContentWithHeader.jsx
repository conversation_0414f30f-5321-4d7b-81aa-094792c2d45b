import { IconButton } from '@ksmartikm/ui-components';
import Expand from 'assets/Expand';
import UnExpand from 'assets/UnExpand';
import React from 'react';

const CardContentWithHeader = ({
  title = '', content, isCollapsed = false, collapseMenuFlag = false, handleToggle = () => ({})
}) => {
  return (
    <div
      className={`bg-[#E7EFF5] rounded-lg overflow-hidden ${collapseMenuFlag ? 'cursor-pointer' : ''}`}
    >
      <div
        className="bg-[#E7EFF5] px-4 py-2"
        aria-hidden
        onClick={collapseMenuFlag ? handleToggle : null}
      >
        <div className="flex">
          <div className="flex-grow">
            <h2 className="text-[#153171] font-medium text-[16px] pt-[9px]">{title}</h2>
          </div>
          {
            collapseMenuFlag && (
              <div className="flex-none">
                <IconButton
                  variant="unstyled"
                  leftIcon={isCollapsed ? <Expand width="21px" height="21px" /> : <UnExpand width="21px" height="21px" />}
                />
              </div>
            )
          }
        </div>
      </div>
      {content && (
        <div className="p-4 border border-[#E7EFF5] bg-white">
          {content}
        </div>
      )}
    </div>
  );
};

export default CardContentWithHeader;
