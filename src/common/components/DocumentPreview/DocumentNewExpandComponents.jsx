import {
  Modal, ModalBody, ModalContent, ModalOverlay
} from '@ksmartikm/ui-components';
import DocumentPreview from '.';

const DocumentNewExpandComponents = ({
  noteCardDetails, full = false, openNewExpand = false, setOpenNewExpand, setFull
}) => {
  return (
    <Modal isOpen={openNewExpand} size={full ? 'full' : '3xl'} closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalBody>
          <DocumentPreview
            preview={noteCardDetails}
            expandEnable
            full={full}
            openNewExpand={openNewExpand}
            setOpenNewExpand={setOpenNewExpand}
            setFull={setFull}
          />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default DocumentNewExpandComponents;
