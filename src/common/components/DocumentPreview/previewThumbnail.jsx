import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  IconButton, Modal, ModalOverlay, ModalContent, ModalBody, Tooltip, t,
  ModalHeader
} from 'common/components';
import CloseIcon from 'assets/Close';
import { useState, useEffect } from 'react';
import ImageIcon from 'assets/Image';
import { PdfViewer, Spinner } from '@ksmartikm/ui-components';
import View from 'assets/View';
import { actions as commonSliceActions } from 'pages/common/slice';
import MiniScreenIcon from 'assets/MiniScreen';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import Rotate from 'assets/Rotate';
import CloseOutlineIcon from 'assets/CloseOutline';
import FullScreenIcon from 'assets/FullScreen';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { DOCUMENT_TYPES } from 'common/constants';
import { secondary } from 'utils/color';
import Edit from 'assets/Edit';
import { handleContentType } from 'utils/common';
import { DocumentSvg, ExcelSvg, PdfSvg } from './Icons';

const PreviewThumbnail = (props) => {
  const {
    handlePreviewRemove, fileType, item, handlePreview, preview = {}, loading, setLoading, from, documentName, setAlertAction, haveEdit = false, handleEdit = () => {}
  } = props;
  const [open, setOpen] = useState(false);
  const [previewData, setPreviewData] = useState('');
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);

  const [full, setFull] = useState(false);

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };

  useEffect(() => {
    if (preview) {
      if (fileType) {
        const arr = new Uint8Array(preview);
        const blob = new Blob([arr], {
          type: fileType
        });
        const url = window.URL.createObjectURL(blob);
        setLoading(false);
        setPreviewData(url);
      }
    }
  }, [preview]);

  const handleOpen = () => {
    handlePreview(item);
    setLoading(true);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    setLoading(false);
  };

  const handleDeleteClick = () => {
    handlePreviewRemove(item);
  };

  const handleEditClick = () => {
    handleEdit(item);
  };

  const handleConfirmDelete = () => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: `${t('areYouSureWanttoDelete')}?`,
      title: t('deleteConfirmation'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      forwardAction: () => handleDeleteClick()
    });
  };

  const handleFull = () => {
    setFull(!full);
  };

  const previewBox = () => {
    switch (fileType) {
      case DOCUMENT_TYPES.PDF:
        return <PdfSvg />;
      case DOCUMENT_TYPES.EXCEL:
        return <ExcelSvg />;
      case DOCUMENT_TYPES.WORD:
        return <DocumentSvg />;
      default:
        return <ImageIcon />;
    }
  };

  const printAck = () => {
    printBlob(previewData);
  };

  const downloadAck = () => {
    downloadBlob({ blob: previewData, fileName: `KSMART-FILE-DOCUMENT${handleContentType(fileType)}` });
  };

  return (
    <>
      {
        from === 'table'
          ? (
            <IconButton onClick={handleOpen} icon={<View />} variant="ghost" />

          )
          : (
            <Tooltip label={documentName}>
              <div className="flex rounded-lg bg-white border items-center px-3 py-1 gap-1">
                <IconButton variant="unstyled" onClick={handleOpen} icon={previewBox()} />
                <div className="flex gap-2 pl-2" style={{ borderLeft: '1px solid #eee' }}>
                  {haveEdit
                  && <IconButton size="xs" variant="unstyled" onClick={() => handleEditClick()} icon={<Edit color={secondary} />} />}
                  <IconButton size="xs" variant="unstyled" onClick={() => handleConfirmDelete()} icon={<CloseIcon color={secondary} />} />
                </div>
              </div>
            </Tooltip>

          )
      }

      <Modal isOpen={open} size={full ? 'full' : '4xl'} onClose={handleClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center justify-end w-full pt-2">
              <Tooltip label={t('fullScreen')}>
                <IconButton variant="unstyled" onClick={() => handleFull()} leftIcon={full ? <MiniScreenIcon width="21px" height="21px" /> : <FullScreenIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('print')}>
                <IconButton variant="unstyled" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('download')}>
                <IconButton variant="unstyled" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('rotate')}>
                <IconButton variant="unstyled" onClick={rotatePreview} leftIcon={<Rotate style={{ transform: `rotate(${rotatePreviewFlag}deg)` }} width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('close')}>
                <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>

            {loading ? <div className="px-3 py-5 h-[500px] w-[100%]"> <Spinner style={{ marginTop: '230px', marginLeft: 'calc(50% - 30px)' }} /> </div> : (
              <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto">
                <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                  {fileType.includes('image') ? <img width="100%" src={previewData} alt="Preview" /> : <PdfViewer title="inward" width="100%" data={previewData} aria-label="loading" />}
                </div>
              </div>
            )}

          </ModalBody>

        </ModalContent>
      </Modal>
    </>
  );
};

const mapStateToProps = createStructuredSelector({

});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PreviewThumbnail);
