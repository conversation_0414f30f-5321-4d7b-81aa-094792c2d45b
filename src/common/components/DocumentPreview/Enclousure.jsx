import React, { useState, useEffect } from 'react';
import {
  Modal, ModalOverlay, ModalContent, ModalBody, PdfViewer, t
} from 'common/components';
import { useGenerateDocs } from 'hooks/useGenerateDocs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner } from '@ksmartikm/ui-components';
import { DOCUMENT_ENCLOUSURE_URL } from 'pages/file/details/constants';
import { DOCUMENT_TYPES } from 'common/constants';
import MinimizeNew from 'assets/MinimizeNew';
import CloseNew from 'assets/CloseNew';
import FullScreenArrow from 'assets/FullScreenArrow';

const IconButton = ({
  icon, label = '', onClick, className = ''
}) => {
  return (
    <button
      onClick={onClick}
      className={`w-8 h-7 flex items-center justify-center gap-2 rounded-[4px] bg-[#E8EFF4] border border-[#DFE4EA] hover:bg-gray-200 shadow-sm transition-all ${className}`}
    >
      {icon}
      {label && <span className="font-semibold text-[14px]">{t(label)}</span>}
    </button>
  );
};

const EnclousurePreview = (props) => {
  const {
    open, setOpenEnPreview, enclosurePreviewItem, draftId
  } = props;

  const [flag, setFlag] = useState(false);
  const [baseCode, setBaseCode] = useState(null);
  const [url, setUrl] = useState(null);
  const [contentType, setContentType] = useState(null);
  const [content, setContent] = useState(null);
  const [full, setFull] = useState(false);

  const { loading, previewData } = useGenerateDocs({
    url, flag, contentType, content
  });

  useEffect(() => {
    if (previewData && url) {
      setBaseCode(previewData);
    }
  }, [previewData]);

  useEffect(() => {
    if (enclosurePreviewItem?.enclosureFiles?.enclosureFileId) {
      const data = {
        draftId,
        enclosureDocumentId: enclosurePreviewItem?.enclosureFiles?.enclosureFileId,
        enclosureId: enclosurePreviewItem?.id
      };
      setUrl(DOCUMENT_ENCLOUSURE_URL);
      setContentType(enclosurePreviewItem?.enclosureFiles?.contentType ? enclosurePreviewItem?.enclosureFiles?.contentType : DOCUMENT_TYPES.PDF);
      setContent(data);
      setFlag(!flag);
    }
  }, [JSON.stringify(enclosurePreviewItem)]);

  const close = () => {
    setOpenEnPreview(false);
  };

  const handleFull = () => {
    setFull(!full);
  };

  const previewFunc = (cType) => {
    let contentReturn = loading ? (
      <div className="px-3 py-5 h-[500px] w-[100%]"> <Spinner style={{ marginTop: '230px', marginLeft: 'calc(50% - 30px)' }} /> </div>
    ) : (
      <img
        title="File Documents"
        style={{ background: 'none', borderRadius: '20px' }}
        width="100%"
        height={cType === DOCUMENT_TYPES.PDF ? '600px' : '100%'}
        src={baseCode}
        aria-label="loading"
      />
    );

    if (cType === DOCUMENT_TYPES.PDF) {
      contentReturn = (
        <div className="text-center max-w-[620px] min-h-[500px] m-auto">
          <PdfViewer
            width="500"
            data={baseCode}
            variant="normal"
            enablePagination
          />
        </div>
      );
    }

    return contentReturn;
  };

  return (
    <div>
      <Modal isOpen={open} size={full ? 'full' : '4xl'} onClose={close} className="custom-form-modal">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader
            py={2}
            px={5}
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            gap={4}
          >
            <div className="flex items-center gap-3">
              <h6 className="text-[#232F50] font-bold text-[16px]">
                {enclosurePreviewItem?.enclosureName}
              </h6>
              <p className="text-[#5C6E93] font-normal text-sm">
                {t('preview')}
              </p>
            </div>
            <div className="flex gap-2 items-center">
              <IconButton
                icon={full ? <MinimizeNew /> : <FullScreenArrow />}
                onClick={handleFull}
              />
              <IconButton
                className="w-[47px]"
                icon={<CloseNew fill="none" w="40" h="40" />}
                onClick={close}
              />
            </div>
          </ModalHeader>
          <ModalBody>
            {previewFunc(contentType)}
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default EnclousurePreview;
