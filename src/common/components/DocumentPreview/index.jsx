import React, { useState, useEffect, useCallback } from 'react';
import {
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  t,
  Toolt<PERSON>,
  ModalHeader
} from 'common/components';
import Forward from 'assets/Forward';
import Backward from 'assets/Backward';
import { dark } from 'utils/color';
import { Spinner } from '@ksmartikm/ui-components';
import { baseApiURL } from 'utils/http';
import NoNotesIcon from 'assets/NoNotesIcon';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import Rotate from 'assets/Rotate';
import FullScreenIcon from 'assets/FullScreen';
import CloseOutlineIcon from 'assets/CloseOutline';
import MiniScreenIcon from 'assets/MiniScreen';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getDocumentId,
  getDocumentNameFromNoteReferences,
  getNoteCardDetails
} from 'pages/common/selectors';
import { getNotes } from 'pages/file/details/selector';
import { generateDocs } from 'hooks/generateDocs';
import { handleContentType } from 'utils/common';
import ZoomIn from 'assets/ZoomIn';
import ZoomOut from 'assets/ZoomOut';
import BackArrow from 'assets/BackIcon';
import ShowAllDocuments from 'pages/file/details/components/notes/ShowAllDocuments';
import { useParams } from 'react-router-dom';
import { actions as sliceActions } from 'pages/file/details/slice';
import { ZoomComponent } from '../Zoom/Zoom';
import Pagination from '../Pagination/Pagination';
import ExcelViewer from '../ExcelView/ExcelViewer';
import DocxViewer from '../DocxView/DocsViewer';
import { DOCUMENT_TYPES } from '../../constants';

const styles = {
  roundPagination: {
    display: 'flex',
    justifyContent: 'center',
    borderRadius: 25,
    alignItems: 'center',
    border: '1px solid #A4ABAE',
    position: 'absolute',
    top: 'calc(50% - 20px)',
    left: '-20px',
    zIndex: 1
  },
  closeButton: {
    position: 'absolute',
    right: '20px',
    top: '10px',
    borderRadius: '50px',
    background: '#fff',
    padding: 0,
    minWidth: '30px',
    height: '32px'
  }
};

const DocumentPreview = (props) => {
  const {
    noteCardDetails = [],
    documentId = {},
    from = 'summary',
    // notes
    isOneDocumentSelect,
    setIsOneDocumentSelect = () => { },
    // setNoteCardDetails = () => { },
    expandEnable = false,
    setOpenNewExpand = () => { },
    openNewExpand,
    setFull = () => { },
    full,
    setShowingAllDocs = () => { },
    setIsOnSelectNote
  } = props;

  const [page, setPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [baseCode, setBaseCode] = useState(null);
  const [contentType, setContentType] = useState('');
  const [rotateFlag, setRotateFlag] = useState(0);
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);
  const [loading, setLoading] = useState(false);
  const [zoom, setZoom] = useState(1);
  const params = useParams();

  function identifyZipBasedType(arrayBuffer) {
    // Look deeper into ZIP-based formats
    const textDecoder = new TextDecoder();
    const content = textDecoder.decode(arrayBuffer);

    if (content.includes('[Content_Types].xml')) {
      if (content.includes('word/')) return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; // DOCX
      if (content.includes('sheet/')) return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // XLSX
      if (content.includes('presentation/')) return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'; // PPTX
    }
    return 'application/octet-stream'; // Generic ZIP file
  }

  function getMimeType(arrayBuffer) {
    const bytes = new Uint8Array(arrayBuffer).subarray(0, 4);
    const header = bytes.reduce(
      (acc, byte) => acc + byte.toString(16).padStart(2, '0'),
      ''
    );

    switch (header) {
      case '25504446':
        return 'application/pdf'; // PDF magic number
      case 'ffd8ffe0':
      case 'ffd8ffe1':
      case 'ffd8ffe2':
      case 'ffd8ffe3':
      case 'ffd8ffe8':
        return 'image/jpeg'; // JPEG magic numbers
      case '89504e47':
        return 'image/png'; // PNG magic number
      case '47494638':
        return 'image/gif'; // GIF magic number
      case '504b0304':
        // ZIP-based formats (e.g., DOCX, XLSX, PPTX)
        return identifyZipBasedType(arrayBuffer);
      default:
        return 'application/octet-stream'; // Unknown type
    }
  }

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.2, 5));
  };

  // Handle zoom out
  const handleZoomOut = (event) => {
    event.preventDefault(); // Prevent the context menu from showing on right-click
    setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1));
  };
  const handlePreview = async (docUrl, content) => {
    setLoading(true);
    const generate = generateDocs({ url: docUrl, contentType, content });
    const { data, status } = await generate.then((result) => result);
    if (status === 'success') {
      setLoading(false);
      setShowPreview(true);
      setRotatePreviewFlag(0);
      setBaseCode(data);
    } else {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (noteCardDetails?.length > 0 && Object.keys(documentId).length > 0) {
      setTotalItems(noteCardDetails?.length);
      const filteredArray = noteCardDetails?.filter(
        (item) => (item?.content?.notesDocumentId || item?.content?.fileId) === documentId?.docId
      );
      const data = documentId?.docId ? filteredArray[0] : noteCardDetails[0];
      if (documentId?.docId) {
        const findIndex = noteCardDetails?.findIndex(
          (item) => (item?.content?.notesDocumentId || item?.content?.fileId) === documentId?.docId
        );
        if (findIndex > -1) {
          setPage(findIndex + 1);
        }
      }
      const docUrl = `${baseApiURL}${data?.link}?fileNo=${data?.content?.fileNo}&notesId=${data?.content?.notesId}&notesDocumentId=${data?.content?.notesDocumentId}&notesDocInfoId=${data?.content?.notesDocInfoId}`;
      const docInwardUrl = `${baseApiURL}${data?.link}`;
      handlePreview(
        data?.content?.inwardId ? docInwardUrl : docUrl,
        data?.content?.inwardId ? data?.content : null
      );
      setContentType(data?.contentType);
    } else {
      setTotalItems(0);
    }
  }, [JSON.stringify(noteCardDetails), documentId]);

  const handlePageIndex = (item) => {
    if (item === 'next') {
      return page + 1;
    }
    if (item === 'previous') {
      return page - 1;
    }
    return item;
  };

  const handlePage = (item) => {
    const data = noteCardDetails[handlePageIndex(item) - 1];
    const docUrl = `${baseApiURL}${data?.link}?fileNo=${data?.content?.fileNo}&notesId=${data?.content?.notesId}&notesDocumentId=${data?.content?.notesDocumentId}&notesDocInfoId=${data?.content?.notesDocInfoId}`;
    const docInwardUrl = `${baseApiURL}${data?.link}`;
    handlePreview(
      data?.content?.inwardId ? docInwardUrl : docUrl,
      data?.content?.inwardId ? data?.content : null
    );
    setContentType(data?.contentType);
    setPage(handlePageIndex(item));
  };

  const handleClose = () => {
    setShowPreview(false);
  };

  const handleFull = () => {
    if (!openNewExpand) {
      setOpenNewExpand(true);
    } else {
      setOpenNewExpand(false);
    }

    if (!full) {
      setFull(true);
    } else {
      setFull(false);
    }
  };

  const documentTypesChack = (type) => {
    if (type && [DOCUMENT_TYPES.WORD, DOCUMENT_TYPES.DOC, DOCUMENT_TYPES.DOCX]?.includes(type)) {
      return 'document';
    }
    if (type && [DOCUMENT_TYPES.EXCEL, DOCUMENT_TYPES.XLSX, DOCUMENT_TYPES.CSV, DOCUMENT_TYPES.XLS, DOCUMENT_TYPES.ODS]?.includes(type)) {
      return 'excel';
    }
    if (type && type === DOCUMENT_TYPES.PDF) {
      return 'pdf';
    }
    if (type && [DOCUMENT_TYPES.PNG, DOCUMENT_TYPES.JPG, DOCUMENT_TYPES.JPEG, DOCUMENT_TYPES.GIF]?.includes(type)) {
      return 'image';
    }
    return 'image';
  };

  const previewFunc = useCallback(
    (cType) => {
      const type = contentType || cType;
      if (loading) {
        return <Spinner style={{ marginTop: '230px' }} />;
      }
      if (documentTypesChack(type) === 'excel') {
        return <ExcelViewer blobUrl={baseCode} />;
      }
      if (documentTypesChack(type) === 'document') {
        return <DocxViewer blobUrl={baseCode} />;
      }
      return (
        <ZoomComponent
          image={baseCode}
          type={documentTypesChack(type)}
          zoom={zoom}
        />
      );
    },
    [loading, contentType]
  );

  const downloadAck = () => {
    downloadBlob({
      blob: baseCode,
      fileName: `KSMART-FILE-DOCUMENT${handleContentType(contentType)}`
    });
  };

  const rotateAck = () => {
    if (rotateFlag === 270) {
      setRotateFlag(0);
    } else {
      setRotateFlag(rotateFlag + 90);
    }
  };

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };
  const printBlobUrl = async (blob) => {
    // console.log('>>>2>', blob);
    const response = await fetch(blob);
    // console.log('>>>3>', response);
    const arrayBuffer = await response.arrayBuffer();
    // console.log('>>>4>', arrayBuffer);
    const mimeType = getMimeType(arrayBuffer);
    // console.log('>>>5>', mimeType);
    const pdfBlob = new Blob([arrayBuffer], {
      type: mimeType || 'application/pdf'
    });
    // console.log('>>>6>', pdfBlob);
    const pdfUrl = URL.createObjectURL(pdfBlob);
    // console.log('>>>7>', pdfUrl);
    printBlob(pdfUrl);
  };

  const printAck = () => {
    // console.log('>>>1>', baseCode);
    printBlobUrl(baseCode);
  };

  const handleShowDocumentComponents = () => {
    setIsOneDocumentSelect({});
    // setNoteCardDetails([]);
    setShowingAllDocs(false);
    setIsOnSelectNote('');
    return <ShowAllDocuments fileNo={params?.fileNo} />;
  };

  const noteCardDocumnet = (item) => {
    if (
      documentTypesChack(documentId ? contentType : item.contentType) === 'excel'
    ) {
      return <ExcelViewer blobUrl={baseCode} />;
    }
    if (
      documentTypesChack(documentId ? contentType : item.contentType) === 'document'
    ) {
      return <DocxViewer blobUrl={baseCode} />;
    }
    return (
      <ZoomComponent
        image={baseCode}
        type={documentTypesChack(documentId ? contentType : item.contentType)}
        zoom={zoom}
      />
    );
  };

  return (
    <>
      <div className="flex min-h-[750px]">
        <div className="flex-none relative">
          <IconButton
            variant="unstyled"
            aria-label="Backward"
            icon={(
              <Backward
                name="backward"
                color={page === 0 ? '#A4ABAE' : '#fff'}
              />
            )}
            style={styles.roundPagination}
            background={page === 0 ? 'gray.200' : dark}
            onClick={() => handlePage('previous')}
            isDisabled={page === 1}
          />
        </div>
        {isOneDocumentSelect?.docId && (
          <IconButton
            onClick={() => handleShowDocumentComponents()}
            variant="unstyled"
            icon={<BackArrow color={dark} width="8" height="8" />}
          />
        )}
        <div className="flex-grow" style={{ overflow: 'scroll', scrollbarWidth: 'none' }}>
          <div className="p-0">
            <div className="flex items-center justify-end w-full mb-3">
              {expandEnable && noteCardDetails?.length > 0 && (
                <Tooltip label={t('fullScreen')}>
                  <IconButton
                    variant="unstyled"
                    onClick={handleFull}
                    leftIcon={
                      full ? (
                        <MiniScreenIcon width="21px" height="21px" />
                      ) : (
                        <FullScreenIcon width="21px" height="21px" />
                      )
                    }
                  />
                </Tooltip>
              )}
              {noteCardDetails?.length > 0 && (
                <Tooltip label={t('zoomIn')}>
                  <IconButton
                    variant="unstyled"
                    onClick={handleZoomIn}
                    leftIcon={
                      <ZoomIn width="21px" height="21px" color="#718096" />
                    }
                  />
                </Tooltip>
              )}
              {noteCardDetails?.length > 0 && (
                <Tooltip label={t('zoomOut')}>
                  <IconButton
                    variant="unstyled"
                    onClick={handleZoomOut}
                    leftIcon={
                      <ZoomOut width="21px" height="21px" color="#718096" />
                    }
                  />
                </Tooltip>
              )}
              {noteCardDetails?.length > 0 && (
                <Tooltip label={t('print')}>
                  <IconButton
                    variant="unstyled"
                    onClick={printAck}
                    leftIcon={<PrintIcon width="21px" height="21px" />}
                  />
                </Tooltip>
              )}
              {noteCardDetails?.length > 0 && (
                <Tooltip label={t('download')}>
                  <IconButton
                    variant="unstyled"
                    onClick={downloadAck}
                    leftIcon={<DownloadIcon width="21px" height="21px" />}
                  />
                </Tooltip>
              )}
              {noteCardDetails?.length > 0 && (
                <Tooltip label={t('rotate')}>
                  <IconButton
                    variant="unstyled"
                    onClick={rotateAck}
                    leftIcon={(
                      <Rotate
                        style={{ transform: `rotate(${rotateFlag}deg)` }}
                        width="21px"
                        height="21px"
                      />
                    )}
                  />
                </Tooltip>
              )}
            </div>
          </div>
          {noteCardDetails.map(
            (item, index) => index + 1 === page && (
            <div
              style={{ transform: `rotate(${rotateFlag}deg)` }}
              key={item?.link}
            >
              <div
                className="flex justify-center cursor-pointer mx-10 max-h-[750px] min-h-[750px] overflow-y-auto overflow-x-scroll"
                aria-hidden="true"
              >
                {loading ? (
                  <Spinner style={{ marginTop: '230px' }} />
                ) : (
                  <div className="w-full">
                    <h4 className="mb-3">{item?.documentName}</h4>
                    {noteCardDocumnet(item)}
                  </div>
                )}
              </div>
            </div>
            )
          )}

          {noteCardDetails?.length > 0 && (
            <Pagination
              totalPages={noteCardDetails?.length}
              currentPage={page}
              onPageChange={handlePage}
            />
          )}
        </div>
        <div className="flex-none relative">
          <IconButton
            variant="unstyled"
            aria-label="Forward"
            icon={(
              <Forward
                name="forward"
                color={
                  totalItems / 3 === page || totalItems === 0
                    ? '#A4ABAE'
                    : '#fff'
                }
              />
            )}
            style={styles.roundPagination}
            onClick={() => handlePage('next')}
            background={
              totalItems === page || totalItems === 0 ? 'gray.200' : dark
            }
            isDisabled={totalItems === page || totalItems === 0}
          />
        </div>
      </div>

      {noteCardDetails?.length === 0 && (
        <div className="text-center min-h-[700px]">
          <div className="w-[300px] mx-auto m-10 py-10 -mt-[600px]">
            <NoNotesIcon width="300px" />
          </div>
          {t('noDocumentFound')}
        </div>
      )}

      <Modal
        isOpen={showPreview && !from === 'summary'}
        size={full ? 'full' : '4xl'}
        onClose={handleClose}
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center justify-end w-full pt-2">
              {!from === 'summary' && (
                <Tooltip label={t('fullScreen')}>
                  <IconButton
                    variant="unstyled"
                    onClick={() => handleFull()}
                    leftIcon={
                      full ? (
                        <MiniScreenIcon width="21px" height="21px" />
                      ) : (
                        <FullScreenIcon width="21px" height="21px" />
                      )
                    }
                  />
                </Tooltip>
              )}
              <Tooltip label={t('zoomIn')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleZoomIn}
                  leftIcon={
                    <ZoomIn width="21px" height="21px" color="#718096" />
                  }
                />
              </Tooltip>
              <Tooltip label={t('zoomOut')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleZoomOut}
                  leftIcon={
                    <ZoomOut width="21px" height="21px" color="#718096" />
                  }
                />
              </Tooltip>
              <Tooltip label={t('print')}>
                <IconButton
                  variant="unstyled"
                  onClick={printAck}
                  leftIcon={<PrintIcon width="21px" height="21px" />}
                />
              </Tooltip>
              <Tooltip label={t('download')}>
                <IconButton
                  variant="unstyled"
                  onClick={downloadAck}
                  leftIcon={<DownloadIcon width="21px" height="21px" />}
                />
              </Tooltip>
              <Tooltip label={t('rotate')}>
                <IconButton
                  variant="unstyled"
                  onClick={rotatePreview}
                  leftIcon={(
                    <Rotate
                      style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}
                      width="21px"
                      height="21px"
                    />
                  )}
                />
              </Tooltip>
              <Tooltip label={t('close')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleClose}
                  leftIcon={<CloseOutlineIcon width="21px" height="21px" />}
                />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>
            <div
              style={{
                height: full ? 'calc(100vh - 120px)' : 'calc(100vh - 250px)'
              }}
              className="overflow-y-auto"
            >
              <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                {previewFunc(contentType)}
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  documentId: getDocumentId,
  documentName: getDocumentNameFromNoteReferences,
  notes: getNotes,
  noteCardDetails: getNoteCardDetails
});

const mapDispatchToProps = (dispatch) => ({
  setShowingAllDocs: (data) => dispatch(sliceActions.setShowingAllDocs(data)),
  setIsOnSelectNote: (data) => dispatch(sliceActions.setIsOnSelectNote(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DocumentPreview);
