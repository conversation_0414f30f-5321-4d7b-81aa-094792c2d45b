import React, { useEffect, useState } from 'react';
import {
  Modal, ModalOverlay, ModalContent, ModalBody, PdfViewer, <PERSON>dal<PERSON>oot<PERSON>, t, ModalHeader
} from 'common/components';
import { fileBlob } from 'utils/fileBlob';
import { DOCUMENT_TYPES } from 'common/constants';
import MinimizeNew from 'assets/MinimizeNew';
import FullScreenArrow from 'assets/FullScreenArrow';
import CloseNew from 'assets/CloseNew';
import Delete from 'assets/delete';

const IconButton = ({
  icon, label = '', onClick, className = ''
}) => {
  return (
    <button
      onClick={onClick}
      className={`w-8 h-7 flex items-center justify-center gap-2 rounded-[4px] bg-[#E8EFF4] border border-[#DFE4EA] hover:bg-gray-200 shadow-sm transition-all ${className}`}
    >
      {icon}
      {label && <span className="font-semibold text-[14px]">{t(label)}</span>}
    </button>
  );
};

const PreviewLocal = (props) => {
  const {
    open, close, previewItem, onDelete = () => {}
  } = props;

  const [full, setFull] = useState(false);
  const [type, setType] = useState(previewItem?.file?.type);

  const handleFull = () => {
    setFull(!full);
  };

  useEffect(() => {
    if (previewItem?.file?.type) {
      setType(previewItem?.file?.type);
    }
  }, [previewItem?.file?.type]);

  const documentTypesChack = () => {
    if (type && type === DOCUMENT_TYPES.PDF) {
      return 'pdf';
    }
    return 'image';
  };

  const previewFunc = () => {
    switch (documentTypesChack()) {
      case 'pdf':
        return (
          <PdfViewer
            width="500"
            data={previewItem?.file && fileBlob(previewItem?.file)}
            variant="normal"
            enablePagination
          />
        );
      default:
        return (
          <img
            title="File Documents"
            style={{ background: 'none', borderRadius: '20px' }}
            width="100%"
            src={previewItem?.file && fileBlob(previewItem?.file)}
            aria-label="loading"
          />
        );
    }
  };

  return (
    <Modal isOpen={open} size={full ? 'full' : '3xl'} onClose={close} className="custom-form-modal !z-50" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader
          py={2}
          px={5}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          gap={4}
        >
          <div className="flex items-center gap-3">
            <h6 className="text-[#232F50] font-bold text-[16px]">
              {previewItem?.enclosureName}
            </h6>
            <p className="text-[#5C6E93] font-normal text-sm">
              {t('preview')}
            </p>
          </div>
          <div className="flex gap-2 items-center">
            <IconButton
              icon={full ? <MinimizeNew /> : <FullScreenArrow />}
              onClick={handleFull}
            />
            <IconButton
              className="w-[47px]"
              icon={<CloseNew fill="none" w="40" h="40" />}
              onClick={close}
            />
          </div>
        </ModalHeader>
        <ModalBody px={4}>
          <div
            style={{
              height: full ? 'calc(100vh - 140px)' : 'calc(100vh - 270px)',
              overflow: 'scroll',
              scrollBehavior: 'smooth'
            }}
            className="overflow-y-auto mt-3"
          >
            {previewFunc()}
          </div>
        </ModalBody>

        <ModalFooter px={5}>
          <IconButton
            label="delete"
            className="bg-[#fff] w-auto py-4 px-3 rounded-lg justify-start"
            icon={<Delete stroke="#456C86" w="20" h="20" strokeWidth="2" />}
            onClick={() => {
              onDelete();
              close();
            }}
          />
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PreviewLocal;
