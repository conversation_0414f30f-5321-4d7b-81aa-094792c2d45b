function PdfSvg({ w = '21', h = '21' }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" color="#F5474C" width={w} height={h} fill="currentColor" className="bi bi-file-earmark-pdf-fill" viewBox="0 0 16 16">
      <path d="M5.523 12.424c.14-.082.293-.162.459-.238a7.878 7.878 0 0 1-.45.606c-.28.337-.498.516-.635.572a.266.266 0 0 1-.035.012.282.282 0 0 1-.026-.044c-.056-.11-.054-.216.04-.36.106-.165.319-.354.647-.548m2.455-1.647c-.119.025-.237.05-.356.078a21.148 21.148 0 0 0 .5-1.05 12.045 12.045 0 0 0 .51.858c-.217.032-.436.07-.654.114m2.525.939a3.881 3.881 0 0 1-.435-.41c.228.005.434.022.612.054.317.057.466.147.518.209a.095.095 0 0 1 .026.064.436.436 0 0 1-.06.2.307.307 0 0 1-.094.124.107.107 0 0 1-.069.015c-.09-.003-.258-.066-.498-.256M8.278 6.97c-.04.244-.108.524-.2.829a4.86 4.86 0 0 1-.089-.346c-.076-.353-.087-.63-.046-.822.038-.177.11-.248.196-.283a.517.517 0 0 1 .145-.04c.013.03.028.092.032.198.005.122-.007.277-.038.465z" />
      <path fillRule="evenodd" d="M4 0h5.293A1 1 0 0 1 10 .293L13.707 4a1 1 0 0 1 .293.707V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2m5.5 1.5v2a1 1 0 0 0 1 1h2zM4.165 13.668c.09.18.23.343.438.419.207.075.412.04.58-.03.318-.13.635-.436.926-.786.333-.401.683-.927 1.021-1.51a11.651 11.651 0 0 1 1.997-.406c.3.383.61.713.91.95.28.22.603.403.934.417a.856.856 0 0 0 .51-.138c.155-.101.27-.247.354-.416.09-.181.145-.37.138-.563a.844.844 0 0 0-.2-.518c-.226-.27-.596-.4-.96-.465a5.76 5.76 0 0 0-1.335-.05 10.954 10.954 0 0 1-.98-1.686c.25-.66.437-1.284.52-1.794.036-.218.055-.426.048-.614a1.238 1.238 0 0 0-.127-.538.7.7 0 0 0-.477-.365c-.202-.043-.41 0-.601.077-.377.15-.576.47-.651.823-.073.34-.04.736.046 1.136.088.406.238.848.43 1.295a19.697 19.697 0 0 1-1.062 2.227 7.662 7.662 0 0 0-1.482.645c-.37.22-.699.48-.897.787-.21.326-.275.714-.08 1.103z" />
    </svg>
  );
}

function ExcelSvg({ color = '#7BBC6A', w = '21', h = '21' }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" color={color} width={w} height={h} fill="currentColor" className="bi bi-file-earmark-spreadsheet-fill" viewBox="0 0 16 16">
      <path d="M6 12v-2h3v2z" />
      <path d="M9.293 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.707A1 1 0 0 0 13.707 4L10 .293A1 1 0 0 0 9.293 0M9.5 3.5v-2l3 3h-2a1 1 0 0 1-1-1M3 9h10v1h-3v2h3v1h-3v2H9v-2H6v2H5v-2H3v-1h2v-2H3z" />
    </svg>
  );
}

function DocumentSvg({ w = '16', h = '16' }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} fill="currentColor" className="bi bi-file-earmark-word-fill" viewBox="0 0 16 16">
      <path d="M9.293 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.707A1 1 0 0 0 13.707 4L10 .293A1 1 0 0 0 9.293 0M9.5 3.5v-2l3 3h-2a1 1 0 0 1-1-1M5.485 6.879l1.036 4.144.997-3.655a.5.5 0 0 1 .964 0l.997 3.655 1.036-4.144a.5.5 0 0 1 .97.242l-1.5 6a.5.5 0 0 1-.967.01L8 9.402l-1.018 3.73a.5.5 0 0 1-.967-.01l-1.5-6a.5.5 0 1 1 .97-.242z" />
    </svg>
  );
}

const VoiceIcon = ({ color }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
      <path
        d="M12 19.75C8.28 19.75 5.25 16.72 5.25 13V8C5.25 4.28 8.28 1.25 12 1.25C15.72 1.25 18.75 4.28 18.75 8V13C18.75 16.72 15.72 19.75 12 19.75ZM12 2.75C9.11 2.75 6.75 5.1 6.75 8V13C6.75 15.9 9.11 18.25 12 18.25C14.89 18.25 17.25 15.9 17.25 13V8C17.25 5.1 14.89 2.75 12 2.75Z"
        fill={color}
      />
      <path
        d="M12 22.75C6.62 22.75 2.25 18.38 2.25 13V11C2.25 10.59 2.59 10.25 3 10.25C3.41 10.25 3.75 10.59 3.75 11V13C3.75 17.55 7.45 21.25 12 21.25C16.55 21.25 20.25 17.55 20.25 13V11C20.25 10.59 20.59 10.25 21 10.25C21.41 10.25 21.75 10.59 21.75 11V13C21.75 18.38 17.38 22.75 12 22.75Z"
        fill={color}
      />
      <path
        d="M14.6099 8.22988C14.5299 8.22988 14.4399 8.21988 14.3499 8.18988C12.7399 7.60988 10.9699 7.60988 9.35988 8.18988C8.97988 8.32988 8.54988 8.12988 8.40988 7.73988C8.26988 7.34988 8.46988 6.91988 8.85988 6.77988C10.7999 6.07988 12.9299 6.07988 14.8699 6.77988C15.2599 6.91988 15.4599 7.34988 15.3199 7.73988C15.2099 8.04988 14.9199 8.22988 14.6099 8.22988Z"
        fill={color}
      />
      <path
        d="M13.6999 11.2305C13.6399 11.2305 13.5699 11.2205 13.4999 11.2005C12.4299 10.9105 11.2999 10.9105 10.2299 11.2005C9.81992 11.3105 9.41992 11.0705 9.30992 10.6705C9.19992 10.2705 9.43992 9.86047 9.83992 9.75047C11.1699 9.39047 12.5699 9.39047 13.8999 9.75047C14.2999 9.86047 14.5399 10.2705 14.4299 10.6705C14.3299 11.0205 14.0299 11.2305 13.6999 11.2305Z"
        fill={color}
      />
    </svg>
  );
};

export {
  PdfSvg,
  ExcelSvg,
  DocumentSvg,
  VoiceIcon
};
