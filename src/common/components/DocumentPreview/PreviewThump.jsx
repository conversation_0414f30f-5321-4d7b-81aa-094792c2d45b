import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Button,
  IconButton, Modal, ModalOverlay, ModalContent, ModalBody, t,
  ModalHeader,
  Tooltip
} from 'common/components';
import CloseIcon from 'assets/Close';
import React, { useState } from 'react';
import ImageIcon from 'assets/Image';
import {
  PdfViewer, Spinner
} from '@ksmartikm/ui-components';
import View from 'assets/View';
import { actions as commonSliceActions } from 'pages/common/slice';
import MiniScreenIcon from 'assets/MiniScreen';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import Rotate from 'assets/Rotate';
import CloseOutlineIcon from 'assets/CloseOutline';
import FullScreenIcon from 'assets/FullScreen';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { DOCUMENT_TYPES } from 'common/constants';
import { secondary } from 'utils/color';
import AttachmentIcon from 'assets/Attachment';
import { DocumentSvg, ExcelSvg, PdfSvg } from './Icons';

const PreviewThumb = (props) => {
  const {
    handlePreviewRemove, fileType, item, handlePreview, preview = {}, loading, from = 'normal', setAlertAction, index
  } = props;
  const [open, setOpen] = useState(false);
  const [full, setFull] = useState(false);
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);

  const handleOpen = () => {
    handlePreview(item);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const handleDeleteClick = () => {
    handlePreviewRemove(item);
  };

  const handleConfirmDelete = () => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: `${t('areYouSureWanttoDelete')}?`,
      title: t('deleteConfirmation'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      forwardAction: () => handleDeleteClick()
    });
  };

  const previewBox = () => {
    switch (fileType) {
      case DOCUMENT_TYPES.PDF:
        return <PdfSvg />;
      case DOCUMENT_TYPES.EXCEL:
        return <ExcelSvg />;
      case DOCUMENT_TYPES.WORD:
        return <DocumentSvg />;
      default:
        return <ImageIcon />;
    }
  };

  const handleFull = () => {
    setFull(!full);
  };

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };

  const printAck = () => {
    printBlob(preview);
  };

  const downloadAck = () => {
    downloadBlob({ blob: preview, fileName: 'KSMART-FILE-DOCUMENT' });
  };

  return (
    <>
      {
        from === 'table'
        && (
          <IconButton onClick={handleOpen} icon={<View />} variant="ghost" />

        )
      }
      {from === 'normal' && (
        <div className="preview-image mt-3">
          <Button className="preview-icon-button" onClick={handleOpen}>
            {previewBox()}
          </Button>
          <IconButton onClick={() => handleConfirmDelete()} icon={<CloseIcon color={secondary} />} />
        </div>
      )}

      {from === 'note' && (
        <div className="mt-3 px-1">
          <IconButton size="md" onClick={handleOpen} icon={previewBox()} />
        </div>
      )}
      {from === 'note-card' && (
        <div className="mt-3 px-1">
          <Button size="md" onClick={handleOpen} icon={previewBox()} leftIcon={<AttachmentIcon />}> {index} </Button>
        </div>
      )}

      <Modal isOpen={open} size={full ? 'full' : '4xl'} onClose={handleClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center justify-end w-full pt-2">
              <Tooltip label={full ? t('minimize') : t('maximize')}>
                <IconButton variant="unstyled" onClick={() => handleFull()} leftIcon={full ? <MiniScreenIcon width="21px" height="21px" /> : <FullScreenIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('print')}>
                <IconButton variant="unstyled" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('download')}>
                <IconButton variant="unstyled" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('rotate')}>
                <IconButton variant="unstyled" onClick={rotatePreview} leftIcon={<Rotate style={{ transform: `rotate(${rotatePreviewFlag}deg)` }} width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('close')}>
                <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>

            {loading ? <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto mt-3"> <Spinner style={{ marginTop: '230px', marginLeft: 'calc(50% - 30px)' }} /> </div> : (
              <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto mt-3">
                <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                  {fileType.includes('image') ? <img width="100%" src={preview} alt="Preview" /> : <PdfViewer title="inward" width="100%" data={preview} aria-label="loading" />}
                </div>
              </div>
            )}

          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

const mapStateToProps = createStructuredSelector({

});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PreviewThumb);
