import React from 'react';
import './style.css';

const CustomButton = (props) => {
  const {
    label, leftIcon, rightIcon, onClick, variant = 'primary', disabled = false
  } = props;

  return (
    <div className="style-button-cover">
      <button disabled={disabled} onClick={onClick} aria-label="button">
        {leftIcon && <div className={variant === 'primary' ? 'left primary' : 'left secondary'}>{leftIcon}</div>}
        {label}
        {rightIcon && <div className="right">{rightIcon}</div>}
      </button>
    </div>
  );
};

export default CustomButton;
