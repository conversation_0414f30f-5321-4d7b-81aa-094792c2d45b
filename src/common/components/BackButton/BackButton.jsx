import { BASE_PATH, HOME_PATH } from 'common/constants';
import { getFileDetails } from 'pages/file/details/selector';
import React, { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import {
  t, Button
} from 'common/components';
import { connect } from 'react-redux';
import { BPL_CERTICATE_URL } from 'pages/workFlow/constants';
import { getUserInfo } from 'pages/common/selectors';
import * as actions from 'pages/file/details/actions';

const BackButton = ({ fileDetails, fetchFileDetails, userInfo }) => {
  const [urls, setUrls] = useState(null);
  const params = useParams();
  const location = useLocation();

  useEffect(() => {
    if (params.fileNo) {
      fetchFileDetails(params.fileNo);
    }
  }, [params]);

  useEffect(() => {
    if (fileDetails) {
      if (fileDetails?.url) {
        setUrls(fileDetails.url);
      }
    }
  }, [fileDetails]);

  const backToHome = () => {
    window.location.href = HOME_PATH;
  };

  const backToModule = () => {
    if (location.pathname.includes('summary')) {
      window.location.href = HOME_PATH;
    } else if (location.pathname.includes('draft')) {
      window.location.href = `${window?.location?.origin}${BASE_PATH}/file/${params?.fileNo}/notes`;
    } else if (fileDetails?.moduleCode === 'CR') {
      window.location.href = `${window?.location?.origin}/ui/${urls}?submoduleCode=${fileDetails?.submoduleCode}`;
    } else {
      window.location.href = `${window?.location?.origin}/ui/${urls}`;
    }
  };
  const url = params?.fileNo && userInfo.id ? `${BPL_CERTICATE_URL}/bpl/${userInfo.id}/${params?.fileNo}` : null;

  return (
    <Button
      variant="secondary_outline"
      className="mx-2"
      onClick={!(location.pathname.includes('summary')) && url ? backToModule : backToHome}
    > {t('back')}
    </Button>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(BackButton);
