.ProseMirror {
  > * + * {
    margin-top: 0.75em;
  }
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
}

.ProseMirror td,
.ProseMirror th {
  border: 1px solid #e5e7eb;
  box-sizing: border-box;
  min-width: 1em;
  padding: 8px;
  position: relative;
  vertical-align: top;
}

.ProseMirror th {
  background-color: #f9fafb;
  font-weight: 600;
}

.ProseMirror .selectedCell:after {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.ProseMirror .column-resize-handle {
  background-color: #adf;
  bottom: -2px;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
}

.ProseMirror p {
  margin: 0;
}

/* Add to your global CSS or editor styles */
.ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.ProseMirror h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.ProseMirror h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.ProseMirror h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.ProseMirror h6 {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.salutation-line {
  font-weight: bold;
  color: #333;
  margin-bottom: 10em;
  user-select: none;
}

.ProseMirror .salutation-line::after {
  content: '';
  display: block;
  height: 1px;
  width: 100%;
  background-color: #eee;
  margin-top: 0.5em;
}

.salutation-select {
  margin: 10px 25px;
  outline: none;
  background: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  color: #232F50;
  font-size: 15px;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
}