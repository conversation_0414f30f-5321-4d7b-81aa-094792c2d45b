import { useEffect } from 'react';

const usePreventSalutationDeletion = (
  editor,
  showSalutation
) => {
  useEffect(() => {
    if (!editor) return;

    const handleKeyDown = (event) => {
      if (event.key !== 'Backspace') return;

      const { state } = editor;
      const { selection, doc } = state;
      const { from, to } = selection;

      const firstNode = doc.firstChild;

      // Prevent deletion if salutation is the first node and only content
      if (
        firstNode?.attrs?.id === 'salutation'
        && doc.nodeSize === firstNode.nodeSize + 2
      ) {
        event.preventDefault();
        return;
      }

      // Prevent deletion if the entire document is selected (Ctrl + A)
      if (
        from === 1
        && to === doc.nodeSize - 2
        && firstNode?.attrs?.id === 'salutation'
      ) {
        event.preventDefault();
      }
    };

    const handleSelectionChange = () => {
      const { state, commands } = editor;
      const { selection, doc } = state;
      const firstNode = doc.firstChild;

      if (!firstNode || firstNode.attrs?.id !== 'salutation') return;

      const { from } = selection;

      // If salutation is part of the selection, adjust it
      if (from === 1) {
        commands.setTextSelection({ from: 2, to: doc.nodeSize - 2 });
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    editor.on('selectionUpdate', handleSelectionChange);

    // return () => {
    //   document.removeEventListener('keydown', handleKeyDown);
    //   editor.off('selectionUpdate', handleSelectionChange);
    // };
  }, [editor, showSalutation]);
};

export default usePreventSalutationDeletion;
