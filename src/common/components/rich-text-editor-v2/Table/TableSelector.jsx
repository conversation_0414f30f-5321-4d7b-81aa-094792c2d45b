import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';
import { cn } from '../utils';
import { InsertTableIcon } from '../Icons';

/**
 * Table selector component for inserting tables
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Table selector component
 */
const TableSelector = ({ editor }) => {
  const [hoveredCells, setHoveredCells] = useState({ rows: 0, cols: 0 });
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  const buttonRef = useRef(null);
  const dropdownRef = useRef(null);

  const maxRows = 8;
  const maxCols = 8;

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownHeight = maxRows * 28 + 40; // Approximate height of dropdown (cells + padding + header)
      const spaceBelow = window.innerHeight - rect.bottom;

      if (spaceBelow < dropdownHeight && rect.top > dropdownHeight) {
        // Position above if there's not enough space below but enough space above
        setPosition({
          top: rect.top + window.scrollY - dropdownHeight,
          left: rect.left + window.scrollX
        });
      } else {
        // Default position below
        setPosition({
          top: rect.bottom + window.scrollY,
          left: rect.left + window.scrollX
        });
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isOpen
        && buttonRef.current
        && !buttonRef.current.contains(event.target)
        && dropdownRef.current
        && !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleMouseEnter = (row, col) => {
    setHoveredCells({ rows: row + 1, cols: col + 1 });
  };

  const handleSelect = () => {
    if (editor) {
      editor
        .chain()
        .focus()
        .insertTable({
          rows: hoveredCells.rows,
          cols: hoveredCells.cols,
          withHeaderRow: true
        })
        .run();
    }
    setIsOpen(false);
  };

  return (
    <div role="menu" className="relative">
      <button
        type="button"
        ref={buttonRef}
        onClick={(e) => { e?.stopPropagation(); setIsOpen(!isOpen); }}
        className={cn(
          'p-1.5 hover:bg-gray-100 rounded-md flex items-center gap-1',
          isOpen && 'bg-gray-100'
        )}
        title="Insert table"
      >
        <InsertTableIcon />
        <KeyboardDownArrow width="10" height="7" stroke="#456C86" />
      </button>

      {isOpen
        && createPortal(
          <div
            ref={dropdownRef}
            className="absolute z-50 top-full left-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 p-4"
            style={{
              top: `${position.top}px`,
              left: `${position.left}px`,
              overflowY: 'auto',
              position: 'absolute'
            }}
          >
            <div className="mb-2 text-sm text-gray-600">
              {hoveredCells.rows} x {hoveredCells.cols} table
            </div>
            <div className="grid gap-1" style={{ width: 'fit-content' }}>
              {Array.from({ length: maxRows }).map((_, rowIndex) => (
                <div key={`row-${rowIndex.toString()}`} className="flex gap-1">
                  {Array.from({ length: maxCols }).map((__, colIndex) => (
                    <div
                      aria-hidden
                      key={colIndex?.toString()}
                      className={cn(
                        'w-6 h-6 border cursor-pointer transition-colors',
                        rowIndex < hoveredCells.rows
                          && colIndex < hoveredCells.cols
                          ? 'bg-blue-500 border-blue-600'
                          : 'border-gray-300 hover:border-gray-400'
                      )}
                      onClick={handleSelect}
                      onMouseEnter={() => handleMouseEnter(rowIndex, colIndex)}
                    />
                  ))}
                </div>
              ))}
            </div>
          </div>,
          document.body
        )}
    </div>
  );
};

export default TableSelector;
