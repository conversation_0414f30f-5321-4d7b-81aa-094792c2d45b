import MoreButtonIcon from 'assets/MoreButtonIcon';
import React, { useRef, useEffect, useState } from 'react';
/**
 * Cell menu component
 *
 * @param {Object} props - Component props
 * @param {Object} props.position - Position coordinates for the menu
 * @param {Function} props.onAction - Cell action handler
 * @returns {React.ReactElement} Cell menu component
 */
const CellMenu = ({ position, onAction }) => {
  const cellMenuRef = useRef(null);
  const buttonRef = useRef(null);

  const [dropdownPosition, setDropdownPosition] = useState({
    top: '100%',
    left: '0'
  });

  useEffect(() => {
    const checkPosition = () => {
      if (!buttonRef.current) return;

      const menuHeight = 225; // This is Cell menu height adjust by your current needs
      const windowHeight = window.innerHeight;
      const spaceBelow = windowHeight
        - (position.top - window.scrollY + buttonRef.current.offsetHeight);

      // If not enough space below, position the dropdown above the button
      if (spaceBelow < menuHeight) {
        setDropdownPosition({ top: 'auto', bottom: '100%', left: '0' });
      } else {
        setDropdownPosition({ top: '100%', bottom: 'auto', left: '0' });
      }
    };

    // Small delay to ensure the menu is rendered
    setTimeout(checkPosition, 0);

    // Also check on window resize
    window.addEventListener('resize', checkPosition);
    return () => window.removeEventListener('resize', checkPosition);
  }, [position]);

  return (
    <div
      className="cell-menu absolute z-40 flex items-center"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`
      }}
    >
      <div className="relative group">
        <button
          ref={buttonRef}
          type="button"
          className="p-0 hover:bg-gray-100 rounded bg-white border border-gray-200 shadow-sm"
        >
          <MoreButtonIcon h="16" w="16" />
        </button>
        <div
          className="z-50 absolute hidden group-hover:block top-4 right-0 mt-1 bg-white rounded-lg shadow-xl border border-gray-200 py-1 min-w-[180px]"
          style={{
            top: dropdownPosition.top,
            bottom: dropdownPosition.bottom,
            left: dropdownPosition.left
          }}
        >
          <button
            ref={cellMenuRef}
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onAction('addRowBefore');
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100"
          >
            Insert row above
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onAction('addRowAfter');
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100"
          >
            Insert row below
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onAction('addColumnBefore');
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100"
          >
            Insert column left
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onAction('addColumnAfter');
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100"
          >
            Insert column right
          </button>
          <div className="border-t border-gray-200 my-1" />
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onAction('deleteRow');
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-red-50 text-red-600"
          >
            Delete row
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onAction('deleteColumn');
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-red-50 text-red-600"
          >
            Delete column
          </button>
        </div>
      </div>
    </div>
  );
};

export default CellMenu;
