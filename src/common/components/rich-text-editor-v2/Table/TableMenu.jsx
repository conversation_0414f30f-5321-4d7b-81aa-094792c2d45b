import Delete from 'assets/delete';
import React from 'react';

/**
 * Table menu component
 *
 * @param {Object} props - Component props
 * @param {Object} props.position - Position coordinates for the menu
 * @param {Function} props.onDelete - Delete table handler
 * @returns {React.ReactElement} Table menu component
 */
const TableMenu = ({ position, onDelete }) => (
  <div
    className="table-menu fixed z-9999 flex items-center gap-1 bg-white rounded-md shadow-sm border border-gray-200"
    style={{ top: `${position.top}px`, left: `${position.left}px` }}
  >
    <button
      onClick={onDelete}
      className="p-1 hover:bg-red-100 rounded text-red-600"
      title="Delete table"
      type="button"
    >
      <Delete w="15" h="15" />
    </button>
  </div>
);

export default TableMenu;
