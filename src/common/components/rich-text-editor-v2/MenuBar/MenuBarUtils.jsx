import React, { useCallback } from 'react';
import { LinkIcon, InsertAttachmentIcon } from '../Icons';
import TextStyleButton from './buttons/TextStyleButton';

/**
 * Hook to provide image upload functionality
 *
 * @param {Object} editor - TipTap editor instance
 * @returns {Function} Image upload handler
 */
export const useImageUpload = (editor) => {
  return useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (event) => {
      const file = event.target?.files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          editor.chain().focus().setImage({ src: e.target.result }).run();
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  }, [editor]);
};

/**
 * Hook to provide link insertion functionality
 *
 * @param {Object} editor - TipTap editor instance
 * @returns {Function} Link insertion handler
 */
export const useLinkInsertion = (editor) => {
  return useCallback(() => {
    // eslint-disable-next-line no-alert
    const url = window.prompt('Enter URL');
    if (url) editor.chain().focus().setLink({ href: url }).run();
  }, [editor]);
};

/**
 * Component for formatting tools (link, image, horizontal rule)
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Formatting tools component
 */
export const FormattingTools = ({ editor }) => {
  const addImage = useImageUpload(editor);
  const addLink = useLinkInsertion(editor);

  return (
    <>
      <TextStyleButton
        editor={editor}
        Icon={LinkIcon}
        onClick={addLink}
        active="link"
      />
      <TextStyleButton
        editor={editor}
        Icon={InsertAttachmentIcon}
        onClick={addImage}
        active="attachment"
      />
    </>
  );
};
