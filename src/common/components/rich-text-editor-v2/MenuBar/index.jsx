import React from 'react';
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  CodeFormattingIcon,
  InsertHorizontalLineIcon
} from '../Icons';

import { TextStyleButton } from './buttons/TextStyleButton';
import { AlignmentButtons } from './buttons/AlignmentButtons';
import { ListDropdown } from './buttons/ListDropdown';
import { ColorSelector } from './selectors/ColorSelector';
import { FontSizeSelector } from './selectors/FontSizeSelector';
import { FontFamilySelector } from './selectors/FontFamilySelector';
import { HeadingSelector } from './selectors/HeadingSelector';
import TableSelector from '../Table/TableSelector';
// import { FormattingTools } from './MenuBarUtils';

/**
 * Main MenuBar component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @param {Function} props.renderMenubarEnd - Function to render additional content at end of menubar
 * @returns {React.ReactElement} MenuBar component
 */
const MenuBar = ({ editor, renderMenubarEnd = () => null }) => {
  if (!editor) return null;

  const textFormattingButtons = [
    { icon: BoldIcon, command: 'toggleBold', active: 'bold' },
    { icon: ItalicIcon, command: 'toggleItalic', active: 'italic' },
    { icon: UnderlineIcon, command: 'toggleUnderline', active: 'underline' },
    { icon: StrikethroughIcon, command: 'toggleStrike', active: 'strike' },
    { icon: CodeFormattingIcon, command: 'toggleCode', active: 'code' }
  ];

  return (
    <div className="flex flex-wrap items-center gap-2 p-2 border-b border-[#E7EFF5] bg-white">
      {/* Text Formatting */}
      {textFormattingButtons.map(({ icon, command, active }) => (
        <TextStyleButton
          key={command}
          editor={editor}
          Icon={icon}
          command={command}
          active={active}
        />
      ))}

      {/* Colors */}
      <ColorSelector editor={editor} />

      {/* Text Alignment */}
      <AlignmentButtons editor={editor} />

      {/* List Formatting */}
      <ListDropdown editor={editor} />

      {/* Insert Elements */}
      <TableSelector editor={editor} />
      {/* <FormattingTools editor={editor} /> */}
      {/* Commented now */}
      <TextStyleButton
        editor={editor}
        Icon={InsertHorizontalLineIcon}
        command="setHorizontalRule"
        active="horizontal"
      />

      {/* Font Size & Family */}
      <FontSizeSelector editor={editor} />
      <FontFamilySelector editor={editor} />

      {/* Headings */}
      <HeadingSelector editor={editor} />

      {renderMenubarEnd()}
    </div>
  );
};

export default MenuBar;
