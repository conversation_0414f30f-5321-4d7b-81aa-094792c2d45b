import React from 'react';
import { FONT_FAMILIES } from '../../constants';

/**
 * Font family selector component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Font family selector component
 */
export const FontFamilySelector = ({ editor }) => (
  <select
    className="px-2 py-1 border rounded text-sm text-[#456C86] max-w-32"
    onChange={(e) => editor.chain().focus().setFontFamily(e.target.value).run()}
  >
    {FONT_FAMILIES.map((font) => (
      <option key={font} value={font}>
        {font}
      </option>
    ))}
  </select>
);
