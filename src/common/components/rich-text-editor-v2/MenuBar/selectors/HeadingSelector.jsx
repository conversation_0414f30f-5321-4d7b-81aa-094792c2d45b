import React from 'react';

/**
 * Heading selector component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Heading selector component
 */
export const HeadingSelector = ({ editor }) => (
  <select
    className="px-2 py-1 border rounded text-sm text-[#456C86]"
    onChange={(e) => editor
      .chain()
      .focus()
      .setHeading({ level: parseInt(e.target.value, 10) })
      .run()}
  >
    <option value="0">Paragraph</option>
    {[1, 2, 3, 4, 5, 6].map((lvl) => (
      <option key={lvl} value={lvl}>
        Heading {lvl}
      </option>
    ))}
  </select>
);
