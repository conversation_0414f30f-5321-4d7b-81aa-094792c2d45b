import React from 'react';
import { FONT_SIZES } from '../../constants';

/**
 * Font size selector component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Font size selector component
 */
export const FontSizeSelector = ({ editor }) => (
  <select
    defaultValue="16px"
    className="px-2 py-1 border rounded text-sm text-[#456C86]"
    onChange={(e) => editor.chain().focus().setFontSize(e.target.value).run()}
  >
    {FONT_SIZES.map((size) => (
      <option key={size} value={size}>
        {size}
      </option>
    ))}
  </select>
);
