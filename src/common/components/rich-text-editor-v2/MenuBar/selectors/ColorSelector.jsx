import React, { useState, useRef, useEffect } from 'react';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';
import { cn } from '../../utils';
import { ColorPaletteIcon } from '../../Icons';
import { COLORS } from '../../constants';

/**
 * Color selector dropdown component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Color selector component
 */
export const ColorSelector = ({ editor }) => {
  const [isOpen, setIsOpen] = useState(false);

  const buttonRef = useRef(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isOpen
        && buttonRef.current
        && !buttonRef.current.contains(event.target)
        && dropdownRef.current
        && !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleColorSelect = (color) => {
    editor.chain().focus().setColor(color).run();
    setIsOpen(false); // Close the dropdown after color selection
  };

  return (
    <div className="relative group">
      <button
        ref={buttonRef}
        type="button"
        className={cn(
          'p-1.5 hover:bg-gray-100 rounded flex items-center gap-1',
          isOpen && 'bg-gray-100'
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <ColorPaletteIcon />
        <KeyboardDownArrow width="10" height="7" stroke="#456C86" />
      </button>
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute flex flex-wrap gap-1 justify-center bg-white border rounded-lg shadow-lg p-2 z-10 w-40"
        >
          {COLORS.map((color) => (
            <button
              aria-label={`Select ${color} color`}
              key={color}
              type="button"
              onClick={() => handleColorSelect(color)}
              className={cn(
                'w-6 h-6 rounded transition-all hover:scale-110',
                editor.isActive('textStyle', { color })
                  && 'ring-1 ring-offset-1 ring-black'
              )}
              style={{ backgroundColor: color }}
              title={color}
            />
          ))}
        </div>
      )}
    </div>
  );
};
