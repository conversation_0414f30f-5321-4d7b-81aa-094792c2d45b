import {
  AlignJustifyIcon,
  AlignLeftIcon,
  BoldIcon,
  ItalicIcon,
  UnderlineIcon
} from '../Icons';
import { cn } from '../utils';
import { TextStyleButton } from './buttons/TextStyleButton';

const SimpleMenuBar = ({ editor }) => {
  if (!editor) {
    return null;
  }

  const textFormattingButtons = [
    { icon: BoldIcon, command: 'toggleBold', active: 'bold' },
    { icon: ItalicIcon, command: 'toggleItalic', active: 'italic' },
    { icon: UnderlineIcon, command: 'toggleUnderline', active: 'underline' }
  ];

  const alignments = [
    { Icon: AlignLeftIcon, value: 'left' },
    { Icon: AlignJustifyIcon, value: 'justify' }
  ];

  return (
    <div className="flex gap-2 py-1 px-2">
      {textFormattingButtons.map(({ icon, command, active }) => (
        <TextStyleButton
          w="16"
          h="16"
          key={command}
          editor={editor}
          Icon={icon}
          command={command}
          active={active}
        />
      ))}
      {alignments?.map(({ Icon, value }) => (
        <button
          type="button"
          key={value}
          onClick={() => editor.chain().focus().setTextAlign(value).run()}
          className={cn(
            'p-1.5 hover:bg-gray-100 rounded',
            editor.isActive({ textAlign: value }) && 'bg-gray-100'
          )}
        >
          <Icon w="16" h="16" />
        </button>
      ))}
    </div>
  );
};

export default SimpleMenuBar;
