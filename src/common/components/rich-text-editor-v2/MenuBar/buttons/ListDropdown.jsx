import React from 'react';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';
import { cn } from '../../utils';
import { ListIcon, UnOrderedListIcon, OrderedListIcon } from '../../Icons';

/**
 * List formatting dropdown component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} List dropdown component
 */
export const ListDropdown = ({ editor }) => (
  <div className="relative group">
    <button
      type="button"
      className="p-1.5 hover:bg-gray-100 rounded flex items-center gap-1"
    >
      <ListIcon />
      <KeyboardDownArrow width="10" height="7" stroke="#456C86" />
    </button>
    <div className="absolute hidden group-hover:block bg-white border rounded-lg shadow-lg p-2 z-10">
      {[
        { type: 'bullet', Icon: UnOrderedListIcon },
        { type: 'ordered', Icon: OrderedListIcon }
      ].map(({ type, Icon }) => (
        <button
          key={type}
          type="button"
          onClick={() => editor
            .chain()
            .focus()[type === 'bullet' ? 'toggleBulletList' : 'toggleOrderedList']()
            .run()}
          className={cn(
            'w-full p-2 hover:bg-gray-100 rounded flex items-center gap-2',
            editor.isActive(`${type}List`) && 'bg-gray-100'
          )}
        >
          <Icon />
        </button>
      ))}
    </div>
  </div>
);
