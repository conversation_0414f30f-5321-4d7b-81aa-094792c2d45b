import React from 'react';
import { cn } from '../../utils';

/**
 * Text style button component
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @param {React.ComponentType} props.Icon - Icon component
 * @param {string} props.command - Editor command to execute
 * @param {Function} props.onClick - Custom click handler
 * @param {string} props.active - Active state identifier
 * @returns {React.ReactElement} Text style button component
 */
export const TextStyleButton = ({
  editor, Icon, command, onClick, active, w, h
}) => (
  <button
    type="button"
    onClick={() => (onClick ? onClick() : editor.chain().focus()[command]().run())}
    className={cn(
      'p-1.5 hover:bg-gray-100 rounded',
      editor.isActive(active) && 'bg-gray-100'
    )}
  >
    <Icon w={w} h={h} />
  </button>
);
