import React from 'react';
import { cn } from '../../utils';
import {
  AlignLeftIcon,
  AlignCenterIcon,
  AlignRightIcon,
  AlignJustifyIcon
} from '../../Icons';

/**
 * Text alignment buttons group
 *
 * @param {Object} props - Component props
 * @param {Object} props.editor - TipTap editor instance
 * @returns {React.ReactElement} Alignment buttons component
 */
export const AlignmentButtons = ({ editor }) => {
  const alignments = [
    { Icon: AlignLeftIcon, value: 'left' },
    { Icon: AlignCenterIcon, value: 'center' },
    { Icon: AlignRightIcon, value: 'right' },
    { Icon: AlignJustifyIcon, value: 'justify' }
  ];

  return (
    <div className="flex items-center gap-1">
      {alignments.map(({ Icon, value }) => (
        <button
          type="button"
          key={value}
          onClick={() => editor.chain().focus().setTextAlign(value).run()}
          className={cn(
            'p-1.5 hover:bg-gray-100 rounded',
            editor.isActive({ textAlign: value }) && 'bg-gray-100'
          )}
        >
          <Icon />
        </button>
      ))}
    </div>
  );
};
