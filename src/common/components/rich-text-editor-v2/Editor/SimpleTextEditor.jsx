import { useCallback, useEffect } from 'react';
import TextAlign from '@tiptap/extension-text-align';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import SimpleMenuBar from '../MenuBar/SimpleMenuBar';

const SimpleTextEditor = ({
  value,
  isError = false,
  helperText = '',
  onChange = () => {}
}) => {
  const handleUpdate = useCallback(
    ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    [onChange]
  );

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ['paragraph', 'heading']
      })
    ],

    content: value || '<p>Loading...</p>',
    onUpdate: handleUpdate,

    editorProps: {
      attributes: {
        class:
          'prose max-w-none focus:outline-none px-5 py-3 overflow-x-hidden break-all whitespace-normal max-h-[100px] overflow-y-auto'
      }
    }
  });

  // Update content when `value` changes (e.g., API response)
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  return (
    <div>
      <div
        className="border rounded-[8px] overflow-hidden"
        style={{ borderColor: isError ? 'red' : '#E8ECEE' }}
      >
        <SimpleMenuBar editor={editor} />
        <EditorContent editor={editor} />
      </div>
      <p
        className="text-right text-xs mt-1"
        style={{ color: isError ? 'red' : undefined }}
      >
        {helperText}
      </p>
    </div>
  );
};

export default SimpleTextEditor;
