import React, { useState, useEffect, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Color from '@tiptap/extension-color';

import MenuBar from '../MenuBar';
import TableMenu from '../Table/TableMenu';
import CellMenu from '../Table/CellMenu';
import FontSize from '../extensions/FontSize';
import { salutations } from '../constants';
import Paragraph from '../extensions/Paragraph';

/**
 * TiptapEditor component
 *
 * @param {Object} props - Component props
 * @param {string} props.value - HTML content for the editor
 * @param {Function} props.onChange - Callback when content changes
 * @param {Function} props.renderMenubarEnd - Function to render additional elements at the end of the menu bar
 * @returns {React.ReactElement} The editor component
 */
const TiptapEditor = ({
  value,
  showSalutation = false,
  editorProps = {},
  onChange = () => {},
  renderMenubarEnd,
  isMalayalam,
  onSalutationChange = () => {}
}) => {
  const [selectedTable, setSelectedTable] = useState(null);
  const [selectedCell, setSelectedCell] = useState(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [cellMenuPosition, setCellMenuPosition] = useState({ top: 0, left: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [selectedSalutation, setSelectedSalutation] = useState(
    isMalayalam ? salutations[0]?.labelMl : salutations[0]?.labelEn
  );

  const handleSalutationChange = useCallback((e) => {
    const newSalutation = e.target.value;
    setSelectedSalutation(newSalutation);
  }, []);

  const handleUpdate = useCallback(
    ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    [onChange]
  );

  // Create a memoized click handler for the salutation
  //   const handleClick = useCallback((view, pos, event) => {
  //     const node = view.state.doc.nodeAt(pos);

  //     if (node && node.type.name === 'salutation') {
  //       // Move cursor to the end of salutation
  //       const resolvedPos = view.state.doc.resolve(pos + node.nodeSize);
  //       const newSelection = view.state.selection.constructor.near(resolvedPos);
  //       view.dispatch(view.state.tr.setSelection(newSelection));
  //       return true;
  //     }

  //     return false;
  //   }, []);

  // Create a memoized keydown handler for the salutation
  //   const handleKeyDown = useCallback((view, event) => {
  //     const { selection } = view.state;
  //     const { from } = selection;
  //     const pos = view.state.doc.resolve(from);
  //     const node = pos.parent;

  //     if (node.type.name === 'salutation') {
  //       // Allow navigation keys but block content edits
  //       const navigationKeys = [
  //         'ArrowDown',
  //         'ArrowRight',
  //         'ArrowLeft',
  //         'ArrowUp',
  //         'Tab',
  //         'End',
  //         'Home',
  //         'Enter',
  //       ];

  //       if (!navigationKeys.includes(event.key)) {
  //         event.preventDefault();
  //         return true;
  //       }
  //     }

  //     return false;
  //   }, []);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-4'
          }
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-4'
          }
        }
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph']
      }),
      Table.configure({
        resizable: true,
        handleWidth: 4,
        cellMinWidth: 100,
        HTMLAttributes: {
          class: 'w-full border-collapse relative'
        }
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border-b border-gray-200'
        }
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class:
            'border-b border-t-0 border-x-0 border-gray-300 bg-gray-50 p-2 text-left font-semibold'
        }
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'relative border border-gray-200 p-2 align-top [&_p]:m-0'
        }
      }),
      Image.configure({
        allowBase64: true
      }),
      Link.configure({
        openOnClick: true
      }),
      TextStyle,
      FontSize,
      FontFamily,
      Color,
      Paragraph
    ],

    content: value || '<p>Loading...</p>',

    onUpdate: handleUpdate,

    editorProps: {
      attributes: {
        class:
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4'
      },
      handleDOMEvents: {
        click: (view, event) => {
          const { target } = event;
          const table = target.closest('table');
          const cell = target.closest('td, th');

          if (table && !isDragging) {
            const rect = table.getBoundingClientRect();
            setMenuPosition({
              top: rect.top + window.scrollY - 27,
              left: rect.right + window.scrollX - 25
            });
            setSelectedTable(table);
          }

          if (cell && !isDragging) {
            const rect = cell.getBoundingClientRect();
            setCellMenuPosition({
              top: rect.top + window.scrollY,
              left: rect.right + window.scrollX - 20
            });
            setSelectedCell({
              x: cell.cellIndex,
              y: cell.parentElement.rowIndex
            });
          } else if (!cell) {
            setSelectedCell(null);
          }

          if (!table && !cell) {
            setSelectedTable(null);
            setSelectedCell(null);
          }

          return false;
        },
        mouseup: () => {
          if (selectedTable) {
            selectedTable.style.cursor = 'default';
          }
          setIsDragging(false);
          return false;
        }
      },
      //   handleClick,
      //   handleKeyDown,
      ...editorProps
    }
  });

  //   usePreventSalutationDeletion(editor, showSalutation);

  // Update content when `value` changes (e.g., API response)
  useEffect(() => {
    if (onSalutationChange) {
      onSalutationChange(selectedSalutation);
    }
  }, [selectedSalutation]);

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  useEffect(() => {
    setSelectedSalutation(
      isMalayalam ? salutations[0]?.labelMl : salutations[0]?.labelEn
    );
  }, [isMalayalam]);

  useEffect(() => {
    const handleScroll = () => {
      setSelectedTable(null);
      setSelectedCell(null);
    };

    const handleClickOutside = (event) => {
      const { target } = event;
      const isTableClick = target.closest('table');
      const isTableMenu = target.closest('.table-menu');
      const isCellMenu = target.closest('.cell-menu');
      const isCellClick = target.closest('td, th');

      if (!isTableClick && !isTableMenu && !isCellClick && !isCellMenu) {
        setSelectedTable(null);
        setSelectedCell(null);
      }
    };

    document.addEventListener('scroll', handleScroll, true);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('scroll', handleScroll, true);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [editor]);

  // Update the hidden salutation paragraph when the selected salutation changes
  //   useEffect(() => {
  //     if (!editor) return;

  //     if (showSalutation) {
  //       // Check if the salutation paragraph already exists
  //       const content = editor.getHTML();
  //       const parser = new DOMParser();
  //       const doc = parser.parseFromString(content, 'text/html');
  //       const existingSalutation = doc.getElementById('salutation');

  //       if (existingSalutation) {
  //         // Update the existing salutation paragraph
  //         editor.commands.command(({ tr }) => {
  //           // Find the position of the salutation paragraph
  //           const pos = 0; // Assuming it's always at the beginning

  //           // Count the size of the node to replace
  //           const nodeSize = editor.state.doc.firstChild.nodeSize;

  //           // Replace the node with a new paragraph
  //           tr.delete(pos, pos + nodeSize);
  //           tr.insert(
  //             pos,
  //             editor.schema.nodes.paragraph.create(
  //               { id: 'salutation' },
  //               editor.schema.text(selectedSalutation)
  //             )
  //           );

  //           return true;
  //         });
  //       } else {
  //         // Insert a new salutation paragraph at the beginning
  //         editor.commands.command(({ tr }) => {
  //           tr.insert(
  //             0,
  //             editor.schema.nodes.paragraph.create(
  //               { id: 'salutation' },
  //               editor.schema.text(selectedSalutation)
  //             )
  //           );

  //           return true;
  //         });
  //       }

  //       handleUpdate({ editor });
  //     } else {
  //       // Remove the salutation paragraph if showSalutation is false
  //       editor.commands.command(({ tr }) => {
  //         const firstNode = editor.state.doc.firstChild;
  //         const nodeAttrs = firstNode.attrs;

  //         // Check if the first node is our salutation paragraph
  //         if (nodeAttrs.id === 'salutation') {
  //           tr.delete(0, firstNode.nodeSize);
  //         }

  //         return true;
  //       });
  //     }
  //   }, [editor, selectedSalutation, showSalutation]);

  //   // Add CSS to hide the salutation paragraph in the editor
  //   useEffect(() => {
  //     if (!editor) return;

  //     // Add a style tag to hide the salutation paragraph
  //     const style = document.createElement('style');
  //     style.textContent = `
  //       .ProseMirror p[id="salutation"] {
  //         display: none;
  //       }
  //     `;
  //     document.head.appendChild(style);

  //     return () => {
  //       document.head.removeChild(style);
  //     };
  //   }, [editor]);

  const deleteTable = () => {
    if (editor) {
      editor.chain().focus().deleteTable().run();
      setSelectedTable(null);
    }
  };

  const handleCellAction = (action) => {
    if (!editor) return;

    editor.chain().focus();

    switch (action) {
      case 'addRowBefore':
        editor.chain().focus().addRowBefore().run();
        break;
      case 'addRowAfter':
        editor.chain().focus().addRowAfter().run();
        break;
      case 'addColumnBefore':
        editor.chain().focus().addColumnBefore().run();
        break;
      case 'addColumnAfter':
        editor.chain().focus().addColumnAfter().run();
        break;
      case 'deleteRow':
        editor.chain().focus().deleteRow().run();
        break;
      case 'deleteColumn':
        editor.chain().focus().deleteColumn().run();
        break;
      default:
        break;
    }

    // Delay menu closing slightly
    setTimeout(() => setSelectedCell(null), 100);
  };

  return (
    <div className="min-h-[250px] bg-white w-full">
      <MenuBar editor={editor} renderMenubarEnd={renderMenubarEnd} />

      <div>
        {showSalutation && (
          <select
            value={selectedSalutation}
            onChange={handleSalutationChange}
            className="salutation-select"
          >
            {salutations.map(({ labelEn, labelMl }) => {
              const salutation = isMalayalam ? labelMl : labelEn;

              return (
                <option key={salutation} value={salutation}>
                  {salutation}
                </option>
              );
            })}
          </select>
        )}

        <EditorContent
          value={value}
          content={value}
          spellCheck
          editor={editor}
          style={{ padding: '0 10px' }}
        />
      </div>

      {selectedTable && (
        <TableMenu position={menuPosition} onDelete={deleteTable} />
      )}
      {selectedCell && (
        <CellMenu position={cellMenuPosition} onAction={handleCellAction} />
      )}
    </div>
  );
};

export default TiptapEditor;
