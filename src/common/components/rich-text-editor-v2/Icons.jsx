export const BoldIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 4h5a4 4 0 0 1 0 8H8V4zm0 8h6a4 4 0 0 1 0 8H8v-8z"
        stroke="#456C86"
        strokeWidth="2"
      />
    </svg>
  );
};

export const ItalicIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10 4h6M8 20h6M14 4l-4 16" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};

export const UnderlineIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 20H18"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 4V11C17 13.761 14.761 16 12 16C9.239 16 7 13.761 7 11V4"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const StrikethroughIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 10h12M10 4h4a4 4 0 0 1 0 8M14 20h-4a4 4 0 0 1 0-8"
        stroke="#456C86"
        strokeWidth="2"
      />
    </svg>
  );
};

export const LinkIcon = ({ w = '22', h = '22' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.9641 6.03624C17.3451 4.65524 19.5831 4.65524 20.9641 6.03624V6.03624C22.3451 7.41724 22.3451 9.65524 20.9641 11.0362L15.0851 16.9152C13.7041 18.2962 11.4661 18.2962 10.0851 16.9152V16.9152C8.70407 15.5342 8.70407 13.2962 10.0851 11.9152L10.9641 11.0362"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.03624 18.9641C6.65524 20.3451 4.41724 20.3451 3.03624 18.9641V18.9641C1.65524 17.5831 1.65524 15.3451 3.03624 13.9641L8.91524 8.08507C10.2962 6.70407 12.5342 6.70407 13.9152 8.08507V8.08507C15.2962 9.46607 15.2962 11.7041 13.9152 13.0851L13.0002 14.0001"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const AlignLeftIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M4 6h16M4 12h10M4 18h14" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};

export const AlignCenterIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M4 6h16M7 12h10M5 18h14" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};

export const AlignRightIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M4 6h16M10 12h10M6 18h14" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};

export const AlignJustifyIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M4 6h16M4 12h16M4 18h16" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};

export const ListIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 6h0M4 12h0M4 18h0M8 6h12M8 12h12M8 18h12"
        stroke="#456C86"
        strokeWidth="2"
      />
    </svg>
  );
};

export const OrderedListIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 6h2M4 12h2M4 18h2M8 6h12M8 12h12M8 18h12M5 5h0M5 11h0M5 17h0"
        stroke="#456C86"
        strokeWidth="2"
      />
      <text x="3" y="8" fontSize="6" fill="#456C86">
        1
      </text>
      <text x="3" y="14" fontSize="6" fill="#456C86">
        2
      </text>
      <text x="3" y="20" fontSize="6" fill="#456C86">
        3
      </text>
    </svg>
  );
};

export const UnOrderedListIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="5" cy="6" r="1.5" fill="#456C86" />
      <circle cx="5" cy="12" r="1.5" fill="#456C86" />
      <circle cx="5" cy="18" r="1.5" fill="#456C86" />
      <path d="M8 6h12M8 12h12M8 18h12" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};

export const CodeFormattingIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16 18l4-6-4-6M8 6L4 12l4 6M14 4l-4 16"
        stroke="#456C86"
        strokeWidth="2"
      />
    </svg>
  );
};

export const ColorPaletteIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 21a9 9 0 1 1 6.32-15.32A9 9 0 0 1 12 21zm0-3a6 6 0 1 0-4.24-10.24A6 6 0 0 0 12 18zM15 12h0M9 12h0M12 9h0M12 15h0"
        stroke="#456C86"
        strokeWidth="2"
      />
    </svg>
  );
};

export const InsertTableIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 4h16v16H4zM4 10h16M4 14h16M10 4v16M14 4v16"
        stroke="#456C86"
        strokeWidth="2"
      />
    </svg>
  );
};

export const InsertAttachmentIcon = ({ w = '22', h = '22' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.293 12.7068L14.475 9.52484C15.06 8.93984 16.011 8.93984 16.596 9.52484V9.52484C17.181 10.1098 17.181 11.0608 16.596 11.6458L12.707 15.5348C11.535 16.7068 9.63596 16.7068 8.46396 15.5348V15.5348C7.29196 14.3628 7.29196 12.4638 8.46396 11.2918L12 7.75684"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 21H6C4.343 21 3 19.657 3 18V6C3 4.343 4.343 3 6 3H18C19.657 3 21 4.343 21 6V18C21 19.657 19.657 21 18 21Z"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const InsertHorizontalLineIcon = ({ w = '20', h = '20' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M4 12h16" stroke="#456C86" strokeWidth="2" />
    </svg>
  );
};
