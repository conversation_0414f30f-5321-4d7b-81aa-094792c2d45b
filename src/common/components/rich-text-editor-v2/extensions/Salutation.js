import { mergeAttributes, Extension } from '@tiptap/core';

const Salutation = Extension.create({
  name: 'salutation',
  group: 'block',
  content: 'inline*',
  defining: true,

  addOptions() {
    return {
      HTMLAttributes: {}
    };
  },

  addAttributes() {
    return {
      value: {
        default: 'Dear Sir',
        parseHTML: (element) => element.textContent
          || element.getAttribute('data-value')
          || 'Dear Sir',
        renderHTML: (attributes) => ({
          'data-value': attributes.value
        })
      }
    };
  },

  parseHTML() {
    return [
      { tag: 'p[data-type="salutation"]' },
      { tag: 'div[data-type="salutation"]' },
      { tag: 'p.salutation-line' },
      {
        tag: 'p',
        getAttrs: (node) => {
          const text = node.textContent;
          const salutationRegex = /^(Dear\s+(Sir|Madam|Team)|To\s+Whom\s+It\s+May\s+Concern).{0,3}$/i;

          if (salutationRegex.test(text)) {
            return { value: text.trim() };
          }
          return false;
        }
      }
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'p',
      mergeAttributes(
        {
          'data-type': 'salutation',
          class: 'salutation-line'
        },
        HTMLAttributes
      ),
      0 // This is important! It tells TipTap to render the actual content here
    ];
  },

  // Protect salutation from deletion and modification
  addKeyboardShortcuts() {
    return {
      Backspace: ({ editor }) => {
        const { selection } = editor.state;
        const { empty, anchor } = selection;

        // Prevent deletion of salutation with backspace
        if (!empty) return false;

        const pos = editor.state.doc.resolve(anchor);
        const parentNode = pos.parent;

        if (parentNode.type.name === 'salutation') {
          return true; // Prevent backspace in salutation
        }

        return false;
      },
      Delete: ({ editor }) => {
        const { selection } = editor.state;
        const { empty, anchor } = selection;

        // Prevent deletion of salutation with delete
        if (!empty) return false;

        const pos = editor.state.doc.resolve(anchor);
        const parentNode = pos.parent;

        if (parentNode.type.name === 'salutation') {
          return true; // Prevent delete in salutation
        }

        return false;
      },
      Enter: ({ editor }) => {
        const { selection } = editor.state;
        const { from } = selection;
        const pos = editor.state.doc.resolve(from);
        const parentNode = pos.parent;

        if (parentNode.type.name === 'salutation') {
          // Move cursor to the next paragraph
          const nodeEndPos = pos.end();
          editor.commands.setTextSelection(nodeEndPos + 1);
          return true;
        }

        return false;
      }
    };
  },

  // Commands to set and remove salutation
  addCommands() {
    return {
      setSalutation:
        (value) => ({ tr, dispatch, chain }) => {
          const firstNode = tr.doc.firstChild;
          const isSalutation = firstNode && firstNode.type.name === 'salutation';

          if (isSalutation) {
            // Update existing salutation
            if (dispatch) {
              tr.setNodeMarkup(0, undefined, { value });
              dispatch(tr);
            }
            return true;
          }
          // Insert new salutation at the beginning
          return chain()
            .insertContentAt(0, {
              type: 'salutation',
              attrs: { value }
            })
            .run();
        },

      removeSalutation:
        () => ({ tr, dispatch }) => {
          const firstNode = tr.doc.firstChild;
          const isSalutation = firstNode && firstNode.type.name === 'salutation';

          if (isSalutation && dispatch) {
            tr.delete(0, firstNode.nodeSize);
            dispatch(tr);
            return true;
          }

          return false;
        }
    };
  }
});

export default Salutation;
