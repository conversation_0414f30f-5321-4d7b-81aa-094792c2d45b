import { Paragraph as ParagraphExtend } from '@tiptap/extension-paragraph';

const Paragraph = ParagraphExtend.extend({
  addAttributes() {
    return {
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute('id'),
        renderHTML: (attributes) => {
          if (!attributes.id) {
            return {};
          }
          return { id: attributes.id };
        }
      }
    };
  }
});

export default Paragraph;
