import React, { useState, useEffect } from 'react';
import {
  <PERSON>dal, ModalBody, ModalContent, ModalFooter, ModalOverlay, t, Button, Toast
} from 'common/components';
import {
  Pdf<PERSON><PERSON><PERSON>, Spinner
} from '@ksmartikm/ui-components';

import { secondary } from 'utils/color';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';

const DownloadAck = (props) => {
  const {
    handleClose = () => { }, open, downloadUrl
  } = props;

  const [loading, setLoading] = useState(true);
  const [baseCode, setBaseCode] = useState('');
  const { errorTost } = Toast;

  function getDocument(url, token) {
    try {
      fetch(url, {
        method: 'GET',
        headers: {
          Accept: DOCUMENT_TYPES.PDF,
          Authorization: `Bearer ${token}`
        }
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          const arr = new Uint8Array(response);
          const blob = new Blob([arr], {
            type: DOCUMENT_TYPES.PDF
          });
          const urlBlob = window.URL.createObjectURL(blob);
          setLoading(false);
          setBaseCode(urlBlob);
        });
    } catch (error) {
      setLoading(false);
      errorTost({
        title: t('errorOnLoading'),
        description: t('pdfLoadIssue')
      });
    }
  }

  useEffect(() => {
    if (open) {
      getDocument(downloadUrl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN));
    }
  }, [open]);

  const printAck = () => {
    printBlob(baseCode);
  };

  const downloadAck = () => {
    downloadBlob({ blob: baseCode, fileName: 'KSMART-INWARD-ACKNOWLEDGEMENT.pdf' });
  };

  return (
    <Modal size="4xl" isOpen={open}>
      <ModalOverlay />
      <ModalContent style={{ width: '645px' }} className="bg-white">
        <ModalBody py={6}>
          <div style={{ height: 'calc(100vh - 200px)', overflowY: 'auto' }}>
            {loading ? <Spinner style={{ margin: '100px 280px' }} /> : <PdfViewer title="do" height="760px" width="600px" type="application/pdf" data={baseCode} />}
          </div>
        </ModalBody>
        <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center gap-6" />
            <div className="flex items-center gap-2 cursor-pointer">
              <Button variant="secondary_outline" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" color={secondary} />}>{t('print')}</Button>
              <Button variant="secondary_outline" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" color={secondary} />}>{t('download')}</Button>
              <Button
                variant="secondary"
                onClick={handleClose}
              >
                {t('close')}
              </Button>
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default DownloadAck;
