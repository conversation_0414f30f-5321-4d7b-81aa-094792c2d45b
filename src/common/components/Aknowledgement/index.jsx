import React, { useState } from 'react';
import {
  <PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalContent, Modal<PERSON>ooter, ModalOverlay, t, Button, Toast
} from 'common/components';
import {
  <PERSON>dal<PERSON>eader, PdfViewer, <PERSON><PERSON>abel, Spinner
} from '@ksmartikm/ui-components';
import AcknowledgementBg from 'assets/acknowledgement.png';
import { Calendar } from 'assets/Svg';
import Clock from 'assets/Clock';
import Print from 'assets/Print';
import { dark, secondary } from 'utils/color';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';

const Acknowledgement = (props) => {
  const {
    handleClick = () => { }, open, print, data
  } = props;

  const [loading, setLoading] = useState(true);
  const [showPrint, setShowPrint] = useState(false);
  const [baseCode, setBaseCode] = useState('');
  const { errorTost } = Toast;

  function getDocument(url, token) {
    try {
      fetch(url, {
        method: 'GET',
        headers: {
          Accept: DOCUMENT_TYPES.PDF,
          Authorization: `Bearer ${token}`
        }
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          const arr = new Uint8Array(response);
          const blob = new Blob([arr], {
            type: DOCUMENT_TYPES.PDF
          });
          const urlBlob = window.URL.createObjectURL(blob);
          setLoading(false);
          setBaseCode(urlBlob);
        });
    } catch (error) {
      setLoading(false);
      errorTost({
        title: t('errorOnLoading'),
        description: t('pdfLoadIssue')
      });
    }
  }

  const handlePrint = () => {
    setShowPrint(true);
    getDocument(print, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN));
  };

  const printAck = () => {
    printBlob(baseCode);
  };

  const downloadAck = () => {
    downloadBlob({ blob: baseCode, fileName: 'KSMART-INWARD-ACKNOWLEDGEMENT.pdf' });
  };

  return (
    <Modal size="4xl" isOpen={open}>
      <ModalOverlay />
      <ModalContent borderRadius={16} style={{ width: '645px' }} className="bg-white">
        {showPrint ? (
          <>
            <ModalBody py={6}>
              <div style={{ height: 'calc(100vh - 200px)', overflowY: 'auto' }}>
                {loading ? <Spinner style={{ margin: '100px 280px' }} /> : <PdfViewer title="do" height="760px" width="600px" type={DOCUMENT_TYPES.PDF} data={baseCode} />}
              </div>
            </ModalBody>
            <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
              <div className="flex justify-between items-center w-full">
                <div className="flex items-center gap-6" />
                <div className="flex items-center gap-2 cursor-pointer">
                  <Button variant="secondary_outline" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" color={secondary} />}>{t('print')}</Button>
                  <Button variant="secondary_outline" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" color={secondary} />}>{t('download')}</Button>
                  <Button
                    variant="secondary"
                    onClick={handleClick}
                  >
                    {t('close')}
                  </Button>
                </div>
              </div>
            </ModalFooter>
          </>
        ) : (
          <>
            <ModalHeader style={{ color: dark }}>
              <div className="flex justify-between items-center">
                <h3 className="font-bold text-[#09327B] text-lg">{data?.title}</h3>
                <div className="flex items-center gap-2">
                  {/*  eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
                  <div onClick={handlePrint} aria-hidden="true" className="cursor-pointer"><Print /></div>
                </div>
              </div>
            </ModalHeader>
            <ModalBody px={12} py={6} className="flex items-center gap-12">
              <div className="overflow-hidden rounded-[20px] w-1/4">
                <img src={data?.backgroundImage ?? AcknowledgementBg} alt="acknowledgement-bg" />
              </div>
              <div className="w-2/4">
                <h4 className="text-[#09327B] font-bold text-[24px] mb-6">{t('acknowledgement')}</h4>
                <p className="text-[#454545] text-md"><RichLabel value={data?.description} /></p>
              </div>
            </ModalBody>
            <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
              <div className="flex justify-between items-center w-full">
                <div className="flex-grow items-center gap-6">
                  <div className="flex">
                    <div className="flex items-center gap-3">
                      <Calendar />
                      <span className="text-[#454545] font-bold text-lg">{data?.submittedDate}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock />
                      <span className="text-[#454545] font-bold text-lg">{data?.time || data?.submittedTime}</span>
                    </div>
                  </div>
                </div>
                <div className="flex-none">
                  <Button onClick={handleClick} variant="gost">
                    {t('close')}
                  </Button>
                </div>
                {print
                  && (
                    <div className="flex-none">
                      <Button onClick={handlePrint} variant="primary_outline">
                        {t('moreDetails')}
                      </Button>
                    </div>
                  )}
              </div>
            </ModalFooter>
          </>
        )}

      </ModalContent>
    </Modal>
  );
};

export default Acknowledgement;
