import React from 'react';

import {
  Card, CardBody, IconButton, RichLabel
} from 'common/components';
import ProfilePic from 'assets/ProfilePic';
import Edit from 'assets/Edit';
import Delete from 'assets/delete';
import CheckedRadio from 'assets/CheckedRadio';
import UnCheckedRadio from 'assets/UnCheckedRadio';
import { notes } from 'utils/color';
import Expand from 'assets/Expand';
import { DRAFT_STATUS } from 'pages/common/constants';

const ExpandedNoteCard = (props) => {
  const {
    item = {}, title = '', description = '', caption = '', actions = [], handleEdit = () => { }, handleDelete = () => { },
    index = 0, selectedIndex = 0, handleChecked = () => { }, handleExpandOrCollapse = () => { }
  } = props;

  const edit = () => {
    return handleEdit(item);
  };
  const handleRemove = () => {
    return handleDelete(item);
  };

  const profileColor = (color) => {
    switch (color) {
      case DRAFT_STATUS.CREATED:
        return '#BCE2F7';
      case DRAFT_STATUS.VERIFIED:
        return '#FBE2FF';
      case DRAFT_STATUS.APPROVED:
        return '#CBF2C1';
      case DRAFT_STATUS.PENDING:
        return '#F0D0FF';
      default:
        return '#BCE2F7';
    }
  };

  const profileTextColor = (color) => {
    switch (color) {
      case DRAFT_STATUS.CREATED:
        return '#6596CF';
      case DRAFT_STATUS.VERIFIED:
        return '#CF87BB';
      case DRAFT_STATUS.APPROVED:
        return '#7BBC6A';
      case DRAFT_STATUS.PENDING:
        return '#784B8D';
      default:
        return '#784B8D';
    }
  };

  return (
    <Card className="mt-5" borderRadius="10px" style={{ background: notes }} key={item.id}>
      <CardBody>
        <div>
          <div className="flex gap-5">
            <div className="flex-none">
              <div className="rounded-full w-12 h-12 p-3" style={{ background: profileColor(item.status) }}><ProfilePic color={profileTextColor(item.status)} /></div>
            </div>
            <div className="flex-grow">
              <div className="flex items-center">
                <div className="flex-grow">
                  <h4 className="font-medium text-medium capitalize">{title}</h4>
                  <h5 className="font-medium text-xs">{item?.penNo}</h5>
                  <h5 className="font-medium text-xs capitalize">{item?.designation}</h5>
                </div>
                <div className="flex-none">
                  <span className="ml-auto text-xs pr-5">{caption}</span>
                </div>
                <div className="flex-none">
                  <IconButton onClick={() => handleExpandOrCollapse(item)} icon={<Expand />} variant="unstyled" className="" />
                </div>
              </div>
              <div className="max-h-[400px] overflow-y-auto">
                <RichLabel value={description} className="whitespace-pre-wrap" />
              </div>
            </div>
          </div>
          <div className="flex gap-5 items-start">
            <div className="flex-grow" />
            <div className="flex-none text-xs text-right">

              {actions.length > 0 && (
                <>
                  {actions.includes('edit') && <IconButton variant="ghost" className="bg-none" onClick={edit} icon={<Edit />} />}
                  {actions.includes('delete') && <IconButton variant="ghost" onClick={handleRemove} icon={<Delete />} />}
                </>
              )}
            </div>
            {index
              ? (
                <div className="flex-none text-xs text-right">
                  <IconButton variant="ghost" className="bg-none" onClick={() => handleChecked(index, item)} icon={selectedIndex === index ? <CheckedRadio /> : <UnCheckedRadio />} />
                </div>
              )
              : ''}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ExpandedNoteCard;
