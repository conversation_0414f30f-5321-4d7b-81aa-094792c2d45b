/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable react/no-danger */
import React, { useState, useEffect } from 'react';
import mammoth from 'mammoth';

const DocxViewer = ({ blobUrl }) => {
  const [docContent, setDocContent] = useState('');

  useEffect(() => {
    const fetchDocxData = async () => {
      try {
        const response = await fetch(blobUrl);
        const blob = await response.blob();

        const arrayBuffer = await blob.arrayBuffer();

        mammoth.convertToHtml({ arrayBuffer })
          .then((result) => {
            setDocContent(result.value);
          });
      } catch (error) {
        setDocContent('');
      }
    };

    fetchDocxData();
  }, [blobUrl]);

  return (
    <div>
      <div style={{ padding: '20px', border: '1px solid #ccc' }} dangerouslySetInnerHTML={{ __html: docContent }} />
    </div>
  );
};

export default DocxViewer;
