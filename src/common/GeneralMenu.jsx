import NoteIcon from 'assets/Note';
import DraftIcon from 'assets/Draft';
import LinkIcon from 'assets/Link';
import Merge from 'assets/Merge';
import UnMerge from 'assets/UnMerge';
import Child from 'assets/Child';
import Demand from 'assets/Demand';
import Claim from 'assets/Claim';
import PayOrder from 'assets/PayOrder';
import Payment from 'assets/Payment';
import Beneficiary from 'assets/Beneficiary';
import Budget from 'assets/Budget';
import Receipt from 'assets/Receipt';
import Salary from 'assets/Salary';
import RecoveryPayments from 'assets/RecoveryPayments';
import Custodian from 'assets/Custodian';
import InwardDeLink from 'assets/InwardDeLink';
import UnLink from 'assets/UnLink';
// import StandingDemand from 'assets/StandingDemand';
// import Requisition from 'assets/Requisition';
// import RequisitionAllotment from 'assets/RequisitionAllotment';
import Agenda from 'assets/Agenda';
import DemandCancellation from 'assets/DemandCancellation';
// import BillGenerate from 'assets/BillGenerate';
// import BillSend from 'assets/BillSend';
// import OwnFundpayment from 'assets/OwnFundpayment';
import AdvanceClaim from 'assets/AdvanceClaim';
import ImprestClaim from 'assets/ImprestClaim';
import ImprestDisbursement from 'assets/ImprestDisbursement';
import ContraEntry from 'assets/ContraEntry';
import JournalEntry from 'assets/JournalEntry';
import { t } from './components';

export const MENU = [

  {

    title: 'General Features',
    child: [
      {
        label: t('note'),
        icon: <NoteIcon />,
        key: 'notes?show=0',
        isDisabled: false
      },
      {
        label: t('draft'),
        icon: <DraftIcon />,
        key: 'notes?show=1',
        isDisabled: false
      },
      // {
      //   label: t('enquiry'),
      //   icon: <EnquiryIcon />,
      //   key: 'enquiry',
      // isDisabled: false
      // },
      {
        label: t('link'),
        icon: <LinkIcon />,
        key: 'file-link',
        isDisabled: false
      },
      {
        label: t('unLink'),
        icon: <UnLink />,
        key: 'un-link-file',
        isDisabled: false
      },
      {
        label: t('merge'),
        icon: <Merge />,
        key: 'merge-file',
        isDisabled: false
      },
      {
        label: t('unmerge'),
        icon: <UnMerge />,
        key: 'unmerge',
        isDisabled: false
      },
      {
        label: t('child'),
        icon: <Child />,
        key: 'child',
        isDisabled: false
      },
      {
        label: t('beneficiary'),
        icon: <Beneficiary />,
        key: 'beneficiary',
        isDisabled: false
      },

      {
        label: t('custodian_change'),
        icon: <Custodian />,
        key: 'custodian-change',
        isDisabled: false
      },
      {
        label: t('inwardDeLink'),
        icon: <InwardDeLink />,
        key: 'inward-de-link',
        isDisabled: false
      },
      {
        label: t('demand'),
        icon: <Demand />,
        key: 'demand',
        isDisabled: false
      },
      {
        label: t('claim'),
        icon: <Claim />,
        key: 'claim',
        isDisabled: false
      },
      {
        label: t('paymentOrder'),
        icon: <PayOrder />,
        key: 'paymentOrder',
        isDisabled: false
      },
      {
        label: t('payment'),
        icon: <Payment />,
        key: 'payment',
        isDisabled: false
      },
      {
        label: t('budget'),
        icon: <Budget />,
        key: 'budget',
        isDisabled: false
      },
      {
        label: t('receipt'),
        icon: <Receipt />,
        key: 'receipt',
        isDisabled: false
      },
      {
        label: t('salary'),
        icon: <Salary />,
        key: 'salary',
        isDisabled: false
      },
      {
        label: t('recoveryPayment'),
        icon: <RecoveryPayments />,
        key: 'recoveryPayment',
        isDisabled: false
      },
      // {
      //   label: t('standingDemand'),
      //   icon: <StandingDemand />,
      //   key: 'standing-demand',
      //   isDisabled: false
      // },
      // {
      //   label: t('requisition'),
      //   icon: <Requisition />,
      //   key: 'requisition',
      //   isDisabled: false
      // },
      // {
      //   label: t('ownFundpayment'),
      //   icon: <OwnFundpayment />,
      //   key: 'ownFundpayment',
      //   isDisabled: false
      // },
      // {
      //   label: t('requisitionAllotment'),
      //   icon: <RequisitionAllotment />,
      //   key: 'requisitionAllotment',
      //   isDisabled: false
      // },
      {
        label: t('demandCancellation'),
        icon: <DemandCancellation />,
        key: 'demandCancellation',
        isDisabled: false
      },
      // {
      //   label: t('billGenerate'),
      //   icon: <BillGenerate />,
      //   key: 'billGenerate',
      //   isDisabled: false
      // },
      // {
      //   label: t('billSend'),
      //   icon: <BillSend />,
      //   key: 'billSend',
      //   isDisabled: false
      // },
      {
        label: t('agenda'),
        icon: <Agenda />,
        key: 'agenda',
        isDisabled: false
      },
      {
        label: t('advanceClaim'),
        icon: <AdvanceClaim />,
        key: 'advanceClaim',
        isDisabled: false
      },
      {
        label: t('imprestClaim'),
        icon: <ImprestClaim />,
        key: 'imprestClaim',
        isDisabled: false
      },
      {
        label: t('imprestDisbursement'),
        icon: <ImprestDisbursement />,
        key: 'imprestDisbursement',
        isDisabled: false
      },
      {
        label: t('contraEntry'),
        icon: <ContraEntry />,
        key: 'contraEntry',
        isDisabled: false
      },
      {
        label: t('journalEntry'),
        icon: <JournalEntry />,
        key: 'journalEntry',
        isDisabled: false
      }

    ]
  }

];
