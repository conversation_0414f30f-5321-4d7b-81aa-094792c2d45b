export const API_URL = {
  COMMON: {
    COUNTRY: 'egov-mdms-service/v1/common-masters/countries',
    COUNTRY_BY_ID: 'egov-mdms-service/v1/common-masters/countries/:id',
    STATE: 'egov-mdms-service/v1/common-masters/states',
    DISTRICT: 'egov-mdms-service/v1/common-masters/districts/:stateId',
    FETCH_TALUK_OFFICECODE: 'egov-mdms-service/v1/common-masters/taluks/office-lb-code/:officeCode',
    FETCH_VILLAGE: 'egov-mdms-service/v1/common-masters/villages/office-lb-code/:officeCode',
    LOCAL_BODY_TYPE: 'egov-mdms-service/v1/common-masters/lb-types',
    LOCAL_BODY_BY_D_BY_LBT: 'egov-mdms-service/v1/common-masters/local-bodies/district/:districtId/:lbTypeId',
    USER_PROFILE: 'egov-mdms-service/v1/common-masters/user-profile',
    WARD: 'dmdm-services/v1/ward/officeLbCode/:officeCode',
    WARD_YEAR: 'dmdm-services/v1/ward/officeLbCode/:officeCode',
    POST_OFFICE: 'dmdm-services/v1/post-offices/office-lb-code/:offLbCode',
    POST_OFFICE_BY_D_ID: 'dmdm-services/v1/post-offices/district/:dId',
    POST_OFFICE_BY_PIN: 'dmdm-services/v1/post-offices/pin-code/:pincode',
    INSTITUTION_TYPE: 'dmdm-services/v1/officeType',
    INSTITUTIONS: 'dmdm-services/v1/organizations/search',
    BANKS: 'fin-master-services/master/bank/all',
    BANK_BRANCH: 'fin-master-services/master/bank/branch',
    GENDER: 'egov-mdms-service/v1/common-masters/genders',
    EDUCATION: 'egov-mdms-service/v1/birth-death-services/qualifications',
    SERVICES: 'egov-mdms-service/v1/common-masters/service',
    MODULES: 'egov-mdms-service/v1/common-masters/modules',
    SUB_MODULES: 'egov-mdms-service/v1/common-masters/sub-modules',
    SUB_MODULES_BY_ID: 'egov-mdms-service/v1/common-masters/sub-module/id/:id',
    MODULES_BY_ID: 'egov-mdms-service/v1/common-masters/modules/:id',
    FILETYPE: 'egov-mdms-service/v1/file-management-service/file-type',
    FETCH_FILES: 'file-management-services/files/generate-report',
    FILEYEAR: 'egov-mdms-service/v1/common-masters/fileyear',
    CORRESPONDTYPE: 'egov-mdms-service/v1/file-management-service/correspondence-type',
    SEAT: 'egov-mdms-service/v1/common-masters/seat',
    SERVICE: 'service',
    LOCAL_BODIES: 'egov-mdms-service/v1/common-masters/local-bodies/district/:districtId/:officeTypeId',
    DELETE_DOCUMENTS: 'inward-management-services/delete-documents',
    DELETE_NOTE_DOCUMENTS: 'file-management-services/v2/notes/delete/:notesId/:noteDocumentId',
    SERVICES_BY_ID: 'egov-mdms-service/v1/common-masters/service/:moduleId/:subModuleId',
    FETCH_DEPARTMENTS: 'egov-mdms-service/v1/common-masters/department?query',
    FETCH_SEATS: 'hrms-services/hrms/functional-group/posts',
    FETCH_STATUS: 'egov-mdms-service/v1/file-management-service/file-status?query',
    FETCH_YEARS: 'egov-mdms-service/v1/finance-services/year',
    WORKFLOW: 'file-management-services/file/workflow/get-workflow/:fileNo',
    ACTION_FILE: 'file-management-services/file/workflow/v2/take-action',
    POST_BY_FUNC_GROUP: 'hrms-services/hrms/functional-group/get-post-for-functional-group',
    FETCH_POST_BY_FUNC_GROUPS: 'hrms-services/hrms/functional-group/get-post-for-functional-groups?officeId=:officeId',
    SAVE_HOLD_FILE: 'file-management-services/files/create-hold',
    FETCH_LOCAL_BODY_PROPERTY_TYPE: 'egov-mdms-service/v1/common-masters/lb-property-type',
    FETCH_FUNCTIONAL_GROUP: 'egov-mdms-service/v1/common-masters/functional-group',
    FETCH_FUNCTIONS: 'egov-mdms-service/v1/common-masters/function/functionalGroupId/:groupId',
    CHECK_FILE_NO: 'file-management-services/files/exists/:fileNo',
    FETCH_FINANCIAL_STATUS: 'egov-mdms-service/v1/common-masters/financial-status',
    FETCH_CATEGORY: 'egov-mdms-service/v1/common-masters/category',
    FETCH_BUILDING_USAGE: 'egov-mdms-service/v1/common-masters/building-usage',
    FETCH_OWNERSHIP: 'egov-mdms-service/v1/common-masters/ownership',
    SUB_MODULES_BY_MODULE_ID: 'egov-mdms-service/v1/common-masters/sub-module/moduleId/:id',
    LOCAL_BODY_BY_OFFICE_CODE: 'dmdm-services/v1/organizations/:officeId',
    FETCH_SERVICE_VALIDATION: 'egov-mdms-service/v1/common-masters/service-general-details/serviceCode/:serviceCode',
    FETCH_DESIGNATION: 'egov-mdms-service/v1/common-masters/designation',
    ROUTE_KEYS: 'egov-mdms-service/v1/common-masters/routekey',
    FILE_DISPOSAL_TYPE: 'egov-mdms-service/v1/file-management-service/disposal-type',
    FETCH_IS_GROUPED_SERVICE: 'file-management-services/finance/is-grouped-service-present/:fileNo',
    GENERATE_AADHAR_OTP: 'inward-management-services/v1/aadhaar/generate-otp-aadhaar',
    VALIDATE_AADHAR_OTP: 'inward-management-services/v1/aadhaar/validate-otp-aadhaar',
    KSMART_SEARCH: 'user-service/v1/search',
    FETCH_SERVICE_ACCOUNT_HEAD: 'egov-mdms-service/v1/common-masters/service-account-head/code/:serviceCode',
    DFMS_SERVICES: 'egov-mdms-service/v1/common-masters/service-general-details?query',
    FUNCTIONAL_GROUP: 'hrms-services/hrms/functional-group/list?officeId',
    FETCH_BENEFICIARY: 'inward-management-services/get-inward-detail',
    FETCH_DOOR_NUMBER: 'egov-mdms-service/v1/property-services/door-numbers-between',
    FETCH_DOOR_KEY: 'egov-mdms-service/v1/property-services/door-numbers-between/routekey-by-value/:doorValue',
    FETCH_COUNTER_OPERATOR_WITH_ROLE: 'hrms-services/hrms-master/posts/hrms-master/posts/get-post-for-role-and-service-route-file',
    FETCH_BILL_TYPE: 'egov-mdms-service/v1/common-masters/bill-type',
    FETCH_ESTABLISHMENT_TYPE: 'egov-mdms-service/v1/common-masters/establishment-type',
    FETCH_MISSION: 'egov-mdms-service/v1/common-masters/mission',
    FETCH_PROFESSION_TAX_TYPE: 'egov-mdms-service/v1/common-masters/profession-tax-type',
    FETCH_TYPE_OF_AUDIT: 'egov-mdms-service/v1/common-masters/type-of-audit',
    FETCH_AMOUNT_FROM_CLAIM: 'egov-mdms-service/v1/common-masters/amount-from-claim',
    FETCH_OCCUPANCY: 'egov-mdms-service/v1/building-permit/sub-occupancy',
    FETCH_ESTIMATE_AMOUNT: 'egov-mdms-service/v1/common-masters/estimate-amount',
    FETCH_BUILDUP_AREA: 'egov-mdms-service/v1/building-permit/built-area-type',
    FETCH_MEETING_TYPE: 'egov-mdms-service/v1/common-masters/meeting-type',
    FETCH_OFFICE_TYPE: 'hrms-services/hrms-master/posts/list-location',
    FETCH_FUND: 'fin-master-services/master/lookup/fund',
    FETCH_NEXT_ROLE_DETAILS: 'file-management-services/file/workflow/v2/get-next-role/:fileNo',
    ACCOUNT_TYPE: 'egov-mdms-service/v1/finance-services/finance-institution-type',
    TREASURY_TYPE: 'fin-master-services/master/lookup/treasury',
    FETCH_POST_BY_LOCATION: 'hrms-services/hrms-master/posts/get-posts-by-location',
    FETCH_RECOVERY_ACCOUNT_HEAD: 'fin-master-services/master/lookup/recovery_account_head',
    FETCH_ACCOUNT_HEAD: 'fin-master-services/master/account-head/id/:id',
    FETCH_BP_ROUTE_KEY: 'bp-services/v1/building-permit-request/file/routeKey',
    FETCH_USERS_BY_FILE_NO: 'hrms-services/hrms-master/posts/hrms-master/posts/get-users-services-list',
    FETCH_HEIGHT: 'egov-mdms-service/v1/building-permit/built-height-type',
    FETCH_CERTIFICATE: 'file-management-services/get-certificate-template-body/:fileNo',
    FETCH_INWARD_USERS: 'inward-management-services/get-inward-assigner/:inwardId',
    FETCH_WASTE_MANAGEMENT_TYPE: 'egov-mdms-service/v1/common-masters/waste-management-type',
    FETCH_ACCOUNT_ID: 'fin-master-services/master/lookup/:lookupType',
    FETCH_POST_ID_BY_PEN: 'hrms-services/hrms-master/posts/get-post-for-pen',
    UPDATE_FILE_FOR_DISPOSAL_OR_NOT: 'file-management-services/file/process/file-disposal',
    FETCH_MODE_OF_DISPATCH: 'egov-mdms-service/v1/file-management-service/dispatch-mode',
    FETCH_DISPATCH_CLERK_DETAILS: 'hrms-services/hrms-master/post-tagging/list-post-tag-functionalgroup',
    FETCH_LSGI_TYPE: 'egov-mdms-service/v1/common-masters/lsgi-type',
    FETCH_REGION_TYPE: 'egov-mdms-service/v1/common-masters/region-type',
    FETCH_BUILDING_PROJECT_TYPE: 'egov-mdms-service/v1/building-permit/built-project-type',
    ALL_DOCUMENTS: 'egov-mdms-service/v1/file-management-service/document-type',
    FETCH_OFFICE_WISE_POST_DETAILS: 'hrms-services/hrms-master/posts/get-posts-by-office'
  },
  CITIZEN: {
    CREATE_E_FILE: 'inward-management-services/create-efile',
    SAVE_E_FILE_APPLICANT_DETAILS: 'inward-management-services/save-efile-applicant-details',
    PREVIEW_E_FILE: 'inward-management-services/preview-e-file/:id',
    EFILE_DECLARATION: 'inward-management-services/save-efile-declaration/:id?declaration=:declarationValue',
    COMPLETE_SAVE: 'inward-management-services/efile-completion-save/:id',
    GET_USER: 'user-service/v1/getUser/:userId',
    EPAY_GENERATE: 'inward-management-services/epay/create/:officeId/:inwardId',
    SEND_OTP: 'inward-management-services/otp/send',
    VERIFY_OTP: 'inward-management-services/otp/verify/:inwardId',
    RESUBMIT_EFILE: 'inward-management-services/file/resubmit'
  },
  COUNTER: {
    SAVE_APPLICATION: 'inward-management-services/save-services',
    UPDATE_APPLICATION: 'inward-management-services/save-applicant-details',
    FETCH_APPLICATION: 'inward-management-services/get-inward-detail?inwardId=inwardIdParams',
    SERVICE_BY_SERVICE_CODE: 'egov-mdms-service/v1/common-masters/service/:code',
    SAVE_DOCUMENTS: 'inward-management-services/upload-supporting-documents',
    SAVE_MANDATORY_DOCUMENTS: 'inward-management-services/upload-documents',
    DOCUMENT_TYPES: 'egov-mdms-service/v1/common-masters/attachment',
    SAVE_GENERAL_DETAILS: 'inward-management-services/save-general-details',
    SAVE_COMPLETE: 'inward-management-services/inward-completion-save',
    DELETE_APPLICANT: 'inward-management-services/delete-applicant-details',
    GENERATE_DEMANT: 'inward-management-services/inward-demand-save',
    UPDATE_DOCUMENTS: 'inward-management-services/update-documents'
  },
  ARISING: {
    SAVE_APPLICATION: 'inward-management-services/save-services',
    UPDATE_APPLICATION: 'arising/update/:id'
  },
  LEGACYFILES: {
    SAVE_APPLICATION: 'inward-management-services/legacyfile/create',
    UPDATE_APPLICATION: 'inward-management-services/legacyfile/update/:id',
    FETCH_LEGACY_FILE: 'inward-management-services/legacyfile/get-legacyfile?query',
    DELETE_LEGACY_FILE: 'inward-management-services/legacyfile/delete-legacyfile/:id',
    CREATE_LEGACY_FILE: 'inward-management-services/file/create',
    UPLOAD_DOCUMENTS: 'inward-management-services/legacyfile/upload-legacy-documents',
    DELETE_DOCUMENT: 'inward-management-services/legacyfile/delete-legacy-documents'
  },
  DASHBOARD: {
    FETCH_SERVICE_LIST: 'file-management-services/search-homepage',
    VERIFICATION: 'employee-services/auth/verify-otp',
    LOGIN_CITIZEN: 'user-service/v1/login',
    SEND_OTP: 'employee-services/auth/generate-otp',
    FETCH_DISPOSED_LIST: 'file-management-services/search-homepage',
    DISPOSE_FILES: 'file-management-services/file/process/file-dispose'
  },
  INWARD: {
    FETCH_INWARD: 'inward-management-services/get-inward?query',
    CREATE_FILE: 'inward-management-services/file/create',
    PULL_FILE: 'file-management-services/files/pullfile',
    INWARD_DETAILS: 'inward-management-services/get-file-detail'
  },
  INBOX: {
    ENQUIRY: {
      SAVE_ENQUIRY: 'file-management-services/enquiry/save',
      SAVE_ENQUIRY_DOC: 'file-management-services/enquiry/upload-enquiry-documents',
      FETCH_ENQUIRY_DETAILS: 'file-management-services/enquiry/search?query',
      UPDATE_ENQUIRY_TEXT: 'file-management-services/enquiry/update/:fileNo',
      FETCH_ENQUIRY_DOC: 'file-management-services/enquiry/search-document?query',
      UPDATE_ENQUIRY_DOC: 'file-management-services/enquiry/add-document/:fileNo'
    },
    FETCH_INBOX: 'file-management-services/files/get-files?query',
    FETCH_FILE_DETAILS: 'file-management-services/files/search/:id',
    FETCH_SUMMARY_DETAILS_BY_ID: 'inward-management-services/get-inward-detail?query',
    SERVICE_BY_SERVICE_CODE: 'egov-mdms-service/v1/common-masters/service/:code',
    FETCH_APPLICANT_DETAILS_BY_ID: 'file-management-services/applicant-address?query',
    FETCH_PENDING_ACTIONS_BY_ID: 'pending-actions/:id',
    SAVE_DRAFT: 'file-management-services/create-draft',
    EDIT_DRAFT: 'file-management-services/update-draft-and-address/:draftid',
    SENDERS: 'egov-mdms-service/v1/file-management-service/sender',
    RECEIVER: 'egov-mdms-service/v1/file-management-service/receiver',
    FETCH_APPLICANT_BY_ID: 'file-management-services/same-as-applicant-address?query',
    SAVE_ADDRESS: 'file-management-services/save-copy-to-address',
    SAVE_ENCLOSURE: 'file-management-services/upload-enclosure-document',
    WARDMEMBER: 'ward-member-details/wardId',
    FETCH_NOTES: 'file-management-services/v2/notes/search',
    FETCH_ALL_NOTES: 'file-management-services/v2/notes/search-notes-from-and-to-note-no',
    SAVE_UN_HOLD_FILE: 'file-management-services/files/unhold',
    SAVE_NOTE_DOCUMENTS: '',
    SAVE_NOTE: 'file-management-services/v2/notes/upload-notes-documents',
    FETCH_LINKED_MERGED: 'file-management-services/file/process/search-fileprocess/:fileNo',
    FETCH_DRAFT: 'file-management-services/get-drafts/:fileNo',
    FETCH_DRAFT_BY_ID: 'file-management-services/view-draft?query',
    MERGE_FILES: 'file-management-services/file/process/merging',
    LINK_FILES: 'file-management-services/file/process/link-file',
    FUNCTIONS: 'egov-mdms-service/v1/common-masters/function',
    SAVE_NOTE_WITHOUT_DOC: 'file-management-services/v2/notes/save',
    FETCH_FILE_DOCUMENTS: 'file-management-services/files/preview-all/:fileNo',
    FETCH_DRAFT_PREVIEW: 'file-management-services/draft-pdf',
    FETCH_PARTIAL_NOTES: 'file-management-services/v2/notes/search-partialnote',
    DRAFT_EXISTS_OR_NOT: 'file-management-services/drafts-exists/:fileNo/button',
    MAPPED_SEATS: 'hrms-services/hrms/functional-group/get-functional-group-for-service',
    SAVE_CHILD: 'file-management-services/file/process/child-file',
    SAVE_DRAFT_RETURN: 'file-management-services/return-draft/:draftId',
    FINANCE_EXISTS_OR_NOT: 'fin-payorder-services/common/finance/file-details',
    FETCH_CHILD_FILE: 'file-management-services/file/process/child-file/:fileNo',
    UPDATE_CUSTODIAN_CHANGE: 'inward-management-services/custodian-change',
    DE_LINK_INWARD: 'inward-management-services/delink-inward-details',
    SAVE_BENEFICIARY: 'file-management-services/beneficiary/save-beneficiary-details',
    UPDATE_BENEFICIARY: 'file-management-services/beneficiary/update-beneficiary-details',
    FETCH_BENEFICIARY: 'file-management-services/beneficiary/get-beneficiary',
    FETCH_BENEFICIARY_BY_ID: 'file-management-services/beneficiary/get-beneficiary-details/:id',
    DELETE_BENEFICIARY: 'file-management-services/beneficiary/delete-beneficiary-details/:fileNo',
    FETCH_SERVICE_POST_ROUTES: 'hrms-services/hrms-master/service/get-servicepost-routes',
    LIST_COMPLETED_NOTES: 'file-management-services/v2/notes/list-completed-notes',
    LIST_COMPLETED_NOTES_DOCUMENTS: 'file-management-services/v2/notes/list-uploaded-notes-documents',
    DELETE_DRAFT: 'file-management-services/delete-draft/:fileNo/:draftId',
    DELETE_NOTE_REF: 'file-management-services/v2/notes/remove-reference/:notesId/:referenceId',
    DRAFT_ACTIONS: 'file-management-services/draft-take-action/:id',
    DRAFT_MAKE_INACTIVE: 'file-management-services/toggle-draft-is-active',
    FETCH_PENDING_DRAFT_NO: 'file-management-services/get-pending-draft-no/:fileNo',
    FETCH_NOTES_FOR_FILE_SEARCH_SUMMARY: 'file-management-services/v2/notes/search-notes-for-file-search',
    FETCH_NOTES_FOR_FILE_SEARCH_NOTE_PAGE: 'file-management-services/v2/notes/search-notes-file-search-from-and-to-note-no',
    FETCH_DRAFT_FROM_FILE_SEARCH: 'file-management-services/get-drafts-for-file-search/:fileNo',
    FETCH_DOCUMENTS_FROM_FILE_SEARCH: 'file-management-services/files/preview-all-for-file-search/:fileNo',
    FETCH_E_SIGN: 'esign-service/esign/create',
    FETCH_E_SIGN_STATUS_CHECK: 'esign-service/esign/searchdoc',
    FETCH_FORWARD_PLUS_ROLE_FOR_DRAFT_ACTIONS: 'file-management-services/get-drafts-role-for-forward-plus-user/:fileNo'
  },
  MERGE: {
    FETCH_MERGE_SAME_SEAT_FILES: 'file-management/merge/same-seat-files',
    MERGED_FILES: 'file-management-services/file/process/search-merging',
    UN_MERGE_FILES: 'file-management-services/file/process/unmerge',
    UN_LINK_FILES: 'file-management-services/file/process/unlink-file'
  },
  REPORTS: {
    FETCH_STATUS_REPORT: 'fetchTable',
    FETCH_DISTRICBUTION_REGISTER_REPORT: 'inward-management-services/search-inward?query',
    FETCH_PENDING_FILE_REPORT: 'fetchPendingFileTable',
    FETCH_FILE_ABSTRACT_REPORT: 'file-management-services/file-extract-report/:officeId/:fileNo',
    FETCH_CASH_DECLARATION_REPORT: 'file-management-services/fetch-cash',
    FETCH_STATUS_LOG_REPORT: 'file-management-services/files/search-file-movement',
    FILE_LOG_GENERATE_REPORTS: 'file-management-services/file-log-pdf',
    CASH_DECLARATION_GENERATE_REPORTS: 'file-management-services/cash-declaration-pdf',
    DISTRIBUTION_REGISTAR_REPORT_PDF: 'inward-management-services/distribution-register-pdf',
    PERSONAL_REGISTER_REPORT_PDF: 'file-management-services/generate-personal-register-pdf'
  },
  SEARCH_FILES: {
    FETCH_SEARCH_FILES: 'file-management-services/files/files-search',
    MY_FILES: 'inward-management-services/my-files'
  },
  PULL: {
    FETCH_PULL_DRAFT: 'file-management-services/search-draft',
    SAVE_PULL_NOTE: '',
    SEARCH_PULL_DRAFT: ''
  },
  PULL_FILE: {
    FETCH_PULL_FILES: 'file-management-services/files/pull-files-search',
    UPDATE_PULL_FILE: 'file-management-services/files/pull-file'
  },
  FILE_RE_OPEN: {
    RE_OPEN: 'file-management-services/file/process/file-reopen'
  },
  INWARD_SEARCH: {
    FETCH_INWARD_SEARCH_LIST: 'inward-management-services/search-inward?query'
  },
  E_SIGN: {
    SIGN_PDF: 'signpdf',
    SAVE_SIGNED_DRAFT: 'file-management-services/draft-digital-sign-upload/:fileNo/:draftId',
    LOCAL_ENROLLMENT: 'dssignature',
    FETCH_ALL_ENROLL: 'file-management-services/get-e-sign-registration-details',
    SAVE_DS_ENROLL: 'file-management-services/e-sign-registration',
    DATE_CHECK: 'file-management-services/client-datetime-check',
    SAVE_E_SIGNED_DRAFT: 'file-management-services/draft-e-sign-upload/:fileNo/:draftId',
    GET_E_SIGNED_DATA: 'esign-service/esign/download',
    FETCH_E_SIGN_REQUESTS: 'esign-service/kyc/get-user-details/:officeId'
  },
  PUBLIC_FILE_TRACKING: 'file-management-services/files/file-tracker',
  RE_ASSIGN_FILES: 'file-management-services/reassign-files',
  KSWIFT: {
    LOGIN: 'user-service/v1/k-swift-user/login/:userId/:serviceCode'
  },
  DISPATCH: {
    STAMP_INVENTORY: 'file-management-services/stamp/search-stamp',
    FETCH_CASH_DENOMINATION: 'egov-mdms-service/v1/file-management-service/cash-declaration',
    SAVE_STAMP_OPENING_DETAILS: 'file-management-services/stamp/save-opening',
    STAMP_DETAILS: 'file-management-services/stamp/search-stamp-account',
    SAVE_STAMP_DETAILS: 'file-management-services/stamp/save-details',
    DRAFT_SENT_LETTER_LIST: 'file-management-services/dispatch/get-drafts'
  },
  RECORD_ROOM: {
    FETCH_INBOX: 'inward-management-services/records/inbox',
    FETCH_OUTBOX: 'inward-management-services/records/outbox', // not get
    FETCH_INWARD_DETAILS: 'inward-management-services/records/list-documents',
    SEND_OTP: 'inward-management-services/records/otp/send',
    VERIFY_OTP: 'inward-management-services/records/otp/verify/:inwardId',
    ASSIGN_RECORD_ROOM_FILE: 'inward-management-services/records/assign' // not get
  },
  ESIGN: {
    SAVE_APPLICATION: 'esign-service/kyc/create',
    E_SIGN_SEND_FOR_APPROVAL: 'esign-service/kyc/update/:id',
    E_SIGN_APPROVE_REQUEST: 'esign-service/kyc/enroll/:userId',
    FETCH_APPLICATION: 'esign-service/kyc/search-user-details-by-id/:id',
    SERVICE_BY_SERVICE_CODE: 'egov-mdms-service/v1/common-masters/service/:code',
    SAVE_DOCUMENTS: 'esign-service/upload-supporting-documents',
    SAVE_MANDATORY_DOCUMENTS: 'esign-service/upload-documents',
    DOCUMENT_TYPES: 'egov-mdms-service/v1/common-masters/attachment',
    SAVE_GENERAL_DETAILS: 'esign-service/save-general-details',
    SAVE_COMPLETE: 'esign-service/inward-completion-save',
    DELETE_APPLICANT: 'esign-service/delete-applicant-details',
    GENERATE_DEMANT: 'esign-service/inward-demand-save',
    UPDATE_DOCUMENTS: 'esign-service/update-documents'
  }
};
