/* eslint-disable no-misleading-character-class */

export const ML = /[\u0D02\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D28\u0D2A-\u0D39\u0D3E-\u0D43\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D60\u0D61\u0D66-\u0D6F]/;
export const ML_NUMERIC = /^[\u0D02\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D28\u0D2A-\u0D39\u0D3E-\u0D43\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D60\u0D61\u0D66-\u0D6F 0-9 /\s/g]*$/;

export const ML_SPECIAL = /^[\u0D00-\u0D7F!@#$%^&*]+$/;
export const EN_NUMERIC_SPECIAL = /^([a-zA-Z])[a-zA-Z0-9-]*$/;
export const EN = /^[A-Za-z /\s/g]*$/;
export const EN_NUMERIC = /^[A-Za-z0-9 /\s/g]*$/;
export const EN_SPECIAL = /^[a-zA-Z!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?\s]+$/;
export const EN_SPACE = /^[a-zA-Z\s]*$/;

export const NUMERIC = /^[0-9]*$/;
export const AADHAAR = /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/;
export const PASSPORT = /^[A-Z][0-9]{8}$/;
export const MOBILE = /^(\+\d{1,3}[- ]?)?\d{10}$/;
// export const MOBILE = /^[5-9]{1}[0-9]/;
export const MOBILE_INTERNATIONAL = /^(\+\d{1,3}[- ]?)?\d{9}$/;
export const UDID = /^[A-Z]{2}\d{16}$/;
export const NAME = /^[A-Za-z]+$/;
export const EMAIL = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
export const DATE = /^\d{2}-\d{2}-\d{4}$/;

export const EN_NUMERIC_LIMITED_SPECIAL = /^[a-zA-Z0-9/\-.]+$/;
export const EN_SPECIAL_NUMERIC = /^[0-9a-zA-Z\s!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]+$/;
export const ML_SPECIAL_NUMERIC = /^[0-9\u0D02\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D28\u0D2A-\u0D39\u0D3E-\u0D43\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D60\u0D61\u0D66-\u0D6F /!@#$ %^&* ()_ +=\-[\]{}; ':"\\|,.<>/?]+$/;
export const POSTAL_CODE_OUTSIDE = /^[0-9/\-_.]+$/;
export const IMAGE_TYPE = /(\.jpg|\.jpeg|\.bmp|\.gif|\.png|\.pdf)$/i;

export const FILE_NO = /^[0-9]*-[0-9]{4}$/;
export const DOOR__NUM_ONLY = /[^0-9/-/-\s]/g;
export const SMALL_LETTERS = /[a-z]/g;
export const DOOR_SUB_NUM = /[^[^0-9/()a-zA-Z-/\s]/g;
export const ML_ONLY = /[^'\u0D00-\u0D7F\s]/g;
export const ENG_ONLY = /[^a-zA-Z\s]/g;
export const EMAIL_ONLY = /[^a-zA-Z-0-9/@/./!#$%&'*+-/=?^_`{|s]/g;
export const MOBILE_ONLY = /^[^6-9]/;
export const DOOR_NO = /^[^1-9]/;
export const SUB_NO = /^[\W_].+$/;
export const FIRST_CAPS = /\b\w/g;
export const ACCOUNT_NUMBER = /^([^0-9]*[0-9]){9,18}[^0-9]*$/;
export const INCOME_LIMIT = /^[1-9][0-9]+$/;
export const ENG_NUMBER = /[\w\s]/g;

export const STATUS = /[^a-zA-Z ]/g;
export const NUMBER_ONLY = /[0-9]/;
