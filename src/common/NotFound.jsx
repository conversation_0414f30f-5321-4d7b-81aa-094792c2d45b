import { Button } from '@ksmartikm/ui-components';
import Oops from 'assets/Oops';
import React from 'react';

const PageNotFound = () => (
  <svg width="1728" height="485" viewBox="0 0 1728 485" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1682.04 475.257V397.664H1769.98V475.257H1682.04ZM1682.04 358.868V9.69916H1769.98V358.868H1682.04Z" fill="url(#paint0_linear_14694_26150)" fillOpacity="0.04" />
    <path d="M1474.52 484.956C1440.03 484.956 1408.89 478.921 1381.08 466.851C1353.5 454.566 1330.76 437.107 1312.87 414.476C1295.19 391.629 1283.99 364.472 1279.24 333.003L1371.06 319.425C1377.53 346.151 1390.89 366.735 1411.15 381.176C1431.41 395.617 1454.47 402.837 1480.34 402.837C1494.78 402.837 1508.79 400.574 1522.37 396.048C1535.95 391.521 1547.05 384.84 1555.67 376.003C1564.51 367.166 1568.92 356.281 1568.92 343.349C1568.92 338.607 1568.17 334.081 1566.66 329.77C1565.37 325.244 1563.1 321.041 1559.87 317.161C1556.64 313.282 1552 309.618 1545.97 306.169C1540.15 302.721 1532.71 299.595 1523.66 296.793L1402.75 261.23C1393.69 258.643 1383.13 254.979 1371.06 250.237C1359.21 245.496 1347.68 238.706 1336.47 229.869C1325.26 221.032 1315.88 209.393 1308.34 194.952C1301.01 180.296 1297.35 161.868 1297.35 139.667C1297.35 108.415 1305.22 82.4426 1320.95 61.7512C1336.68 41.0597 1357.7 25.6489 1383.99 15.5187C1410.29 5.38849 1439.39 0.431144 1471.29 0.64669C1503.4 1.07776 1532.07 6.57393 1557.29 17.1352C1582.5 27.6965 1603.63 43.1073 1620.65 63.3677C1637.68 83.4126 1649.97 107.984 1657.51 137.081L1562.46 153.246C1559.01 138.159 1552.65 125.442 1543.38 115.096C1534.11 104.751 1523.01 96.8836 1510.08 91.4952C1497.37 86.1068 1484 83.197 1469.99 82.7659C1456.2 82.3349 1443.16 84.2747 1430.87 88.5854C1418.8 92.6806 1408.89 98.7156 1401.13 106.69C1393.59 114.665 1389.81 124.149 1389.81 135.141C1389.81 145.271 1392.94 153.57 1399.19 160.036C1405.44 166.286 1413.31 171.351 1422.79 175.231C1432.27 179.111 1441.97 182.344 1451.89 184.93L1532.71 206.915C1544.78 210.148 1558.15 214.459 1572.8 219.847C1587.46 225.02 1601.47 232.348 1614.83 241.832C1628.41 251.1 1639.51 263.385 1648.13 278.688C1656.97 293.991 1661.39 313.39 1661.39 336.883C1661.39 361.885 1656.11 383.762 1645.55 402.514C1635.2 421.05 1621.19 436.461 1603.52 448.746C1585.84 460.816 1565.8 469.869 1543.38 475.904C1521.18 481.939 1498.23 484.956 1474.52 484.956Z" fill="url(#paint1_linear_14694_26150)" fillOpacity="0.04" />
    <path d="M902.961 475.257V9.69916H1099.53C1104.06 9.69916 1110.09 9.91469 1117.63 10.3458C1125.39 10.5613 1132.29 11.2079 1138.33 12.2856C1166.13 16.5963 1188.87 25.7566 1206.54 39.7664C1224.43 53.7763 1237.58 71.4502 1245.99 92.7883C1254.39 113.911 1258.6 137.512 1258.6 163.592C1258.6 189.672 1254.28 213.381 1245.66 234.719C1237.26 255.841 1224.11 273.408 1206.22 287.417C1188.55 301.427 1165.92 310.588 1138.33 314.898C1132.29 315.76 1125.39 316.407 1117.63 316.838C1109.88 317.269 1103.84 317.485 1099.53 317.485H990.9V475.257H902.961ZM990.9 235.365H1095.65C1100.18 235.365 1105.13 235.15 1110.52 234.719C1115.91 234.288 1120.87 233.426 1125.39 232.132C1137.25 228.899 1146.41 223.511 1152.88 215.967C1159.34 208.208 1163.76 199.694 1166.13 190.426C1168.72 180.943 1170.01 171.998 1170.01 163.592C1170.01 155.186 1168.72 146.349 1166.13 137.081C1163.76 127.597 1159.34 119.084 1152.88 111.54C1146.41 103.781 1137.25 98.2845 1125.39 95.0515C1120.87 93.7582 1115.91 92.8961 1110.52 92.465C1105.13 92.0339 1100.18 91.8184 1095.65 91.8184H990.9V235.365Z" fill="url(#paint2_linear_14694_26150)" fillOpacity="0.04" />
    <path d="M656.647 484.956C610.091 484.956 569.894 474.826 536.054 454.566C502.431 434.305 476.459 405.962 458.138 369.537C440.033 333.111 430.98 290.758 430.98 242.478C430.98 194.198 440.033 151.845 458.138 115.42C476.459 78.9939 502.431 50.651 536.054 30.3906C569.894 10.1302 610.091 -3.05176e-05 656.647 -3.05176e-05C703.203 -3.05176e-05 743.292 10.1302 776.916 30.3906C810.755 50.651 836.727 78.9939 854.832 115.42C873.153 151.845 882.313 194.198 882.313 242.478C882.313 290.758 873.153 333.111 854.832 369.537C836.727 405.962 810.755 434.305 776.916 454.566C743.292 474.826 703.203 484.956 656.647 484.956ZM656.647 402.19C686.175 402.621 710.746 396.263 730.36 383.115C749.974 369.968 764.63 351.324 774.329 327.184C784.244 303.044 789.201 274.809 789.201 242.478C789.201 210.148 784.244 182.128 774.329 158.419C764.63 134.71 749.974 116.282 730.36 103.134C710.746 89.9863 686.175 83.1969 656.647 82.7658C627.118 82.3347 602.547 88.6931 582.933 101.841C563.32 114.988 548.555 133.632 538.641 157.772C528.942 181.912 524.092 210.148 524.092 242.478C524.092 274.809 528.942 302.828 538.641 326.537C548.555 350.246 563.32 368.674 582.933 381.822C602.547 394.97 627.118 401.759 656.647 402.19Z" fill="url(#paint3_linear_14694_26150)" fillOpacity="0.04" />
    <path d="M184.666 484.956C138.11 484.956 97.913 474.826 64.0738 454.566C30.4502 434.305 4.4781 405.962 -13.8425 369.537C-31.9475 333.111 -41 290.758 -41 242.478C-41 194.198 -31.9475 151.845 -13.8425 115.42C4.4781 78.994 30.4502 50.651 64.0738 30.3906C97.913 10.1302 138.11 0 184.666 0C231.222 0 271.312 10.1302 304.935 30.3906C338.775 50.651 364.747 78.994 382.852 115.42C401.172 151.845 410.333 194.198 410.333 242.478C410.333 290.758 401.172 333.111 382.852 369.537C364.747 405.962 338.775 434.305 304.935 454.566C271.312 474.826 231.222 484.956 184.666 484.956ZM184.666 402.19C214.195 402.621 238.766 396.263 258.38 383.115C277.993 369.968 292.65 351.324 302.349 327.184C312.264 303.044 317.221 274.809 317.221 242.478C317.221 210.148 312.264 182.128 302.349 158.419C292.65 134.71 277.993 116.282 258.38 103.134C238.766 89.9863 214.195 83.1969 184.666 82.7658C155.138 82.3348 130.567 88.6931 110.953 101.841C91.3392 114.988 76.5749 133.632 66.6603 157.772C56.9611 181.912 52.1116 210.148 52.1116 242.478C52.1116 274.809 56.9611 302.828 66.6603 326.537C76.5749 350.246 91.3392 368.674 110.953 381.822C130.567 394.97 155.138 401.759 184.666 402.19Z" fill="url(#paint4_linear_14694_26150)" fillOpacity="0.04" />
    <defs>
      <linearGradient id="paint0_linear_14694_26150" x1="1726.01" y1="9.69916" x2="1726.01" y2="735.512" gradientUnits="userSpaceOnUse">
        <stop />
        <stop offset="1" stopOpacity="0" />
      </linearGradient>
      <linearGradient id="paint1_linear_14694_26150" x1="1470.32" y1="0.639954" x2="1470.32" y2="755.697" gradientUnits="userSpaceOnUse">
        <stop />
        <stop offset="1" stopOpacity="0" />
      </linearGradient>
      <linearGradient id="paint2_linear_14694_26150" x1="1080.78" y1="9.69916" x2="1080.78" y2="735.512" gradientUnits="userSpaceOnUse">
        <stop />
        <stop offset="1" stopOpacity="0" />
      </linearGradient>
      <linearGradient id="paint3_linear_14694_26150" x1="656.647" y1="-3.05176e-05" x2="656.647" y2="756.055" gradientUnits="userSpaceOnUse">
        <stop />
        <stop offset="1" stopOpacity="0" />
      </linearGradient>
      <linearGradient id="paint4_linear_14694_26150" x1="184.666" y1="0" x2="184.666" y2="756.055" gradientUnits="userSpaceOnUse">
        <stop />
        <stop offset="1" stopOpacity="0" />
      </linearGradient>
    </defs>
  </svg>

);

const NotFound = () => {
  return (
    <div>
      <div className="notFoundContainer">
        <div className="notFoundSvg" style={{ height: '70%' }}>
          <PageNotFound />
        </div>
        <div className="notFoundContent">
          <div className="notFoundContentBody">
            <Oops />
            <span className="text-[#232F50] text-[101.51px] leading-[101.51px] font-extrabold pt-3">OOPS !</span>
            <span className="text-[#232F50] text-[48px] font-bold pb-3">something went wrong</span>
            <span className="notFoundContentSubHead">We’re trying to fix the problem, It might take a few seconds </span>
            <div className="flex gap-4">
              <Button variant="secondary_outline" borderRadius="full" marginTop="7" style={{ background: 'none' }}>
                Go to Home
              </Button>
              <Button variant="secondary" borderRadius="full" marginTop="7">
                Go Back
              </Button>
            </div>

          </div>

        </div>
      </div>
    </div>
  );
};

export default NotFound;
