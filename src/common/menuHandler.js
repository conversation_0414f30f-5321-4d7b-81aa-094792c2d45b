import { FILE_ROLE, FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';

// export const draftCreateMenu = (status, stage, role, route) => {
//   if (status === FILE_STATUS_FOR_API_PARAMS.CLOSED || status === FILE_STATUS_FOR_API_PARAMS.HOLD) {
//     return false;
//   } if (stage === FILE_STATUS_FOR_API_PARAMS.ROUTE_CHANGE) {
//     if (role !== FILE_ROLE.ROUTE_CHANGE_ROLE) {
//       return true;
//     }
//     return false;
//   }
//   if (role === FILE_ROLE.ROUTE_CHANGE_ROLE) {
//     return false;
//   }
//   if (route === 'routechange') {
//     return false;
//   }
//   return true;
// };

// export const draftEditMenu = (status, stage, role, route, isEdit) => {
//   if (status === FILE_STATUS_FOR_API_PARAMS.CLOSED || status === FILE_STATUS_FOR_API_PARAMS.HOLD) {
//     return false;
//   } if (stage === FILE_STATUS_FOR_API_PARAMS.ROUTE_CHANGE) {
//     if (role !== FILE_ROLE.ROUTE_CHANGE_ROLE && isEdit) {
//       return true;
//     }
//     return false;
//   }
//   if (role === FILE_ROLE.ROUTE_CHANGE_ROLE) {
//     return false;
//   }
//   if (route === 'routechange') {
//     return false;
//   }
//   if (isEdit) {
//     return true;
//   }
//   return false;
// };

export const draftCreateMenu = (status, role, route) => {
  const { CLOSED, HOLD } = FILE_STATUS_FOR_API_PARAMS;
  const { ENQUIRY_OFFICER } = FILE_ROLE;

  if (status === CLOSED || status === HOLD || route === 'routechange' || role === ENQUIRY_OFFICER) {
    return false;
  }
  return true;
};

export const draftEditMenu = (fileDetails, route, item) => {
  const { CLOSED, HOLD, ROUTE_CHANGE } = FILE_STATUS_FOR_API_PARAMS;
  const { ROUTE_CHANGE_ROLE } = FILE_ROLE;

  if (fileDetails?.status === CLOSED || fileDetails?.status === HOLD || route === 'routechange') {
    return false;
  } if (fileDetails?.stage === ROUTE_CHANGE && fileDetails?.role !== ROUTE_CHANGE_ROLE && item?.isEditable) {
    return true;
  } if (item?.isDigitalSIgned) {
    return false;
  } if (!item?.isDigitalSIgned && item?.status === 'APPROVED' && item?.postId === fileDetails?.postId) {
    return true;
  }
  return item?.isEditable;
};

export const generalFeatureMenu = (status, role, route, postIdArray, postId) => {
  const { CLOSED, HOLD } = FILE_STATUS_FOR_API_PARAMS;
  const { ENQUIRY_OFFICER } = FILE_ROLE;

  if (status === CLOSED || status === HOLD || role === ENQUIRY_OFFICER || route === 'routechange') {
    return true;
  }

  // if (stage === ROUTE_CHANGE && role === ROUTE_CHANGE_ROLE) {
  //   return true;
  // }

  if (postIdArray?.includes(postId)) {
    return false;
  }

  // if (role !== ROUTE_CHANGE_ROLE) {
  //   return true;
  // }
  return true;
};

// export const generalFeatureMenu = (status, stage, role, route) => {
//   if (status === FILE_STATUS_FOR_API_PARAMS.CLOSED || status === FILE_STATUS_FOR_API_PARAMS.HOLD) {
//     return false;
//   } if (role === FILE_ROLE.ENQUIRY_OFFICER) {
//     return false;
//   }
//   if (stage === FILE_STATUS_FOR_API_PARAMS.ROUTE_CHANGE) {
//     if (role !== FILE_ROLE.ROUTE_CHANGE_ROLE) {
//       return true;
//     }
//     return false;
//   }
//   if (role === FILE_ROLE.ROUTE_CHANGE_ROLE) {
//     return false;
//   }
//   if (route === 'routechange') {
//     return false;
//   }
//   return true;
// };
