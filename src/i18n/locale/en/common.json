{"welcome": "Welcome", "english": "English", "malayalam": "Malayalam", "number": "Number", "name": "Name", "place": "Place", "search": "Search", "searchHere": "Search Here..", "localBodyType": "Local Body Type", "localBody": "Local Body", "documentType": "Document Type", "advancedSearch": "Advanced Search", "country": "Country", "state": "State", "district": "District", "first": "First", "middle": "Middle", "last": "Last", "house": "House", "ward": "Ward", "door": "Door", "sub": "Sub", "postOffice": "Post Office", "pinCode": "Pin Code", "street": "Street", "localPlace": "Local Place", "locality": "Locality", "mainPlace": "Main Place", "emailId": "Email ID", "mobile": "Mobile", "whatsapp": "Whatsapp", "aadhar": "<PERSON><PERSON><PERSON>", "udid": "UDID", "passport": "Passport", "services": "Services", "details": "Details", "applicant": "Applicant", "document": "Document", "documents": "Documents", "module": "<PERSON><PERSON><PERSON>", "file": "File", "type": "Type", "title": "Title", "description": "Description", "application": "Application", "required": "Required", "guidelines": "Guidelines", "quickAccess": "Quick Access", "process": "Process", "next": "Next", "save": "Save", "useAlphabetsOnly": "Use English only", "useNumbersOnly": "Use Numbers Only", "useAlphaAndNumOnly": "Use English and Numbers Only", "institution": "Institution", "reference": "Reference", "date": "Date", "officer": "Officer", "designation": "Designation", "landLine": "Land Line", "main": "Main", "dashboard": "Dashboard", "report": "Report", "settings": "Settings", "help": "Help", "logoutAccount": "<PERSON><PERSON><PERSON> Account", "to": "To", "from": "From", "keyword": "Keyword", "attachDocuments": "Attach Documents", "department": "Department", "assignee": "Assignee", "select": "Select", "correspondence": "Type of Correspondence", "subject": "Subject", "enter": "Enter", "sender": "Sender", "office": "Office", "address": "Address", "receiver": "Receiver", "enclosure": "Enclosure", "sortByDate": "Sort by Date", "wireframe": "Wireframe", "seat": "<PERSON><PERSON>", "note": "Note", "inEnglishRequired": "in English Required", "inMalayalamRequired": "in Malayalam Required", "toDateGreaterThanFromDateError": "Date must be greater than From date.", "actions": "Actions", "assigneeName": "Assignee Name", "functionalGroup": "Functional Group", "function": "Function", "fileContent": "File Content", "comment": "Comment", "childFile": "Child File", "merge": "Merge File", "link": "Link File", "notAddedReference": "Reference is not Added", "referenceErrorMsg": "Please add the reference", "cashDeclaration": "Cash Declaration", "personalCashInCustody": "Personal Cash In Custody", "employeeName": "Employee Name", "timeOfEntry": "Time of Entry", "remarks": "Remarks", "createDraft": "Create Draft", "searchLinkFile": "Search File to be Linked", "searchFileError": "Search file with same Service", "pleaseSelect": "Please Select", "atleastOne": "Atleast One", "pleaseSelectAction": "Please Select Action"}