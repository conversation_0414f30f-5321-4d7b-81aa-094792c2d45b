{"saved": "Saved", "success": "Success", "fileUploadSuccess": "File Uploaded Successfully", "error": "Error", "fileUploadFailed": "File Upload Failed", "fileDeletedSuccess": "File Deleted Successfully", "fileDeletedFailed": "File Delete Failed", "supportingDocuments": "Supporting Documents", "save": "Save", "cancel": "Cancel", "holdFile": "Hold File", "holdReason": "Hold Reason", "toDate": "To Date", "unHoldFile": "UnHold File", "no": "No", "yes": "Yes", "firstName": "First Name", "lastName": "Last Name", "middleName": "Middle Name", "houseName": "House Name", "local": "(Malayalam)", "jointApplication": "Joint Application", "addApplicant": "Add Applicant", "updateApplicant": "Update Applicant", "general": "General", "category": "Category", "financialStatus": "Financial Status", "ownership": "Ownership", "bank": "Bank", "accountNo": "Account No", "educationalQualification": "Educational Qualification", "ifsc": "IFSC", "dateOfBirth": "Date of Birth", "income": "Income", "doorNo": "Door No", "subNo": "Sub No", "enterDescription": "Enter Description", "legacyFileNo": "Legacy File No", "applicantName": "Applicant Name", "servicee": "Service", "createFile": "Create File", "gender": "Gender", "branch": "Branch", "residenceOwnership": "Residence/Institution Ownership", "cashDeclaration": "Cash Declaration", "ok": "Ok", "submit": "Submit", "year": "Year", "documentName": "Document Name", "documentNo": "Document Number", "issueDate": "Issue Date", "validUpto": "<PERSON><PERSON>", "fileStatusReport": "File Status Report", "status": "Status", "generateReport": "Generate Report", "slNo": "SL No", "fileNumber": "File Number", "fileType": "File Type", "currentSeat": "Current Seat", "distributionRegisterReport": "Distribution Register", "counterOperator": "Counter Operator", "inwardNumber": "Inward Number", "inwardDate": "Inward Date", "custodian": "<PERSON><PERSON><PERSON><PERSON>", "useMalayalamOnly": "Use Malayalam Only", "update": "Update", "arising": "Arising", "missingSections": "Missing Sections", "pleaseComplete": "Please Complete", "the": "The", "service": "Service", "section": "Section", "sections": "Sections", "above": "Above", "counterModule": "Counter Module", "fileOperations": "File Operations", "inwardFile": "Inward File", "inbox": "Inbox", "arisingFile": "Arising File", "allFile": "All File", "pendingFile": "Pending File", "delayedFile": "Delayed File", "fileStatus": "File Status", "holdedFile": "Holded File", "legacyFile": "Legacy File", "searchFile": "Search File", "processedFile": "Processed File", "childFile": "Child File", "mergeFile": "Merge File", "reports": "Reports", "pendingFileCount": "Pending File Count", "fileAbstract": "File Abstract", "enquiryReports": "Enquiry Reports", "legacyFileReport": "LegacyFile Report", "legacy": "Legacy", "legacySaved": "Legacy Saved Success", "legacyUpdated": "Legacy Update Success", "fileNo": "File No", "view": "View", "select": "Select", "legacyNotSelected": "Legacy Not Selected", "pleaseSelectAnLegacyNumber": "Please Select An Legacy Number", "serviceSearch": "Service Search", "searchHere": "Search here..", "Module": "module", "subModule": "Sub Module", "title": "Title", "description": "Description", "fileStartDate": "File Start Date", "noteFileLastPageNumber": "Note File Last Page Number", "fileNoteEndDate": "File Note End Date", "lastCorrespondenceType": "Last Correspondence Type", "lastCorrespondenceDate": "Last Correspondence Date", "delete": "Delete", "fileNotes": "File Notes", "correspondencee": "Correspondence", "attachFileNotes": "Attach File Notes", "dropOrChooseFilesToUpload": "Drop or Choose files to upload", "attachCorrespondence": "Attach Correspondence", "filesNotSelected": "Files Not Selected", "selectBothFileNoteDocsAndCorrespondDocs": "Select File Note Documents And Correspond Documents", "fileUpload": "File Upload", "document": "Document", "pleaseEnterValue": "Please Enter Value", "denomination": "Denomination", "count": "Count", "amount": "Amount", "notes": "Notes", "coins": "Coins", "totalAmount": "Total Amount", "doYouWantToSave": "Do you want to save?", "cashDeclarationSuccessfully": "Cash Declaration Completed Successfully", "submitted": "Submitted", "enquiry": "Enquiry", "file-management": "File Management", "createLegacyFile": "Create Legacy File", "legacyFileList": "Legacy File List", "advanceSearch": "Advance Search", "module": "<PERSON><PERSON><PERSON>", "advaceSearch": "Advance Search", "go": "Go", "mandatoryDocuments": "Mandatory Documents", "services": "Services", "documentUpload": "Document Upload", "fileSizeLimit": "Please upload PDF/Image File less than 2 MB", "supportType": "Support PDF/Image only", "documentNameMust": "Document Name Needed", "documentNumberMust": "Document Number Needed", "note": "Note", "draft": "Draft", "link": "Link", "merge": "<PERSON><PERSON>", "unmerge": "Un-merge", "child": "Child", "demand": "Demand", "claim": "<PERSON><PERSON><PERSON>", "paymentOrder": "Payment Order", "payment": "Payment", "receipt": "Receipt", "fileYear": "File Year", "lastPageNumber": "Last Page Number", "nameOfApplicant": "Name Of Applicant", "nextAction": "Next Action", "remainingDays": "Remaining Days", "allApplications": "All Applications", "sortByStatus": "Sort by status", "priority": "Priority", "modulesCount": "Module/ Sub Module/ Service", "modules": "<PERSON><PERSON><PERSON>", "pullFile": "Pull File", "approvedFile": "Approved File", "generateAgenda": "Generate Agenda", "archivedFile": "Archived File", "disposedFile": "Disposed File", "fileMovementRegister": "File Movement Register", "fileTransitRegister": "File Transit Register", "distributionRegister": "Distribution Register", "noteFileExtract": "Note file extract", "correspondences": "Correspondences", "attachSupportingDocuments": "Attach Supporting Documents", "selectDocumentType": "Select Document Type", "searchMergeFile": "Search File to be Merge", "chooseFile": "Choose <PERSON>", "inEnglishRequired": "in English Required", "inMalayalamRequired": "in Malayalam Required", "draftContent": "Draft Content", "fileManagement": "File Management", "newInward": "New Inward", "draftInward": "Draft Inward", "inwardList": "Inward List", "streetNoName": "StreetNo/StreetName", "cityTown": "City / Town", "streetNo": "Stree No / Street Name", "postZipCode": "Post/Zip Code", "applicationForBPLCertificate": "Application for BPL certificate", "here": "here", "placeholder": "Placeholder", "fileSuccessfullyCreated": "File Successfully Created", "fileSuccessfullyUpload": "File Successfully Upload", "legacySuccessfullySaved": "Legacy Successfully Saved", "legacySuccessfullyUpdated": "Legacy Successfully Updated", "legacySuccessfullyDeleted": "Legacy Successfully Deleted", "fileSuccessfullyDeleted": "File Successfully Deleted", "serviceSuccessfullySaved": "Service Successfully Saved", "summary": "Summary", "morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "previousNotes": "Previous Notes", "finance": "Finance", "viewDraft": "View Draft", "noteFile": "Note File", "attach": "Attach", "addAddress": "Add Address", "preview": "Preview", "saveEnquiry": "Save Enquiry", "mergeFileList": "Merge File List", "unMerge": "UnMerge", "fileSuccessfullyHolded": "File Successfully Holded", "fileSuccessfullyUnHolded": "File Successfully UnHolded", "Unhold": "Unhold", "doYouWantTo": "Do you want to", "thisFile": "this file?", "enquiryReport": "Enquiry Report", "enterEnquiryReports": "Enter Enquiry Report here..", "dfms": "DFMS", "address": "Address", "enclosure": "Enclosure", "copyTo": "Copy To", "addEnclosure": "Add Enclosure", "addDocuments": "Add Documents", "videoRecorder": "Video Recorder", "audioRecorder": "Audio Recorder", "imageCapture": "Image Capture", "fileEnquiry": "File Enquiry", "file": "File", "areYouSureWantTo": "Are you sure want to", "addAttachment": "Add Attachment", "saveNote": "Save Note", "applicantDetails": "Applicant Details", "close": "Close", "return": "Return", "forward": "Forward", "search": "Search", "fromDate": "From Date", "create": "Create", "inwardNotSelected": "Inward Not Selected.", "pleaseSelectAnInwardNumber": "Please Select An Inward Number", "multipleInwardsNotAllowed": "Multiple Inwards Not Allowed", "oneInwardIsAllowedAtATimeInwardNumber": "One Inward Is Allowed At A Time Inward Number", "applications": "Applications", "addToExistingFile": "Add To Existing File", "draftInwards": "Draft Inwards", "inwards": "Inwards", "pleaseChooseAnyValue": "Please Choose Any Value", "noteCannotBeEmpty": "Note Cannot Be Empty", "addAttachments": "Add Attachments", "fileNotSelected": "File Not Selected", "selectAFileFromTheListedTable": "Select a file from the listed table", "addFile": "Add File", "fileNoteStartDate": "File Note Start Date", "subject": "Subject", "dateUpTo": "Date Up To", "fileDate": "File Date", "wardName": "Ward Name", "routingKeys": "Routing <PERSON>", "completed": "Completed", "incomplete": "In Complete", "incompleteDetails": "Incomplete Details", "pleaseCompleteMissingDetails": "Please complete Missing Details", "requiredFields": "Required <PERSON>", "localBodyPropertyType": "Local Body Property Type", "buildingUsage": "Building Usage", "buildingArea": "Building Area", "functionalGroup": "Functional Group", "functions": "Functions", "selectNote": "Select Note", "pullNoteSaveSuccess": "Note Pulled Successfully", "selectFileNumber": "Please Select File Number", "referenceFileNo": "Reference File No", "fileNumberisNotValid": "File Number is not Valid", "fileNumberisValid": "File Number is Valid", "searchByService": "Search by Service", "commonFields": "Common Fields", "generalDetailsSavedSucessfully": "General Details Saved Sucessfully", "generalDetailsSaveFailed": "General Details Save Failed", "acknowledgement": "Acknowledgement", "referenceNo": "Reference File Number", "proceed": "Proceed", "addReceiverNameAndAddress": "Add Receiver Name and Address", "sameFile": "Same File", "enclosureFile": "Enclosure File", "pull": "<PERSON><PERSON>", "draftSuccessfullySaved": "Draft Saved Successfully", "draftType": "Draft Type ", "generalInformation": "General Information", "numberofMandatoryDocuments": "Number of Mandatory Documents", "addMore": "Add More", "keywordSearch": "Keyword Search", "aadharNumber": "<PERSON><PERSON><PERSON> Number", "mobileNumber": "Mobile Number", "whatsAppNumber": "WhatsApp Number", "bankName": "Bank Name", "bankBranch": "Bank Branch", "uuid": "UUID", "referenceNumber": "Reference Number", "institutionName": "Institution Name", "officerName": "Officer Name", "designation": "Designation", "landLineNumber": "Land Line Number", "selectAnyData": "Select Any Data", "declaration": "Declaration", "verification": "Verification", "localBodyName": "Local Body Name", "appliedForOthers": "Applied For Others", "workflow": "Workflow", "areYouSureWantToUnMergeTheseFilesFromTheMainFile": "Are you sure want to Un-Merge these files from the Main file", "stage": "Stage", "searchByNameNumberSubject": "Search by Name, Number, Subject", "currentUser": "Current User", "beneficiary": "Beneficiary", "noFile": "There are no files to be display", "noMergedFiles": "No Merged files found..", "action": "Action", "documents": "Documents", "application": "Application", "mergeDesc": "Merge these files with the Main file", "linkDesc": "Link these files with the Main file", "fileflow": "File Flow", "submittedBy": "Submitted by", "createdOn": "Created On", "currentStatus": "Current Status", "drafts": "Drafts", "proceedAndPreview": "Proceed and Preview", "noItemsToDisplay": "No Items to Display", "noApplicantDataToDisplay": "No Applicant data to display.", "draftPreview": "Draft Preview", "noDraftPreview": "No Draft Preview", "moreDetails": "More Details", "senderMissing": "Please select the sender Address", "senderRequired": "Sender is Required", "headOfOffice": "Head of Office", "verify": "Verify", "approve": "Approve", "saveandverify": "Save and Verify", "saveandapprove": "Save and Approve", "login": "<PERSON><PERSON>", "sendOtp": "Send Otp", "reSendOtp": "Re-send Otp", "employeeLogin": "Employee Login", "citizenLogin": "<PERSON>", "name": "Name", "edit": "Edit", "officeName": "Office Name", "referenceNoAndData": "Reference No & Data", "pleaseEnterAReferenceValue": "Please enter Reference", "enclosureName": "Enclosure Name", "draftStatus": "Draft Status", "createdBy": "Created By", "seat": "<PERSON><PERSON>", "createdDate": "Created Date", "enquiryText": "Enquiry Text", "documentAttached": "Document Attached", "agenda": "Agenda", "routeChange": "Route Change", "draftUpdatedSuccessfully": "Draft Updated Successfully", "draftUpdate": "Draft Update", "draftCreatedSuccessfully": "Draft Created Successfully", "draftVerifiedSuccessfully": "Draft Verified Successfully", "draftApprovedSuccessfully": "Draft Approved Successfully", "draftSavedSuccessfully": "Draft Saved Successfully", "draftCreate": "Draft Create", "forwardedBy": "Forwarded By", "deliveryDate": "Delivery Date", "serviceName": "Service Name", "department": "Department", "pullReason": "<PERSON><PERSON> Reason", "add": "Add", "noteIsEmpty": "Note is Empty", "pleaseSaveNoteBeforeWorkflowAction": "Please save note before workflow action", "entries": "Entries", "newApplication": "New Application", "selectDraft": "Select a draft", "inwardDetails": "Inward Details", "generalDetails": "General Details", "accountNumber": "Account Number", "missingRequiredFields": "Missing required fields", "addressIsRequired": "Address is Required", "selectDisposeFileType": "Select Dispose File Type", "doYouWantToDisposeTheFile": "Do you want to dispose the file?", "confirmDisposeFile": "Confirm Dispose File", "back": "Back", "download": "Download", "print": "Print", "aAdharNumber": "<PERSON><PERSON><PERSON> Number", "UDIDNumber": "UDID Number", "documentNumber": "Document Number", "archivedFiles": "Archived Files", "fileCloseType": "File Close Type", "noNotesToList": "No notes to list", "serviceDate": "Service Date", "processedFiles": "Processed Files", "pleaseConfirmDisposalType": "Please confirm disposal type", "temporaryDisposal": "Temporary", "permanantDisposal": "Permanant", "fileDisposalType": "File Disposal Type", "modeofDisposal": "Mode of Disposal", "workFlowActionFailed": "Workflow Action Failed", "workFlowActionSuccess": "Workflow Action Success", "workFlowAction": "Workflow Action", "incomeDigitsOnly": "Income Digit's Only", "doorNumberShouldNotBeZero": "Door number should not be Zero", "aadharOtpSendSuccess": "<PERSON><PERSON><PERSON>", "aadharOtpSendFailed": "<PERSON><PERSON><PERSON>p Send Failed", "aadharOtpVerificationSuccess": "<PERSON><PERSON><PERSON>p Verification Success", "aadharOtpVerificationFailed": "<PERSON><PERSON>har Otp Verification Failed", "send": "Send", "validate": "Validate", "noDocumentFound": "No Document Found", "addReference": "Add Reference", "enableEnglish": "Enable English", "profileDetailsFetchedFromKSmart": "Profile Details Fetched From KSmart", "serviceDelivaryDate": "Service Delivary Date", "draftSuccessfullyReturned": "Draft Returned Successfully", "draftSuccessfullyRejected": "Draft Rejected Successfully", "draftReturnFailed": "Draft Return Failed", "areyouSureWantToReturnDraft": "Are you sure want to return draft", "reject": "Reject", "confirm": "Confirm", "areYouSureWanttoRejectDraft": "Are you sure want to Reject Draft", "areYouSureWanttoSaveDraft": "Are you sure Want to Save Draft", "areYouSureWanttoCreateDraft": "Are you sure Want to Create Draft", "areYouSureWanttoVerifyDraft": "Are you sure Want to Verify Draft", "areYouSureWanttoApproveDraft": "Are you sure Want to Approve Draft", "theFile": "The File", "unmergeFile": "File Unmerged", "linkFiled": "File Linked", "mergeFiled": "File Merged", "newDraft": "New Draft", "temporaryDisposedFile": "Temporary Disposed File", "minimize": "Minimize", "expand": "Expand", "legacyFileNumber": "Legacy File Number", "comment": "Comment", "heading": "Heading", "fileSearch": "File Search", "inwardSearch": "Inward Search", "pleaseAddAtleastOne": "Please add atleast one search criteria", "maxOrMinimum": "minimum 9 and maximum 18", "yearMismatch": "Year Mismatch", "clear": "Clear", "documentNameisRequired": "Document Name is Required", "noBeneficiaryDataToDisplay": "No Beneficiary data to display.", "toDateGreaterError": "To date must be greater than From date.", "toDateRequired": "To Date is required when fromDate is selected", "role": "Role", "completeBeneficiary": "Please Complete the Beneficiary Details", "addAnotherBeneficiary": "Add another beneficiary", "noDraftsFound": "No Drafts Found", "moveToCustodian": "Move To Custodian", "reOpen": "Re-Open", "reason": "Reason", "fileMovedToCustodianSuccessfully": "File successfully moved to custodian", "pullFileStatus": "Pull File Status", "pullStatus": "Pull Status", "filesAreUnmergedFromMainFile": "files are unmerged from main file", "filesAreMergedToMainFile": "files are merged to main file", "filesAreLinkedToMainFile": "files are linked to main file", "draftNumber": "draft number", "draftCreatedWith": "Draft created with", "draftVerifiedWith": "Draft verified with", "draftApprovedWith": "Draft approved with", "draftRejectedWith": "Draft rejected with", "draftReturnedWith": "Draft returned with", "draftTypeSmall": "draft type", "fetch": "<PERSON>tch", "noDetailsFoundFor": "No details found for", "noDataFound": "No Data Found", "pleaseRegister": "Please Register with KSmart", "register": "Register", "phoneNumber": "Phone Number", "submittingTo": "Submitting to", "applicationFor": "Application for", "distributionStatusReport": "Distribution Status Report", "pendingFileReport": "Pending File Report", "fileAbstractReport": "File Abstract Report", "cashDeclarationReport": "Cash Declaration Report", "noNotesFound": "No Notes Found", "pullDraft": "Pull Draft", "pullNote": "Pull Note", "unLink": "Un-Link", "noLinkedFiles": "No linked files found", "areYouSureWantToUnLinkTheseFilesFromTheMainFile": "Are you sure want to unLink these files from the main file", "filesAreUnLinkedFromMainFile": "Files are unLinked from main file", "attachments": "Attachments", "payNow": "Pay Now", "noName": "No/Name", "previewNotAvailable": "Preview not available", "pleaseCompleteSelfDeclaration": "Please complete self declaration", "noFilesFound": "No files found", "mandatoryDocument": "Mandatory document", "documentDeleteSuccess": "Document delete success", "documentDeleteFailed": "Document delete failed", "financialTransactionPending": "Financial transaction pending", "pendingDraftExists": "Pending Draft Exists", "ksebPostNo": "Kseb Post No", "roadName": "Road Name", "landmark": "Landmark", "ownerName": "Owner Name", "village": "Village", "surveyNumber": "Survey Number", "taluk": "Taluk", "dateOfEvent": "Date of Event", "receiptNo": "Receipt Number", "greaterThanNine": "greater than 9", "downloadDataSheet": "Download Data Sheet", "dataSheetDownloadInfo": "Download the data sheet then fill the details and sign. After convert and upload as PDF document", "unsupportedDocumentType": "Unsupported document type", "useImageOnly": "use Image only", "usePDFOnly": "use PDF only", "useImagesAndPdfOnly": "use Images or PDF only", "areYouSure": "Are you sure", "localFirstName": "First Name (Malayalam)", "localMiddleName": "Middle Name (Malayalam)", "localLastName": "Last Name (Malayalam)", "localHouseName": "House Name (Malayalam)", "localStreetName": "Street (Malayalam)", "localLocalName": "Local Place (Malayalam)", "localMainName": "Main Place (Malayalam)", "otpVerification": "OTP Verification", "otpSuccessfullySendto": "OTP successfully send to", "otpSendingFailed": "OTP sending failed", "otpSuccessfullyVerified": "OTP Successfully verified", "otpVerificationFailed": "OTP verification failed", "otp": "OTP", "iAgree": "I Agree", "iHerebyDeclareThat": "I here by declare that,  all the information given above is correct to the best of my knowledge and  belief and will be liable to legal action against me ,if any of the information is false or incorrect", "charAndNumbers": "Character and Numbers only", "minimumNineOnly": "Minimum 9 characters/numbers needed", "maximumOnly": "Maximum 18 characters/numbers needed", "pincode": "Pincode", "failed": "Failed", "addBeneficiary": "Add Beneficiary", "noGeneralInformationToDisplay": "No General information to display", "fileTrackingReport": "File Tracking Report", "sla": "SLA", "operator": "Operator", "verifier": "Verifier", "recommendingOfficer": "Recommending Officer", "approver": "Approver", "authorisor": "Authorisor", "penNo": "Pen", "actionTakenDate": "Action Taken Date", "seatName": "Seat Name", "postName": "Post Name", "fileLogDetails": "File Log Details", "downloadAcknowledgement": "Download Acknowledgement", "fileSearchByInward": "File Search (by <PERSON><PERSON>)", "searchByNameNumber": "Search by Name, Number", "mobileNo": "Mobile Number", "createdUser": "Created User", "reSubmit": "Resubmit", "thisFeatureWillAvailableShortly": "This Feature will available shortly", "sorryfortheinconvenience": "Sorry for the inconvenience!", "budget": "Budget", "areYoursureTo": "Are your sure to", "resubmitFile": "resubmit file", "resubmitEfileFailed": "Resubmit e-file failed", "efileSuccessfullyReSubmitted": "e-file Successfully Resubmitted", "custodian_change": "Custodian <PERSON>", "billType": "<PERSON>", "establishmentType": "Establishment Type", "mission": "Mission", "professionalTaxType": "Professional Tax Type", "typeOfAudit": "Type of Audit", "lbBuilding": "LB Building", "amountFromClaim": "Amount From Claim", "estimateAmount": "Estimate Amount", "areYouSureWantToChange": "Are you sure want to change?", "custodianChangeFailed": "Custodian change failed", "serviceCode": "Service Code", "rotate": "Rotate", "fullScreen": "Full Screen", "maximize": "Maximize", "recommend": "Recommend", "fileSearchByApplicationOrInwardNumber": "File Search (by Application/Inward number)", "applicationOrInwardNumber": "Application/Inward Number", "occupancy": "Occupancy ", "returnToCitizen": "Return To Citizen", "buildUpArea": "Builtup Area", "meetingType": "Meeting Type", "officeType": "Office Type", "fund": "Fund", "user": "User", "employeeNotMapped": "Employee Not Mapped", "eventStartDate": "Event Start Date", "eventEndDate": "Event End Date", "salary": "Salary", "surveyNo": "Survey Number", "talukName": "Taluk Name", "villageName": "Village Name", "ward": "Ward", "allStatus": "All status", "verified": "Verified", "notVerified": "Not Verified", "errorOnLoading": "Error on Loading", "pdfLoadIssue": "Some issue happens on PDF load", "pending": "Pending", "created": "Created", "approved": "Approved", "returned": "Returned", "rejected": "Rejected", "recommended": "Recommended", "areYouSureWanttoDelete": "Are you sure want to Delete?", "deleteConfirmation": "Delete Confirmation", "fileTracking": "File Tracking", "duration": "Duration", "receivedOn": "Received On", "noRecordsFound": "No Records Found", "location": "Location", "selectedLegacyFileisPartialStage": "Selected legacy file is partial stage", "incompleted": "Incompleted", "accountType": "Account Type", "treasuryType": "Treasury Type", "treasuryAccountNo": "Treasury Account Number", "recoveryPayment": "Recovery Payment", "routeType": "Route Type", "sourceType": "Source Type", "headOfAccount": "Head of Account", "lastNote": "Return Note", "deductionHead": "Deduction Head", "landMark": "Land Mark", "confirmation": "Confirmation", "sendingTo": "Sending to", "continue": "Continue", "fileMovement": "File Movement", "forwardingTo": "Forwarding to", "penCap": "PEN", "seatCap": "SEAT", "dateAndTime": "Date & Time", "landline": "Land line", "localInstitutionName": "Institution Name in Malayalam", "localDesignation": "Designation in Malayalam", "referenceDate": "Reference Date", "routeKeys": "Route Keys", "routeKeysAndUsers": "Route Keys and Users", "fetchUser": "Fetch User", "byPostalOrEmail": "By Postal or Email", "users": "Users", "beneficiarySaveFailed": "Beneficiary Save Failed", "fileReturningToCitizen": "File Returning To Citizen", "insertTemplate": "Insert Template", "areYouSureWanttoDeLinkInward": "Are you sure want to de-link inward", "inwardDeLinkedFailed": "Inward De-Linked Failed", "inwardDeLink": "Inward de-link", "applicantSaveFailed": "Applicant Save Failed", "receiptDate": "Receipt Date", "receiptDetailsAtTheTimeOfApplication": "Receipt Details at the time of Application", "amountInRs": "Amount (Rs)", "noReceiptDetailsToDisplay": "No Receipt details to display", "routeKey": "Route Key", "routeKeyValue": "Route Key Value", "userMapping": "User Mapping", "beneficiarySavedSuccessfully": "Beneficiary Saved Successfully", "saveBeneficiary": "Save Beneficiary", "newBeneficiary": "New Beneficiary", "addNew": "Add New", "beneficiaryName": "Beneficiary Name", "insideLocalBody": "Inside Local Body", "beneficiaryUpdatedSuccessfully": "Beneficiary Updated Successfully", "items": "Items", "areYouSureWanttoDeleteBeneficiaries": "Are you sure you want to delete Beneficiaries", "deleteBeneficiaryFailed": "Delete Beneficiary Failed", "deleteBeneficiarySuccess": "Delete Beneficiary Success", "lbName": "Localname", "lbType": "Lb Type", "currentStage": "Current Stage", "appliedOn": "Applied On", "applied": "Applied", "inwardNo": "Inward Number", "noInwardsToDisplay": "No inwards to display ", "moduleType": "Module Type", "inwardDeLinkedSuccess": "In<PERSON> De Linked Success", "deLink": "De-Link", "standingDemand": "Standing Demand", "areYouSureWantToDelete": "Are you sure want to delete", "legacyDeletedFailed": "Legacy Deleted Failed", "requisition": "Requisition", "bulkUpload": "Bulk Upload", "beneficiaryBulkUpload": "Beneficiary Bulk Upload", "benaficiaryName": "Benaficiary Name", "phoneNo": "Phone No", "upload": "Upload", "fileNumberNotValid": "File Number Not Valid", "issuingAuthority": "Issuing Authority", "uploadRequiredDocumnet": "Upload Required Document", "home": "Home", "noInwardDeatailsToDisplay": "No Inward details to display", "park": "Park", "parkingFile": "Parking File", "parkingReason": "Parking Reason", "fileSuccessfullyParked": "File Successfully Parked", "fileFailedtoParkedPleaseTryAgain": "File failed to parked please try again", "reasonForRejection": "Reason for rejection", "reasonForReturn": "Reason for return", "fileDetails": "File details", "scpList": "Self certified permit", "collapse": "Collapse", "revoke": "Revoke", "draftRecommendedSuccessfully": "Draft Recommended Successfully", "draftRecommendedWith": "Draft Recommended with", "wasteManagementType": "Waste Management Type", "areYouSureWantToCreateChildFile": "Are you sure want to create child file", "e-sign": "E-Sign", "page": "Page", "pageNo": "Page No", "generate": "Generate", "sameLocalBody": "Same as Local Body", "sameAsApplicant": "Same as Applicant", "notTheSameAsApplicant": "Not the same as Applicant", "areYouSureWantToDigitalSign": "Are you sure want to<br/> digitally sign the document?", "draftUpdatedwithDigitalSignature": "Draft updated with digital signature", "digitalSign": "Digital Sign", "accountId": "Account Id", "requisitionAuthorization": "Requisition Authorization", "requisitionAllotment": "Requisition Allotment", "DoYouWantToDigitalySignTheDocument": "Do you want to digitaly sign the Document", "noteafterDigitalSignatureNoUpdatesCanBeDone": "Note: After digital signature no updates can be done", "digitalSignature": "Digital Signature", "failedToDigitalSignPleaseTryAgain": "Failed to Digital Sign<br/> Please Try Again", "digitalSignatureEnrollment": "Enroll Digital Signature", "yourInformation": "Your Information", "documentAttachments": "Document Attachments", "areYouSureWantToEnroll": "Are you sure want to Enroll", "enroll": "Enroll", "digitalSignatureSuccessfullyEnrolled": "Digital Signature Successfully Enrolled", "digitalSignatureEnrollmentFailed": "Digital Signature Enrollment Failed", "deactivate": "Deactivate", "areYouSureWantToDeactivate": "Are you sure want to Deactivate", "deactivatedList": "Deactivated List", "profile": "Profile", "pan": "PAN", "panCopy": "PAN Copy", "nameinPAN": "Name in PAN", "addressProof": "Address Proof", "yourPhoto": "Your Photo", "emailId": "Email-Id", "desiredUserName": "Desired UserName", "username": "User Name", "certKeyNo": "Cert Key No", "validFrom": "<PERSON><PERSON>", "validTo": "<PERSON><PERSON>", "digitalSignatureEnrollmentNotCompleted": "Digital Signature Enrollment Not Completed", "clickBelowEnrollButtonIfNotDoneYet": "Click below enroll button if not done yet", "pleaseCheckAndTryAgain": "Please Check and Try Again", "digitalSignatureTokenNotMatch": "Digital Signature Token Not Matching", "suspendedFiles": "Suspended Files", "reAssign": "Re Assign", "pleaseSelectFileNo": "Please Select File Number(s)", "reassignSuccessfully": "Successfully Reassigned", "reassignFailed": "Reassign Failed", "securelySigninToKsmart": "Securely Sign in to Ksmart", "recordRoom": "Record Room", "inHandRecords": "In hand records", "requested": "Requested", "sentRecords": "Sent records", "designatedSeat": "Designated Seat", "currentLocation": "Current Location", "arrivalDate": "Arrival Date", "requestedPost": "Requested Post", "requestedBy": "Requested By", "previewWithDs": "Preview with Digital Signature", "confirmDs": "Confirm Digital Signature", "certkeyno": "Certificate Key Number", "certslno": "Certificate Sl Number", "tokenslno": "Token Sl Number", "validfrom": "<PERSON><PERSON>", "validto": "<PERSON><PERSON>", "thumb": "Thumb", "tokenValidityExpired": "Token validity expired", "pleaseCheck": "Please check", "somethingWentWrong": "Something Went Wrong", "pleaseTryAgain": "Please try again", "demandCancellation": "Demand Cancellation", "issuer": "Issuer", "issuerCat": "Issuer Category", "forDispose": "For Dispose", "areYouSureToSendThisFileForDisposal": "Are You sure to send this file for disposal", "doYouWantToUncheckThisFileFromDispose": "Do you want to uncheck this file  from dispose", "successfullySendThisFileForDisposal": "Successfully selected for Dispose.Please Submit File", "successfullyUncheckThisFileFromDispose": "Successfully Uncheck This File From Dispose", "sendThisFileForDisposalFailed": "Send This File For Disposal Failed", "uncheckThisFileFromDisposeFailed": "Uncheck This File From Dispose Failed", "draftUpdateFailed": "Draft Update Failed", "digitalSignatureSuccessfullyDiactivated": "Digital Signature Deactivated Successfully", "floatingSearch": "Floating Search", "minimalSearch": "Minimal Search", "applicationNumber": "Application Number", "insideOffice": "Inside Office", "billGenerate": "<PERSON>", "billSend": "<PERSON>", "systemDateNoteUpdated": "Incorrect system date", "pleaseSelectBeneficiaries": "Please select beneficiary", "personalRegisterReport": "Personal Register Report", "dispatchOutbox": "Dispatch - outbox", "letter": "Letter", "sentDate": "Sent Date", "recipient": "Recipient", "sender": "Sender", "stampInventory": "Stamp Inventory", "stampValue": "Stamp Value", "openingBalance": "Opening Balance", "createClaim": "Create <PERSON><PERSON><PERSON>", "expenditure": "Expenditure", "currentYear": "Current Year", "balance": "Balance", "unsentLetterCount": "Unsent letter count", "total": "Total", "grandTotal": "Grand Total", "individual": "Individual", "downloads": "Downloads", "template": "Template", "useExcelorCSVOnly": "use Excel or CSV only", "pleaseUseTemplatesForUpload": "Please use templates for upload", "updateBeneficiary": "Update Beneficiary", "havePhysicalCopy": "Have physical copy", "outbox": "Outbox", "fileLocation": "File Location", "physicalDocument": "Physical Document", "noOf": "No Of", "typeOfOffice": "Type of Office", "unit": "Unit", "to": "To", "receiver": "Receiver", "messenger": "<PERSON>", "supportDocumentType": "Support document type", "modeOfDispatch": "Mode Of Dispatch", "dispatchSection": "Dispatch Section", "stampInHand": "Stamp In Hand", "stampCount": "Stamp Count", "viewOpeningBalance": "View Opening Balance", "stampType": "Stamp Type", "ownFundpayment": "Own Fund payment", "dispatchSendLetter": "Dispatch - Send Letter", "dispose": "Dispose", "forDisposal": "For Disposal", "disposeReason": "Dispose Reason", "successfullyDisposed": "Successfully Disposed", "DisposedFailed": "Disposed Failed", "documentsUpdatedSuccessfully": "Documents Updated Successfully", "draftNo": "Draft No", "draftDate": "Draft Date", "noOfCommunications": "No Of Communications", "dispatchSendLetterPreview": "Dispatch Send Letter Preview", "doYouWantToReturnFile": "Do you want to return file", "lsgiType": "Lsgi Type", "regionType": "Region Type", "buildingProjectType": "Building Project Type", "submittedOn": "Submitted On", "noPreviousNotes": "No Previous Notes", "actions": "Actions", "autoNotes": "Auto Notes", "pleaseAttachDocuments": "Please Attach Documents", "createDraft": "Create Draft", "noDocumentSelected": "No Document Selected.", "showAllDocuments": "Show all Documents", "allDocument": "All Document", "doYouWantToDelete": "Do you want to delete?", "needReview": "Need Review", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "pleaseSelectAction": "Please Select Action", "revise": "Revise", "makeInActive": "Make Inactive", "makeActive": "Make Active", "lastNoteNo": "Last Note Number", "noteFileLastNoteNumber": "Note File Last Note Number", "forwardTo": "Forwarding to", "parkFile": "Park File", "noDrafts": "No Drafts", "allDocuments": "All Documents", "fileCanBeSubmittedOnlyAfterSavingTheNote": "File can be submitted only after saving the note.", "correspondenceName": "Correspondence", "parkedFiles": "Parked Files", "unableToReOpen": "Unable To ReOpen", "noteFileLastParaNumber": "Note File Last Para Number", "inwardApplicationDetails": "Inward Application Details", "fileHistory": "File History", "DoYouWantToSign": "Do You Want To Sign", "advanceClaim": "Advance Claim", "imprestClaim": "<PERSON><PERSON><PERSON>", "eSign": "E Sign", "myFiles": "My Files", "referenceNos": "Reference Nos", "panNo": "PAN Number", "gstNo": "GST Number", "useImagesPdfExelAndWordOnly": "Use Images, Pdf, Excel and Word Only", "siNo": "SI No", "panNumber": "Pan Number", "kycStatus": "status", "eSignRequests": "E Sign Requests", "requestFetchingFailed": "Request Fetching Failed", "more": "More", "es": "ES", "ds": "DS", "confirmDS": "Confirm DS", "imprestDisbursement": "Imprest Disbursement", "contraEntry": "Contra Entry", "journalEntry": "Journal Entry", "new": "New", "institution": "Institution", "dispatch": "Dispatch", "references": "References", "editReference": "Edit Reference", "draftDetails": "Draft Details", "fileSummary": "File Summary", "content": "Content", "import": "Import"}