{"invalidType": "Invalid {{type}}", "mustBeAtLeast": "{{type}} must be at least {{count}} {{unit}}", "mustBe": "{{type}} must be {{count}} {{unit}}", "shouldNotBeGreaterThan": "{{type}} should not greater than {{count}} length", "valueMustBeInBetween": "{{type}} must be between {{start}} and {{end}} {{unit}}", "isRequired": "{{type}} is required", "isExists": "{{type}} already exists", "labelIn": "{{label}} ({{type}})", "concatLabel": "{{label}} {{type}}", "asName": "{{type}} Name", "fetchSuccessForId": "Details Fetched Successfully for {{type}}", "actionBy": "{{action}} by {{person}}", "requestSubmit": "Your Service Request has been submitted to {{type}}", "savedConfirmationMessage": "{{type}} Saved Successfully", "updatedConfirmationMessage": "{{type}} Updated Successfully", "deletedConfirmationMessage": "{{type}} Deleted Successfully"}