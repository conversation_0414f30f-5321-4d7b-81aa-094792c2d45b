{"welcome": "സ്വാഗതം", "locality": "പ്രദേശം", "english": "ഇംഗ്ലീഷ്", "malayalam": "മലയാളം", "number": "നമ്പർ", "name": "പേര്", "place": "സ്ഥലം", "search": "തിരയുക", "searchHere": "ഇവിടെ തിരയൂ..", "localBodyType": "പ്രാദേശിക ബോഡി തരം", "localBody": "ലോക്കൽ ബോഡി", "documentType": "പ്രമാണ തരം", "advancedSearch": "വിപുലമായ തിരയൽ", "country": "രാജ്യം", "state": "സംസ്ഥാനം", "district": "ജില്ല", "first": "ആദ്യം", "middle": "മധ്യഭാഗം", "last": "അവസാനത്തെ", "house": "വീട്", "ward": "വാർഡിൽ", "door": "വാതിൽ", "sub": "ഉപ", "postOffice": "പോസ്റ്റ് ഓഫീസ്", "pinCode": "പിൻ കോഡ്", "street": "തെരുവ്", "localPlace": "പ്രാദേശിക സ്ഥലം", "mainPlace": "പ്രധാന സ്ഥലം", "emailId": "ഇ - മെയിൽ ഐഡി", "mobile": "മൊബൈൽ", "whatsapp": "Whatsapp", "aadhar": "ആധാർ", "udid": "യുഡിഐഡി", "passport": "പാസ്പോർട്ട്", "services": "സേവനങ്ങള്", "details": "വിശദാംശങ്ങൾ", "applicant": "അപേക്ഷക", "document": "പ്രമാണം", "documents": "പ്രമാണങ്ങൾ", "module": "മൊഡ്യൂൾ", "file": "ഫയൽ", "type": "ടൈപ്പ്", "title": "തലക്കെട്ട്", "description": "വിവരണം", "application": "അപേക്ഷ", "required": "ആവശ്യമാണ്", "guidelines": "മാർഗ്ഗനിർദ്ദേശങ്ങൾ", "quickAccess": "ദ്രുത പ്രവേശനം", "process": "പ്രക്രിയ", "next": "അടുത്തത്", "save": "സേവ്", "useAlphabetsOnly": "ഇംഗ്ലീഷ് മാത്രം ഉപയോഗിക്കുക", "useNumbersOnly": "നമ്പറുകൾ മാത്രം ഉപയോഗിക്കുക", "useAlphaAndNumOnly": "ഇംഗ്ലീഷും നമ്പറുകളും മാത്രം ഉപയോഗിക്കുക", "institution": "സ്ഥാപനം", "reference": "റഫറൻസ്", "date": "തീയതി", "officer": "ഉദ്യോഗസ്ഥൻ", "designation": "പദവി", "landLine": "ലാൻഡ് ലൈൻ", "main": "പ്രധാന", "dashboard": "ഡാഷ്ബോർഡ്", "report": "റിപ്പോർട്ട്", "settings": "ക്രമീകരണങ്ങൾ", "help": "സഹായം", "logoutAccount": "ലോഗ്ഔട്ട് അക്കൗണ്ട്", "to": "ലേക്ക്", "from": "നിന്ന്", "keyword": "കീവേഡ്", "attachDocuments": "പ്രമാണങ്ങൾ അറ്റാച്ചുചെയ്യുക", "department": "വകുപ്പ്", "assignee": "അസൈനി", "select": "തിരഞ്ഞെടുക്കുക", "correspondence": "കത്തിടപാടുകളുടെ തരം", "subject": "വിഷയം", "enter": "നൽകുക", "sender": "അയച്ചയാൾ", "office": "ഓഫീസ്", "address": "വിലാസം", "receiver": "റിസീവർ", "enclosure": "എൻക്ലോഷർ", "sortByDate": "തീയതി പ്രകാരം അടുക്കുക", "wireframe": "വയർഫ്രെയിം", "seat": "ഇരിപ്പിടം", "note": "കുറിപ്പ്", "toDateGreaterThanFromDateError": "തീയതി തീയതിയേക്കാൾ വലുതായിരിക്കണം.", "actions": "പ്രവർത്തനങ്ങൾ", "assigneeName": "അസൈനിയുടെ പേര്", "functionalGroup": "ഫങ്ഷണൽ ഗ്രൂപ്പ്", "function": "ഫംഗ്ഷൻ", "fileContent": "ഫയൽ ഉള്ളടക്കം", "comment": "അഭിപ്രായം", "childFile": "ചൈൽഡ് ഫയൽ", "merge": "ഫയൽ ലയിപ്പിക്കുക", "link": "ലിങ്ക് ഫയൽ", "notAddedReference": "റഫറൻസ് ചേർത്തിട്ടില്ല", "referenceErrorMsg": "ദയവായി അവലംബം ചേർക്കുക", "cashDeclaration": "പണ പ്രഖ്യാപനം", "personalCashInCustody": "വ്യക്തിഗത പണം കസ്റ്റഡിയിൽ", "employeeName": "ജീവനക്കാരന്റെ പേര്", "timeOfEntry": "പ്രവേശന സമയം", "remarks": "പരാമർശത്തെ", "createDraft": "ഡ്രാഫ്റ്റ് സൃഷ്ടിക്കുക", "searchLinkFile": "ലിങ്ക് ചെയ്യേണ്ട ഫയൽ തിരയുക", "searchFileError": "ഒരേ സേവനം ഉപയോഗിച്ച് ഫയൽ തിരയുക"}