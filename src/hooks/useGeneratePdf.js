import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';
import { useState, useEffect } from 'react';

export function useGeneratePdf({
  url, flag
}) {
  const [loading, setLoading] = useState(false);
  const [previewData, setPreviewData] = useState([]);
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

  useEffect(() => {
    setLoading(true);
    if (url) {
      fetch(url, {
        method: 'GET',
        headers: {
          Accept: DOCUMENT_TYPES.PDF,
          Authorization: `Bearer ${token}`
        }
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          const arr = new Uint8Array(response);
          const blob = new Blob([arr], {
            type: DOCUMENT_TYPES.PDF
          });
          const blobUrl = window.URL.createObjectURL(blob);
          setPreviewData(blobUrl);
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [url, flag]);

  return { loading, previewData };
}
