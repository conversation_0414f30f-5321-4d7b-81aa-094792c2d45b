import { STORAGE_KEYS } from 'common/constants';
import { useState, useEffect } from 'react';

export function useGenerateDocs({
  url, flag, contentType, content
}) {
  const [loading, setLoading] = useState([]);
  const [previewData, setPreviewData] = useState([]);
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

  useEffect(() => {
    setLoading(true);
    if (url) {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(content)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          const arr = new Uint8Array(response);

          const blob = new Blob([arr], {
            type: contentType
          });
          const blobUrl = window.URL.createObjectURL(blob);
          setPreviewData(blobUrl);
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [url, flag]);

  return { loading, previewData };
}
