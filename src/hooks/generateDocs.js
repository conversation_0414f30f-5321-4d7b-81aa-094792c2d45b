import { STORAGE_KEYS } from 'common/constants';

const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

export const generateDocs = async ({
  url, contentType, content = null
}) => {
  try {
    const response = await fetch(url, {
      method: content ? 'POST' : 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: content ? JSON.stringify(content) : null
    })
      .then((res) => res.arrayBuffer())
      .then((res) => {
        const arr = new Uint8Array(res);
        const blob = new Blob([arr], {
          type: contentType
        });
        const blobUrl = window.URL.createObjectURL(blob);
        return { data: blobUrl, status: 'success' };
      });
    return response;
  } catch (e) {
    return { data: e, status: 'failed' };
  }
};
