import { useEffect, useRef } from 'react';

export function useShortcut({
  key, callback, options = {}, enabled = true
}) {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    if (!enabled) return;

    const handler = (e) => {
      const matchKey = e.key.toLowerCase() === key?.toLowerCase();
      const matchCtrl = !!options?.ctrlKey === e.ctrlKey;
      const matchShift = !!options?.shiftKey === e.shiftKey;
      const matchAlt = !!options?.altKey === e.altKey;
      const matchMeta = !!options?.metaKey === e.metaKey;

      if (matchKey && matchCtrl && matchShift && matchAlt && matchMeta) {
        e.preventDefault();
        callbackRef.current(e);
      }
    };

    window.addEventListener('keydown', handler);
    // eslint-disable-next-line consistent-return
    return () => {
      window.removeEventListener('keydown', handler);
    };
  }, [key, options, enabled]);
}
