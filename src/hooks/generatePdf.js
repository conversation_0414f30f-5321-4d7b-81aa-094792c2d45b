import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';

const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

export const generatePdf = async ({
  url
}) => {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: DOCUMENT_TYPES.PDF,
        Authorization: `Bearer ${token}`
      }
    })
      .then((res) => res.arrayBuffer())
      .then((res) => {
        const arr = new Uint8Array(res);
        const blob = new Blob([arr], {
          type: DOCUMENT_TYPES.PDF
        });
        const blobUrl = window.URL.createObjectURL(blob);
        return { data: blobUrl, status: 'success' };
      });
    return response;
  } catch (e) {
    return { data: e, status: 'failed' };
  }
};
