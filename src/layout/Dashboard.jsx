import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import MainHeader from 'common/components/MainHeader';
import Verification from './Verification';

const Dashboard = () => {
  const [openVerification, setVerification] = useState(false);
  const location = useLocation();

  const handleCloseVerification = () => {
    setVerification(false);
  };

  useEffect(() => {
    const items = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    if (items) {
      setVerification(false);
    } else {
      setVerification(true);
    }
  }, [localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)]);

  return (
    <div className="w-screen h-screen pt-[70px]">
      <MainHeader />
      <div
        className="grid flex grid-flow-col auto-cols-max gap-x-4 px-10 py-4"
        style={{ background: '#E7EFF5' }}
      >
        <div className="grow">
          <Outlet />
        </div>
        {location.pathname === `${BASE_PATH}/dashboard` && <Verification open={openVerification} handleClose={handleCloseVerification} />}
      </div>
    </div>
  );
};

export default Dashboard;
