import React from 'react';
import { useLocation } from 'react-router-dom';

const Footer = () => {
  const { pathname } = useLocation();

  if (['register', 'login'].includes(pathname.split('/').at(-1))) {
    return null;
  }

  return (
    <div className="flex justify-between px-8 py-1.5 bg-white text-[#1C253D] font-semibold shadow-sm border-t fixed bottom-0 w-full z-50">
      <div>
        <p className="text-sm">Copyright © {new Date().getFullYear()}, KSuite, Government of Kerala.</p>
      </div>
      <div className="flex gap-4 items-center">
        <p className="text-sm">Terms & Conditions</p>
        <p className="text-sm">Privacy & Policy</p>
      </div>
    </div>
  );
};

export default Footer;
