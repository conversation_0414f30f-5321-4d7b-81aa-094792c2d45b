import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>se,
  Flex,
  IconButton,
  t
} from 'common/components';
import FileDocumentSecondary from 'assets/FileDocumentSecondary';
import ServiceInfoIcon from 'assets/ServiceInfoIcon';
import SearchIcon from 'assets/SearchIcon';
// import MenuBarHorizontal from 'assets/MenuBarHorizontal';
import { useState } from 'react';
import RoutingActions from 'pages/file/details/components/summaryDetails/routingActions';

const FileActionButton = ({
  icon,
  bg,
  bgHover = 'none',
  color,
  text,
  onClick = () => {}
}) => (
  <Button
    leftIcon={icon}
    bg={bg}
    color={color}
    fontWeight="500"
    borderRadius="8px"
    px={4}
    py={0}
    fontSize="14px"
    minH={0}
    height="36px"
    _hover={{ bg: bgHover }}
    onClick={onClick}
    display={{ sm: 'none', md: 'flex' }}
  >
    {text}
  </Button>
);

const DetailRow = ({ label, value }) => (
  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
    <span className="text-[13px] sm:text-[14px] font-semibold text-[#09327B]">
      {label}
    </span>
    <span className="text-[13px] sm:text-[14px] font-bold text-gray-800">
      {value}
    </span>
  </div>
);

const FileHeader = ({
  breadItems,
  fileDetails,
  isSummaryPage,
  isShowFeaturesMenu,
  renderFeaturesMenuTemp
}) => {
  const [isFileSummaryOpen, setIsFileSummaryOpen] = useState(false);

  const toggleFileSummary = () => setIsFileSummaryOpen(!isFileSummaryOpen);

  return (
    <>
      <div className="py-2 px-4 sm:px-9 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
        <Breadcrumbs items={breadItems} />
        <Flex align="center" gap={2} sm:gap={4}>
          {/* Show full buttons on large screens, and icons on small screens */}
          {!isSummaryPage && (
            <>
              <FileActionButton
                icon={<FileDocumentSecondary />}
                bg="#F2F6F9"
                color="#323232"
                bgHover="gray.200"
                text={t('fileSummary')}
                onClick={toggleFileSummary}
              />
              <FileActionButton
                icon={<ServiceInfoIcon />}
                bg="none"
                color="#323232"
                text={t('Service Related Info.')}
              />
              <FileActionButton
                icon={<SearchIcon color="#00B2EB" />}
                bg="none"
                color="#00B2EB"
                text={t('File Search')}
              />

              {/* Show Icon Buttons on Mobile */}
              <IconButton
                size="sm"
                variant="link"
                icon={<FileDocumentSecondary />}
                display={{ md: 'none' }}
                onClick={toggleFileSummary}
              />
              <IconButton
                size="sm"
                variant="link"
                icon={<ServiceInfoIcon />}
                display={{ md: 'none' }}
              />
              <IconButton
                size="sm"
                variant="link"
                icon={<SearchIcon color="#00B2EB" />}
                display={{ md: 'none' }}
              />
            </>
          )}

          {isSummaryPage && <RoutingActions />}

          {/* Features Menu */}
          {isShowFeaturesMenu && (
            <>
              {/* Commented for use it later */}
              {/* <IconButton size="sm" variant="link" icon={<MenuBarHorizontal />} />  */}
              {renderFeaturesMenuTemp()}
            </>
          )}
        </Flex>
      </div>

      <div className="border-t px-4 sm:px-10">
        <Collapse open={isFileSummaryOpen}>
          <Flex
            justifyContent="space-between"
            wrap="wrap"
            className="mb-4 pb-3 pt-2"
          >
            <DetailRow label={t('role')} value={fileDetails?.role} />
            <DetailRow label={t('service')} value={fileDetails?.serviceName} />
            <DetailRow label={t('fileDate')} value={fileDetails?.createdAt} />
            <DetailRow
              label={t('submittedBy')}
              value={fileDetails?.createdByName}
            />
            <DetailRow
              label={t('submittedOn')}
              value={fileDetails?.updatedAt}
            />
            <DetailRow label={t('currentStatus')} value={fileDetails?.stage} />
          </Flex>
        </Collapse>
      </div>
    </>
  );
};

export default FileHeader;
