import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect, useSelector } from 'react-redux';
import { Outlet, useLocation, useSearchParams } from 'react-router-dom';
import { dark, light, secondary } from 'utils/color';
import MainHeader from 'common/components/MainHeader';
import { actions as commonSliceActions } from 'pages/common/slice';
import FormTitle from 'common/components/FormTitle';
import { STATE_REDUCER_KEY } from 'pages/common';
import BackArrow from 'assets/BackIcon';
import { Button, IconButton } from '@ksmartikm/ui-components';
import { CITIZEN_SERVICE_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { getEFilePreview } from 'pages/citizen/e-file/selectors';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { t } from 'i18next';
import InfoSolid from 'assets/InfoSolid';
import { KSWIFT_HOME_URL } from 'pages/kswift/login/constants';
import { Body, Sidebar } from './components';

const Layout = ({
  eFilePreview,
  setAlertAction
}) => {
  const {
    layout: { formTitle: { title = 'welcome', variant = 'normal' } } = {},
    sidebarData: { activeStep }
  } = useSelector((state) => state[STATE_REDUCER_KEY]);

  const location = useLocation();
  const [noteTrigger, setNoteTrigger] = useState(false);
  const [searchParams] = useSearchParams();

  const handleOpen = () => {
    setAlertAction({
      open: true, title: eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED ? t('reasonForRejection') : t('reasonForReturn'), message: eFilePreview?.lastNote, variant: 'alert', backwardActionText: t('ok')
    });
  };

  const backToHome = () => {
    if (location.pathname.includes('citizen/e-file')) {
      if (searchParams?.get('kswiftId')) {
        localStorage.clear();
        window.location.href = KSWIFT_HOME_URL;
      } else {
        window.location.href = CITIZEN_SERVICE_PATH;
      }
    } else {
      window.location.href = EMPLOYEE_SERVICE_PATH;
    }
  };

  const offset = 5;

  const handleScroll = (e, top) => {
    window.scrollTo({
      top: (Number(e) * 60) + top,
      behavior: 'smooth'
    });
  };

  useEffect(() => {
    if (activeStep) {
      setTimeout(() => {
        handleScroll(activeStep, offset);
      }, 100);
    }
  }, [activeStep]);

  useEffect(() => {
    if ((eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.RETURN_TO_CITIZEN && eFilePreview?.lastNote && !noteTrigger) || (eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED && eFilePreview?.lastNote && !noteTrigger)) {
      setAlertAction({
        open: true, title: eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED ? t('reasonForRejection') : t('reasonForReturn'), message: eFilePreview?.lastNote, variant: 'alert', backwardActionText: t('ok')
      });
      setNoteTrigger(true);
    }
  }, [eFilePreview]);

  return (
    <div style={{ backgroundColor: light }} className="font-body">
      <MainHeader />
      <div className="fixed w-full top-[65px] px-[80px] z-50 pb-3" style={{ backgroundColor: light }}>
        <div className="flex items-center gap-3 mt-5 pb-1">
          <div className="flex-none">
            <IconButton onClick={backToHome} variant="ghost" icon={<BackArrow color={dark} width="12" height="12" />} />
          </div>
          <div className="flex-grow">
            <FormTitle title={title} variant={variant} />
          </div>
          {(eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.RETURN_TO_CITIZEN || eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED) && (
            <div className="flex-none">
              <Button variant="ghost" onClick={handleOpen} leftIcon={<InfoSolid color={secondary} />}>
                { eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED ? t('reasonForRejection') : t('reasonForReturn')}
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className="px-[80px] pt-[140px]">
        <div className="col-span-12">

          <div className="flex auto-cols-max gap-5">
            <div className="flex-none w-[250px]">
              <div className="fixed w-[250px] left-[80px]">
                <Sidebar />
              </div>
            </div>
            <div className="grow mt-1">
              <Body>
                <Outlet />
              </Body>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  eFilePreview: getEFilePreview
});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Layout);
