import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  Button,
  Select,
  t
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { TextInput } from '@ksmartikm/ui-components';
import * as actions from '../pages/common/actions';

const Verification = ({
  open, handleClose, fetchVerification, loginCitizen, sendOtp
}) => {
  const pens = [
    { name: 'OPERATOR - CSEM03', pen: 'G67930' },
    { name: 'VERIFIER - CSEM03', pen: 'G66257' },
    { name: 'APPROVER - CSEM03', pen: 'G67789' },
    { name: 'OPERATOR - SEPA05', pen: 'G67706' },
    { name: 'VERIFIER - SEPA05', pen: 'G67988' },
    { name: 'APPROVER - SEPA05', pen: 'G67932' }

  ];

  const [pen, setPen] = useState(null);
  const [log, setLog] = useState(null);

  const submit = (type) => {
    switch (type) {
      case 'employee':
        fetchVerification({ pen, otp: '2732' });
        handleClose();
        break;
      case 'citizen':
        loginCitizen({
          phoneNumber: '9567541955', otp: '123456', otpId: '', userType: 'CITIZEN'
        });
        handleClose();
        break;
      default:
        break;
    }
  };

  const handleOtp = () => {
    sendOtp({ pen });
  };

  return (
    <Modal isOpen={open} size="md" closeOnOverlayClick={false} onClose={handleClose} closeOnEsc={false} className="custom-form-modal">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <span size="xl">
            <h4 className="rounded-[98px] bg-[#E7EFF5] p-[14px] text-center text-[#09327B]">{t('login')}</h4>
          </span>
        </ModalHeader>
        <ModalBody>
          <div className="grid grid-rows-3 gap-4 mb-6">

            <div className="flex gap-2">
              <TextInput value={log} onChange={(event) => { setPen(event.target.value); setLog(event.target.value); }} />
              <Button variant="primary" style={{ height: '55px' }} onClick={handleOtp}>
                Send Otp
              </Button>
            </div>

            <h4 className="text-center m-2">or</h4>

            <div className="flex gap-2">
              <Select value={log} onChange={(data) => { setPen(data.pen); setLog(data.name); }} options={pens} optionKey="name" />
              <Button variant="primary" style={{ height: '55px' }} onClick={handleOtp}>
                {t('sendOtp')}
              </Button>
            </div>
            <Button
              onClick={() => submit('employee')}
              size="lg"
              variant="secondary"
              colorScheme="pink"
              style={{ height: '55px' }}
            >
              {t('employeeLogin')}
            </Button>
            <h4 className="text-center m-2">or</h4>

            <Button
              onClick={() => submit('citizen')}
              size="lg"
              variant="secondary_outline"
              colorScheme="pink"
              style={{ height: '55px' }}
            >
              {t('citizenLogin')}
            </Button>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({

});

const mapDispatchToProps = (dispatch) => ({
  fetchVerification: (data) => dispatch(actions.fetchVerification(data)),
  loginCitizen: (data) => dispatch(actions.loginCitizen(data)),
  sendOtp: (data) => dispatch(actions.sendOtp(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Verification);
