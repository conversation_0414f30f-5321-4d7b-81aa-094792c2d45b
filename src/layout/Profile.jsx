import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import MainHeader from 'common/components/MainHeader';
import { Button } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { t } from 'i18next';
import { Sidebar } from './components';

const Profile = () => {
  const hyphenStyle = (
    <div className="px-2" style={{ color: dark, fontSize: '21px' }}>
      /
    </div>
  );
  const navigate = useNavigate();
  const location = useLocation();

  const backToProfile = () => {
    if (location?.pathname.includes('deactivated')) {
      navigate('ds/enroll');
    } else {
      window.history.back();
    }
  };

  const handleDeactivatedList = () => {
    navigate('ds/deactivated');
  };

  return (
    <div className="w-screen h-screen">
      <MainHeader />
      <div className="fixed top-[75px] w-full bg-[#E7EFF5] z-10">
        <div className="flex gap-4 px-20 py-4">
          <Button
            style={{ paddingLeft: 0, paddingRight: 0 }}
            variant="ghost"
            leftIcon={<BackArrow width="40px" height="40px" color={dark} />}
            onClick={backToProfile}
            rightIcon={hyphenStyle}
          >
            <h4 className="flex-grow font-semibold text-[21px]" style={{ color: dark }}>
              {t('profile')}
            </h4>
          </Button>
          <Button style={{ paddingLeft: 0, paddingRight: 0 }} variant="ghost">
            <h4 className="flex-grow font-semibold text-[21px]" style={{ color: dark }}>
              {location.pathname.includes('es') && t('eSignature')}
              {location.pathname.includes('ds') && t('digitalSignature')}
            </h4>
          </Button>
          <div className="flex-grow" />
          {!location?.pathname.includes('deactivated') && location?.pathname.includes('ds') && (
            <div>
              <Button variant="primary_outline" size="xs" onClick={handleDeactivatedList}>
                {t('deactivatedList')}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Updated Layout */}
      <div className="px-[80px] pt-[150px]">
        <div className="col-span-12">
          <div className="flex auto-cols-max gap-5">
            {/* Sidebar (conditionally rendered) */}
            {!location?.pathname.includes('ds') && (
              <div className="flex-none w-[250px]">
                <div className="fixed w-[250px] left-[80px]">
                  <Sidebar />
                </div>
              </div>
            )}
            {/* Main Content */}
            <div className="grow mt-1">
              <Outlet />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
