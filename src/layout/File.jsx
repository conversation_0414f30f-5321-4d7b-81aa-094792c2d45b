/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import {
  Outlet,
  useParams, useNavigate, useLocation, useSearchParams
} from 'react-router-dom';
import FileHead from 'common/components/Header';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { getFileDetails, getMergeLinkFiles } from 'pages/file/details/selector';
import { dark, light } from 'utils/color';
import MainHeader from 'common/components/MainHeader';
import { GeneralFeaturesMenu } from '@ksmartikm/ui-components/src/custom-components';
import { MENU } from 'common/GeneralMenu';
import {
  BASE_PATH, EMPLOYEE_ROLES, FINANCE_MODULES, MEETING_MANAGEMENT_MODULES
} from 'common/constants';
import { FILE_ROLE } from 'pages/common/constants';
import { generalFeatureMenu } from 'common/menuHandler';
import { routeRedirect } from 'utils/common';
import Summary from 'assets/Summary';
import NotesNewIcon from 'assets/NoteNewIcon';
import LinkIcon from 'assets/Link';
import UnLink from 'assets/UnLink';
import Merge from 'assets/Merge';
import UnMerge from 'assets/UnMerge';
import Beneficiary from 'assets/Beneficiary';
import { t } from 'i18next';
import { getPostIdByPenNoDetails, getUserInfo } from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { actions as sliceActions } from 'pages/file/details/slice';
import * as actions from 'pages/file/details/actions';
import { Body } from './components';
import FileHeader from './components/FileHeader';

const File = ({
  fileDetails, userInfo: { userDetails: { pen = '' } = {}, officeId } = {}, fetchPostIdByPenNo,
  postIdByPenNoDetails, setMergedBackButton, fetchFileDetails, mergeLinkFiles,
  fetchMergeLink, fetchEsignStatusCheck, setDraftNumber, setActionOccured,
  fetchForwardPlusRoleForDraftActions
}) => {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [breadItems, setBreadItems] = useState([]);
  const [linkedOptions, setLinkedOptions] = useState([]);
  const [mergedOptions, setMergedOptions] = useState([]);
  const DEMAND_SERVICES = ['BPPA01', 'BPPA02', 'BPPA03', 'BPPA07'];

  useEffect(() => {
    if (params?.fileNo) {
      fetchMergeLink(params?.fileNo);
    }
  }, [params?.fileNo]);

  useEffect(() => {
    if (mergeLinkFiles?.MERGING?.length > 0) {
      const merged = mergeLinkFiles?.MERGING?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      setMergedOptions(merged);
    } else {
      setMergedOptions([]);
    }
    if (mergeLinkFiles?.LINKED?.length > 0) {
      const linked = mergeLinkFiles?.LINKED?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      setLinkedOptions(linked);
    } else {
      setLinkedOptions([]);
    }
  }, [mergeLinkFiles]);

  const disableUnmegeMenu = () => {
    if (fileDetails?.postId !== fileDetails?.custodian?.id) {
      return true;
    } if (mergedOptions?.length === 0 || mergedOptions === undefined) {
      return true;
    }
    return false;
  };

  const featureMenu = () => {
    const menuUpdate = MENU[0]?.child;
    const menuArray = MENU;

    const disableMenuItem = (key) => {
      const findIndex = menuUpdate.findIndex((item) => item.key === key);
      if (findIndex !== -1) {
        menuArray[0].child[findIndex].isDisabled = true;
      }
    };

    const enableMenuItem = (key) => {
      const findIndex = menuUpdate.findIndex((item) => item.key === key);
      if (findIndex !== -1) {
        menuArray[0].child[findIndex].isDisabled = false;
      }
    };

    if (fileDetails?.role) {
      if (![FILE_ROLE.OPERATOR, FILE_ROLE.VERIFIER, FILE_ROLE.APPROVER].includes(fileDetails.role)) {
        disableMenuItem('custodian-change');
      }
      if (!['HRET01', 'HRET15', 'HRET06', 'HRWC03', 'HRET16', 'HREE04', 'HRET04'].includes(fileDetails.serviceCode)) {
        disableMenuItem('salary');
      }
      if (fileDetails.serviceCode !== 'FMAS42') {
        disableMenuItem('recoveryPayment');
      }
      if (fileDetails.serviceCode !== 'FMBD01') {
        disableMenuItem('budget');
      }
      if (DEMAND_SERVICES.includes(fileDetails.serviceCode) || [FILE_ROLE.ROUTE_CHANGE_ROLE].includes(fileDetails.role)) {
        disableMenuItem('demand');
      }
      if (fileDetails.serviceCode !== 'FMAS52') {
        disableMenuItem('standing-demand');
      }
      if (fileDetails?.postId !== fileDetails?.custodian?.id) {
        disableMenuItem('merge-file');
      }
      if (disableUnmegeMenu()) {
        disableMenuItem('unmerge');
      } else if (disableUnmegeMenu() === false) {
        enableMenuItem('unmerge');
      }
      if (linkedOptions?.length === 0 || linkedOptions === undefined) {
        disableMenuItem('un-link-file');
      } else {
        enableMenuItem('un-link-file');
      }

      if (fileDetails?.role !== FILE_ROLE.OPERATOR) {
        disableMenuItem('inward-de-link');
      } else {
        enableMenuItem('inward-de-link');
      }

      if (fileDetails.serviceCode !== 'FMAS60') {
        disableMenuItem('imprestClaim');
      }
      if (fileDetails.serviceCode !== 'FMAS05') {
        disableMenuItem('imprestDisbursement');
      }
      if (fileDetails.serviceCode !== 'FMAS35') {
        disableMenuItem('contraEntry');
      } else {
        enableMenuItem('contraEntry');
      }

      if (fileDetails.serviceCode !== 'FMAS37') {
        disableMenuItem('journalEntry');
      } else {
        enableMenuItem('journalEntry');
      }

      if ([FILE_ROLE.ROUTE_CHANGE_ROLE].includes(fileDetails.role)) {
        disableMenuItem('file-link');
        disableMenuItem('child');
        disableMenuItem('beneficiary');
        disableMenuItem('claim');
        disableMenuItem('paymentOrder');
        disableMenuItem('payment');
        disableMenuItem('receipt');
        disableMenuItem('demandCancellation');
        disableMenuItem('agenda');
        disableMenuItem('advanceClaim');
      } else {
        enableMenuItem('file-link');
        enableMenuItem('child');
        enableMenuItem('beneficiary');
        enableMenuItem('claim');
        enableMenuItem('paymentOrder');
        enableMenuItem('payment');
        enableMenuItem('receipt');
        enableMenuItem('demandCancellation');
        enableMenuItem('agenda');
        enableMenuItem('advanceClaim');
      }
    }

    return menuArray;
  };

  const submit = (data) => {
    if (data?.isDisabled) {
      return null;
    }
    if (data?.key === FINANCE_MODULES.DEMAND.KEY) {
      window.location.href = `${FINANCE_MODULES.DEMAND.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.CLAIM.KEY) {
      window.location.href = `${FINANCE_MODULES.CLAIM.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.PAYMENT_ORDER.KEY) {
      window.location.href = `${FINANCE_MODULES.PAYMENT_ORDER.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.PAYMENT.KEY) {
      window.location.href = `${FINANCE_MODULES.PAYMENT.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.BUDGET.KEY) {
      window.location.href = `${FINANCE_MODULES.BUDGET.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.RECEIPT.KEY) {
      window.location.href = `${FINANCE_MODULES.RECEIPT.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.SALARY.KEY) {
      window.location.href = `${FINANCE_MODULES.SALARY.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.RECOVERY_PAYMENT.KEY) {
      window.location.href = `${FINANCE_MODULES.RECOVERY_PAYMENT.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.STANDING_DEMAND.KEY) {
      window.location.href = `${FINANCE_MODULES.STANDING_DEMAND.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.REQUISITION.KEY) {
      window.location.href = `${FINANCE_MODULES.REQUISITION.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.OWN_FUND_PAYMENT.KEY) {
      window.location.href = `${FINANCE_MODULES.OWN_FUND_PAYMENT.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.REQUISITION_ALLOTMENT.KEY) {
      window.location.href = `${FINANCE_MODULES.REQUISITION_ALLOTMENT.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.DEMAND_CANCELLATION.KEY) {
      window.location.href = `${FINANCE_MODULES.DEMAND_CANCELLATION.URL}/${params?.fileNo}`;
    } else if (data?.key === MEETING_MANAGEMENT_MODULES.AGENDA.KEY) {
      window.location.href = `${MEETING_MANAGEMENT_MODULES.AGENDA.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.BILL_GENERATE.KEY) {
      window.location.href = `${FINANCE_MODULES.BILL_GENERATE.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.BILL_SEND.KEY) {
      window.location.href = `${FINANCE_MODULES.BILL_SEND.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.IMPREST_CLAIM.KEY) {
      window.location.href = `${FINANCE_MODULES.IMPREST_CLAIM.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.ADVANCE_CLAIM.KEY) {
      window.location.href = `${FINANCE_MODULES.ADVANCE_CLAIM.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.IMPREST_DISBURSEMENT.KEY) {
      window.location.href = `${FINANCE_MODULES.IMPREST_DISBURSEMENT.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.CONTRA_ENTRY.KEY) {
      window.location.href = `${FINANCE_MODULES.CONTRA_ENTRY.URL}/${params?.fileNo}`;
    } else if (data?.key === FINANCE_MODULES.JOURNAL_ENTRY.KEY) {
      window.location.href = `${FINANCE_MODULES.JOURNAL_ENTRY.URL}/${params?.fileNo}`;
    } else {
      navigate(`${BASE_PATH}/file/${params?.fileNo}/${data?.key}`);
    }
    return null;
  };

  const activeColor = (activeProp) => {
    return activeProp ? dark : '#5C6E93';
  };

  const isActiveByLocation = (type) => {
    const paths = {
      summary: '/summary',
      note: '/notes',
      draft: '/draft',
      filelink: '/file-link',
      fileUnLink: '/un-link-file',
      mergeFile: '/merge-file',
      unmergeFile: '/unmerge',
      child: '/child',
      beneficiary: '/beneficiary',
      custodianChange: '/custodian-change',
      inwardDeLink: 'inward-de-link'
    };

    return Object.keys(paths).some((key) => {
      return location.pathname.includes(paths[key]) && type === key;
    });
  };

  const paramsValues = new URLSearchParams(window.location.search);
  const fromValue = paramsValues.get('from');

  const handlePreviousRoute = () => {
    const returnUrl = searchParams.get('returnUrl');

    if (returnUrl) {
      window.location.href = `${window.location.origin}${returnUrl}`;
      return;
    }

    if (location.pathname.includes('/draft')) {
      if (searchParams.get('from') === 'summary') {
        navigate(`${BASE_PATH}/file/${params?.fileNo}/summary`);
      } else {
        navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
      }
    } else if (location.pathname.includes('/summary')) {
      if (fromValue === 'search-files') {
        navigate(`${BASE_PATH}/services/search-files?${paramsValues}`);
      } else if (fromValue === 'disposed-file') {
        navigate(`${BASE_PATH}/services/disposed-files`);
      } else if (fromValue === 'archived-file') {
        navigate(`${BASE_PATH}/services/archived-files`);
      } else if (fromValue === 'outbox-list') {
        routeRedirect('ui/home/<USER>/dashboard/outbox');
      } else {
        routeRedirect('ui/home/<USER>/dashboard/files');
      }
    } else if (location.pathname.includes('/notes')) {
      if (fromValue === 'search-files' || fromValue === 'outbox-list') {
        paramsValues.delete('show');
        navigate(`${BASE_PATH}/file/${params?.fileNo}/summary?${paramsValues}`);
      } else {
        navigate(`${BASE_PATH}/file/${params?.fileNo}/summary`);
      }
    } else {
      // routeRedirect(`ui/${fileDetails?.url}`);
      setMergedBackButton(true);
      fetchFileDetails(params.fileNo);
    }
  };

  const handleSummary = () => {
    setMergedBackButton(true);
    fetchFileDetails(params.fileNo);
  };

  const hyphenStyle = <div className="px-2" style={{ color: dark, fontSize: '21px' }}>/</div>;

  const breadItemBack = {
    isRouteBackAction: true, onClick: () => handlePreviousRoute()
  };

  // const breadItemDash = {
  //   text: 'Inbox', rightIcon: hyphenStyle, leftIcon: '', color: activeColor(false), onClick: () => routeRedirect('ui/home/<USER>/dashboard')
  // };

  const breadItemSummary = {
    text: 'Summary', rightIcon: hyphenStyle, leftIcon: <Summary width="24px" height="24px" color={activeColor(isActiveByLocation('summary'))} />, color: activeColor(isActiveByLocation('summary')), onClick: () => handleSummary()
  };

  const breadItemInbox = {
    text: 'Inbox',
    onClick: () => { routeRedirect('ui/home/<USER>/dashboard/files'); }
  };

  const breadItemsNotes = {
    text: 'Note File',
    rightIcon: hyphenStyle,
    leftIcon: <NotesNewIcon width="24px" height="24px" color={activeColor(isActiveByLocation('note'))} />,
    color: activeColor(isActiveByLocation('note')),
    onClick: () => {
      const baseUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=0`;
      const paramsValuesCopy = new URLSearchParams(paramsValues);
      paramsValuesCopy.delete('show');
      const finalUrl = paramsValuesCopy.toString() ? `${baseUrl}&${paramsValuesCopy}` : baseUrl;
      navigate(finalUrl);
    }
  };

  const breadItemsNoteWithFileNumber = {
    text: <span className="text-[15px]">File No. <b className="text-[#456C86]">{params?.fileNo}</b></span>,
    onClick: () => {
      const baseUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=0`;
      const paramsValuesCopy = new URLSearchParams(paramsValues);
      paramsValuesCopy.delete('show');
      const finalUrl = paramsValuesCopy.toString() ? `${baseUrl}&${paramsValuesCopy}` : baseUrl;
      navigate(finalUrl);
    }
  };

  const breadItemsDraft = { text: 'New Draft' };

  const breadItemFileLink = {
    text: 'Link File', rightIcon: hyphenStyle, leftIcon: <LinkIcon width="24px" height="24px" color={activeColor(isActiveByLocation('filelink'))} />, color: activeColor(isActiveByLocation('filelink')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/file-link`)
  };

  const breadItemFileUnLink = {
    text: 'UnLink File', rightIcon: hyphenStyle, leftIcon: <UnLink width="24px" height="24px" color={activeColor(isActiveByLocation('fileUnLink'))} />, color: activeColor(isActiveByLocation('fileUnlink')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/un-link-file`)
  };

  const breadItemMergeFile = {
    text: 'Merge File', rightIcon: hyphenStyle, leftIcon: <Merge width="24px" height="24px" color={activeColor(isActiveByLocation('mergeFile'))} />, color: activeColor(isActiveByLocation('mergeFile')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/merge-file`)
  };

  const breadItemUnMergeFile = {
    text: 'UnMerge File', rightIcon: hyphenStyle, leftIcon: <UnMerge width="24px" height="24px" color={activeColor(isActiveByLocation('unmergeFile'))} />, color: activeColor(isActiveByLocation('unmergeFile')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/unmerge`)
  };

  const breadItemChild = {
    text: 'Child File', rightIcon: hyphenStyle, leftIcon: <UnMerge width="24px" height="24px" color={activeColor(isActiveByLocation('child'))} />, color: activeColor(isActiveByLocation('child')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/child`)
  };

  const breadItemBeneficiary = {
    text: 'Beneficiary', rightIcon: hyphenStyle, leftIcon: <Beneficiary width="24px" height="24px" color={activeColor(isActiveByLocation('beneficiary'))} />, color: activeColor(isActiveByLocation('beneficiary')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/beneficiary`)
  };

  const breadItemCustodian = {
    text: 'Custodian Change', rightIcon: hyphenStyle, leftIcon: <Beneficiary width="24px" height="24px" color={activeColor(isActiveByLocation('custodianChange'))} />, color: activeColor(isActiveByLocation('custodianChange')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/custodian-change`)
  };

  const breadItemInwardDeLink = {
    text: 'Inward De-Link', rightIcon: hyphenStyle, leftIcon: <Beneficiary width="24px" height="24px" color={activeColor(isActiveByLocation('inwardDeLink'))} />, color: activeColor(isActiveByLocation('inwardDeLink')), onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/inward-de-link`)
  };

  const fileBreadCrumb = () => {
    if (location.pathname.includes('/summary')) {
      setBreadItems([breadItemBack, breadItemSummary]);
    } if (location.pathname.includes('/notes')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemsNotes]);
    }
    if (location.pathname.includes('/draft')) {
      setBreadItems([breadItemBack, breadItemInbox, breadItemsNoteWithFileNumber, breadItemsDraft]);
    }
    if (location.pathname.includes('/file-link')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemFileLink]);
    }
    if (location.pathname.includes('/un-link-file')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemFileUnLink]);
    }
    if (location.pathname.includes('/merge-file')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemMergeFile]);
    }
    if (location.pathname.includes('/unmerge')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemUnMergeFile]);
    }
    if (location.pathname.includes('/child')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemChild]);
    }
    if (location.pathname.includes('/beneficiary')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemBeneficiary]);
    }
    if (location.pathname.includes('/custodian-change')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemCustodian]);
    }
    if (location.pathname.includes('/inward-de-link')) {
      setBreadItems([breadItemBack, breadItemSummary, breadItemInwardDeLink]);
    }
  };

  useEffect(() => {
    fileBreadCrumb();
  }, [location?.pathname, fileDetails?.url]);

  useEffect(() => {
    if (pen) fetchPostIdByPenNo({ penNo: pen });
  }, [pen]);

  const forEsign = paramsValues.get('for');
  const moduleId = paramsValues.get('moduleId');
  const draftNo = paramsValues.get('draftNo');

  useEffect(() => {
    if (forEsign === 'esign' && moduleId && draftNo) {
      setDraftNumber(Number(draftNo));
      setActionOccured(true);
      fetchEsignStatusCheck({
        officeCode: officeId, moduleUid: moduleId, fileNo: params?.fileNo
      });
    }
  }, [forEsign, moduleId, officeId, draftNo]);

  useEffect(() => {
    if (fileDetails?.role === EMPLOYEE_ROLES.FORWARD_PLUS_ROLE) {
      fetchForwardPlusRoleForDraftActions({ fileNo: fileDetails?.fileNo });
    }
  }, [fileDetails]);

  const renderFeaturesMenu = () => {
    return (
      <GeneralFeaturesMenu size="small" menu={featureMenu()} handleSubmit={submit} location={location} isDisabled={generalFeatureMenu(fileDetails?.status, fileDetails?.role, searchParams.get('flowaction'), postIdByPenNoDetails, fileDetails?.postId)} />
    );
  };

  return (
    <div
      style={{ backgroundColor: light }}
      className="h-screen overflow-auto font-body pb-10"
    >
      <MainHeader />

      <div className="fixed w-full top-[70px] z-40 border-t shadow-md bg-white">
        <FileHeader
          breadItems={breadItems}
          fileDetails={fileDetails}
          isSummaryPage={location.pathname.includes('/summary')}
          isShowFeaturesMenu={location.pathname.includes(`${BASE_PATH}/file`)}
          renderFeaturesMenuTemp={renderFeaturesMenu}
        />
        {location.pathname.includes('/summary') && <FileHead />}
      </div>

      <div
        className={
          location.pathname.includes('/summary')
            ? 'pl-[36px] pr-[21px] pt-[215px]'
            : 'pl-[36px] pr-[21px] pt-[144px]'
        }
      >
        <Body>
          <Outlet />
        </Body>
        <ksmart-widget applicationId="ksmart-floating-widget" />
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  userInfo: getUserInfo,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  mergeLinkFiles: getMergeLinkFiles
});

const mapDispatchToProps = (dispatch) => ({
  fetchPostIdByPenNo: (data) => dispatch(commonActions.fetchPostIdByPenNo(data)),
  setMergedBackButton: (data) => dispatch(sliceActions.setMergedBackButton(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  fetchMergeLink: (data) => dispatch(actions.fetchMergeLink(data)),
  fetchEsignStatusCheck: (data) => dispatch(actions.fetchEsignStatusCheck(data)),
  setDraftNumber: (data) => dispatch(sliceActions.setDraftNumber(data)),
  setActionOccured: (data) => dispatch(sliceActions.setActionOccured(data)),
  fetchForwardPlusRoleForDraftActions: (data) => dispatch(actions.fetchForwardPlusRoleForDraftActions(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(File);
