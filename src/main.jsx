import React, { Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import {
  InfoPages, ThemeProvider, theme, ToastContainer, Navigator
} from 'common/components';
import Alert from 'common/components/Alert';
import { routes } from 'pages/routes';
import { Fonts } from '@ksmartikm/ui-components';
import { store } from './app/store';
import './index.css';
import './i18n';

// Make React available globally for UI components
window.React = React;

const container = document.getElementById('root');
const root = createRoot(container);
const renderRoutes = (appRoutes) => appRoutes.map((route, index) => {
  const keyIndex = `${index}-route`;
  return (
    <Route
      key={keyIndex}
      path={route.path}
      element={route.element}
      {...(route.errorElement && { errorElement: route.errorElement })}
    >
      {route.children && renderRoutes(route.children)}
    </Route>
  );
});

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <Suspense fallback={<InfoPages.Loading />}>
        <ThemeProvider theme={theme}>
          <BrowserRouter>
            <Routes>
              {renderRoutes(routes)}
            </Routes>
            <Fonts />
            <Navigator />
            <Alert />
          </BrowserRouter>
          <ToastContainer />
        </ThemeProvider>
      </Suspense>
    </Provider>
  </React.StrictMode>
);
