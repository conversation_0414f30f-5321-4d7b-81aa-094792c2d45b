import React, { useState, useEffect } from 'react';
import {
  CustomTab,
  FormController,
  FormModal, Toast, t
} from 'common/components';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { convertInputDate, convertToLocalDate } from 'utils/date';
import { DATE_FORMAT, FILTER_TYPE, SOURCE_TYPE } from 'pages/common/constants';
import _ from 'lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import { AdvanceSearchSchema, ApplicantNumberSchema } from '../validate';

// const InwardSearch = ({ onSubmitForm }) => {
//   const {
//     control,
//     handleSubmit,
//     formState: { errors }
//   } = useForm({
//     mode: 'all',
//     defaultValues: {
//       inwardNo: null,
//       applicantName: null,
//       fromDate: null,
//       toDate: null
//     },
//     resolver: yupResolver(InwardSearchSchema)
//   });
//   return (
//     <form
//       id="advance-file-form"
//       action="enter"
//       onSubmit={handleSubmit(onSubmitForm)}
//     >
//       <div className="grid grid-cols-1 gap-4">
//         <FormController
//           name="inwardNumber"
//           type="text"
//           label={t('inwardNumber')}
//           placeholder={t('number')}
//           control={control}
//           required
//           errors={errors}
//         />
//       </div>
//     </form>
//   );
// };
const FileSearch = ({
  onSubmitForm, modulesDropdown, subModulesDropdown, servicesDropdown,
  seatsDropdown, handleFieldChange, statusDropdown, fileTypeDropdown, department,
  setSearchListParams
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors }, watch
  } = useForm({
    mode: 'all',
    defaultValues: {
      serviceCode: null,
      moduleCode: null,
      submoduleCode: null,
      fileNo: null,
      applicantName: null,
      department: null,
      seat: null,
      fromDate: null,
      toDate: null,
      fileStatus: null,
      sourceType: null
    },
    resolver: yupResolver(AdvanceSearchSchema)
  });

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const navigate = useNavigate();

  useEffect(() => {
    if (queryParams) {
      const searchParams = {
        keyword: queryParams.get('keyword') || null,
        search: queryParams.get('search') || null,
        serviceCode: queryParams.get('serviceCode') || null,
        moduleCode: queryParams.get('moduleCode') || null,
        submoduleCode: queryParams.get('submoduleCode') || null,
        fileNumber: queryParams.get('fileNo') || null,
        applicantName: queryParams.get('applicantName') || null,
        department: queryParams.get('department') || null,
        seat: queryParams.get('seat') || null,
        fromDate: queryParams.get('fromDate') || null,
        toDate: queryParams.get('toDate') || null,
        statusList: queryParams.get('statusList') || null,
        createdAt: queryParams.get('createdAt') || null,
        source: queryParams.get('source') || null,
        officeId: queryParams.get('officeId'),
        page: 0
      };

      const filteredSearchParams = Object.fromEntries(
        Object.entries(searchParams).filter(([, value]) => value)
      );

      setSearchListParams(filteredSearchParams);
      const newQueryString = new URLSearchParams(filteredSearchParams).toString();
      navigate(`?${newQueryString}`, { replace: true });
    }
  }, [JSON.stringify(queryParams)]);

  const fromDateChange = watch('fromDate');

  return (
    <form
      id="advance-file-form"
      action="enter"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <div className="grid grid-cols-3 gap-4">
        <FormController
          name="fileNumber"
          type="text"
          label={t('fileNumber')}
          placeholder={t('number')}
          control={control}
        />
        <FormController
          name="applicantName"
          type="text"
          label={t('applicantName')}
          placeholder={t('applicantName')}
          control={control}
        />
        <FormController
          name="modules"
          type="select"
          label={t('module')}
          placeholder={t('module')}
          control={control}
          options={_.get(modulesDropdown, 'data', [])}
          handleChange={(data) => handleFieldChange(FILTER_TYPE.MODULES, data)}
        />
        <FormController
          name="subModule"
          type="select"
          label={t('subModule')}
          placeholder={t('subModule')}
          control={control}
          options={subModulesDropdown}
          handleChange={(data) => handleFieldChange(FILTER_TYPE.SUB_MODULES, data)}
        />
        <FormController
          name="services"
          type="select"
          label={t('services')}
          placeholder={t('searchHere')}
          control={control}
          options={servicesDropdown}
          handleChange={(data) => {
            handleFieldChange(FILTER_TYPE.SERVICES, data);
          }}
        />
        <FormController
          name="department"
          type="select"
          label={t('department')}
          placeholder={t('searchHere')}
          optionKey="name"
          control={control}
          options={department}
          handleChange={(data) => handleFieldChange(FILTER_TYPE.DEPARTMENT, data)}
        />

        <FormController
          name="seat"
          type="select"
          label={t('seat')}
          placeholder={t('searchHere')}
          control={control}
          options={seatsDropdown}
          optionKey="name"
        />
        <FormController
          type="date"
          name="fromDate"
          label={t('fromDate')}
          control={control}
          errors={errors}
          dateFormat="dd-MM-yyyy"
          maxDate={new Date()}
        />
        <FormController
          type="date"
          name="toDate"
          label={t('toDate')}
          control={control}
          errors={errors}
          dateFormat="dd-MM-yyyy"
          minDate={fromDateChange}
          maxDate={new Date()}
        />
        <FormController
          name="fileStatus"
          type="select"
          label={t('fileStatus')}
          placeholder={t('fileStatus')}
          control={control}
          options={statusDropdown}
          optionKey="name"
        />
        <FormController
          name="fileType"
          type="select"
          label={t('fileType')}
          placeholder={t('fileType')}
          control={control}
          options={_.get(fileTypeDropdown, 'data', [])}
        />
        <FormController
          name="sourceType"
          type="select"
          label={t('sourceType')}
          placeholder={t('sourceType')}
          control={control}
          options={SOURCE_TYPE}
          optionKey="id"
        />
      </div>
    </form>
  );
};

const ApplicantNumberSearch = ({ onSubmitForm, setSearchListParams }) => {
  const {
    control,
    handleSubmit,
    formState: { errors }, setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
      applicationNumber: null
    },
    resolver: yupResolver(ApplicantNumberSchema)
  });

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const navigate = useNavigate();

  useEffect(() => {
    if (queryParams) {
      const searchParams = {
        keyword: queryParams.get('keyword') || null,
        search: queryParams.get('search'),
        serviceCode: queryParams.get('serviceCode'),
        moduleCode: queryParams.get('moduleCode'),
        submoduleCode: queryParams.get('submoduleCode'),
        fileNumber: queryParams.get('fileNo'),
        applicantName: queryParams.get('applicantName'),
        department: queryParams.get('department'),
        seat: queryParams.get('seat'),
        fromDate: queryParams.get('fromDate'),
        toDate: queryParams.get('toDate'),
        statusList: queryParams.get('statusList') || null,
        createdAt: queryParams.get('createdAt') || null,
        applicationNumber: queryParams.get('applicationNumber') || null,
        officeId: queryParams.get('officeId'),
        page: 0
      };

      const filteredSearchParams = Object.fromEntries(
        Object.entries(searchParams).filter(([, value]) => value)
      );

      setSearchListParams(filteredSearchParams);
      setValue('fileNumber', queryParams.get('fileNo'));
      setValue('applicantName', queryParams.get('applicantName'));
      setValue('modules', queryParams.get('moduleCode'));
      setValue('subModule', queryParams.get('submoduleCode'));
      setValue('services', queryParams.get('serviceCode'));
      setValue('department', queryParams.get('department'));
      setValue('seat', queryParams.get('seat'));
      setValue('fromDate', convertInputDate(queryParams.get('fromDate'), DATE_FORMAT.LOCAL_DATE_REVERSE));
      setValue('toDate', convertInputDate(queryParams.get('toDate'), DATE_FORMAT.LOCAL_DATE_REVERSE));
      setValue('fileType', queryParams.get('fileType'));
      setValue('sourceType', queryParams.get('sourceType'));
      setValue('applicationNumber', queryParams.get('applicationNumber'));

      const newQueryString = new URLSearchParams(filteredSearchParams).toString();
      navigate(`?${newQueryString}`, { replace: true });
    }
  }, [JSON.stringify(queryParams)]);

  return (
    <form
      id="advance-file-form"
      action="enter"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <div className="grid grid-cols-1 gap-4">
        <FormController
          name="applicationNumber"
          type="text"
          label={t('applicationOrInwardNumber')}
          placeholder={t('number')}
          control={control}
          required
          errors={errors}
        />
      </div>
    </form>
  );
};

function ModalForm({
  modulesDropdown,
  handleFieldChange,
  subModulesDropdown,
  servicesDropdown,
  setSearchListParams, searchFileParams,
  seatsDropdown, handleClose, statusDropdown, fileTypeDropdown, department, userInfo, setPage, setSeats
}) {
  const { errorTost } = Toast;
  const [activeIndex, setActiveIndex] = useState(0);
  const [seatArray, setSeatArray] = useState([]);
  useEffect(() => {
    const updatedData = seatsDropdown?.map((obj) => {
      return {
        name: obj.postName, id: obj.postId, employeeName: obj?.employeeName, penNo: obj?.penNo
      };
    });
    setSeatArray(updatedData);
  }, [seatsDropdown]);

  const onSubmitForm = (data) => {
    const result = !Object.values(data).every((o) => o === null || o === undefined);
    if (result) {
      if (data?.inwardNumber && activeIndex === 1) {
        setPage(0);
        setSearchListParams({
          search: true,
          inwardNo: data?.inwardNumber,
          officeId: userInfo.officeId,
          applicationNumber: null,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          keyword: null,
          statusList: null,
          fileType: null,
          source: null
        });
        // } else if (data?.applicationNumber && activeIndex === 2) {
        //   setPage(0);
        //   setSearchListParams({
        //     ...searchFileParams,
        //     search: true,
        //     officeId: userInfo.officeId,
        //     applicationNumber: data?.applicationNumber,
        //     inwardNo: null,
        //     serviceCode: null,
        //     moduleCode: null,
        //     submoduleCode: null,
        //     fileNo: null,
        //     applicantName: null,
        //     department: null,
        //     seat: null,
        //     fromDate: null,
        //     toDate: null,
        //     keyword: null,
        //     statusList: null,
        //     fileType: null,
        //     source: null
        //   });
      } else {
        setPage(0);
        setSearchListParams({
          ...searchFileParams,
          serviceCode: data.services,
          moduleCode: data.modules,
          submoduleCode: data.subModule,
          fileNo: data.fileNumber,
          applicantName: data.applicantName,
          department: data.department,
          seat: data.seat,
          fromDate: convertToLocalDate(data.fromDate, DATE_FORMAT.DATE_LOCAL),
          toDate: convertToLocalDate(data.toDate, DATE_FORMAT.DATE_LOCAL),
          search: true,
          keyword: null,
          statusList: data.fileStatus,
          fileType: data.fileType,
          officeId: userInfo.officeId,
          source: data?.sourceType,
          applicationNumber: data?.applicationNumber,
          inwardNo: null
        });
      }
      handleClose();
      setSeatArray([]);
      setSeats([]);
    } else {
      errorTost({
        description: t('selectAnyData')
      });
    }
  };
  const tabs = [
    {
      title: t('fileSearch'),
      content: <FileSearch
        onSubmitForm={onSubmitForm}
        modulesDropdown={modulesDropdown}
        subModulesDropdown={subModulesDropdown}
        servicesDropdown={servicesDropdown}
        setSearchListParams={setSearchListParams}
        handleFieldChange={handleFieldChange}
        seatsDropdown={seatArray}
        statusDropdown={statusDropdown}
        fileTypeDropdown={fileTypeDropdown}
        department={department}
      />
    },
    // {
    //   title: t('fileSearchByInward'),
    //   content: <InwardSearch
    //     onSubmitForm={onSubmitForm}
    //   />
    // },
    {
      title: t('fileSearchByApplicationOrInwardNumber'),
      content: <ApplicantNumberSearch
        onSubmitForm={onSubmitForm}
        setSearchListParams={setSearchListParams}
      />
    }
  ];
  const handleTabsChange = (data) => {
    setSearchListParams({
      search: false,
      sortDirection: 'desc',
      page: 0,
      size: 10,
      officeId: userInfo.officeId,
      inwardNo: null,
      applicationNumber: null,
      serviceCode: null,
      moduleCode: null,
      submoduleCode: null,
      fileNo: null,
      applicantName: null,
      department: null,
      seat: null,
      fromDate: null,
      toDate: null,
      keyword: null,
      statusList: null,
      fileType: null,
      source: null
    });
    setActiveIndex(data?.index);
  };
  return (
    <div className="h-96">
      <CustomTab data={tabs} handleChange={handleTabsChange} currentIndex={activeIndex} />
    </div>
  );
}

const AdvanceSearch = ({
  modulesDropdown, open, subModulesDropdown, servicesDropdown, handleFieldChange, setSearchListParams, searchFileParams, seatsDropdown, handleClose, statusDropdown, fileTypeDropdown, department, userInfo,
  setPage, setSeats
}) => {
  return (
    <FormModal
      modalTitle={t('advanceSearch')}
      open={open}
      close={handleClose}
      actionButtonText={t('go')}
      content={(
        <ModalForm
          modulesDropdown={modulesDropdown}
          subModulesDropdown={subModulesDropdown}
          servicesDropdown={servicesDropdown}
          handleFieldChange={handleFieldChange}
          setSearchListParams={setSearchListParams}
          searchFileParams={searchFileParams}
          seatsDropdown={seatsDropdown}
          handleClose={handleClose}
          statusDropdown={statusDropdown}
          fileTypeDropdown={fileTypeDropdown}
          department={department}
          userInfo={userInfo}
          setPage={setPage}
          setSeats={setSeats}
        />
      )}
      formId="advance-file-form"
      type="submit"
      modalSize="4xl"
      closeButtonText={t('cancel')}
      closeOnOverlayClick={false}
      closeOnEsc={false}
    />
  );
};

export default AdvanceSearch;
