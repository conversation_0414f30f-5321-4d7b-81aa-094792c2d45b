import SearchIcon from 'assets/SearchIcon';
import {
  Button,
  IconButton,
  Input, InputGroup, InputRightElement, t
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import { actions as commonSliceActions } from 'pages/common/slice';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { convertInputDate, convertToLocalDate } from 'utils/date';
import {
  DATE_FORMAT, FILTER_TYPE, X_STATE_CODE
} from 'pages/common/constants';
import * as commonActions from 'pages/common/actions';
import {
  getFileTypeDropdown, getFunctionalGroups, getModulesDropdown, getSeats, getStatus, getTableLoader, getUserInfo
} from 'pages/common/selectors';
import { capitalizeFirstLetter } from 'utils/capitalize';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import TableView from 'assets/TableView';
import { AUDIT_APPLICATION_PATH, BASE_UI_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { useLocation, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import * as actions from '../actions';
import {
  getSearchList, getSearchListParams, getServicesSearchList, getSubModulesSearchList
} from '../selectors';
import { actions as sliceActions } from '../slice';
import AdvanceSearch from './AdvanceSearch';
import {
  handleApplicantName, handleCurrentUser, handleCustodian, handleFileNumber, handleFileStage, handleFileStatus, handleInwardNumber, handleServiceName, handleTitle
} from './Helper';

const styles = {
  head: {
    fontSize: '18px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #CBD5E0',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const List = ({
  fetchSearchFiles, searchFileData, setSearchListParams, searchFileParams, fetchModulesOptions, fetchSubModuleByIdSearch, fetchServicesById,
  modulesDropdown, subModulesDropdown, servicesDropdown, seatsDropdown, fetchStatus, statusDropdown,
  fetchSeats, fileTypeDropdown, fetchFileTypeOptions, fetchFunctionalGroups, userInfo, functionalGroups, setBackButton,
  setTableLoader, tableLoader, setSeats
}) => {
  const {
    watch
  } = useForm({
    mode: 'all'
  });
  const activeRows = [{}];
  const [search, setSearch] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [date, setDate] = useState();
  const [status, setStatus] = useState('');
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [open, setOpen] = useState(false);
  const [subId, setSubId] = useState('');
  const [department, setDepartment] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const navigate = useNavigate();

  const viewActions = (data) => {
    const filteredParams = Object.fromEntries(
      Object.entries(searchFileParams).filter(([, value]) => value !== null && value !== undefined)
    );

    const params = new URLSearchParams({
      from: 'search-files',
      ...filteredParams
    });

    params.delete('show');

    const finalUrl = `${BASE_UI_PATH}${data?.url}?${params.toString()}`;

    window.location = finalUrl;
  };

  const columns = [

    {
      header: t('fileNumber'),
      alignment: 'left',
      field: 'fileNo',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('inwardNumber'),
      alignment: 'left',
      field: 'inwardNo',
      cell: (field) => handleInwardNumber(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('title'),
      alignment: 'left',
      field: 'title',
      cell: (field) => handleTitle(field)
    },
    {
      header: t('custodian'),
      alignment: 'left',
      field: 'custodian',
      cell: (field) => handleCustodian(field)
    },
    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('currentUser'),
      alignment: 'left',
      field: 'currentUser',
      cell: (field) => handleCurrentUser(field)
    },
    {
      header: t('stage'),
      alignment: 'left',
      field: 'currentStage',
      cell: (field) => handleFileStage(field)
    },
    {
      header: t('status'),
      alignment: 'left',
      field: 'fileStatus',
      cell: (field) => handleFileStatus(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            viewActions(row);
          }
        }
      ]
    }

  ];

  useEffect(() => {
    setBackButton(true);
  }, []);
  const onPageClick = (data) => {
    setPage(data);
    setSearchListParams({
      ...searchFileParams, page: data, search: true, officeId: userInfo?.officeId
    });
  };

  useEffect(() => {
    if (queryParams) {
      const searchParams = {
        keyword: queryParams.get('keyword') || watch('keyword') || null,
        search: queryParams.get('search'),
        serviceCode: queryParams.get('serviceCode'),
        moduleCode: queryParams.get('moduleCode'),
        submoduleCode: queryParams.get('submoduleCode'),
        fileNo: queryParams.get('fileNo'),
        applicantName: queryParams.get('applicantName'),
        department: queryParams.get('department'),
        seat: queryParams.get('seat'),
        fromDate: queryParams.get('fromDate'),
        toDate: queryParams.get('toDate'),
        statusList: queryParams.get('statusList') || watch('statusList') || null,
        createdAt: queryParams.get('createdAt') || date || null,
        source: queryParams.get('source') || null,
        officeId: queryParams.get('officeId'),
        page: 0
      };

      const filteredSearchParams = Object.fromEntries(
        Object.entries(searchParams).filter(([, value]) => value)
      );

      setSearchListParams(filteredSearchParams);
      setSearch(queryParams.get('keyword'));
      setDate(convertInputDate(queryParams.get('createdAt'), DATE_FORMAT.LOCAL_DATE_REVERSE));
      setStatus(queryParams.get('statusList'));

      const newQueryString = new URLSearchParams(filteredSearchParams).toString();
      navigate(`?${newQueryString}`, { replace: true });
    }
  }, [JSON.stringify(queryParams)]);

  useEffect(() => {
    if (searchFileParams?.search) {
      setTableLoader({ loading: true, id: 'search-file-table' });
      fetchSearchFiles();
    }
  }, [searchFileParams]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setSearchListParams({
          ...searchFileParams,
          keyword: data || null,
          search: true,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          statusList: null,
          officeId: userInfo?.officeId,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setSearchListParams({
          ...searchFileParams,
          createdAt: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          search: true,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          statusList: null,
          officeId: userInfo?.officeId,
          page: 0
        });
        setDate(data);
        break;
      case FILTER_TYPE.STATUS:
        setSearchListParams({
          ...searchFileParams,
          statusList: data || null,
          search: true,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          officeId: userInfo?.officeId,
          page: 0
        });
        setStatus(data);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (functionalGroups?.length) {
      const array = [];
      functionalGroups.map((dep) => {
        array.push({
          name: dep.functionalGroupName,
          id: dep.functionalGroupId
        });
        return true;
      });
      setDepartment(array);
    }
  }, [functionalGroups]);

  useEffect(() => {
    if (searchFileData) {
      setTableLoader({ loading: false, id: 'search-file-table' });
      if (Object.keys(searchFileData).length > 0) {
        if (searchFileData?.content) {
          setTableData(searchFileData?.content);
          setTotalItems(Number(`${searchFileData.totalPages}0`));
          setNumberOfElements(Number(searchFileData.numberOfElements));
        } else {
          setTableData([searchFileData]);
          setTotalItems(0);
        }
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [searchFileData]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.MODULES:
        fetchSubModuleByIdSearch(data.id);
        setSubId(data.id);
        break;
      case FILTER_TYPE.SUB_MODULES:
        fetchServicesById({ module: subId, subModule: data.id });
        break;
      case FILTER_TYPE.DEPARTMENT:
        fetchSeats({ officeId: userInfo?.officeId, functionalGroupId: data?.id });
        break;
      case FILTER_TYPE.SERVICES:
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    fetchModulesOptions();
    fetchStatus({ code: X_STATE_CODE });
    fetchFileTypeOptions();
    if (userInfo?.officeId) {
      fetchFunctionalGroups(`officeId=${userInfo?.officeId}`);
    }
  }, [userInfo]);

  const backToHome = () => {
    window.location.href = userInfo?.userDetails?.isAuditor ? AUDIT_APPLICATION_PATH : EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center">

        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="grow-[1]">
          <h4 style={styles.head}>
            <strong>
              {t('searchFile')}
            </strong>
          </h4>
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
            />
          </InputGroup>
        </div>

        <div style={styles.sort} className="flex-none">
          <div style={styles.label}>{t('status')}:</div>
          <select onChange={(event) => triggerSearch(FILTER_TYPE.STATUS, event.target.value)} value={status} style={styles.select}>
            <option value="">{t('allStatus')}</option>
            {statusDropdown?.map((item) => (
              <option key={item?.id} value={item?.name}>{capitalizeFirstLetter(item?.name.toLowerCase())}</option>
            ))}
          </select>
        </div>
        <div className="flex-none">
          <Button
            onClick={handleOpen}
            size="lg"
            colorScheme="pink"
            style={{
              height: '44px', borderRadius: '25px', width: '153px', fontSize: '14px'
            }}
          >
            {t('advanceSearch')}
          </Button>
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'search-file-table'}
          numberOfElements={numberOfElements}
        />
      </div>

      <AdvanceSearch
        modulesDropdown={modulesDropdown}
        subModulesDropdown={subModulesDropdown}
        servicesDropdown={servicesDropdown}
        handleFieldChange={handleFieldChange}
        setSearchListParams={setSearchListParams}
        searchFileParams={searchFileParams}
        seatsDropdown={seatsDropdown}
        handleClose={handleClose}
        open={open}
        statusDropdown={statusDropdown}
        fileTypeDropdown={fileTypeDropdown}
        department={department}
        userInfo={userInfo}
        setPage={setPage}
        setSeats={setSeats}

      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  searchFileData: getSearchList,
  searchFileParams: getSearchListParams,
  modulesDropdown: getModulesDropdown,
  subModulesDropdown: getSubModulesSearchList,
  servicesDropdown: getServicesSearchList,
  seatsDropdown: getSeats,
  statusDropdown: getStatus,
  fileTypeDropdown: getFileTypeDropdown,
  userInfo: getUserInfo,
  functionalGroups: getFunctionalGroups,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchSearchFiles: (data) => dispatch(actions.fetchSearchFiles(data)),
  setSearchListParams: (data) => dispatch(sliceActions.setSearchListParams(data)),
  fetchModulesOptions: () => dispatch(commonActions.fetchModuleDetails()),
  fetchSubModuleByIdSearch: (data) => dispatch(actions.fetchSubModuleByIdSearch(data)),
  fetchServicesById: (data) => dispatch(actions.fetchServicesById(data)),
  fetchSeats: (data) => dispatch(commonActions.fetchSeats(data)),
  fetchStatus: (data) => dispatch(commonActions.fetchStatus(data)),
  setSearchFileList: (data) => dispatch(sliceActions.setSearchFileList(data)),
  fetchFileTypeOptions: () => dispatch(commonActions.fetchFileTypeDetails()),
  fetchFunctionalGroups: (data) => dispatch(commonActions.fetchFunctionalGroups(data)),
  setBackButton: (data) => dispatch(commonSliceActions.setBackButton(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setSeats: (data) => dispatch(commonSliceActions.setSeats(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
