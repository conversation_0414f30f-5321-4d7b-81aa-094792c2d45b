import { STATUS } from 'common/regex';
import { t } from 'i18next';
import { FILE_STATUS } from 'pages/common/constants';
import _ from 'lodash';

export const handleUserDetails = (userDetails = {}) => {
  return (
    <div>
      {(userDetails?.employeeName !== ' ' || userDetails?.employeeName !== '' || userDetails?.employeeName !== null || userDetails?.employeeName !== undefined) && <div className="text-[16px]">{userDetails?.employeeName}</div>}
      {(userDetails?.penNo !== ' ' || userDetails?.penNo !== '' || userDetails?.penNo !== null || userDetails?.penNo !== undefined) && <div className="text-[14px]">{t('penNo')} : {userDetails?.penNo}</div>}
      {(userDetails?.postNameInEng !== ' ' || userDetails?.postNameInEng !== '' || userDetails?.postNameInEng !== null || userDetails?.postNameInEng !== undefined) && <div className="text-[14px]">{t('seat')} : {userDetails?.postNameInEng}</div>}
      {(userDetails?.designation !== ' ' || userDetails?.designation !== '' || userDetails?.designation !== null || userDetails?.designation !== undefined) && <div className="text-[14px]">{t('designation')} : {userDetails?.designation}</div>}
      {/* {(userDetails?.location !== ' ' || userDetails?.location !== '' || userDetails?.location !== null || userDetails?.location !== undefined) && <div className="text-[14px]">{t('location')} : {userDetails?.location}</div>} */}

    </div>
  );
};

export const statusChange = (stage) => {
  switch (stage) {
    case stage?.currentStage === FILE_STATUS.RUNNING:
      return 'bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center min-w-[100px]';
    case stage?.currentStage === FILE_STATUS.VERY_URGENT:
      return 'bg-[#E83A7A] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#FFFFFF] text-center min-w-[100px]';
    case stage?.currentStage === FILE_STATUS.URGENT:
      return 'bg-[#FED0D0] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#E33A7A] text-center min-w-[100px]';
    case stage?.currentStage === FILE_STATUS.PENDING:
      return 'bg-[#F0F3DE] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#8F914B] text-center min-w-[100px]';
    case stage?.currentStage === FILE_STATUS.DELAYED:
      return 'bg-[#BAEAFE] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#00B2EB] text-center min-w-[100px]';
    case stage?.currentStage === FILE_STATUS.NORMAL:
      return 'bg-[#C6E5D3] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#53C684] text-center min-w-[100px]';
    default:
      return 'bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center min-w-[100px]';
  }
};
export const handleFileStage = (val) => {
  let stage;
  if (val?.row) {
    const cellData = val?.row;
    stage = (
      <div className={statusChange(cellData)}>
        {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
      </div>
    );
  }
  return <div className="inline-block">{stage}</div>;
};

export const handleFileStatus = (val) => {
  let stat;
  if (val?.row) {
    const cellData = val?.row;
    stat = (
      <div className="min-w-[100px]">
        {_.capitalize(cellData?.fileStatus?.replace(STATUS, ' ')) || _.capitalize(cellData?.status?.replace(STATUS, ' '))}
      </div>
    );
  }
  return <div className="inline-block">{stat}</div>;
};

export const handleInwardNumber = (val) => {
  let inwardNumber;
  if (val?.row) {
    const cellData = val?.row;
    inwardNumber = (
      <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">
        {cellData?.inwardNo || cellData?.applicationNumber}
      </div>
    );
  }
  return <div className="inline-block">{inwardNumber}</div>;
};

export const handleCurrentUser = (val) => {
  const { currentUser = {} } = val.row;
  return <div className="inline-block">{handleUserDetails(currentUser)}</div>;
};

export const handleCustodian = (val) => {
  const { custodian = {} } = val.row;
  return <div className="inline-block">{handleUserDetails(custodian)}</div>;
};

export const handleFileNumber = (fileData) => {
  let fileNumber;
  if (fileData?.row) {
    const cellData = fileData?.row;
    fileNumber = (
      <>
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
        <div className="text-[13px] font-[900] text-[#B5B5B5]">{cellData?.fileDate ? cellData?.fileDate : cellData?.date}</div>
      </>
    );
  }
  return <div className="block min-w-[100px]">{fileNumber}</div>;
};

export const handleTitle = (fileData) => {
  let title;
  if (fileData?.row) {
    const cellData = fileData?.row;
    title = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData?.title}</div>;
  }
  return <div className="block">{title}</div>;
};

export const handleServiceName = (fileData) => {
  let serviceName;
  if (fileData?.row) {
    const cellData = fileData?.row;
    serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.serviceName}</div>;
  }
  return <div className="block">{serviceName}</div>;
};

export const handleApplicantName = (fileData) => {
  let applicantName;
  if (fileData?.row) {
    const cellData = fileData?.row;
    applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.applicantName || cellData?.createdByName}</div>;
  }
  return <div className="block">{applicantName}</div>;
};
