import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { API_URL } from 'common';
import { actions as commonSliceActions } from 'pages/common/slice';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { getSearchListParams } from './selectors';

export function* fetchSearchFiles() {
  const apiParams = yield select(getSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const finalParams = _.omit(updatedParams, ['search']);
  let url;
  if (_.has(finalParams, 'inwardNo')) {
    url = API_URL.INWARD.INWARD_DETAILS;
  } else if (_.has(finalParams, 'applicationNumber')) {
    url = API_URL.SEARCH_FILES.FETCH_SEARCH_FILES;
  } else {
    url = API_URL.SEARCH_FILES.FETCH_SEARCH_FILES;
  }
  yield fork(handleAPIRequest, api.fetchSearchFiles, finalParams, url);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SEARCH_FILES_SUCCESS,
    ACTION_TYPES.FETCH_SEARCH_FILES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SEARCH_FILES_SUCCESS) {
    yield put(sliceActions.setSearchFileList(responsePayLoad?.data));
    // yield put(sliceActions.setSearchListParams({
    //   ...finalParams,
    //   sortDirection: 'desc',
    //   page: 0,
    //   size: 10,
    //   search: false
    // }));
  } else {
    yield put(sliceActions.setSearchFileList([]));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'search-file-table' }));
  }
}

export function* fetchSubModulesByIdSearch({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSubModulesById, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH_SUCCESS,
    ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH_SUCCESS) {
    yield put(sliceActions.setSubModulesSearchList(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchServiceById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchServicesById, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_SERVICES_BY_ID_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS) {
    yield put(sliceActions.setServicesSearchList(_.get(responsePayLoad, 'data', {})));
  }
}

export default function* applicationsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_SEARCH_FILES, fetchSearchFiles),
    takeLatest(ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH, fetchSubModulesByIdSearch),
    takeLatest(ACTION_TYPES.FETCH_SERVICES_BY_ID, fetchServiceById)

  ]);
}
