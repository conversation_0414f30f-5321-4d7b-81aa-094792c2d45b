import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getSearch = (state) => state[STATE_REDUCER_KEY];

const searchList = (state) => state?.searchFileList;
export const getSearchList = flow(getSearch, searchList);

const searchListParams = (state) => state?.searchListParams;
export const getSearchListParams = flow(getSearch, searchListParams);

const subModulesSearchList = (state) => state?.subModulesSearchList;
export const getSubModulesSearchList = flow(getSearch, subModulesSearchList);

const servicesSearchList = (state) => state?.servicesSearchList;
export const getServicesSearchList = flow(getSearch, servicesSearchList);
