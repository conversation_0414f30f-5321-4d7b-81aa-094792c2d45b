import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_SEARCH_FILES: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES`,
  FET<PERSON>_SEARCH_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES_REQUEST`,
  FETCH_SEARCH_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES_SUCCESS`,
  FETCH_SEARCH_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES_FAILURE`,

  FETCH_SUB_MODULES_BY_ID_SEARCH: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_SEARCH`,
  FETCH_SUB_MODULES_BY_ID_SEARCH_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_SEARCH_BY_ID_REQUEST`,
  FETCH_SUB_MODULES_BY_ID_SEARCH_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_SEARCH_BY_ID_SUCCESS`,
  FETCH_SUB_MODULES_BY_ID_SEARCH_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_SEARCH_BY_ID_FAILURE`,

  FETCH_SERVICES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID`,
  FETCH_SERVICES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_REQUEST`,
  FETCH_SERVICES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_SUCCESS`,
  FETCH_SERVICES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_FAILURE`

};

export const fetchSearchFiles = createAction(ACTION_TYPES.FETCH_SEARCH_FILES);
export const fetchSubModuleByIdSearch = createAction(ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH);
export const fetchServicesById = createAction(ACTION_TYPES.FETCH_SERVICES_BY_ID);
