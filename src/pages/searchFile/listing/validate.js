import * as yup from 'yup';

import { t } from 'common/components';

export const AdvanceSearchSchema = yup.object().shape({
  fromDate: yup.string().nullable().default(null),
  toDate: yup.string().when('fromDate', (fromDate, schema) => {
    return (fromDate[0] !== null && fromDate[0] !== 'null') ? schema.required(t('toDateRequired'))
      .test({
        test(toDate) {
          if (!toDate) {
            return true; // Validation passes if toDate is not provided
          }
          return new Date(toDate) >= new Date(fromDate); // Validates toDate is greater than fromDate
        },
        message: t('toDateGreaterError')
      }) : schema;
  }).nullable().default(null)
});

export const InwardSearchSchema = yup.object().shape({
  inwardNumber: yup.string().required(),
  fromDate: yup.string().nullable().default(null),
  toDate: yup.string().when('fromDate', (fromDate, schema) => {
    return (fromDate[0] !== null && fromDate[0] !== 'null') ? schema.required(t('toDateRequired'))
      .test({
        test(toDate) {
          if (!toDate) {
            return true; // Validation passes if toDate is not provided
          }
          return new Date(toDate) >= new Date(fromDate); // Validates toDate is greater than fromDate
        },
        message: t('toDateGreaterError')
      }) : schema;
  }).nullable().default(null)
});

export const ApplicantNumberSchema = yup.object({
  applicationNumber: yup.string().required(t('isRequired', { type: t('applicationNumber') }))
});
