import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  searchFileList: {},
  searchListParams: {
    sortDirection: 'desc',
    page: 0,
    size: 10,
    search: false,
    officeId: JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID))
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setSearchFileList: (state, { payload }) => {
      _.set(state, 'searchFileList', payload);
    },
    setSearchListParams: (state, { payload }) => {
      _.set(state, 'searchListParams', payload);
    },

    setSubModulesSearchList: (state, { payload }) => {
      _.set(state, 'subModulesSearchList', payload);
    },
    setServicesSearchList: (state, { payload }) => {
      _.set(state, 'servicesSearchList', payload);
    }
  }
});

export const { actions, reducer } = slice;
