import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchSearchFiles = (finalParams, url) => {
  return {
    url,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SEARCH_FILES_REQUEST,
        ACTION_TYPES.FETCH_SEARCH_FILES_SUCCESS,
        ACTION_TYPES.FETCH_SEARCH_FILES_FAILURE
      ],
      params: finalParams
    }
  };
};

export const fetchSubModulesById = (data) => {
  return {
    url: API_URL.COMMON.SUB_MODULES_BY_MODULE_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH_REQUEST,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH_SUCCESS,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SEARCH_FAILURE
      ]
    },
    data
  };
};

export const fetchServicesById = (data) => {
  const { module, subModule } = data;
  return {
    url: API_URL.COMMON.SERVICES_BY_ID.replace(':moduleId', module).replace(':subModuleId', subModule),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_SERVICES_BY_ID_FAILURE
      ]
    },
    data
  };
};
