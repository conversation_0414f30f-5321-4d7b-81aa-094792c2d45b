import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import { getTemporaryDisposedSearchListParams } from './selectors';

export function* fetchTemporaryDisposedFileTableList() {
  const apiParams = yield select(getTemporaryDisposedSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchTemporaryDisposedFileTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST_SUCCESS,
    ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST_SUCCESS) {
    yield put(sliceActions.setTemporaryDisposedTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'disposed-file-table' }));
  }
}

export default function* counterSaga() {
  yield all([

    takeLatest(ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST, fetchTemporaryDisposedFileTableList)
  ]);
}
