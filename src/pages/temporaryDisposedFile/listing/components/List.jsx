import {
  t,
  Input, InputGroup, InputRightElement, IconButton, Tooltip
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import TableView from 'assets/TableView';
import { DATE_FORMAT, FILE_STATUS, FILTER_TYPE } from 'pages/common/constants';
import SearchIcon from 'assets/SearchIcon';
import { convertToLocalDate } from 'utils/date';
import { actions as commonSliceActions } from 'pages/common/slice';
import BackArrow from 'assets/BackIcon.jsx';
import { dark } from 'utils/color.js';
import { AUDIT_APPLICATION_PATH, BASE_UI_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import _ from 'lodash';
import { STATUS } from 'common/regex';
import { Text } from '@ksmartikm/ui-components';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import { getTemporaryDisposedListData, getTemporaryDisposedSearchListParams } from '../selectors';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const List = ({
  userInfo, fetchTemporaryDisposedFileTableList, setTemporaryDisposedListParams, temporaryDisposedTableList, temporaryDisposedTableListParams,
  setTableLoader, tableLoader
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [search, setSearch] = useState('');
  const [date, setDate] = useState();
  const today = new Date().toISOString().split('T')[0];
  // const [stage, setStage] = useState('HOLD');
  const [numberOfElements, setNumberOfElements] = useState(0);

  const viewActions = (data) => {
    sessionStorage.setItem('file-nav-history', 'disposed-file');
    sessionStorage.setItem('file-nav-history-payload', JSON.stringify(temporaryDisposedTableListParams));
    window.location = `${BASE_UI_PATH}${data?.url}`;
  };

  // const filterOptions = [
  //   { value: 'HOLD', name: 'Temporary Disposed' }
  //   // { value: 'RETURN_TO_CITIZEN', name: 'Return to Citizen' }
  // ];

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      if (cellData?.currentStage === FILE_STATUS.HOLD) {
        status = (
          <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
            {t('temporaryDisposedFile')}
          </div>
        );
      } else if (cellData?.currentStage === FILE_STATUS.RETURN_TO_CITIZEN) {
        status = (
          <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
            {t('returnToCitizen')}
          </div>
        );
      } else {
        status = (
          <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
            {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
          </div>
        );
      }
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNumber = (
        <>
          <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
          <div className="text-[14px] font-[700] text-[#B5B5B5]">{cellData?.fileDate}</div>
        </>
      );
    }
    return <div className="block">{fileNumber}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.applicantName}</div>
      );
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleForwardedBy = (fileData) => {
    let forwardedBy;
    if (fileData?.row) {
      const cellData = fileData?.row;
      forwardedBy = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px]">{cellData?.forwardedBy}</div>;
    }
    return <div className="block">{forwardedBy}</div>;
  };

  const handleTitleName = (fileData) => {
    let titleName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      titleName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">
          <Tooltip
            label={cellData.title && cellData.title.length > 25 ? cellData.title : ''}
            placement="top"
          >
            <Text lineClamp={1} isTruncated>
              {cellData.title}
            </Text>
          </Tooltip>
        </div>
      );
    }
    return <div className="block">{titleName}</div>;
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('nameOfApplicant'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('title'),
      field: 'title',
      alignment: 'left',
      cell: (field) => handleTitleName(field)
    },
    {
      header: t('service'),
      field: 'serviceName',
      alignment: 'left',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('status'),
      field: 'currentStage',
      cell: (field) => handleFileStatus(field)
    },
    {
      header: t('forwardedBy'),
      field: 'forwardedBy',
      alignment: 'left',
      cell: (field) => handleForwardedBy(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            viewActions(row);
          }
        }
      ]
    }

  ];

  useEffect(() => {
    if (sessionStorage.getItem('file-nav-history-payload')) {
      setTemporaryDisposedListParams({
        ...JSON.parse(sessionStorage.getItem('file-nav-history-payload'))
      });
      const values = JSON.parse(sessionStorage.getItem('file-nav-history-payload'));
      setSearch(values?.keyword);
      setDate(convertToLocalDate(values?.createdAt, DATE_FORMAT.LOCAL_DATE_REVERSE));
      // setStage(values?.fileStatus);
      sessionStorage.removeItem('file-nav-history-payload');
    }
  }, [sessionStorage.getItem('file-nav-history-payload')]);

  useEffect(() => {
    if (temporaryDisposedTableListParams && !sessionStorage.getItem('file-nav-history-payload')) {
      setTableLoader({ loading: true, id: 'disposed-file-table' });
      fetchTemporaryDisposedFileTableList();
    }
  }, [temporaryDisposedTableListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setTemporaryDisposedListParams({
      ...temporaryDisposedTableListParams,
      page: data,
      officeId: userInfo.id
      // fileStatus: stage
    });
  };

  useEffect(() => {
    if (temporaryDisposedTableList) {
      setTableLoader({ loading: false, id: 'disposed-file-table' });
      if (Object.keys(temporaryDisposedTableList).length > 0) {
        setTableData(temporaryDisposedTableList.content);
        setTotalItems(Number(`${temporaryDisposedTableList.totalPages}0`));
        setNumberOfElements(Number(temporaryDisposedTableList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [temporaryDisposedTableList]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setTemporaryDisposedListParams({
          ...temporaryDisposedTableListParams,
          keyword: data || null,
          // fileStatus: stage,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setTemporaryDisposedListParams({
          ...temporaryDisposedTableListParams,
          createdAt: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          // fileStatus: stage,
          page: 0
        });
        setDate(data);
        break;
      default:
        break;
    }
  };

  const backToHome = () => {
    window.location.href = userInfo?.userDetails?.isAuditor ? AUDIT_APPLICATION_PATH : EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1]">
          {t('parkedFiles')}
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
              max={today}
            />
          </InputGroup>
        </div>
        {/* <div style={styles.sort} className="flex-none">
          <div style={styles.label}>{t('stage')}:</div>
          <select
            onChange={(event) => {
              setStage(event.target.value);
              setTemporaryDisposedListParams({
                ...temporaryDisposedTableListParams,
                fileStatus: event.target.value
              });
            }}
            value={stage}
            style={styles.select}
          >
            {filterOptions?.map((item) => (
              <option key={item?.value} value={item?.value}>{item?.name}</option>
            ))}
          </select>
        </div> */}
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'disposed-file-table'}
          numberOfElements={numberOfElements}
        />

      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  temporaryDisposedTableList: getTemporaryDisposedListData,
  temporaryDisposedTableListParams: getTemporaryDisposedSearchListParams,
  tableLoader: getTableLoader

});

const mapDispatchToProps = (dispatch) => ({
  fetchTemporaryDisposedFileTableList: (data) => dispatch(actions.fetchTemporaryDisposedFileTableList(data)),
  setTemporaryDisposedListParams: (data) => dispatch(sliceActions.setTemporaryDisposedListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(List);
