import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchTemporaryDisposedFileTableList = (params) => {
  return {
    url: API_URL.DASHBOARD.FETCH_DISPOSED_LIST,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST_REQUEST,
        ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST_SUCCESS,
        ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST_FAILURE
      ],
      params
    }
  };
};
