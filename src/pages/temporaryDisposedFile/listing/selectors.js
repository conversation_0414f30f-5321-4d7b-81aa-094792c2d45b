import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getTemporaryDisposedDetails = (state) => state[STATE_REDUCER_KEY];

const temporaryDisposedListData = (state) => state?.temporaryDisposedTableList;
export const getTemporaryDisposedListData = flow(getTemporaryDisposedDetails, temporaryDisposedListData);

const temporaryDisposedSearchListParams = (state) => state?.temporaryDisposedTableListParams;
export const getTemporaryDisposedSearchListParams = flow(getTemporaryDisposedDetails, temporaryDisposedSearchListParams);
