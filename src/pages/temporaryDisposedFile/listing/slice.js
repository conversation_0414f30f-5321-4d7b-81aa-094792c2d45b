import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  temporaryDisposedTableListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    moduleCode: null,
    submoduleCode: null,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    fileStatus: FILE_STATUS_FOR_API_PARAMS.HOLD
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setTemporaryDisposedTableList: (state, { payload }) => {
      _.set(state, 'temporaryDisposedTableList', payload);
    },
    setTemporaryDisposedListParams: (state, { payload }) => {
      _.set(state, 'temporaryDisposedTableListParams', payload);
    }
  }
});

export const { actions, reducer } = slice;
