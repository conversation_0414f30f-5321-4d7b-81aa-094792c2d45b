import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_TEMPORARY_DISPOSED_FILE_LIST: `${STATE_REDUCER_KEY}/FETCH_TEMPORARY_DISPOSED_FILE_LIST`,
  FETCH_TEMPORARY_DISPOSED_FILE_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_TEMPORARY_DISPOSED_FILE_LIST_REQUEST`,
  FETCH_TEMPORARY_DISPOSED_FILE_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_TEMPORARY_DISPOSED_FILE_LIST_SUCCESS`,
  FETCH_TEMPORARY_DISPOSED_FILE_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_TEMPORARY_DISPOSED_FILE_LIST_FAILURE`

};
export const fetchTemporaryDisposedFileTableList = createAction(ACTION_TYPES.FETCH_TEMPORARY_DISPOSED_FILE_LIST);
