import { lazy } from 'react';

const EsignEnroll = lazy(() => import('./es/components/index'));
const DeactivatedList = lazy(() => import('./ds/components/DeactivatedList'));
const Enroll = lazy(() => import('./ds/components/Enroll'));

const routes = [
  {
    path: '',
    children: [
      {
        path: 'es',
        element: <EsignEnroll />
      },
      {
        path: 'es/enroll',
        element: <EsignEnroll />
      },
      {
        path: 'es/enroll/:id',
        element: <EsignEnroll />
      },
      {
        path: 'es/enroll/:id/approve',
        element: <EsignEnroll forApproval />
      },
      {
        path: 'ds',
        element: <Enroll />
      },
      {
        path: 'ds/enroll',
        element: <Enroll />
      },
      {
        path: 'ds/deactivated',
        element: <DeactivatedList />
      }
    ]
  }
];

export { routes };
