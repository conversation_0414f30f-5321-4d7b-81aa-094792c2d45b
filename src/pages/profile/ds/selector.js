import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getDsNew = (state) => state[STATE_REDUCER_KEY];

const dsLocalEnrollment = (state) => state?.dsLocalEnrollment;
export const getDsLocalEnrollment = flow(getDsNew, dsLocalEnrollment);

const allEnroll = (state) => state?.allEnroll;
export const getAllEnrolls = flow(getDsNew, allEnroll);

const triggerDraftSign = (state) => state?.triggerDraftSign;
export const getTriggerDraftSign = flow(getDsNew, triggerDraftSign);
