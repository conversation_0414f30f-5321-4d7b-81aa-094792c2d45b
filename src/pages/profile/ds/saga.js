import {
  all, call, fork, put, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'i18next';
import { routeRedirect } from 'utils/common';
import { JAR_VERSION } from 'common/constants';
import { actions as sliceActions } from './slice';
import * as api from './api';
import { ACTION_TYPES } from './actions';
import { checkSignatureIsValid } from './helper';

export function* fetchDsEnroll({ payload = {} }) {
  const { from, handleEsign, activeEnrolls } = payload;
  yield fork(handleAPIRequest, api.fetchDsEnroll, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DS_ENROLL_SUCCESS,
    ACTION_TYPES.FETCH_DS_ENROLL_FAILURE
  ]);

  if (type === ACTION_TYPES.FETCH_DS_ENROLL_SUCCESS) {
    if (responsePayLoad?.data?.length > 0) {
      if (responsePayLoad?.data[0]?.versionno !== JAR_VERSION) {
        yield put(
          commonSliceActions.setAlertAction({
            open: true,
            variant: 'error',
            message: 'Digital signer detected older version <br /> Please download the latest version',
            backwardActionText: t('ok')
          })
        );
        // yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
        yield put(commonSliceActions.setActionTriggered({ id: 'fetch-local-ds', loading: false }));
      } else if (responsePayLoad?.data[0]?.status !== 'Success') {
        yield put(
          commonSliceActions.setAlertAction({
            open: true,
            variant: 'error',
            message: 'Kindly ensure that the<br/> Token Driver is installed correctly.',
            backwardActionText: t('ok')
          })
        );
        // yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
        yield put(commonSliceActions.setActionTriggered({ id: 'fetch-local-ds', loading: false }));
      } else {
        // yield put(commonSliceActions.setAlertAction({ open: false }));
        // yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
        yield put(commonSliceActions.setActionTriggered({ id: 'fetch-local-ds', loading: false }));
        yield put(sliceActions.setDsLocalEnrollment(responsePayLoad?.data[0]));
        if (from === 'draft-save') {
          if (checkSignatureIsValid(responsePayLoad?.data[0], activeEnrolls[0]) === 'notexpired') {
            handleEsign();
          } else {
            if (checkSignatureIsValid(responsePayLoad?.data[0], activeEnrolls[0]) === 'expired') {
              yield put(
                commonSliceActions.setAlertAction({
                  open: true,
                  variant: 'error',
                  message: 'The digital signature validity expire <br/> Please Check',
                  backwardActionText: t('ok')
                })
              );
            }
            if (checkSignatureIsValid(responsePayLoad?.data[0], activeEnrolls[0]) === 'notvalid') {
              yield put(
                commonSliceActions.setAlertAction({
                  open: true,
                  variant: 'error',
                  message: 'Kindly ensure that the<br/> Token Driver is same as enrolled',
                  backwardActionText: t('ok')
                })
              );
            }
          }
        }
      }
    }
  } else {
    yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
    yield put(commonSliceActions.setActionTriggered({ id: 'fetch-local-ds', loading: false }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: 'Please start the<br/> KSMART Signer Tool ',
        backwardActionText: t('ok')
      })
    );
  }
}

export function* fetchAllEnroll({ payload = {} }) {
  const { from, handleEsign, handleCloseForDsLoader = () => { } } = payload;
  yield fork(handleAPIRequest, api.fetchAllEnroll, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_ALL_ENROLL_SUCCESS,
    ACTION_TYPES.FETCH_ALL_ENROLL_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_ALL_ENROLL_SUCCESS) {
    if (from === 'draft-save') {
      if (responsePayLoad?.data?.esignRegistrationList && responsePayLoad?.data?.esignRegistrationList?.length > 0) {
        const activeEnrolls = responsePayLoad?.data?.esignRegistrationList?.filter((item) => item?.activated === true);
        if (activeEnrolls?.length > 0) {
          yield call(fetchDsEnroll, { payload: { from, handleEsign, activeEnrolls } });
        } else {
          // yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
          yield put(
            commonSliceActions.setAlertAction({
              open: true,
              variant: 'warning',
              message: `${t('digitalSignatureEnrollmentNotCompleted')} <br/><div style="font-size: 14px"> ${t(
                'clickBelowEnrollButtonIfNotDoneYet'
              )} </div>`,
              title: t('warning'),
              backwardActionText: t('close'),
              forwardActionText: t('enroll'),
              forwardAction: () => routeRedirect('ui/file-management/profile/ds/enroll'),
              backwardAction: () => handleCloseForDsLoader()
            })
          );
        }
      } else {
        // yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
        yield put(
          commonSliceActions.setAlertAction({
            open: true,
            variant: 'warning',
            message: `${t('digitalSignatureEnrollmentNotCompleted')} <br/><div style="font-size: 14px"> ${t(
              'clickBelowEnrollButtonIfNotDoneYet'
            )} </div>`,
            title: t('warning'),
            backwardActionText: t('close'),
            forwardActionText: t('enroll'),
            forwardAction: () => routeRedirect('ui/file-management/profile/ds/enroll')
          })
        );
      }
    }
  } else if (type === ACTION_TYPES.FETCH_ALL_ENROLL_FAILURE) {
    yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
  }
}

export function* saveDsEnroll({ payload = {} }) {
  const { activated } = payload;
  yield fork(handleAPIRequest, api.saveDsEnroll, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_DS_ENROLL_SUCCESS,
    ACTION_TYPES.SAVE_DS_ENROLL_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_DS_ENROLL_SUCCESS) {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: activated ? t('digitalSignatureSuccessfullyEnrolled') : t('digitalSignatureSuccessfullyDiactivated'),
        backwardActionText: t('ok')
      })
    );
    yield call(fetchAllEnroll, { payload: {} });
  } else {
    const { error: { response: { data: { message = '' } = {} } = {} } = {} } = responsePayLoad;
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message,
        backwardActionText: t('ok')
      })
    );
  }
}

export default function* dSSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_DS_ENROLL, fetchDsEnroll),
    takeLatest(ACTION_TYPES.FETCH_ALL_ENROLL, fetchAllEnroll),
    takeLatest(ACTION_TYPES.SAVE_DS_ENROLL, saveDsEnroll)
  ]);
}
