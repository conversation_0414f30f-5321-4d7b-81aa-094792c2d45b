import dayjs from 'dayjs';
import { t } from 'i18next';
import { formatDate } from 'utils/date';

export const formateDsData = (data) => {
  const today = dayjs();
  // const formateToday = formatDateYMD(today);
  const formateData = dayjs(data?.validto || data?.validTo);

  return [
    {
      title: t('certkeyno'),
      value: data?.certkeyno || data?.certKeyNo
    },
    {
      title: t('certslno'),
      value: data?.certslno || data?.certSlNo
    },
    {
      title: t('issuer'),
      value: data?.issuer
    },
    {
      title: t('issuerCat'),
      value: data?.issuerCat || data?.issuercat
    },
    {
      title: t('country'),
      value: data?.country
    },
    {
      title: t('name'),
      value: data?.name
    },
    {
      title: t('office'),
      value: data?.office
    },
    {
      title: t('state'),
      value: data?.state
    },
    {
      title: t('thumb'),
      value: data?.thumb
    },
    {
      title: t('tokenslno'),
      value: data?.tokenslno || data?.tokenSlNo
    },
    {
      title: t('validfrom'),
      value: formatDate(data?.validfrom || data?.validFrom)
    },
    {
      title: t('validto'),
      value: formateData.isBefore(today, 'day') ? <div className="text-red-500">Expired : {formatDate(data?.validto || data?.validTo)}</div> : formatDate(data?.validto || data?.validTo)
    },
    {
      title: t('version'),
      value: data?.versionNo || data?.versionno
    }
  ];
};

export const checkSignatureIsValid = (local, live) => {
  if (local?.certslno === live.certSlNo) {
    const today = dayjs();
    if (dayjs(live?.validTo).isAfter(today)) {
      return 'notexpired';
    }
    return 'expired';
  } return 'notvalid';
};
