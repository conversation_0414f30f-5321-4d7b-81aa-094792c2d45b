import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';
import { ACTION_TYPES } from './actions';

const slice = createSlice({
  initialState: {
    dsLocalEnrollment: {},
    triggerDraftSign: false
  },
  name: STATE_REDUCER_KEY,
  reducers: {
    setDsLocalEnrollment: (state, { payload }) => {
      _.set(state, 'dsLocalEnrollment', payload);
    },
    setTriggerDraftSign: (state, { payload }) => {
      _.set(state, 'triggerDraftSign', payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.FETCH_ALL_ENROLL_SUCCESS, (state, { payload }) => {
        _.set(state, 'allEnroll', payload.data);
      });
  }
});

export const { actions, reducer } = slice;
