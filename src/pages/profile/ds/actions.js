import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_DS_ENROLL: `${STATE_REDUCER_KEY}/FETCH_DS_ENROLL`,
  FETCH_DS_ENROLL_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DS_ENROLL_REQUEST`,
  FETCH_DS_ENROLL_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DS_ENROLL_SUCCESS`,
  FETCH_DS_ENROLL_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DS_ENROLL_FAILURE`,

  FETCH_ALL_ENROLL: `${STATE_REDUCER_KEY}/FETCH_ALL_ENROLL`,
  FETCH_ALL_ENROLL_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ALL_ENROLL_REQUEST`,
  FET<PERSON>_ALL_ENROLL_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ALL_ENROLL_SUCCESS`,
  FETCH_ALL_ENROLL_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ALL_ENROLL_FAILURE`,

  SAVE_DS_ENROLL: `${STATE_REDUCER_KEY}/SAVE_DS_ENROLL`,
  SAVE_DS_ENROLL_REQUEST: `${STATE_REDUCER_KEY}/SAVE_DS_ENROLL_REQUEST`,
  SAVE_DS_ENROLL_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_DS_ENROLL_SUCCESS`,
  SAVE_DS_ENROLL_FAILURE: `${STATE_REDUCER_KEY}/SAVE_DS_ENROLL_FAILURE`

};

export const fetchDsEnroll = createAction(ACTION_TYPES.FETCH_DS_ENROLL);
export const fetchAllEnroll = createAction(ACTION_TYPES.FETCH_ALL_ENROLL);
export const saveDsEnroll = createAction(ACTION_TYPES.SAVE_DS_ENROLL);
