import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchDsEnroll = () => {
  return {
    url: API_URL.E_SIGN.LOCAL_ENROLLMENT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DS_ENROLL_REQUEST,
        ACTION_TYPES.FETCH_DS_ENROLL_SUCCESS,
        ACTION_TYPES.FETCH_DS_ENROLL_FAILURE
      ]
    }
  };
};

export const fetchAllEnroll = () => {
  return {
    url: API_URL.E_SIGN.FETCH_ALL_ENROLL,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ALL_ENROLL_REQUEST,
        ACTION_TYPES.FETCH_ALL_ENROLL_SUCCESS,
        ACTION_TYPES.FETCH_ALL_ENROLL_FAILURE
      ]
    }
  };
};

export const saveDsEnroll = (data) => {
  return {
    url: API_URL.E_SIGN.SAVE_DS_ENROLL,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DS_ENROLL_REQUEST,
        ACTION_TYPES.SAVE_DS_ENROLL_SUCCESS,
        ACTION_TYPES.SAVE_DS_ENROLL_FAILURE
      ],
      data
    }
  };
};
