import { Button } from '@ksmartikm/ui-components';
import { t } from 'i18next';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { primary } from 'utils/color';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered } from 'pages/common/selectors';
import DownloadIcon from 'assets/Download';
import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';
import { downloadBlob } from 'utils/downloadBlob';
import dayjs from 'dayjs';
import { baseApiURL, baseDSURL } from 'utils/http';
import DsImage from 'assets/ds.svg';
import { API_URL } from 'common';
import * as actions from '../actions';
import { getAllEnrolls, getDsLocalEnrollment } from '../selector';
import { formateDsData } from '../helper';
import { base64Sample } from './base64';

const Enroll = (props) => {
  const {
    fetchDsEnroll,
    dsLocalEnrollment,
    setActionTriggered,
    actionTriggered,
    setAlertAction,
    fetchAllEnroll,
    allEnrolls,
    saveDsEnroll
  } = props;

  const [showActiveEnrollment, setShowActiveEnrollment] = useState(false);
  const [cardData, setCardData] = useState([]);
  const [activeEnroll, setActiveEnroll] = useState(null);
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchAllEnroll();
  }, []);

  const handleCheckDateTime = async () => {
    const response = await fetch(
      `${baseApiURL}/${API_URL.E_SIGN.DATE_CHECK}?datetime=${dayjs().format('DD/MM/YYYY')}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    const res = await response.json();
    if (res?.payload?.isSame) {
      setActionTriggered({ id: 'fetch-local-ds', loading: true });
      fetchDsEnroll();
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('systemDateNoteUpdated')} <br/><div style="font-size: 14px"> ${t('pleaseCheck')} </div>`,
        title: t('warning'),
        backwardActionText: t('close')
      });
    }
  };

  useEffect(() => {
    if (Object.keys(dsLocalEnrollment).length > 0) {
      setShowActiveEnrollment(true);
      setCardData(formateDsData(dsLocalEnrollment));
    } else {
      setShowActiveEnrollment(false);
    }
  }, [dsLocalEnrollment]);

  useEffect(() => {
    if (allEnrolls?.esignRegistrationList?.length > 0) {
      const activeEnrolls = allEnrolls?.esignRegistrationList?.filter((item) => item?.activated === true);

      if (activeEnrolls?.length > 0) {
        setCardData(formateDsData(activeEnrolls[0]));
        setShowActiveEnrollment(true);
        setActiveEnroll(activeEnrolls[0]);
      } else {
        setActiveEnroll(null);
        setShowActiveEnrollment(false);
      }
    }
  }, [allEnrolls]);

  const handleEnroll = () => {
    saveDsEnroll({ ...dsLocalEnrollment, activated: true });
  };

  const signPdfTrigger = async () => {
    setAlertAction({
      open: false
    });
    const options = {
      method: 'POST',
      body: JSON.stringify(base64Sample),
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      }
    };
    const response = await fetch(`${baseDSURL}/signpdf`, options);
    if (response?.ok === true) {
      handleEnroll();
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('somethingWentWrong')} <br/> ${t('pleaseTryAgain')}`,
        title: t('error'),
        backwardActionText: t('ok')
      });
    }
  };

  const handleDeactivateEnroll = () => {
    saveDsEnroll({ ...activeEnroll, activated: false });
  };

  const handleConfirmEnroll = () => {
    const today = dayjs();
    const formateData = dayjs(dsLocalEnrollment?.validto || dsLocalEnrollment?.validTo);
    if (formateData.isBefore(today, 'day')) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('tokenValidityExpired')} <br/> ${t('pleaseCheck')}`,
        title: t('expired'),
        backwardActionText: t('ok')
      });
    } else {
      setAlertAction({
        open: true,
        variant: 'alert',
        message: `${t('areYouSureWantToEnroll')} <br/> ${t('digitalSignature')}?`,
        title: t('confirm'),
        backwardActionText: t('no'),
        forwardActionText: t('yes'),
        forwardAction: () => signPdfTrigger()
      });
    }
  };

  const handleConfirmDeactivateEnroll = () => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: `${t('areYouSureWantToDeactivate')} <br/> ${t('digitalSignature')}?`,
      title: t('confirm'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      forwardAction: () => handleDeactivateEnroll()
    });
  };

  const handleDownloadJava = () => {
    setLoading(true);
    fetch(`${baseApiURL}/file-management-services/download-e-sign-exe`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        accept: 'application/octet-stream'
      }
    })
      .then((response) => response.arrayBuffer())
      .then((response) => {
        const arr = new Uint8Array(response);
        const blob = new Blob([arr], {
          type: DOCUMENT_TYPES.EXE
        });
        const blobUrl = window.URL.createObjectURL(blob);
        downloadBlob({ blob: blobUrl, fileName: 'KSMART-SIGNER-1.1.2.exe' });
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <div className="flex gap-4">
      <div className="flex-grow bg-white rounded-lg p-5" style={{ height: 'calc(100vh - 220px)' }}>
        {showActiveEnrollment ? (
          <>
            <div style={{ height: 'calc(100vh - 320px)', overflowY: 'auto' }}>
              <table className="table text-left rounded-lg w-full text-sm">
                <tbody>
                  {cardData?.map((item) => (
                    <tr key={item.title} className="border">
                      <th className="bg-slate-100 p-3">{t(item?.title)}</th>
                      <th className="p-3">{item?.value}</th>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="w-full flex mt-3">
              <div className="flex-grow" />
              <Button
                variant={activeEnroll ? 'secondary' : 'primary'}
                onClick={activeEnroll ? handleConfirmDeactivateEnroll : handleConfirmEnroll}
              >
                {activeEnroll ? t('deactivate') : t('enroll')}
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center pt-20">
            <img src={DsImage} alt="ds" style={{ margin: 'auto' }} />
            <h4 className="mt-10">Plugin Your USB Token</h4>
            <Button
              variant="primary"
              className="mt-5"
              isLoading={actionTriggered?.id === 'fetch-local-ds' && actionTriggered?.loading === true}
              onClick={() => handleCheckDateTime()}
            >
              {t('digitalSignatureEnrollment')}
            </Button>
          </div>
        )}
      </div>
      <div className="flex-none bg-white w-[300px] rounded-lg">
        <h4 style={{ background: primary }} className="p-3 text-white rounded-lg flex gap-3">
          {' '}
          <DownloadIcon width="24px" height="24px" /> Downloads
        </h4>
        <div className="w-full mt-3 px-5">
          <Button
            variant="link"
            isLoading={loading}
            loadingText="1. KSMART SIGNER"
            onClick={() => handleDownloadJava()}
            mt={2}
            rightIcon={<DownloadIcon width="18px" height="18px" />}
          >
            1. KSMART SIGNER
          </Button>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  dsLocalEnrollment: getDsLocalEnrollment,
  actionTriggered: getActionTriggered,
  allEnrolls: getAllEnrolls
});

const mapDispatchToProps = (dispatch) => ({
  fetchDsEnroll: () => dispatch(actions.fetchDsEnroll()),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchAllEnroll: (data) => dispatch(actions.fetchAllEnroll(data)),
  saveDsEnroll: (data) => dispatch(actions.saveDsEnroll(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Enroll);
