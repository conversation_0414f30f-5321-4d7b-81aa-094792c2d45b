import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { CommonTable } from 'common/components/Table';
import { t } from 'i18next';
import { IconButton } from '@ksmartikm/ui-components';
import View from 'assets/View';
import { actions as commonSliceActions } from 'pages/common/slice';
import { formatDate } from 'utils/date';
import { getAllEnrolls } from '../selector';
import * as actions from '../actions';
import { formateDsData } from '../helper';

const DeactivatedList = (props) => {
  const {
    allEnrolls,
    fetchAllEnroll,
    setAlertAction
  } = props;

  useEffect(() => {
    fetchAllEnroll();
  }, []);

  const handleView = (row) => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: '',
      size: '2xl',
      content: (
        <table className="table text-left rounded-lg w-full text-sm">
          <tbody>
            {formateDsData(row?.row)?.map((item) => (
              <tr key={item.title} className="border">
                <th className="bg-slate-100 p-3">{t(item?.title)}</th>
                <th className="p-3">{item?.value}</th>
              </tr>
            ))}
          </tbody>
        </table>
      ),
      title: t('deactivated'),
      backwardActionText: t('close')
    });
  };

  const tableActions = (row) => {
    return (
      <IconButton onClick={() => handleView(row)} icon={<View />} />
    );
  };

  const header = [
    {
      header: t('name'),
      alignment: 'left',
      field: 'name'
    },
    {
      header: t('validFrom'),
      alignment: 'left',
      field: 'validFrom',
      cell: (row) => (formatDate(row?.row?.validFrom))
    },
    {
      header: t('validTo'),
      alignment: 'left',
      field: 'validTo',
      cell: (row) => (formatDate(row?.row?.validTo))
    },
    {
      header: t('certKeyNo'),
      alignment: 'left',
      field: 'certKeyNo'
    },
    {
      header: t('action'),
      alignment: 'left',
      cell: (row) => tableActions(row)
    }
  ];

  return (
    <div className="w-full bg-white rounded-lg p-5">
      <CommonTable columns={header} tableData={allEnrolls?.esignRegistrationList?.filter((item) => item?.activated === false)} />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  allEnrolls: getAllEnrolls
});

const mapDispatchToProps = (dispatch) => ({
  fetchAllEnroll: (data) => dispatch(actions.fetchAllEnroll(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DeactivatedList);
