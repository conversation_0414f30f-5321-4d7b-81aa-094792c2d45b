import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getCounterNew = (state) => state[STATE_REDUCER_KEY];

const subModuleById = (state) => state?.subModule;
export const getSubModuleById = flow(getCounterNew, subModuleById);

const moduleById = (state) => state?.module;
export const getmoduleById = flow(getCounterNew, moduleById);

const subModulesSearchList = (state) => state?.subModulesSearchList;
export const getSubModulesSearchList = flow(getCounterNew, subModulesSearchList);

const servicesSearchList = (state) => state?.servicesSearchList;
export const getServicesSearchList = flow(getCounterNew, servicesSearchList);

const activeApplicationData = (state) => state?.activeCounterFormData;
export const getActiveApplicationData = flow(getCounterNew, activeApplicationData);

const activeDocumentsDetails = (state) => state?.documentDetails?.data;
export const getDocumentsDetails = flow(getCounterNew, activeDocumentsDetails);

const setDocumentDropdownTypes = (state) => state?.documentTypes;
export const getDocumentDropdownTypes = flow(getCounterNew, setDocumentDropdownTypes);

const jointApplicant = (state) => state?.jointApplicant;
export const getJointApplicant = flow(getCounterNew, jointApplicant);

const setActiveInwardId = (state) => state?.activeInwardId;
export const getActiveInwardId = flow(getCounterNew, setActiveInwardId);

const serviceByCodeDetails = (state) => state?.serviceByCodeDetails;
export const getServiceByCodeDetails = flow(getCounterNew, serviceByCodeDetails);

const generalFieldValidation = (state) => state?.generalFieldValidation;
export const getGeneralFieldValidation = flow(getCounterNew, generalFieldValidation);

const requiredDocsCount = (state) => state?.requiredDocsCount;
export const getRequiredDocsCount = flow(getCounterNew, requiredDocsCount);

const esignFormData = (state) => state?.esignFormData;
export const getEsignFormData = flow(getCounterNew, esignFormData);
