import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const saveApplicationServiceApi = (data) => {
  return {
    url: API_URL.ESIGN.SAVE_APPLICATION,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_FAILURE
      ],
      data
    }
  };
};

export const eSignSendForApprovalApi = (payload) => {
  const { id, ...rest } = payload;
  return {
    url: API_URL.ESIGN.E_SIGN_SEND_FOR_APPROVAL.replace(':id', id),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.E_SIGN_SEND_FOR_APPROVAL_REQUEST,
        ACTION_TYPES.E_SIGN_SEND_FOR_APPROVAL_SUCCESS,
        ACTION_TYPES.E_SIGN_SEND_FOR_APPROVAL_FAILURE
      ],
      data: { ...rest }
    }
  };
};

export const eSignApproveRequestApi = (payload) => {
  const { userId } = payload;
  return {
    url: API_URL.ESIGN.E_SIGN_APPROVE_REQUEST.replace(':userId', userId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.E_SIGN_APPROVE_REQUEST_REQUEST,
        ACTION_TYPES.E_SIGN_APPROVE_REQUEST_SUCCESS,
        ACTION_TYPES.E_SIGN_APPROVE_REQUEST_FAILURE
      ]
    }
  };
};

export const saveGeneralDetails = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_GENERAL_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_GENERAL_DETAILS_REQUEST,
        ACTION_TYPES.SAVE_GENERAL_DETAILS_SUCCESS,
        ACTION_TYPES.SAVE_GENERAL_DETAILS_FAILURE
      ],
      data
    }
  };
};

export const fetchApplication = (data) => {
  return {
    url: API_URL.ESIGN.FETCH_APPLICATION.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.FETCH_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.FETCH_APPLICATION_SERVICE_FAILURE
      ]
    },
    data
  };
};

export const fetchServiceByServiceCode = (data) => {
  return {
    url: API_URL.COUNTER.SERVICE_BY_SERVICE_CODE.replace(':code', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_BY_SERVICE_CODE_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_BY_SERVICE_CODE_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_BY_SERVICE_CODE_FAILURE
      ]
    },
    data
  };
};

export const fetchSubModulesByModuleId = (data) => {
  return {
    url: API_URL.COMMON.SUB_MODULES_BY_MODULE_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_REQUEST,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE
      ]
    },
    data
  };
};

export const saveDocuments = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};
export const saveMandatoryDocuments = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_MANDATORY_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};

export const getDocumentTypes = (data) => {
  return {
    url: API_URL.COUNTER.DOCUMENT_TYPES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DOCUMENT_TYPES_REQUEST,
        ACTION_TYPES.FETCH_DOCUMENT_TYPES_SUCCESS,
        ACTION_TYPES.FETCH_DOCUMENT_TYPES_FAILURE
      ]
    },
    data
  };
};

export const saveDocumentsTypeData = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_DOCUMENTS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_REQUEST,
        ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_SUCCESS,
        ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_FAILURE
      ],
      data
    }
  };
};
export const saveComplete = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_COMPLETE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.COMPLETE_SAVE_REQUEST,
        ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
        ACTION_TYPES.COMPLETE_SAVE_FAILURE
      ],
      data
    }
  };
};

export const deleteApplicant = (data) => {
  return {
    url: API_URL.COUNTER.DELETE_APPLICANT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_APPLICANT_REQUEST,
        ACTION_TYPES.DELETE_APPLICANT_SUCCESS,
        ACTION_TYPES.DELETE_APPLICANT_FAILURE
      ],
      data
    }
  };
};

export const generateDemand = (data) => {
  return {
    url: API_URL.COUNTER.GENERATE_DEMANT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.GENERATE_DEMANT_REQUEST,
        ACTION_TYPES.GENERATE_DEMANT_SUCCESS,
        ACTION_TYPES.GENERATE_DEMANT_FAILURE
      ],
      data
    }
  };
};

export const updateDocuments = (data) => {
  return {
    url: API_URL.COUNTER.UPDATE_DOCUMENTS,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_DOCUMENTS_REQUEST,
        ACTION_TYPES.UPDATE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.UPDATE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};
