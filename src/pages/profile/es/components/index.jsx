import { useEffect, useState, useMemo } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  AccordionComponent,
  t,
  Button,
  VerticalStepper,
  Select
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  getAcknowledgeTriggered,
  getActionTriggered,
  getCompletedSteps,
  getInwardNextUserModal,
  getInwardUsers,
  getServiceAccountHead,
  getSidebarData,
  getUserInfo
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { HOME_UI_PATH, MODULE_PATH } from 'common/constants';
import Acknowledgement from 'common/components/Aknowledgement';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import * as commonActions from 'pages/common/actions';
import InwardUserConfirmation from 'common/components/WorkflowConfirmation';
import { convertToLocalDateTime } from 'utils/date';
import { removeDuplicates } from 'utils/validateFile';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import Services from './Services';
import {
  SIDEBAR_KEYS,
  COUNTER_APPLICATION_KEYS_INDEX,
  COUNTER_APPLICATION_KEYS,
  inwardAknowledgementUrl
} from '../constants';
import {
  getActiveApplicationData,
  getActiveInwardId,
  getEsignFormData,
  getRequiredDocsCount,
  getServiceByCodeDetails
} from '../selectors';
import './style.css';

const EsignEnroll = (props) => {
  const {
    setFormTitle,
    setActiveAccordian,
    formComponentData,
    setFormComponentData,
    setSidebarStatus,
    activeApplicationData,
    setActiveCounterFormData,
    fetchApplication,
    activeInwardId,
    saveComplete,
    acknowledgeTriggered,
    setAcknowledgeTriggered,
    serviceByCodeDetails,
    userInfo,
    fetchServiceValidation,
    requiredDocsCount,
    setActionTriggered,
    actionTriggered,
    setAlertAction,
    fetchServiceAccountHead,
    serviceAccountHead,
    generateDemand,
    completedSteps,
    fetchInwardUsers,
    inwardUsers,
    inwardNextUserModal,
    setInwardNextUserModal,
    eSignSendForApproval,
    esignData,
    eSignApproveRequest,
    forApproval = false
  } = props;

  const params = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  let sidebarData = [];
  const [haveReceipt, setHaveReceipt] = useState(false);
  const [receiptOk, setReceiptOk] = useState(false);
  const [nextRole, setNextRole] = useState([]);
  const [nextUser, setNextUser] = useState({});

  const isModeApproval = useMemo(
    () => forApproval && userInfo?.userDetails?.isAdministrator,
    [searchParams, userInfo]
  );

  useEffect(() => {
    if (inwardUsers?.length > 0) {
      setNextRole(
        inwardUsers?.map((ps) => ({
          id: ps?.postId,
          name: `${ps?.employeeName ? ps?.employeeName : ''} - ${
            ps?.penNo ? ps?.penNo : ''
          } - ${ps?.designation ? ps?.designation : ''} - ${
            ps?.seat ? ps?.seat : ''
          }`,
          employeeName: ps?.employeeName,
          fileId: ps?.fileId,
          fileNo: ps?.fileNo,
          penNo: ps?.penNo,
          role: ps?.role,
          designation: ps?.designation,
          seat: ps?.seat
        }))
      );
    }
  }, [JSON.stringify(inwardUsers)]);

  useEffect(() => {
    if (nextRole?.length === 1) {
      setNextUser({
        postId: nextRole[0]?.id,
        inwardNo: activeApplicationData?.inwardNo,
        forwardingTo: nextRole[0]?.employeeName,
        designation: nextRole[0]?.designation,
        role: nextRole[0]?.role || null,
        penNo: nextRole[0]?.penNo,
        seat: nextRole[0]?.seat,
        dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextUser({});
    }
  }, [nextRole]);

  useEffect(() => {
    if (searchParams?.get('receipt') === 'ok') {
      setTimeout(() => {
        scrollToTop('counter-payment-ok');
      }, 300);
    }
  }, [searchParams]);

  useEffect(() => {
    setFormTitle({ title: t('newInward'), variant: 'normal' });
  }, []);

  useEffect(() => {
    if (params) {
      if (params.id) {
        fetchApplication(params.id);
      } else {
        setActiveCounterFormData({});
      }
    } else {
      setSidebarStatus({
        activeStep: 1
      });
    }
  }, []);

  useEffect(() => {
    if (activeInwardId) {
      navigate(`${MODULE_PATH}/counter/${activeInwardId}`);
    }
  }, [activeInwardId]);

  useEffect(() => {
    if (activeApplicationData?.serviceCode) {
      fetchServiceValidation(activeApplicationData?.serviceCode);
      fetchServiceAccountHead(activeApplicationData?.serviceCode);
      if (activeApplicationData?.demandStatus === 'CLOSED') {
        setReceiptOk(true);
      }
    }
    if (activeApplicationData?.generalDetailsResponses) {
      fetchInwardUsers(params?.id);
    }
  }, [activeApplicationData]);

  useEffect(() => {
    if (serviceAccountHead?.receiptAtTheTimeOfApplication) {
      setHaveReceipt(serviceAccountHead?.receiptAtTheTimeOfApplication === 1);
    }
  }, [serviceAccountHead]);

  const onClickSidebarItem = (currentStep) => {
    const formFieldKeys = Object.keys(COUNTER_APPLICATION_KEYS);
    const currentFormFieldKey = formFieldKeys[Number(currentStep - 1)];
    setSidebarStatus({
      activeStep: currentStep,
      completedSteps: [...formComponentData.completedSteps, '']
    });
    switch (COUNTER_APPLICATION_KEYS[currentFormFieldKey]) {
      case COUNTER_APPLICATION_KEYS.YourInformation:
        setActiveAccordian(COUNTER_APPLICATION_KEYS.YourInformation);
        break;
      // case COUNTER_APPLICATION_KEYS.Address:
      //   setActiveAccordian(COUNTER_APPLICATION_KEYS.Address);
      //   break;
      // case COUNTER_APPLICATION_KEYS.Document:
      //   setActiveAccordian(COUNTER_APPLICATION_KEYS.Document);
      //   break;
      default:
        return false;
    }
    return true;
  };

  const accordionData = [
    {
      title: t('yourInformation'),
      content: (
        <Services
          isModeApproval={isModeApproval}
          formActiveData={activeApplicationData}
        />
      ),
      onPress: () => onClickSidebarItem(COUNTER_APPLICATION_KEYS_INDEX.YourInformation),
      isCompleted: completedSteps.includes(
        COUNTER_APPLICATION_KEYS_INDEX.YourInformation
      )
    }
    // {
    //   title: t('address'),
    //   content: <ApplicantDetails formActiveData={activeApplicationData} />,
    //   onPress: () => onClickSidebarItem(COUNTER_APPLICATION_KEYS_INDEX.Address),
    //   isCompleted: completedSteps.includes(COUNTER_APPLICATION_KEYS_INDEX.Address)
    // },
    // {
    //   title: t('documentAttachments'),
    //   content: <Documents formActiveData={activeApplicationData} />,
    //   onPress: () => onClickSidebarItem(COUNTER_APPLICATION_KEYS_INDEX.DocumentAttachments),
    //   isCompleted: completedSteps.includes(COUNTER_APPLICATION_KEYS_INDEX.DocumentAttachments)
    // }
  ];

  const SIDEBAR_STEPPER_STEPS = {
    APPLICATION: [
      {
        title: t('yourInformation'),
        onClick: () => onClickSidebarItem(COUNTER_APPLICATION_KEYS_INDEX.YourInformation)
      }
      // {
      //   title: t('address'),
      //   onClick: () => onClickSidebarItem(COUNTER_APPLICATION_KEYS_INDEX.Address)
      // },
      // {
      //   title: t('documentAttachments'),
      //   onClick: () => onClickSidebarItem(COUNTER_APPLICATION_KEYS_INDEX.DocumentAttachments)
      // }
    ]
  };

  const getSidebarAccordionData = () => {
    return [
      {
        title: t('application'),
        key: SIDEBAR_KEYS.APPLICATION,
        content: (
          <VerticalStepper
            steps={SIDEBAR_STEPPER_STEPS.APPLICATION}
            activeStep={Number(formComponentData.activeStep)}
            completedSteps={formComponentData?.completedSteps}
          />
        )
      },
      {
        title: t('concatLabel', { label: t('required'), type: t('documents') }),
        key: SIDEBAR_KEYS.REQUIRED_DOCUMENTS,
        content: ''
      },
      { title: t('guidelines'), key: SIDEBAR_KEYS.GUIDELINES, content: '' },
      { title: t('quickAccess'), key: SIDEBAR_KEYS.QUICK_ACCESS, content: '' },
      { title: t('process'), key: SIDEBAR_KEYS.PROCESS, content: '' }
    ];
  };

  useEffect(() => {
    sidebarData = getSidebarAccordionData();
    setFormComponentData({
      data: sidebarData,
      steps: SIDEBAR_STEPPER_STEPS.APPLICATION
    });
  }, [formComponentData?.activeStep]);

  const handleAfterSuccessRoute = () => {
    window.location.href = `/${HOME_UI_PATH}/home/<USER>/my-profile`;
  };

  const save = (actionType) => {
    setActionTriggered({ loading: true, id: 'esignCreate' });

    const eSignDataObject = esignData?.[0];

    if (actionType === 'final') {
      if (eSignDataObject?.id) {
        eSignSendForApproval({
          data: eSignDataObject,
          onSuccessCallback: handleAfterSuccessRoute
        });
      }
      return;
    }

    if (actionType === 'approve') {
      const data = {
        userId: eSignDataObject?.id
      };

      eSignApproveRequest({ data, onSuccessCallback: () => window.history.back() });
      return;
    }

    const steps = [];
    if (activeApplicationData) {
      if (activeApplicationData?.inwardId) {
        const find = steps.findIndex(
          (item) => item === COUNTER_APPLICATION_KEYS.YourInformation
        );
        if (find === -1) {
          steps.push(COUNTER_APPLICATION_KEYS.YourInformation);
        }
      }
      if (activeApplicationData?.applicantDetailsAddress?.length > 0) {
        const find = steps.findIndex(
          (item) => item === COUNTER_APPLICATION_KEYS.Address
        );
        if (find === -1) {
          steps.push(COUNTER_APPLICATION_KEYS.Address);
        }
      }
      if (activeApplicationData?.applicantDetailsAddress?.length > 0) {
        if (
          activeApplicationData?.generalDetailsResponses?.details?.length
          >= activeApplicationData?.applicantDetailsAddress?.length
        ) {
          const find = steps.findIndex(
            (item) => item === COUNTER_APPLICATION_KEYS.DocumentAttachments
          );
          if (find === -1) {
            steps.push(COUNTER_APPLICATION_KEYS.DocumentAttachments);
          }
        }
      }
      if (
        requiredDocsCount === 0
        || activeApplicationData?.documentsTypeUpload?.length === requiredDocsCount
      ) {
        const find = steps.findIndex(
          (item) => item === COUNTER_APPLICATION_KEYS.Document
        );
        if (find === -1) {
          steps.push(COUNTER_APPLICATION_KEYS.Document);
        }
      }
    }

    if (steps.length > 0) {
      const findService = steps.findIndex(
        (item) => item === COUNTER_APPLICATION_KEYS.YourInformation
      );
      const findApplicant = steps.findIndex(
        (item) => item === COUNTER_APPLICATION_KEYS.Address
      );
      const findGeneral = steps.findIndex(
        (item) => item === COUNTER_APPLICATION_KEYS.DocumentAttachments
      );
      const findDocs = steps.findIndex(
        (item) => item === COUNTER_APPLICATION_KEYS.Document
      );
      if (findService === -1) {
        setActiveAccordian(COUNTER_APPLICATION_KEYS.YourInformation);
        setSidebarStatus({
          activeStep: 0,
          completedSteps: [
            ...formComponentData.completedSteps,
            formComponentData.activeStep
          ]
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('service')} ${t(
            'section'
          )}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('information_details');
        setActionTriggered({ loading: false, id: 'esignCreate' });
      } else if (findApplicant === -1) {
        setActiveAccordian(COUNTER_APPLICATION_KEYS.Address);
        setSidebarStatus({
          activeStep: 1,
          completedSteps: [
            ...formComponentData.completedSteps,
            formComponentData.activeStep
          ]
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('applicant')} ${t(
            'section'
          )}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('address_details');
        setActionTriggered({ loading: false, id: 'esignCreate' });
      } else if (findGeneral === -1) {
        setActiveAccordian(COUNTER_APPLICATION_KEYS.DocumentAttachments);
        setSidebarStatus({
          activeStep: 2,
          completedSteps: [
            ...formComponentData.completedSteps,
            formComponentData.activeStep
          ]
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('general')} ${t(
            'section'
          )}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('general_details');
        setActionTriggered({ loading: false, id: 'esignCreate' });
      } else if (findDocs === -1) {
        setActiveAccordian(COUNTER_APPLICATION_KEYS.Document);
        setSidebarStatus({
          activeStep: 3,
          completedSteps: [
            ...formComponentData.completedSteps,
            formComponentData.activeStep
          ]
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('document')} ${t(
            'section'
          )}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('address_details');
        setActionTriggered({ loading: false, id: 'esignCreate' });
      } else if (actionType === 'receipt') {
        const sendData = {
          inwardId: params?.id,
          filestoreId: 0,
          userInfo: {
            officeId: userInfo?.id
          }
        };
        generateDemand(sendData);
      } else {
        // const saveData = {
        //   inwardId: params.id,
        //   filestoreId: 0,
        //   userInfo: {
        //     officeId: userInfo?.id
        //   }
        // };
        // saveComplete(saveData);
        if (Object.keys(nextUser).length === 0) {
          setAlertAction({
            open: true,
            variant: 'warning',
            message: 'Please select Forward to',
            title: 'Warning',
            backwardActionText: t('ok'),
            closeOnOverlayClick: false,
            closeOnEsc: false
          });
        } else {
          setInwardNextUserModal(true);
        }
        setActionTriggered({ loading: false, id: 'esignCreate' });
      }
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseComplete')} ${t('the')} ${t('above')} ${t(
          'section'
        )}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
      scrollToTop('information_details');
      setActiveAccordian(COUNTER_APPLICATION_KEYS.YourInformation);
      setSidebarStatus({
        activeStep: 0,
        completedSteps: [
          ...formComponentData.completedSteps,
          formComponentData.activeStep
        ]
      });
      setActionTriggered({ loading: false, id: 'esignCreate' });
    }
  };

  const handleChange = (data) => {
    if (data) {
      setNextUser({
        postId: data?.id,
        inwardNo: activeApplicationData?.inwardNo,
        forwardingTo: data?.employeeName,
        designation: data?.designation,
        role: data?.role || null,
        penNo: data?.penNo,
        seat: data?.seat,
        dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextUser({});
      setInwardNextUserModal(false);
    }
  };

  const handleSave = () => {
    const saveData = {
      inwardId: params.id,
      filestoreId: 0,
      postId: nextUser?.postId,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    saveComplete(saveData);
  };

  const handleClose = () => {
    setInwardNextUserModal(false);
  };

  const handleReset = () => {
    setAcknowledgeTriggered(false);
    window.location.href = `${MODULE_PATH}/counter`;
  };

  const anknowledgePreview = {
    date: activeApplicationData?.inwardDate,
    time: '',
    district: 'Thiruvananthapuram',
    localBody: 'Neyyattinkara',
    title: serviceByCodeDetails ? serviceByCodeDetails[0]?.name : '',
    description: `Counter Application Submitted Successfully against 
    ${
  serviceByCodeDetails ? serviceByCodeDetails[0]?.name : ''
} with reference number ${activeApplicationData?.inwardNo}`
  };

  const printData = `${inwardAknowledgementUrl}${userInfo?.id}/${params.id}`;

  return (
    <div className="pb-[50px]">
      <AccordionComponent
        data={accordionData}
        allowMultiple={false}
        currentIndexes={[formComponentData.activeStep]}
        offset={0}
      />
      <div id="counter-payment-ok" />
      <div className="grid grid-cols-12 gap-x-5 my-5 bg-white rounded-lg py-5 px-10">
        {haveReceipt && !receiptOk && (
          <>
            <div className="lg:col-span-8 md:col-span-8 col-span-12" />
            <div className="lg:col-span-4 md:col-span-4 col-span-12 flex justify-end">
              <Button
                type="submit"
                form="hook-form"
                onClick={() => save('receipt')}
                variant="secondary_outline"
                isLoading={
                  actionTriggered?.id === 'esignCreate'
                  && actionTriggered?.loading
                }
              >
                {t('proceed')}
              </Button>
            </div>
          </>
        )}
        <div className="lg:col-span-8 md:col-span-8 col-span-12" />
        {esignData && (
          <div className="lg:col-span-4 md:col-span-4 col-span-12 flex gap-3 items-center justify-end">
            {(nextRole?.filter((item) => item?.id !== null).length > 0
              && haveReceipt
              && receiptOk)
            || (nextRole?.filter((item) => item?.id !== null).length > 0
              && !haveReceipt) ? (
                <Select
                  name="user"
                  type="select"
                  value={nextUser?.postId}
                  label={t('forwardTo')}
                  placeholder={t('select')}
                  optionKey="id"
                  onChange={(data) => handleChange(data)}
                  options={removeDuplicates(nextRole, 'penNo') || []}
                  isClearable
                />
              ) : (
                ''
              )}
            {(haveReceipt && receiptOk) || !haveReceipt ? (
              <Button
                type="submit"
                form="hook-form"
                onClick={() => save(isModeApproval ? 'approve' : 'final')}
                variant="secondary"
                isLoading={
                  actionTriggered?.id === 'esignCreate'
                  && actionTriggered?.loading
                }
              >
                {t(isModeApproval ? 'approve' : 'submit')}
              </Button>
            ) : null}
          </div>
        )}
      </div>
      <Acknowledgement
        open={acknowledgeTriggered}
        handleClick={handleReset}
        print={printData}
        data={anknowledgePreview}
      />
      <InwardUserConfirmation
        open={inwardNextUserModal}
        close={handleClose}
        handleSelect={handleSave}
        nextUser={nextUser}
        isLoading={
          actionTriggered?.id === 'esignCreate' && actionTriggered?.loading
        }
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  formComponentData: getSidebarData,
  activeApplicationData: getActiveApplicationData,
  activeInwardId: getActiveInwardId,
  acknowledgeTriggered: getAcknowledgeTriggered,
  serviceByCodeDetails: getServiceByCodeDetails,
  userInfo: getUserInfo,
  requiredDocsCount: getRequiredDocsCount,
  actionTriggered: getActionTriggered,
  serviceAccountHead: getServiceAccountHead,
  completedSteps: getCompletedSteps,
  inwardUsers: getInwardUsers,
  inwardNextUserModal: getInwardNextUserModal,
  esignData: getEsignFormData
});

const mapDispatchToProps = (dispatch) => ({
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  setFormComponentData: (data) => dispatch(commonSliceActions.setFormComponentData(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActiveCounterFormData: (data) => dispatch(sliceActions.setActiveCounterFormData(data)),
  fetchApplication: (data) => dispatch(actions.fetchApplicationService(data)),
  saveComplete: (data) => dispatch(actions.saveComplete(data)),
  setAcknowledgeTriggered: (data) => dispatch(commonSliceActions.setAcknowledgeTriggered(data)),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchServiceAccountHead: (data) => dispatch(commonActions.fetchServiceAccountHead(data)),
  generateDemand: (data) => dispatch(actions.generateDemand(data)),
  fetchInwardUsers: (data) => dispatch(commonActions.fetchInwardUsers(data)),
  setInwardNextUserModal: (data) => dispatch(commonSliceActions.setInwardNextUserModal(data)),
  eSignSendForApproval: (data) => dispatch(actions.eSignSendForApprovalAction(data)),
  eSignApproveRequest: (data) => dispatch(actions.eSignApproveRequestAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(EsignEnroll);
