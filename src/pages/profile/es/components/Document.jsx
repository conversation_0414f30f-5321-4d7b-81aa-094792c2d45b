import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, t, To<PERSON>, ErrorText, But<PERSON>
} from 'common/components';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import { convertToLocalDate, reFormattedDate } from 'utils/date';
import * as commonActions from 'pages/common/actions';
import PreviewThumbnail from 'common/components/DocumentPreview/previewThumbnail';
import { dark, light } from 'utils/color';
import {
  getActionTriggered,
  getAllDocumentsType,
  getServiceValidation,
  getSidebarData,
  getUserInfo
} from 'pages/common/selectors';
import { DATE_FORMAT } from 'pages/common/constants';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { actions as commonSliceActions } from 'pages/common/slice';
import { STORAGE_KEYS } from 'common/constants';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import { DocumentDetailsFormSchema } from '../validate';
import { COUNTER_APPLICATION_KEYS, DOCUMENTS_IGNORE_LIST, documenturl } from '../constants';

const Documents = (props) => {
  const [tempArray, setTempArray] = useState([]);
  const [docPreview, setDocPreview] = useState(null);
  const { errorTost } = Toast;

  const {
    documentTypeDropdown,
    saveDocuments,
    deleteDocuments,
    fecthAllDocumentTypes,
    fetchApplication,
    saveMandatoryDocuments,
    formActiveData,
    userInfo,
    servicevalidation,
    setRequiredDocsCount,
    setActiveAccordian,
    setSidebarStatus,
    formComponentData,
    setActionTriggered,
    actionTriggered,
    updateDocuments
  } = props;

  const params = useParams();

  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    reset,
    watch,
    handleSubmit
  } = useForm({
    mode: 'all',
    defaultValues: {
      supportDocumentsName: '',
      supportingDocuments: '',
      documents: '',
      documentType: '',
      issueDate: '',
      validUpto: '',
      documentNo: '',
      haveHardCopy: false,
      activeIdEdit: null
    },
    resolver: yupResolver(DocumentDetailsFormSchema)
  });

  const [existingSupportingDocs, setExistingSupportingDocs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [documentIds, setDocumentIds] = useState([]);
  const [supportingValidateShow, setSupportingValidateShow] = useState('');
  const [selectedSupportingDocs, setSelectedSupportingDocs] = useState(null);
  const [editMandatoryId, setEditMandatoryId] = useState(null);

  useEffect(() => {
    if (formActiveData) {
      setEditMandatoryId(null);
      if (formActiveData?.supportingDocs) {
        if (formActiveData?.supportingDocs?.length > 0) {
          setExistingSupportingDocs(formActiveData?.supportingDocs);
          setValue('supportingDocuments', null);
          setValue('supportDocumentsName', '');
        } else {
          setExistingSupportingDocs([]);
        }
      }
    }
  }, [formActiveData]);

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  useEffect(() => {
    if (servicevalidation) {
      setDocumentIds(servicevalidation?.documentId);
    }
  }, [servicevalidation]);

  const formateDocType = (response) => {
    setTempArray([]);
    const formattedResponse = (response || []).map((item) => ({
      id: item.id,
      name: item.name,
      nameInLocal: item.nameInLocal,
      data: []
    }));

    if (formActiveData) {
      const documentsTypeUpload = formActiveData?.documentsTypeUpload;
      if (documentsTypeUpload) {
        if (documentsTypeUpload?.length > 0) {
          for (let i = 0; i < documentsTypeUpload?.length; i += 1) {
            const findFileTypeIndex = formattedResponse.findIndex(
              (item) => item.id === parseInt(documentsTypeUpload[i]?.documentType, 10)
            );
            const exist = formattedResponse[findFileTypeIndex]
              ? JSON.parse(JSON.stringify(formattedResponse[findFileTypeIndex]?.data))
              : [];
            const newData = [documentsTypeUpload[i]];
            const merge = [...exist, ...newData];
            if (findFileTypeIndex > -1) {
              formattedResponse[findFileTypeIndex].data = merge;
            }
          }
        }
      }
    }

    setTempArray(formattedResponse);
  };

  useEffect(() => {
    if (documentIds.length > 0 && !DOCUMENTS_IGNORE_LIST.includes(formActiveData?.serviceCode)) {
      if (documentIds[0] !== 0) {
        const sortedDocs = documentIds.map((ids) => {
          const sortedIds = documentTypeDropdown.find((item) => item.id === ids);
          return sortedIds;
        });
        setRequiredDocsCount(sortedDocs.length);
        if (sortedDocs) {
          formateDocType(sortedDocs);
        }
      }
    } else {
      setRequiredDocsCount(0);
    }
  }, [documentTypeDropdown, formActiveData, documentIds]);

  useEffect(() => {
    if (params.id) {
      fetchApplication(params.id);
      fecthAllDocumentTypes();
    }
  }, [params.id]);

  useEffect(() => {
    fecthAllDocumentTypes();
  }, []);

  const resetData = () => {
    setExistingSupportingDocs([]);
    setTempArray([]);
    reset({
      supportDocumentsName: '',
      supportingDocuments: '',
      documents: '',
      documentType: '',
      issueDate: '',
      validUpto: '',
      documentNo: ''
    });
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  const handleFieldChange = (field, data, index) => {
    switch (field) {
      case 'supportDocumentsName':
        setSupportingValidateShow(false);
        break;
      case 'supportDocumentType':
        setValue('supportDocumentType', data?.id);
        if (!data?.name.includes('Other')) {
          setValue('supportDocumentsName', data?.name); // supportDocumentType is not other so supportDocumentsName and supportDocumentType are same
          setSupportingValidateShow(false);
        } else {
          setValue('supportDocumentsName', ''); // supportDocumentType is other so supportDocumentsName and supportDocumentType are not same
          setSupportingValidateShow(false);
        }
        break;
      case 'docNo':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].documentNo = data; // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      case 'issueDate':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].issueDate = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      case 'validUpto':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].validUpto = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      default:
        break;
    }
  };

  const handleClear = () => {
    setValue('supportDocumentsName', null);
    setValue('supportDocumentType', null);
    setValue('issueDate', null);
    setValue('validUpto', null);
  };

  const onFileSelected = (data) => {
    if (!data) {
      setValue('supportingDocuments', null);
      return;
    }

    if (params?.id) {
      if (data) {
        if (getValues('supportDocumentsName') === '' || getValues('supportDocumentsName') === undefined) {
          setSupportingValidateShow(true);
        } else {
          setSupportingValidateShow(false);
          const saveData = {
            inwardId: params?.id,
            documentName: getValues('supportDocumentsName'),
            issueDate: convertToLocalDate(getValues('issueDate'), DATE_FORMAT.DATE_LOCAL),
            validUpto: convertToLocalDate(getValues('validUpto'), DATE_FORMAT.DATE_LOCAL),
            documentTypeId: getValues('supportDocumentType'),
            hardCopyReceived: getValues('haveHardCopy'),
            userInfo: {
              officeId: userInfo?.id
            }
          };
          setActionTriggered({ loading: true, id: 'counter-supporting-doc-upload' });
          saveDocuments({ supportingDocs: data, request: saveData, handleClear });
          setValue('supportDocumentsName', '');
          setValue('documentType', '');
          setValue('haveHardCopy', false);
          setValue('activeIdEdit', null);
        }
      } else {
        setSupportingValidateShow(false);
        setValue('supportingDocuments', null);
      }
    } else {
      errorTost({
        title: t('missingSections'),
        description: `${t('pleaseComplete')} ${t('the')} ${t('servicee')} ${t('section')}`
      });
      setValue('supportingDocuments', null);
      setActiveAccordian(COUNTER_APPLICATION_KEYS.Service);
      setSidebarStatus({
        activeStep: 0,
        completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep]
      });
    }
  };

  const handleSupportDoc = (data) => {
    setSelectedSupportingDocs(data);
  };

  const onSubmitForm = () => {
    if (watch('activeIdEdit')) {
      const sendData = {
        inwardId: params?.id,
        documentTypeId: getValues('supportDocumentType'),
        fileId: watch('activeIdEdit'),
        isSupportingDoc: true,
        issueDate: convertToLocalDate(getValues('issueDate'), DATE_FORMAT.DATE_LOCAL),
        validUpto: convertToLocalDate(getValues('validUpto'), DATE_FORMAT.DATE_LOCAL),
        hardCopyReceived: getValues('haveHardCopy'),
        userInfo: {
          officeId: userInfo?.id
        },
        documentName: getValues('supportDocumentsName')
      };
      setActionTriggered({ loading: true, id: 'counter-supporting-doc-upload' });
      updateDocuments({ requestData: sendData, handleClear });
    } else {
      onFileSelected(selectedSupportingDocs);
    }
  };

  const onHandleRemove = (data) => {
    const sendData = {
      inwardId: params.id,
      documentTypeId: data.documentType,
      fileId: data.id,
      userInfo: {
        officeId: userInfo?.id
      },
      source: 'counter'
    };
    deleteDocuments(sendData);
  };

  const handleUpdateMandatory = (item, id) => {
    const sendData = {
      inwardId: params?.id,
      documentTypeId: id,
      fileId: item?.data[0]?.id,
      isSupportingDoc: false,
      issueDate: convertToLocalDate(watch(`fieldIssueDate${id}`), DATE_FORMAT.DATE_LOCAL),
      validUpto: convertToLocalDate(watch(`fieldValidUpto${id}`), DATE_FORMAT.DATE_LOCAL),
      hardCopyReceived: item?.data[0]?.hardCopyReceived || false,
      documentNo: watch(`fieldDocNo${id}`),
      userInfo: {
        officeId: userInfo?.id
      },
      documentName: item?.data[0]?.name
    };
    setActionTriggered({ loading: true, id: 'counter-supporting-doc-upload' });
    updateDocuments({ requestData: sendData, handleClear });
  };

  const onHandleEdit = (data, type) => {
    if (type === 2) {
      setValue('activeIdEdit', data?.id);
      setValue('supportDocumentsName', data.documentName);
      setValue('supportDocumentType', data.documentTypeId);
      setValue('issueDate', reFormattedDate(data.issueDate));
      setValue('validUpto', reFormattedDate(data.validUpto));
      setValue('haveHardCopy', data.hardCopyReceived);
      setEditMandatoryId(null);
    } else {
      setValue(`fieldDocNo${Number(data?.documentType)}`, data.documentNo);
      setValue(`fieldIssueDate${data.id}`, reFormattedDate(data.issueDate));
      setValue(`fieldValidUpto${data.id}`, reFormattedDate(data.validUpto));
      setEditMandatoryId(Number(data?.documentType));
    }
  };

  function getDocument(url, token, body) {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  }

  const handlePreview = (data, type, typeId) => {
    const sendData = {
      inwardId: params.id,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: data.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };

  const handleCancelUpdate = () => {
    reset({
      supportDocumentsName: '',
      supportingDocuments: '',
      documents: '',
      documentType: '',
      issueDate: '',
      validUpto: '',
      documentNo: '',
      haveHardCopy: false,
      activeIdEdit: null
    });
    setEditMandatoryId(null);
  };

  const onSelectDocumentTypes = (data, id, index) => {
    if (!data) {
      return;
    }
    if (data) {
      const saveData = {
        inwardId: params.id,
        documentTypeId: id,
        documentNo: tempArray[index]?.documentNo,
        issueDate: tempArray[index]?.issueDate,
        validUpto: tempArray[index]?.validUpto,
        hardCopyReceived: false,
        userInfo: {
          officeId: userInfo?.id
        },
        documentName: tempArray[index]?.name
      };
      setActionTriggered({ loading: true, id: `counter-mandatory-doc-upload-${id}` });
      saveMandatoryDocuments({ documentTypeDocs: data, request: saveData });
    }
  };

  const getDocumentData = (data, optionKey) => {
    if (data?.data?.length > 0) {
      const returnData = data?.data[0];
      if (returnData[optionKey]) {
        return returnData[optionKey];
      }
      return null;
    }
    return null;
  };

  const getDocumentNoStatus = (data) => {
    if (data?.data?.length > 0) {
      return true;
    }
    return false;
  };

  return (
    <FormWrapper py="2">
      <div id="counter_documents" />

      {tempArray?.length > 0 && (
        <div className="col-span-12 border border-[#e8ecee] rounded-[12px] p-[20px] grid mb-5">
          <div className="col-span-12">
            <div className="px-5 py-3 rounded-full font-medium" style={{ background: light, color: dark }}>
              {t('numberofMandatoryDocuments')}
              <span>: {tempArray.length}</span>
            </div>
          </div>

          {tempArray?.map((item, index) => {
            return (
              <div className="col-span-12 mt-5 mb-5" key={item.id}>
                <div className="mb-5">
                  <h4 className="font-medium" style={{ color: dark }}>
                    {index + 1}. {item.name}
                  </h4>
                </div>

                <div className="grid grid-cols-3 gap-5">
                  {getDocumentNoStatus(item) && editMandatoryId !== item.id ? (
                    <>
                      <FormController
                        name={`fieldDocNo${item.id}`}
                        type="text"
                        label={t('documentNo')}
                        placeholder={t('documentNo')}
                        control={control}
                        errors={errors}
                        value={getDocumentData(item, 'documentNo')}
                        isReadOnly
                      />
                      <FormController
                        name={`fieldIssueDate${item.id}`}
                        variant="outlined"
                        type="date"
                        label={t('issueDate')}
                        control={control}
                        errors={errors}
                        value={reFormattedDate(getDocumentData(item, 'issueDate'))}
                        handleChange={(data) => handleFieldChange('issueDate', data, index)}
                        maxDate={new Date()}
                        disabled
                      />

                      <FormController
                        name={`fieldValidUpto${item.id}`}
                        variant="outlined"
                        type="date"
                        label={t('validUpto')}
                        control={control}
                        errors={errors}
                        value={reFormattedDate(getDocumentData(item, 'validUpto'))}
                        toYear={new Date().getFullYear() + 20}
                        fromYear={new Date().getFullYear()}
                        handleChange={(data) => handleFieldChange('validUpto', data, index)}
                        minDate={getValues(`fieldIssueDate${item.id}`, '')}
                        disabled
                      />
                    </>
                  ) : (
                    <>
                      <FormController
                        name={`fieldDocNo${item.id}`}
                        type="text"
                        label={t('documentNo')}
                        placeholder={t('documentNo')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('docNo', data.target.value, index)}
                      />

                      <FormController
                        name={`fieldIssueDate${item.id}`}
                        variant="outlined"
                        type="date"
                        label={t('issueDate')}
                        control={control}
                        errors={errors}
                        value={reFormattedDate(getDocumentData(item, 'issueDate'))}
                        handleChange={(data) => handleFieldChange('issueDate', data, index)}
                        maxDate={new Date()}
                      />

                      <FormController
                        name={`fieldValidUpto${item.id}`}
                        variant="outlined"
                        type="date"
                        label={t('validUpto')}
                        control={control}
                        errors={errors}
                        value={reFormattedDate(getDocumentData(item, 'validUpto'))}
                        toYear={new Date().getFullYear() + 20}
                        fromYear={new Date().getFullYear()}
                        handleChange={(data) => handleFieldChange('validUpto', data, index)}
                        minDate={getValues(`fieldIssueDate${item.id}`, '')}
                      />
                    </>
                  )}
                </div>

                <div className="col-span-12 mt-5">
                  <div className="grid grid-cols-12 gap-5 items-center">
                    {item?.data?.length === 0 && (
                      <div className="col-span-4">
                        <FormController
                          name={`fieldDocuments${item.id}`}
                          type="file"
                          accept="image,pdf"
                          label={item.name}
                          placeholder={t('dropOrChooseFilesToUpload')}
                          control={control}
                          errors={errors}
                          handleChange={(data) => onSelectDocumentTypes(data, item?.id, index)}
                          optionKey="image"
                          loading={
                            actionTriggered?.id === `counter-mandatory-doc-upload-${item.id}`
                            && actionTriggered?.loading
                          }
                        />
                      </div>
                    )}
                    <div
                      className={
                        item?.data?.length === 0
                          ? 'col-span-8 flex gap-3 justify-between'
                          : 'col-span-12 flex gap-3 justify-between'
                      }
                    >
                      <div className="flex gap-3">
                        {item?.data?.length > 0 && (
                          <PreviewThumbnail
                            key={item.id}
                            handlePreviewRemove={(data) => {
                              onHandleRemove(data);
                              setValue(`fieldDocuments${item.id}`, null);
                            }}
                            handlePreview={(data) => handlePreview(data, 1, data?.documentType)}
                            item={item.data[0]}
                            preview={docPreview}
                            fileType={item?.data[0]?.documentContentType}
                            setLoading={setLoading}
                            loading={loading}
                            haveEdit
                            handleEdit={(data) => onHandleEdit(data, 1)}
                          />
                        )}
                      </div>
                      {item.data.length > 0 && editMandatoryId === item.id && (
                        <div className="col-span-12 text-right gap-3">
                          <Button
                            variant="primary_outline"
                            onClick={() => handleUpdateMandatory(item, item?.id)}
                            isLoading={
                              actionTriggered?.id === 'counter-mandatory-doc-upload' && actionTriggered?.loading
                            }
                          >
                            {t('update')}
                          </Button>
                          <Button variant="secondary_outline" onClick={() => handleCancelUpdate()}>
                            {t('cancel')}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
      <div className="col-span-12 border border-[#e8ecee] rounded-[12px] p-[20px] mb-5">
        <form onSubmit={handleSubmit(onSubmitForm)}>
          <div className="col-span-12 mb-5">
            <div
              className="px-5 py-3 bg-slate-200 rounded-full font-medium text-blue-900"
              style={{ background: light, color: dark }}
            >
              {t('supportingDocuments')}
            </div>
          </div>
          <div className="grid grid-cols-3 gap-5 mb-5">
            <div>
              <FormController
                name="supportDocumentType"
                variant="outlined"
                type="select"
                label={t('documentType')}
                control={control}
                optionKey="id"
                errors={errors}
                options={documentTypeDropdown || []}
                handleChange={(data) => handleFieldChange('supportDocumentType', data)}
                required={watch('haveHardCopy')}
              />
            </div>

            <div>
              <FormController
                name="supportDocumentsName"
                type="text"
                label={t('documentName')}
                placeholder={t('documentName')}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('supportDocumentsName', data)}
                // required
              />
              {supportingValidateShow && <ErrorText error={t('documentNameisRequired')} />}
            </div>

            <FormController
              name="issueDate"
              variant="outlined"
              type="date"
              label={t('issueDate')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('issueDate', data)}
              maxDate={new Date()}
            />

            <FormController
              name="validUpto"
              variant="outlined"
              type="date"
              label={t('validUpto')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('validUpto', data)}
              toYear={new Date().getFullYear() + 20}
              fromYear={new Date().getFullYear()}
              minDate={getValues('issueDate', '')}
            />
            {!watch('activeIdEdit') && (
              <FormController
                name="supportingDocuments"
                type="file"
                accept="image,pdf"
                label={t('attachSupportingDocuments')}
                placeholder={t('dropOrChooseFilesToUpload')}
                control={control}
                errors={errors}
                handleChange={(data) => handleSupportDoc(data)}
                loading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
                isDisabled={watch('activeIdEdit')}
              />
            )}

            <div className="flex gap-3 justify-end">
              <Button
                variant="primary_outline"
                type="submit"
                isLoading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
              >
                {watch('activeIdEdit') ? t('update') : t('upload')}
              </Button>
              {watch('activeIdEdit') && (
                <Button variant="secondary_outline" onClick={() => handleCancelUpdate()}>
                  {t('cancel')}
                </Button>
              )}
            </div>
          </div>

          <div className="col-span-12 flex gap-3 flex-wrap mt-5">
            {existingSupportingDocs.map((item) => (
              <PreviewThumbnail
                key={item.id}
                handlePreviewRemove={(data) => onHandleRemove(data)}
                handlePreview={(data) => handlePreview(data, 2, 0)}
                item={item}
                preview={docPreview}
                fileType={item?.documentContentType}
                documenturl={documenturl}
                setLoading={setLoading}
                loading={loading}
                documentName={item.documentName}
                haveEdit
                handleEdit={(data) => onHandleEdit(data, 2)}
              />
            ))}
          </div>
        </form>
      </div>
    </FormWrapper>
  );
};

const mapStateToProps = createStructuredSelector({
  documentTypeDropdown: getAllDocumentsType,
  formComponentData: getSidebarData,
  userInfo: getUserInfo,
  servicevalidation: getServiceValidation,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  saveDocuments: (data) => dispatch(actions.saveDocuments(data)),
  fecthAllDocumentTypes: (data) => dispatch(commonActions.fecthAllDocumentTypes(data)),
  fetchApplication: (data) => dispatch(actions.fetchApplicationService(data)),
  saveMandatoryDocuments: (data) => dispatch(actions.saveMandatoryDocuments(data)),
  deleteDocuments: (data) => dispatch(commonActions.deleteDocuments(data)),
  setRequiredDocsCount: (data) => dispatch(sliceActions.setRequiredDocsCount(data)),
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  updateDocuments: (data) => dispatch(actions.updateDocuments(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Documents);
