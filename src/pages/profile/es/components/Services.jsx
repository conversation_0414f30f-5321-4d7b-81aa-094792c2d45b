import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FormWrapper, FormController, t, But<PERSON>, ErrorText
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  getFileTypeDropdown,
  getSubModule,
  getUserInfo,
  getActionTriggered,
  getModuleById,
  getGender,
  getCountry,
  getState,
  getDistricts,
  getServiceDfmsDropdown,
  getSidebarData,
  getAllDocumentsType,
  getUserLocalBody
} from 'pages/common/selectors';
import { DEFAULT_COUNTRY, DEFAULT_STATE } from 'common/constants';
import * as commonActions from 'pages/common/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { ENG_ONLY } from 'common/regex';
import { nameValidation } from 'utils/validateFile';
import { formatDateYMDModify } from 'utils/date';
import * as actions from '../actions';
import { InformationFormSchema } from '../validate';
import { getEsignFormData } from '../selectors';
import { informationDefaultValues } from './helper';

const Services = (props) => {
  const {
    fetchDfmsServices,
    fetchCountry,
    fetchStates,
    countryDropdown,
    documentTypeDropdown,
    fecthAllDocumentTypes,
    stateDropdown,
    fetchFileTypeOptions,
    genderOptions,
    fetchGender,
    userInfo,
    setActionTriggered,
    actionTriggered,
    saveApplicationService,
    fetchApplication,
    esignData,
    isModeApproval
  } = props;

  const params = useParams();
  const navigate = useNavigate();

  const [mode, setMode] = useState(params.id ? 'edit' : 'create');

  useEffect(() => {
    setMode(params.id ? 'edit' : 'create');
  }, [params.id]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch
  } = useForm({
    mode: 'all',
    defaultValues: informationDefaultValues,
    resolver: yupResolver(InformationFormSchema)
  });

  const countrySelected = watch('countryId');
  const [supportingValidateShow, setSupportingValidateShow] = useState('');

  useEffect(() => {
    setValue('countryId', DEFAULT_COUNTRY.id);
    setValue('stateId', DEFAULT_STATE.id);
  }, []);
  const resetData = () => {
    reset({ ...informationDefaultValues }, { keepValues: false });
  };
  useEffect(() => {
    if (params.id) {
      fetchApplication(params.id);
    } else {
      resetData();
    }
  }, [params.id]);

  useEffect(() => {
    fetchDfmsServices('serviceTypes=1|1,2|1,3&inputTemplateType=1&eFiling=0&eFiling=1');
    setActionTriggered({ loading: true, id: 'counter-service-dropdown' });
    fetchFileTypeOptions();
    fetchGender();
    fecthAllDocumentTypes();
    fetchCountry();
    fetchStates();
  }, []);

  useEffect(() => {
    if (esignData) {
      const userData = esignData?.[0];

      // Find ADDRESS_PROOF document
      const addressProofDoc = userData.kycUserDocumentResponse.find((doc) => doc.documentType === 'ADDRESS_PROOF');

      // Find PHOTO document
      // const photoDoc = userData.kycUserDocumentResponse.find((doc) => doc.documentType === 'PHOTO');

      // Find ID_PROOF document
      // const idProofDoc = userData.kycUserDocumentResponse.find((doc) => doc.documentType === 'ID_PROOF');
      setValue('pan', userData.pan);
      setValue('nameInPAN', userData.nameOnPan);
      setValue('emailId', userData.emailId);
      setValue('genderId', userData.gender);
      setValue('username', userData.username);
      setValue('city', userData.city);
      setValue('mobile', userData.mobileNumber);
      // setValue('dateOfBirth', convertInputDate(userData.dateOfBirth));
      setValue('countryId', userData.countryId || DEFAULT_COUNTRY.id);
      setValue('stateId', userData.stateCode || DEFAULT_STATE.id);
      setValue('pincode', userData.pincode);
      // setValue('panCopy', idProofDoc?.documentInfo?.s3ObjectKey || '');
      setValue('supportDocumentType', addressProofDoc?.documentType);
      setValue('supportDocumentsName', addressProofDoc?.documentName || '');
      // setValue('photoCopy', photoDoc?.documentInfo?.s3ObjectKey);
      setValue('address', userData.address);

      // const formData = {
      //   pan: userData.pan,
      //   nameInPAN: userData.nameOnPan,
      //   emailId: userData.emailId,
      //   mobile: userData.mobileNumber,
      //   username: userData.username,
      //   genderId: userData.gender,
      //   dateOfBirth: userData.dateOfBirth,
      //   countryId: DEFAULT_COUNTRY.id, // Assuming default country
      //   stateId: userData.stateCode,
      //   city: userData.city,
      //   address: userData.address,
      //   pincode: userData.pincode,
      //   supportDocumentType: addressProofDoc?.documentType || '', // ADDRESS_PROOF document type
      //   supportDocumentsName: addressProofDoc?.documentName || '', // ADDRESS_PROOF document name
      //   photoCopy: photoDoc?.documentInfo?.s3ObjectKey || '', // PHOTO document URL
      //   panCopy: idProofDoc?.documentInfo?.s3ObjectKey || '' // ID_PROOF document URL
      // };

      // console.log('=============formData', userData);
      // console.log('=============mode', mode);
    }
  }, [esignData, params?.id]);

  const handleFieldChange = (field, data) => {
    const nameValidate = nameValidation(data?.target?.value);
    switch (field) {
      case 'pan':
        setValue('pan', data.target?.value || '');
        break;
      case 'nameInPAN':
        setValue('nameInPAN', data.target?.value || '');
        break;
      case 'emailId':
        setValue('emailId', data.target?.value || '');
        break;
      case 'userame':
        setValue('userame', nameValidate.replace(ENG_ONLY, ''));
        break;
      case 'genderId':
        setValue('genderId', data.name);
        break;
      case 'countryId':
        setValue('countryCode', data.countryCode);
        setValue('stateId', null);
        if (data.id !== DEFAULT_COUNTRY.id) {
          setValue('pincode', null);
        } else {
          setValue('pincode', null);
        }
        break;
      case 'stateId':
        setValue('stateCode', data.stateCode);
        break;
      case 'city':
        setValue('city', data.target?.value || '');
        break;
      case 'mobile':
        setValue('mobile', data.target?.value || '');
        break;
      case 'supportDocumentsName':
        setSupportingValidateShow(false);
        break;
      case 'supportDocumentType':
        setValue('supportDocumentType', data?.id);
        if (!data?.name.includes('Other')) {
          setValue('supportDocumentsName', data?.name);
          setSupportingValidateShow(false);
        } else {
          setValue('supportDocumentsName', '');
          setSupportingValidateShow(false);
        }
        break;
      case 'photoCopyAttachment':
        setValue('photoCopy', data.target.files[0]);
        break;
      default:
        break;
    }
  };

  const convertFileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(',')[1]); // Extract the Base64 string
      reader.onerror = (error) => reject(error);
    });
  };

  const handleAfterSuccessRoute = (userId) => {
    navigate(`${userId}`);
  };

  const handleNextStep = async (data) => {
    try {
      setActionTriggered({ loading: true, id: 'informationServiceCreate' });

      // Convert files to Base64
      const photoBase64 = data.photoCopy ? await convertFileToBase64(data.photoCopy) : null;
      const idProofBase64 = data.panCopy ? await convertFileToBase64(data.panCopy) : null;
      const addressProofBase64 = data.supportingDocuments ? await convertFileToBase64(data.supportingDocuments) : null;

      // Prepare the payload
      const saveData = {
        emailId: data.emailId,
        mobileNumber: data.mobile,
        pan: data.pan,
        nameOnPan: data.nameInPAN,
        username: data.username,
        photoBase64: photoBase64 || '',
        stateCode: data.stateId,
        countryCode: data.countryId,
        pincode: data.pincode,
        address: data.address,
        gender: data.genderId,
        dateOfBirth: formatDateYMDModify(data.dateOfBirth),
        city: data.city,
        idProofBase64: idProofBase64 || '',
        addressProofBase64: addressProofBase64 || '',
        txn: '',
        documentName: data.supportDocumentsName,
        officeCode: userInfo?.id
      };
      // Call the save or update function based on mode

      if (mode === 'edit') {
        //  updateApplicationService({
        //   data: { id: params?.id, data: saveData },
        //   onSuccessCallback: handleAfterSuccessRoute,
        // });
      } else {
        saveApplicationService({
          data: saveData,
          onSuccessCallback: handleAfterSuccessRoute
        });
      }
    } catch (error) {
      // console.error('Error:', error);
    }
  };

  return (
    <>
      <form id="information-form" onSubmit={handleSubmit(handleNextStep)}>
        <div id="information_details" />
        <FormWrapper>
          <div className="lg:col-span-4 md:col-span-12 col-span-12">
            <FormController
              name="pan"
              type="text"
              label={t('pan')}
              placeholder={t('pan')}
              handleChange={(data) => handleFieldChange('pan', data)}
              control={control}
              errors={errors}
              disabled={isModeApproval}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-12 col-span-12">
            <FormController
              name="nameInPAN"
              type="text"
              label={t('nameinPAN')}
              placeholder={t('nameInPancard')}
              handleChange={(data) => handleFieldChange('nameInPAN', data)}
              control={control}
              errors={errors}
              disabled={isModeApproval}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="mobile"
              type="text"
              label={t('mobileNo')}
              control={control}
              errors={errors}
              rules={{
                required: 'Mobile number is required',
                pattern: {
                  value: /^[0-9]{10}$/, // Only allows exactly 10 digits
                  message: 'Mobile number must be exactly 10 digits'
                }
              }}
              disabled={isModeApproval}
              handleChange={(data) => handleFieldChange('mobile', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="emailId"
              type="text"
              label={t('emailId')}
              control={control}
              errors={errors}
              disabled={isModeApproval}
              handleChange={(data) => handleFieldChange('emailId', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-12 col-span-12">
            <FormController
              name="username"
              type="text"
              label={t('desiredUserName')}
              placeholder={t('userName')}
              handleChange={(data) => handleFieldChange('username', data)}
              control={control}
              errors={errors}
              disabled={isModeApproval}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-12 col-span-12">
            <FormController
              name="dateOfBirth"
              variant="outlined"
              type="date"
              label={t('dateOfBirth')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('dateOfBirth', data)}
              maxDate={new Date()}
              disabled={isModeApproval}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-12 col-span-12">
            <FormController
              name="genderId"
              variant="outlined"
              type="select"
              label={t('gender')}
              optionKey="name"
              control={control}
              errors={errors}
              isClearable
              disabled={isModeApproval}
              options={_.get(genderOptions, 'data', [])}
              handleChange={(data) => handleFieldChange('genderId', data)}
            />
          </div>
          <div className="col-span-12">
            <h2 className="text-[#153171] font-medium text-[16px] mb-2 mt-2">{t('address')}</h2>
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="countryId"
              type="select"
              label={t('country')}
              control={control}
              errors={errors}
              options={_.get(countryDropdown, 'data', [])}
              handleChange={(data) => handleFieldChange('countryId', data)}
              isLoading={actionTriggered?.id === 'counter-applicant-country' && actionTriggered?.loading}
              optionKey="id"
              required
              isClearable
              disabled={isModeApproval}
            />
          </div>
          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="stateId"
                  type="select"
                  label={t('state')}
                  control={control}
                  errors={errors}
                  optionKey="id"
                  options={_.get(stateDropdown, 'data', [])}
                  handleChange={(data) => handleFieldChange('stateId', data)}
                  isLoading={actionTriggered?.id === 'counter-applicant-state' && actionTriggered?.loading}
                  required
                  isClearable
                  disabled={isModeApproval}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="city"
                  type="text"
                  label={t('city')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('city', data)}
                  required
                  disabled={isModeApproval}
                />
              </div>
            </>
          )}

          <div className="lg:col-span-4 md:col-span-4 col-span-12">
            <FormController
              name="address"
              type="textarea"
              label={t('address')}
              control={control}
              errors={errors}
              disabled={isModeApproval}
              handleChange={(data) => handleFieldChange('address', data)}
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="pincode"
              type="number"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pincode' : 'postZipCode')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('pincode', data)}
              required={Number(countrySelected) === DEFAULT_COUNTRY.id}
              disabled={isModeApproval}
            />
          </div>
          <div className="col-span-12" />

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            {/* Your Photo */}
            <div className="col-span-12">
              <h2 className="text-[#153171] font-medium text-[16px] mb-5">{t('yourPhoto')}</h2>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="photoCopy"
                  type="fileUI"
                  accept="image"
                  label={t('photo')}
                  placeholder={t('dropOrChooseFilesToUpload')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('photoCopyAttachment', data)}
                  loading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
                  isDisabled={watch('activeIdEdit') || isModeApproval}
                />
              </div>
            </div>

            {/* PAN Copy */}
            <div className="col-span-12">
              <h2 className="text-[#153171] font-medium text-[16px] mb-5 mt-3">{t('panCopy')}</h2>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="panCopy"
                  type="file"
                  accept="image,pdf"
                  label={t('panCopy')}
                  placeholder={t('dropOrChooseFilesToUpload')}
                  control={control}
                  errors={errors}
                  onChange={(event) => setValue('panCopy', event.target.files[0])}
                  loading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
                  isDisabled={watch('activeIdEdit') || isModeApproval}
                />
              </div>
            </div>
          </div>
          {/* Address Proof */}
          <div className="col-span-12">
            <h2 className="text-[#153171] font-medium text-[16px] mb-5 mt-3">{t('addressProof')}</h2>
            <div className="grid grid-cols-12 gap-4">
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="supportDocumentType"
                  variant="outlined"
                  type="select"
                  label={t('documentType')}
                  control={control}
                  optionKey="id"
                  errors={errors}
                  options={documentTypeDropdown || []}
                  handleChange={(data) => handleFieldChange('supportDocumentType', data)}
                  required={watch('haveHardCopy')}
                  disabled={isModeApproval}
                />
              </div>

              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="supportDocumentsName"
                  type="text"
                  label={t('documentName')}
                  placeholder={t('documentName')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('supportDocumentsName', data)}
                  disabled={isModeApproval}
                />
                {supportingValidateShow && <ErrorText error={t('documentNameisRequired')} />}
              </div>

              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="supportingDocuments"
                  type="file"
                  accept="image,pdf"
                  label={t('attachSupportingDocuments')}
                  placeholder={t('dropOrChooseFilesToUpload')}
                  control={control}
                  errors={errors}
                  onChange={(event) => setValue('supportingDocuments', event.target.files[0])}
                  loading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
                  isDisabled={watch('activeIdEdit') || isModeApproval}
                />
              </div>
            </div>
          </div>
        </FormWrapper>
      </form>
      {!isModeApproval && (
        <FormWrapper>
          <div className="col-span-12 text-right">
            <Button
              type="submit"
              variant="secondary_outline"
              className="shadow-md"
              form="information-form"
              isLoading={actionTriggered?.id === 'informationServiceCreate' && actionTriggered?.loading}
              loadingText={mode === 'edit' ? 'Updating...' : 'Proceeding...'}
            >
              {mode === 'edit' ? t('update') : t('save')}
            </Button>
          </div>
        </FormWrapper>
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  countryDropdown: getCountry,
  stateDropdown: getState,
  districtDropdown: getDistricts,
  serviceDfmsDropdown: getServiceDfmsDropdown,
  documentTypeDropdown: getAllDocumentsType,
  formComponentData: getSidebarData,
  fileTypeDropdown: getFileTypeDropdown,
  moduleById: getModuleById,
  subModule: getSubModule,
  genderOptions: getGender,
  userLocalBody: getUserLocalBody,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  esignData: getEsignFormData
});

const mapDispatchToProps = (dispatch) => ({
  fetchCountry: () => dispatch(commonActions.fetchCountry()),
  fetchStates: () => dispatch(commonActions.fetchState()),
  fetchDfmsServices: (data) => dispatch(commonActions.fetchDfmsServices(data)),
  fetchSubModuleById: (data) => dispatch(commonActions.fetchSubModuleById(data)),
  fetchModuleById: (data) => dispatch(commonActions.fetchModuleById(data)),
  fetchApplication: (data) => dispatch(actions.fetchApplicationService(data)),
  fetchServiceByServiceCode: (data) => dispatch(actions.fetchServiceByServiceCode(data)),
  fetchFileTypeOptions: () => dispatch(commonActions.fetchFileTypeDetails()),
  fetchGender: () => dispatch(commonActions.fetchGender()),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  saveApplicationService: (data) => dispatch(actions.saveApplicationService(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  saveDocuments: (data) => dispatch(actions.saveDocuments(data)),
  fecthAllDocumentTypes: (data) => dispatch(commonActions.fecthAllDocumentTypes(data)),
  saveMandatoryDocuments: (data) => dispatch(actions.saveMandatoryDocuments(data)),
  deleteDocuments: (data) => dispatch(commonActions.deleteDocuments(data)),
  updateDocuments: (data) => dispatch(actions.updateDocuments(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Services);
