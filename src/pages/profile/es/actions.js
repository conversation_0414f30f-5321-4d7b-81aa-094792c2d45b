import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  SAVE_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE`,
  SAVE_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_REQUEST`,
  SAVE_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_SUCCESS`,
  SAVE_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_FAILURE`,

  E_SIGN_SEND_FOR_APPROVAL: `${STATE_REDUCER_KEY}/E_SIGN_SEND_FOR_APPROVAL`,
  E_SIGN_SEND_FOR_APPROVAL_REQUEST: `${STATE_REDUCER_KEY}/E_SIGN_SEND_FOR_APPROVAL_REQUEST`,
  E_SIGN_SEND_FOR_APPROVAL_SUCCESS: `${STATE_REDUCER_KEY}/E_SIGN_SEND_FOR_APPROVAL_SUCCESS`,
  E_SIGN_SEND_FOR_APPROVAL_FAILURE: `${STATE_REDUCER_KEY}/E_SIGN_SEND_FOR_APPROVAL_FAILURE`,

  E_SIGN_APPROVE_REQUEST: `${STATE_REDUCER_KEY}/E_SIGN_APPROVE_REQUEST`,
  E_SIGN_APPROVE_REQUEST_REQUEST: `${STATE_REDUCER_KEY}/E_SIGN_APPROVE_REQUEST_REQUEST`,
  E_SIGN_APPROVE_REQUEST_SUCCESS: `${STATE_REDUCER_KEY}/E_SIGN_APPROVE_REQUEST_SUCCESS`,
  E_SIGN_APPROVE_REQUEST_FAILURE: `${STATE_REDUCER_KEY}/E_SIGN_APPROVE_REQUEST_FAILURE`,

  FETCH_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE`,
  FETCH_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE_REQUEST`,
  FETCH_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE_SUCCESS`,
  FETCH_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE_FAILURE`,

  FETCH_SERVICE_BY_SERVICE_CODE: `${STATE_REDUCER_KEY}/FETCH_SUB_BY_SERVICE_CODE_MODULES`,
  FETCH_SERVICE_BY_SERVICE_CODE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_BY_SERVICE_CODE_REQUEST`,
  FETCH_SERVICE_BY_SERVICE_CODE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_CODE_SERVICE_SUCCESS`,
  FETCH_SERVICE_BY_SERVICE_CODE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_BY_SERVICE_CODE_FAILURE`,

  FETCH_SUB_MODULES_BY_MODULE_ID: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID`,
  FETCH_SUB_MODULES_BY_MODULE_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID_REQUEST`,
  FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS`,
  FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE`,

  SAVE_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS`,
  SAVE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_REQUEST`,
  SAVE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_SUCCESS`,
  SAVE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_FAILURE`,

  SAVE_MANDATORY_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS`,
  SAVE_MANDATORY_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_REQUEST`,
  SAVE_MANDATORY_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_SUCCESS`,
  SAVE_MANDATORY_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_FAILURE`,

  FETCH_DOCUMENT_TYPES: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES`,
  FETCH_DOCUMENT_TYPES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES_REQUEST`,
  FETCH_DOCUMENT_TYPES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES_SUCCESS`,
  FETCH_DOCUMENT_TYPES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES_FAILURE`,

  SAVE_GENERAL_DETAILS: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS`,
  SAVE_GENERAL_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS_REQUEST`,
  SAVE_GENERAL_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS_SUCCESS`,
  SAVE_GENERAL_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS_FAILURE`,

  COMPLETE_SAVE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE`,
  COMPLETE_SAVE_REQUEST: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_REQUEST`,
  COMPLETE_SAVE_SUCCESS: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_SUCCESS`,
  COMPLETE_SAVE_FAILURE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_FAILURE`,

  DELETE_APPLICANT: `${STATE_REDUCER_KEY}/DELETE_APPLICANT`,
  DELETE_APPLICANT_REQUEST: `${STATE_REDUCER_KEY}/DELETE_APPLICANT_REQUEST`,
  DELETE_APPLICANT_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_APPLICANT_SUCCESS`,
  DELETE_APPLICANT_FAILURE: `${STATE_REDUCER_KEY}/DELETE_APPLICANT_FAILURE`,

  GENERATE_DEMANT: `${STATE_REDUCER_KEY}/GENERATE_DEMANT`,
  GENERATE_DEMANT_REQUEST: `${STATE_REDUCER_KEY}/GENERATE_DEMANT_REQUEST`,
  GENERATE_DEMANT_SUCCESS: `${STATE_REDUCER_KEY}/GENERATE_DEMANT_SUCCESS`,
  GENERATE_DEMANT_FAILURE: `${STATE_REDUCER_KEY}/GENERATE_DEMANT_FAILURE`,

  UPDATE_DOCUMENTS: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS`,
  UPDATE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS_REQUEST`,
  UPDATE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS_SUCCESS`,
  UPDATE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS_FAILURE`

};

export const saveApplicationService = createAction(ACTION_TYPES.SAVE_APPLICATION_SERVICE);
export const eSignSendForApprovalAction = createAction(ACTION_TYPES.E_SIGN_SEND_FOR_APPROVAL);
export const eSignApproveRequestAction = createAction(ACTION_TYPES.E_SIGN_APPROVE_REQUEST);
export const fetchApplicationService = createAction(ACTION_TYPES.FETCH_APPLICATION_SERVICE);
export const fetchServiceByServiceCode = createAction(ACTION_TYPES.FETCH_SERVICE_BY_SERVICE_CODE);
export const fetchSubModuleByModuleId = createAction(ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID);
export const saveDocuments = createAction(ACTION_TYPES.SAVE_DOCUMENTS);
export const saveMandatoryDocuments = createAction(ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS);
export const getDocumentTypes = createAction(ACTION_TYPES.FETCH_DOCUMENT_TYPES);
export const saveGeneralDetails = createAction(ACTION_TYPES.SAVE_GENERAL_DETAILS);
export const saveComplete = createAction(ACTION_TYPES.COMPLETE_SAVE);
export const deleteApplicant = createAction(ACTION_TYPES.DELETE_APPLICANT);
export const generateDemand = createAction(ACTION_TYPES.GENERATE_DEMANT);
export const updateDocuments = createAction(ACTION_TYPES.UPDATE_DOCUMENTS);
