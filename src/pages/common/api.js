import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchSmartProfile = (params) => {
  return {
    url: API_URL.COMMON.KSMART_SEARCH,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SMART_PROFILE_REQUEST,
        ACTION_TYPES.FETCH_SMART_PROFILE_SUCCESS,
        ACTION_TYPES.FETCH_SMART_PROFILE_FAILURE
      ],
      params
    }
  };
};
export const fetchUserProfileApi = () => {
  return {
    url: API_URL.COMMON.USER_PROFILE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_USER_PROFILE_REQUEST,
        ACTION_TYPES.FETCH_USER_PROFILE_SUCCESS,
        ACTION_TYPES.FETCH_USER_PROFILE_FAILURE
      ]
    }
  };
};
export const fetchCountryApi = () => {
  return {
    url: API_URL.COMMON.COUNTRY,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_COUNTRY_REQUEST,
        ACTION_TYPES.FETCH_COUNTRY_SUCCESS,
        ACTION_TYPES.FETCH_COUNTRY_FAILURE
      ]
    }
  };
};
export const fetchCountryById = () => {
  return {
    url: API_URL.COMMON.COUNTRY_BY_ID,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_COUNTRY_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_COUNTRY_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_COUNTRY_BY_ID_FAILURE
      ]
    }
  };
};

export const fetchStateApi = () => {
  return {
    url: API_URL.COMMON.STATE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_STATE_REQUEST,
        ACTION_TYPES.FETCH_STATE_SUCCESS,
        ACTION_TYPES.FETCH_STATE_FAILURE
      ]
    }
  };
};

export const fetchDistrictsApi = (stateCode) => {
  return {
    url: API_URL.COMMON.DISTRICT.replace(':stateId', stateCode),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DISTRICTS_REQUEST,
        ACTION_TYPES.FETCH_DISTRICTS_SUCCESS,
        ACTION_TYPES.FETCH_DISTRICTS_FAILURE
      ]
    }
  };
};

export const fetchDepartmentApi = () => {
  return {
    url: API_URL.COMMON.FETCH_FUNCTIONAL_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DEPARTMENTS_REQUEST,
        ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS,
        ACTION_TYPES.FETCH_DEPARTMENTS_FAILURE
      ]
    }
  };
};

// common for counter module

export const fetchServices = (data) => {
  const { serviceType, inputTemplateType } = data;
  return {
    url: API_URL.COMMON.SERVICES.replace(':serviceType', serviceType).replace(':inputTemplateType', inputTemplateType),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICES_REQUEST,
        ACTION_TYPES.FETCH_SERVICES_SUCCESS,
        ACTION_TYPES.FETCH_SERVICES_FAILURE
      ]
    }
  };
};

export const fetchModules = (data) => {
  return {
    url: API_URL.COMMON.MODULES.replace(':serviceId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MODULES_REQUEST,
        ACTION_TYPES.FETCH_MODULES_SUCCESS,
        ACTION_TYPES.FETCH_MODULES_FAILURE
      ]
    },
    data
  };
};

export const fetchSubModules = (data) => {
  return {
    url: API_URL.COMMON.SUB_MODULES.replace(':moduleId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUB_MODULES_REQUEST,
        ACTION_TYPES.FETCH_SUB_MODULES_SUCCESS,
        ACTION_TYPES.FETCH_SUB_MODULES_FAILURE
      ]
    },
    data
  };
};

export const fetchFileType = (data) => {
  return {
    url: API_URL.COMMON.FILETYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILETYPE_REQUEST,
        ACTION_TYPES.FETCH_FILETYPE_SUCCESS,
        ACTION_TYPES.FETCH_FILETYPE_FAILURE
      ]
    },
    data
  };
};

export const fetchWard = (data) => {
  return {
    url: API_URL.COMMON.WARD.replace(':officeCode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_WARD_REQUEST,
        ACTION_TYPES.FETCH_WARD_SUCCESS,
        ACTION_TYPES.FETCH_WARD_FAILURE
      ]
    },
    data
  };
};

export const fetchWardYear = (data) => {
  return {
    url: API_URL.COMMON.WARD_YEAR.replace(':officeCode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_WARD_YEAR_REQUEST,
        ACTION_TYPES.FETCH_WARD_YEAR_SUCCESS,
        ACTION_TYPES.FETCH_WARD_YEAR_FAILURE
      ]
    },
    data
  };
};

export const fetchInstitutionTypesApi = (data) => {
  return {
    url: API_URL.COMMON.INSTITUTION_TYPE.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INSTITUTION_TYPE_REQUEST,
        ACTION_TYPES.FETCH_INSTITUTION_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_INSTITUTION_TYPE_FAILURE
      ]
    }
  };
};

export const fetchLocalBodyApi = ({ districtId, officeTypeId }) => {
  return {
    url: API_URL.COMMON.LOCAL_BODIES.replace(':districtId', districtId).replace(':officeTypeId', officeTypeId),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LOCAL_BODY_REQUEST,
        ACTION_TYPES.FETCH_LOCAL_BODY_SUCCESS,
        ACTION_TYPES.FETCH_LOCAL_BODY_FAILURE
      ]
    }
  };
};

export const fetchInstitutionsApi = (data) => {
  return {
    url: API_URL.COMMON.INSTITUTIONS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INSTITUTIONS_REQUEST,
        ACTION_TYPES.FETCH_INSTITUTIONS_SUCCESS,
        ACTION_TYPES.FETCH_INSTITUTIONS_FAILURE
      ],
      data
    }
  };
};

export const fetchPostOffices = ({ offLbCode }) => {
  return {
    url: API_URL.COMMON.POST_OFFICE.replace(':offLbCode', offLbCode),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_OFFICE_REQUEST,
        ACTION_TYPES.FETCH_POST_OFFICE_SUCCESS,
        ACTION_TYPES.FETCH_POST_OFFICE_FAILURE
      ]
    }
  };
};

export const fetchPostOfficesByDId = ({ districtId }) => {
  return {
    url: API_URL.COMMON.POST_OFFICE_BY_D_ID.replace(':dId', districtId),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_OFFICE_REQUEST,
        ACTION_TYPES.FETCH_POST_OFFICE_SUCCESS,
        ACTION_TYPES.FETCH_POST_OFFICE_FAILURE
      ]
    }
  };
};

export const fetchPostOfficeByPin = (data) => {
  return {
    url: API_URL.COMMON.POST_OFFICE_BY_PIN.replace(':pincode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_OFFICE_BY_PIN_REQUEST,
        ACTION_TYPES.FETCH_POST_OFFICE_BY_PIN_SUCCESS,
        ACTION_TYPES.FETCH_POST_OFFICE_BY_PIN_FAILURE
      ]
    }
  };
};

export const fetchFiles = (data) => {
  return {
    url: API_URL.COMMON.FETCH_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILES_REQUEST,
        ACTION_TYPES.FETCH_FILES_SUCCESS,
        ACTION_TYPES.FETCH_FILES_FAILURE
      ],
      data
    }
  };
};

export const fetchFileYear = (data) => {
  return {
    url: API_URL.COMMON.FILEYEAR,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILEYEAR_REQUEST,
        ACTION_TYPES.FETCH_FILEYEAR_SUCCESS,
        ACTION_TYPES.FETCH_FILEYEAR_FAILURE
      ]
    },
    data
  };
};

export const fetchCorrespondType = (data) => {
  return {
    url: API_URL.COMMON.CORRESPONDTYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_CORRESPONDTYPE_REQUEST,
        ACTION_TYPES.FETCH_CORRESPONDTYPE_SUCCESS,
        ACTION_TYPES.FETCH_CORRESPONDTYPE_FAILURE
      ]
    },
    data
  };
};

export const fetchSeat = (data) => {
  return {
    url: API_URL.COMMON.SEAT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SEAT_REQUEST,
        ACTION_TYPES.FETCH_SEAT_SUCCESS,
        ACTION_TYPES.FETCH_SEAT_FAILURE
      ]
    },
    data
  };
};

export const fetchService = () => {
  return {
    url: API_URL.COMMON.SERVICE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_FAILURE
      ]
    }
  };
};

export const fetchBanks = () => {
  return {
    url: API_URL.COMMON.BANKS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BANKS_REQUEST,
        ACTION_TYPES.FETCH_BANKS_SUCCESS,
        ACTION_TYPES.FETCH_BANKS_FAILURE
      ]
    }
  };
};

export const fetchBranchByBank = (data) => {
  return {
    url: API_URL.COMMON.BANK_BRANCH,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BRANCHES_BY_BANK_REQUEST,
        ACTION_TYPES.FETCH_BRANCHES_BY_BANK_SUCCESS,
        ACTION_TYPES.FETCH_BRANCHES_BY_BANK_FAILURE
      ],
      data
    }
  };
};

export const fetchEducation = () => {
  return {
    url: API_URL.COMMON.EDUCATION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_EDUCATION_REQUEST,
        ACTION_TYPES.FETCH_EDUCATION_SUCCESS,
        ACTION_TYPES.FETCH_EDUCATION_FAILURE
      ]
    }
  };
};

export const fetchGender = () => {
  return {
    url: API_URL.COMMON.GENDER,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_GENDER_REQUEST,
        ACTION_TYPES.FETCH_GENDER_SUCCESS,
        ACTION_TYPES.FETCH_GENDER_FAILURE
      ]
    }
  };
};

export const deleteDocuments = (data) => {
  return {
    url: API_URL.COMMON.DELETE_DOCUMENTS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_DOCUMENTS_REQUEST,
        ACTION_TYPES.DELETE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.DELETE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};

export const fetchSubModuleById = (data) => {
  return {
    url: API_URL.COMMON.SUB_MODULES_BY_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchServicesById = (data) => {
  const { module, subModule } = data;
  return {
    url: API_URL.COMMON.SERVICES_BY_ID.replace(':moduleId', module).replace(':subModuleId', subModule),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_SERVICES_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchDepartments = (data) => {
  const {
    code
  } = data;

  const paramData = `?X-STATE-CODE=${code}`;
  return {
    url: API_URL.COMMON.FETCH_DEPARTMENTS.replace('?query', paramData),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DEPARTMENTS_REQUEST,
        ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS,
        ACTION_TYPES.FETCH_DEPARTMENTS_FAILURE
      ]
    },
    data
  };
};

export const fetchSeats = (params) => {
  return {
    url: API_URL.COMMON.FETCH_SEATS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SEATS_REQUEST,
        ACTION_TYPES.FETCH_SEATS_SUCCESS,
        ACTION_TYPES.FETCH_SEATS_FAILURE
      ],
      params
    }
  };
};

export const fetchStatus = (data) => {
  const {
    code
  } = data;

  const paramData = `?X-STATE-CODE=${code}`;
  return {
    url: API_URL.COMMON.FETCH_STATUS.replace('?query', paramData),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_STATUS_REQUEST,
        ACTION_TYPES.FETCH_STATUS_SUCCESS,
        ACTION_TYPES.FETCH_STATUS_FAILURE
      ]
    },
    data
  };
};

export const fetchCounterOperator = (data) => {
  return {
    url: API_URL.COMMON.FETCH_FUNCTIONAL_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_COUNTER_OPERATOR_REQUEST,
        ACTION_TYPES.FETCH_COUNTER_OPERATOR_SUCCESS,
        ACTION_TYPES.FETCH_COUNTER_OPERATOR_FAILURE
      ]
    },
    data
  };
};

export const fetchDepartmentsForFilter = (data) => {
  const {
    code
  } = data;

  const paramData = `?X-STATE-CODE=${code}`;

  return {
    url: API_URL.COMMON.FETCH_DEPARTMENTS.replace('?query', paramData),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER_REQUEST,
        ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER_SUCCESS,
        ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER_FAILURE
      ]
    },
    data
  };
};

export const fetchLocalBodyPropertyType = (data) => {
  return {
    url: API_URL.COMMON.FETCH_LOCAL_BODY_PROPERTY_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LOCAL_BODY_PROPERTY_TYPE_REQUEST,
        ACTION_TYPES.FETCH_LOCAL_BODY_PROPERTY_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_LOCAL_BODY_PROPERTY_TYPE_FAILURE
      ]
    },
    data
  };
};

export const fetchFunctionalGroup = (data) => {
  return {
    url: API_URL.COMMON.FETCH_FUNCTIONAL_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_REQUEST,
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_SUCCESS,
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_FAILURE
      ]
    },
    data
  };
};

export const fetchFunctions = (data) => {
  return {
    url: API_URL.COMMON.FETCH_FUNCTIONS.replace(':groupId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FUNCTIONS_REQUEST,
        ACTION_TYPES.FETCH_FUNCTIONS_SUCCESS,
        ACTION_TYPES.FETCH_FUNCTIONS_FAILURE
      ]
    }
  };
};

export const fetchLocalBodyType = (data) => {
  return {
    url: API_URL.COMMON.LOCAL_BODY_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LOCAL_BODY_TYPE_REQUEST,
        ACTION_TYPES.FETCH_LOCAL_BODY_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_LOCAL_BODY_TYPE_FAILURE
      ]
    },
    data
  };
};

export const fetchLocalBodyByDistrictByType = ({ districtId, lbTypeId }) => {
  return {
    url: API_URL.COMMON.LOCAL_BODY_BY_D_BY_LBT.replace(':districtId', districtId).replace(':lbTypeId', lbTypeId),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LOCAL_BODY_NAME_REQUEST,
        ACTION_TYPES.FETCH_LOCAL_BODY_NAME_SUCCESS,
        ACTION_TYPES.FETCH_LOCAL_BODY_NAME_FAILURE
      ]
    }
  };
};

export const fetchLocalBodyByDistrictByTypeApplicant = ({ districtId, lbTypeId }) => {
  return {
    url: API_URL.COMMON.LOCAL_BODY_BY_D_BY_LBT.replace(':districtId', districtId).replace(':lbTypeId', lbTypeId),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LOCAL_BODY_NAME_APPLICANT_REQUEST,
        ACTION_TYPES.FETCH_LOCAL_BODY_NAME_APPLICANT_SUCCESS,
        ACTION_TYPES.FETCH_LOCAL_BODY_NAME_APPLICANT_FAILURE
      ]
    }
  };
};

export const checkFileNo = (data) => {
  return {
    url: API_URL.COMMON.CHECK_FILE_NO.replace(':fileNo', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.CHECK_FILE_NO_REQUEST,
        ACTION_TYPES.CHECK_FILE_NO_SUCCESS,
        ACTION_TYPES.CHECK_FILE_NO_FAILURE
      ]
    },
    data
  };
};

export const fetchFinancialStatus = (data) => {
  return {
    url: API_URL.COMMON.FETCH_FINANCIAL_STATUS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FINANCIAL_STATUS_REQUEST,
        ACTION_TYPES.FETCH_FINANCIAL_STATUS_SUCCESS,
        ACTION_TYPES.FETCH_FINANCIAL_STATUS_FAILURE
      ]
    },
    data
  };
};
export const fetchSubModulesByModuleId = (data) => {
  return {
    url: API_URL.COMMON.SUB_MODULES_BY_MODULE_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_REQUEST,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchCategory = (data) => {
  return {
    url: API_URL.COMMON.FETCH_CATEGORY,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_CATEGORY_REQUEST,
        ACTION_TYPES.FETCH_CATEGORY_SUCCESS,
        ACTION_TYPES.FETCH_CATEGORY_FAILURE
      ]
    },
    data
  };
};

export const fetchBuildingUsage = (data) => {
  return {
    url: API_URL.COMMON.FETCH_BUILDING_USAGE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BUILDING_USAGE_REQUEST,
        ACTION_TYPES.FETCH_BUILDING_USAGE_SUCCESS,
        ACTION_TYPES.FETCH_BUILDING_USAGE_FAILURE
      ]
    },
    data
  };
};

export const fetchOwnership = (data) => {
  return {
    url: API_URL.COMMON.FETCH_OWNERSHIP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_OWNERSHIP_REQUEST,
        ACTION_TYPES.FETCH_OWNERSHIP_SUCCESS,
        ACTION_TYPES.FETCH_OWNERSHIP_FAILURE
      ]
    },
    data
  };
};
export const fetchEmployeeNameById = ({ params }) => {
  return {
    url: API_URL.COMMON.FETCH_SEATS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID_FAILURE
      ],
      params
    }
  };
};

export const fetchLocalBodyByOfficeCode = (data) => {
  return {
    url: API_URL.COMMON.LOCAL_BODY_BY_OFFICE_CODE.replace(':officeId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LOCAL_BODY_BY_OFFICE_CODE_REQUEST,
        ACTION_TYPES.FETCH_LOCAL_BODY_BY_OFFICE_CODE_SUCCESS,
        ACTION_TYPES.FETCH_LOCAL_BODY_BY_OFFICE_CODE_FAILURE
      ]
    },
    data
  };
};

export const fetchServiceValidation = (data) => {
  return {
    url: API_URL.COMMON.FETCH_SERVICE_VALIDATION.replace(':serviceCode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_VALIDATION_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_VALIDATION_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_VALIDATION_FAILURE
      ]
    },
    data
  };
};

export const fetchDesignation = (params) => {
  return {
    url: API_URL.COMMON.FETCH_DESIGNATION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DESIGNATION_REQUEST,
        ACTION_TYPES.FETCH_DESIGNATION_SUCCESS,
        ACTION_TYPES.FETCH_DESIGNATION_FAILURE
      ],
      params
    }
  };
};

export const fetchRouteKey = () => {
  return {
    url: API_URL.COMMON.ROUTE_KEYS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ROUTE_KEY_REQUEST,
        ACTION_TYPES.FETCH_ROUTE_KEY_SUCCESS,
        ACTION_TYPES.FETCH_ROUTE_KEY_FAILURE
      ]
    }
  };
};

export const fetchServiceAccountHead = (data) => {
  return {
    url: API_URL.COMMON.FETCH_SERVICE_ACCOUNT_HEAD.replace(':serviceCode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_ACCOUNT_HEAD_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_ACCOUNT_HEAD_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_ACCOUNT_HEAD_FAILURE
      ]
    }
  };
};

export const generateAadharOtp = (data) => {
  return {
    url: API_URL.COMMON.GENERATE_AADHAR_OTP,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.GEN_OTP_REQUEST,
        ACTION_TYPES.GEN_OTP_SUCCESS,
        ACTION_TYPES.GEN_OTP_FAILURE
      ],
      data
    }
  };
};

export const verifyAadharOtp = (data) => {
  return {
    url: API_URL.COMMON.VALIDATE_AADHAR_OTP,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.VERIFY_OTP_REQUEST,
        ACTION_TYPES.VERIFY_OTP_SUCCESS,
        ACTION_TYPES.VERIFY_OTP_FAILURE
      ],
      data
    }
  };
};

export const fetchDfmsServices = (data) => {
  return {
    url: API_URL.COMMON.DFMS_SERVICES.replace('query', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DFMS_SERVICES_REQUEST,
        ACTION_TYPES.FETCH_DFMS_SERVICES_SUCCESS,
        ACTION_TYPES.FETCH_DFMS_SERVICES_FAILURE
      ]
    }
  };
};

export const deleteNoteDocuments = (data) => {
  const { notesId, noteDocumentId } = data;
  return {
    url: API_URL.COMMON.DELETE_NOTE_DOCUMENTS.replace(':notesId', notesId).replace(':noteDocumentId', noteDocumentId),
    method: REQUEST_METHOD.DELETE,
    payload: {
      types: [
        ACTION_TYPES.DELETE_NOTE_DOCUMENTS_REQUEST,
        ACTION_TYPES.DELETE_NOTE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.DELETE_NOTE_DOCUMENTS_FAILURE
      ]
    }
  };
};

export const fetchFunctionalGroups = (data) => {
  return {
    url: API_URL.COMMON.FUNCTIONAL_GROUP.replace('officeId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUPS_REQUEST,
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUPS_SUCCESS,
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUPS_FAILURE
      ]
    },
    data
  };
};

export const fetchTalukOfficeCode = (data) => {
  return {
    url: API_URL.COMMON.FETCH_TALUK_OFFICECODE.replace(':officeCode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_TALUK_OFFICECODE_REQUEST,
        ACTION_TYPES.FETCH_TALUK_OFFICECODE_SUCCESS,
        ACTION_TYPES.FETCH_TALUK_OFFICECODE_FAILURE
      ]
    }
  };
};

export const fetchVillage = (data) => {
  return {
    url: API_URL.COMMON.FETCH_VILLAGE.replace(':officeCode', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_VILLAGE_REQUEST,
        ACTION_TYPES.FETCH_VILLAGE_SUCCESS,
        ACTION_TYPES.FETCH_VILLAGE_FAILURE
      ]
    }
  };
};

export const fetchDoorKey = (data) => {
  return {
    url: API_URL.COMMON.FETCH_DOOR_KEY.replace(':doorValue', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DOOR_KEY_REQUEST,
        ACTION_TYPES.FETCH_DOOR_KEY_SUCCESS,
        ACTION_TYPES.FETCH_DOOR_KEY_FAILURE
      ]
    }
  };
};

export const fetchDoor = () => {
  return {
    url: API_URL.COMMON.FETCH_DOOR_NUMBER,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DOOR_REQUEST,
        ACTION_TYPES.FETCH_DOOR_SUCCESS,
        ACTION_TYPES.FETCH_DOOR_FAILURE
      ]
    }
  };
};

export const fetchCounterOperatorWithRole = (params) => {
  const data = [
    {
      routeKey2: '',
      routeKeyValue: ''
    }
  ];
  return {
    url: API_URL.COMMON.FETCH_COUNTER_OPERATOR_WITH_ROLE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE_REQUEST,
        ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE_SUCCESS,
        ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE_FAILURE
      ],
      params,
      data
    }
  };
};

export const fetchBillType = () => {
  return {
    url: API_URL.COMMON.FETCH_BILL_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BILL_TYPE_REQUEST,
        ACTION_TYPES.FETCH_BILL_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_BILL_TYPE_FAILURE
      ]
    }
  };
};

export const fetchEstablishmentType = () => {
  return {
    url: API_URL.COMMON.FETCH_ESTABLISHMENT_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ESTABLISHMENT_TYPE_REQUEST,
        ACTION_TYPES.FETCH_ESTABLISHMENT_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_ESTABLISHMENT_TYPE_FAILURE
      ]
    }
  };
};

export const fetchMission = () => {
  return {
    url: API_URL.COMMON.FETCH_MISSION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MISSION_REQUEST,
        ACTION_TYPES.FETCH_MISSION_SUCCESS,
        ACTION_TYPES.FETCH_MISSION_FAILURE
      ]
    }
  };
};

export const fetchProfessionalTaxType = () => {
  return {
    url: API_URL.COMMON.FETCH_PROFESSION_TAX_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PROFESSION_TAX_TYPE_REQUEST,
        ACTION_TYPES.FETCH_PROFESSION_TAX_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_PROFESSION_TAX_TYPE_FAILURE
      ]
    }
  };
};

export const fetchTypeofAudit = () => {
  return {
    url: API_URL.COMMON.FETCH_TYPE_OF_AUDIT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_TYPE_OF_AUDIT_REQUEST,
        ACTION_TYPES.FETCH_TYPE_OF_AUDIT_SUCCESS,
        ACTION_TYPES.FETCH_TYPE_OF_AUDIT_FAILURE
      ]
    }
  };
};

export const fetchLbBuilding = () => {
  return {
    url: API_URL.COMMON.FETCH_LOCAL_BODY_PROPERTY_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LB_BUILDING_REQUEST,
        ACTION_TYPES.FETCH_LB_BUILDING_SUCCESS,
        ACTION_TYPES.FETCH_LB_BUILDING_FAILURE
      ]
    }
  };
};

export const fetchAmountFromClaim = () => {
  return {
    url: API_URL.COMMON.FETCH_AMOUNT_FROM_CLAIM,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_AMOUNT_FROM_CLAIM_REQUEST,
        ACTION_TYPES.FETCH_AMOUNT_FROM_CLAIM_SUCCESS,
        ACTION_TYPES.FETCH_AMOUNT_FROM_CLAIM_REQUEST
      ]
    }
  };
};

export const fetchOccupancy = () => {
  return {
    url: API_URL.COMMON.FETCH_OCCUPANCY,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_OCCUPANCY_REQUEST,
        ACTION_TYPES.FETCH_OCCUPANCY_SUCCESS,
        ACTION_TYPES.FETCH_OCCUPANCY_FAILURE
      ]
    }
  };
};

export const fetchEstimateAmount = () => {
  return {
    url: API_URL.COMMON.FETCH_ESTIMATE_AMOUNT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ESTIMATE_AMOUNT_REQUEST,
        ACTION_TYPES.FETCH_ESTIMATE_AMOUNT_SUCCESS,
        ACTION_TYPES.FETCH_ESTIMATE_AMOUNT_FAILURE
      ]
    }
  };
};

export const fetchBuildUpArea = () => {
  return {
    url: API_URL.COMMON.FETCH_BUILDUP_AREA,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BUILDUP_AREA_REQUEST,
        ACTION_TYPES.FETCH_BUILDUP_AREA_SUCCESS,
        ACTION_TYPES.FETCH_BUILDUP_AREA_FAILURE
      ]
    }
  };
};

export const fetchMeetingType = () => {
  return {
    url: API_URL.COMMON.FETCH_MEETING_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MEETING_TYPE_REQUEST,
        ACTION_TYPES.FETCH_MEETING_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_MEETING_TYPE_FAILURE
      ]
    }
  };
};

export const fetchOfficeType = (params) => {
  return {
    url: API_URL.COMMON.FETCH_OFFICE_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_OFFICE_TYPE_REQUEST,
        ACTION_TYPES.FETCH_OFFICE_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_OFFICE_TYPE_FAILURE
      ],
      params
    }
  };
};

export const fetchFund = () => {
  return {
    url: API_URL.COMMON.FETCH_FUND,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FUND_REQUEST,
        ACTION_TYPES.FETCH_FUND_SUCCESS,
        ACTION_TYPES.FETCH_FUND_FAILURE
      ]
    }
  };
};

export const fetchModuleById = (data) => {
  return {
    url: API_URL.COMMON.MODULES_BY_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MODULES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_MODULES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_MODULES_BY_ID_FAILURE
      ],
      data
    }
  };
};

export const fetchVerification = (data) => {
  return {
    url: API_URL.DASHBOARD.VERIFICATION,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_VERIFICATION_REQUEST,
        ACTION_TYPES.FETCH_VERIFICATION_SUCCESS,
        ACTION_TYPES.FETCH_VERIFICATION_FAILURE
      ],
      data
    }

  };
};

export const loginCitizen = (data) => {
  return {
    url: API_URL.DASHBOARD.LOGIN_CITIZEN,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.LOGIN_CITIZEN_REQUEST,
        ACTION_TYPES.LOGIN_CITIZEN_SUCCESS,
        ACTION_TYPES.LOGIN_CITIZEN_FAILURE
      ],
      data
    }

  };
};

export const sendOtp = (params) => {
  return {
    url: API_URL.DASHBOARD.SEND_OTP,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.LOGIN_CITIZEN_REQUEST,
        ACTION_TYPES.LOGIN_CITIZEN_SUCCESS,
        ACTION_TYPES.LOGIN_CITIZEN_FAILURE
      ],
      params
    }

  };
};

export const fetchAccountType = (params) => {
  return {
    url: API_URL.COMMON.ACCOUNT_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ACCOUNT_TYPE_REQUEST,
        ACTION_TYPES.FETCH_ACCOUNT_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_ACCOUNT_TYPE_FAILURE
      ],
      params
    }
  };
};

export const fetchTreasuryType = (params) => {
  return {
    url: API_URL.COMMON.TREASURY_TYPE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_TREASURY_TYPE_REQUEST,
        ACTION_TYPES.FETCH_TREASURY_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_TREASURY_TYPE_FAILURE
      ],
      params
    }
  };
};

export const fetchRecoveryAccountHead = () => {
  return {
    url: API_URL.COMMON.FETCH_RECOVERY_ACCOUNT_HEAD,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_RECOVERY_ACCOUNT_HEAD_REQUEST,
        ACTION_TYPES.FETCH_RECOVERY_ACCOUNT_HEAD_SUCCESS,
        ACTION_TYPES.FETCH_RECOVERY_ACCOUNT_HEAD_FAILURE
      ]
    }
  };
};

export const fetchPostsByFunctionalGroups = (payload) => {
  const { data, officeId } = payload;
  return {
    url: API_URL.COMMON.FETCH_POST_BY_FUNC_GROUPS.replace(':officeId', officeId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_FUNC_GROUPS_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_FUNC_GROUPS_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_FUNC_GROUPS_FAILURE
      ],
      data
    }
  };
};

export const fetchHeight = () => {
  return {
    url: API_URL.COMMON.FETCH_HEIGHT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_HEIGHT_REQUEST,
        ACTION_TYPES.FETCH_HEIGHT_SUCCESS,
        ACTION_TYPES.FETCH_HEIGHT_FAILURE
      ]
    }
  };
};

export const fetchInwardUsers = (data) => {
  return {
    url: API_URL.COMMON.FETCH_INWARD_USERS.replace(':inwardId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INWARD_USERS_REQUEST,
        ACTION_TYPES.FETCH_INWARD_USERS_SUCCESS,
        ACTION_TYPES.FETCH_INWARD_USERS_FAILURE
      ]
    }
  };
};

export const fetchWasteManagementType = () => {
  return {
    url: API_URL.COMMON.FETCH_WASTE_MANAGEMENT_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_WASTE_MANAGEMENT_TYPE_REQUEST,
        ACTION_TYPES.FETCH_WASTE_MANAGEMENT_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_WASTE_MANAGEMENT_TYPE_FAILURE
      ]
    }
  };
};

export const fetchSignPdf = (data) => {
  return {
    url: API_URL.E_SIGN.SIGN_PDF,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SIGN_PDF_REQUEST,
        ACTION_TYPES.FETCH_SIGN_PDF_SUCCESS,
        ACTION_TYPES.FETCH_SIGN_PDF_FAILURE
      ],
      data
    }
  };
};

export const saveSignedDraft = (payload) => {
  const { blob, fileNo, draftId } = payload;
  const formData = new FormData();
  formData.append('eSignedDraft', blob);
  const data = formData;
  return {
    url: API_URL.E_SIGN.SAVE_SIGNED_DRAFT.replace(':fileNo', fileNo).replace(':draftId', draftId),
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_SIGNED_DRAFT_REQUEST,
        ACTION_TYPES.SAVE_SIGNED_DRAFT_SUCCESS,
        ACTION_TYPES.SAVE_SIGNED_DRAFT_FAILURE
      ],
      data
    }
  };
};

export const fetchAccountId = ({ params }) => {
  return {
    url: API_URL.COMMON.FETCH_ACCOUNT_ID.replace(':lookupType', params?.lookupType),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ACCOUNT_ID_REQUEST,
        ACTION_TYPES.FETCH_ACCOUNT_ID_SUCCESS,
        ACTION_TYPES.FETCH_ACCOUNT_ID_FAILURE
      ]
    }
  };
};

export const fetchPostIdByPenNo = (params) => {
  return {
    url: API_URL.COMMON.FETCH_POST_ID_BY_PEN,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_ID_BY_PEN_NO_REQUEST,
        ACTION_TYPES.FETCH_POST_ID_BY_PEN_NO_SUCCESS,
        ACTION_TYPES.FETCH_POST_ID_BY_PEN_NO_FAILURE
      ],
      params
    }
  };
};

export const fetchModeOfDispatch = () => {
  return {
    url: API_URL.COMMON.FETCH_MODE_OF_DISPATCH,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MODE_OF_DISPATCH_REQUEST,
        ACTION_TYPES.FETCH_MODE_OF_DISPATCH_SUCCESS,
        ACTION_TYPES.FETCH_MODE_OF_DISPATCH_FAILURE
      ]
    }
  };
};

export const fetchDispatchClerkDetails = (params) => {
  return {
    url: API_URL.COMMON.FETCH_DISPATCH_CLERK_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DISPATCH_CLERK_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_DISPATCH_CLERK_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_DISPATCH_CLERK_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const fetchLsgiType = () => {
  return {
    url: API_URL.COMMON.FETCH_LSGI_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LSGI_TYPE_REQUEST,
        ACTION_TYPES.FETCH_LSGI_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_LSGI_TYPE_FAILURE
      ]
    }
  };
};

export const fetchRegionType = () => {
  return {
    url: API_URL.COMMON.FETCH_REGION_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_REGION_TYPE_REQUEST,
        ACTION_TYPES.FETCH_REGION_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_REGION_TYPE_FAILURE
      ]
    }
  };
};

export const fetchBuildingProjectType = () => {
  return {
    url: API_URL.COMMON.FETCH_BUILDING_PROJECT_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BUILDING_PROJECT_TYPE_REQUEST,
        ACTION_TYPES.FETCH_BUILDING_PROJECT_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_BUILDING_PROJECT_TYPE_FAILURE
      ]
    }
  };
};

export const fetchAllDocumentTypes = () => {
  return {
    url: API_URL.COMMON.ALL_DOCUMENTS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ALL_DOCUMENT_TYPES_REQUEST,
        ACTION_TYPES.FETCH_ALL_DOCUMENT_TYPES_SUCCESS,
        ACTION_TYPES.FETCH_ALL_DOCUMENT_TYPES_FAILURE
      ]
    }
  };
};
