import { Toast } from 'common/components';
import { DOCUMENT_TYPES } from 'common/constants';
import { downloadBlob } from 'utils/downloadBlob';

const { errorTost } = Toast;

const DocumentDownload = (url, token, setLoader = () => { }) => {
  try {
    fetch(url, {
      method: 'GET',
      headers: {
        Accept: DOCUMENT_TYPES.PDF,
        Authorization: `Bearer ${token}`
      }
    })
      .then((response) => response.arrayBuffer())
      .then((response) => {
        const arr = new Uint8Array(response);
        const blob = new Blob([arr], {
          type: DOCUMENT_TYPES.PDF
        });
        const urlBlob = window.URL.createObjectURL(blob);
        downloadBlob({ blob: urlBlob, fileName: 'KSUITE-FILE-LOGS.pdf' });
        setLoader(false);
      });
  } catch (error) {
    setLoader(false);
    errorTost({
      title: 'Error on Loading',
      description: 'Some issue happens on PDF load'
    });
  }
};

export default DocumentDownload;
