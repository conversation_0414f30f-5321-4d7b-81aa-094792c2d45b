import BgPlainCloseIcon from 'assets/BgPlainCloseIcon';
import {
  Button,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  t
} from 'common/components';

/**
 * Higher-Order Component (HOC) to wrap a component inside a modal.
 * @param {React.ComponentType} WrappedComponent - The component to be displayed inside the modal.
 * @returns {React.FC} - A new component wrapped with a modal.
 */
const withActionModal = (WrappedComponent) => {
  const WithActionModal = ({
    open = false,
    header = '',
    submitButtonLabel = 'save',
    submitButtonDisabled = false,
    modalBodyProps = {},
    showFooter = true,
    onClose = () => {},
    onSave = () => {},
    ...rest
  }) => (
    <Modal
      isOpen={open}
      autoFocus
      isCentered
      onCloseComplete={false}
      onClose={onClose}
    >
      <ModalOverlay />
      <ModalContent
        margin={10}
        as="form"
        p={5}
        minW="647px"
        onSubmit={(e) => {
          e?.preventDefault();
          onSave();
        }}
      >
        <ModalHeader p={0} className="flex items-center justify-between">
          <h4 className="text-[#09327B] text-[16px] font-extrabold">
            {t(header)}
          </h4>
          <button type="button" onClick={onClose} aria-label="Close">
            <BgPlainCloseIcon />
          </button>
        </ModalHeader>

        <ModalBody p={0} mt={6} {...modalBodyProps}>
          <WrappedComponent {...rest} />
        </ModalBody>

        {showFooter && (
          <ModalFooter p={0} mb={2} mt={6} justifyContent="center" gap={2}>
            <Button
              type="button"
              variant="secondary_outline"
              py={2}
              minW={140}
              fontSize="sm"
              fontWeight={700}
              onClick={onClose}
            >
              {t('cancel')}
            </Button>
            <Button
              variant="secondary"
              py={2}
              minW={140}
              fontSize="sm"
              fontWeight={700}
              onClick={onSave}
              disabled={submitButtonDisabled}
            >
              {t(submitButtonLabel)}
            </Button>
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  );

  WithActionModal.displayName = `WithActionModal(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  return WithActionModal;
};

export default withActionModal;
