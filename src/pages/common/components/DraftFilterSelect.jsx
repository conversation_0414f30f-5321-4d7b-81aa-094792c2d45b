import FilterCupIcon from 'assets/FilterCupIcon';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';

const DraftFilterSelect = ({
  label = '',
  className = 'w-4.5/12',
  selected,
  startIcon = <FilterCupIcon />,
  options = [],
  onChange
}) => {
  return (
    <div className={className}>
      {label && (
        <span className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </span>
      )}
      <div className="relative">
        {startIcon && (
          <div className="absolute left-1 top-1/2 -translate-y-1/2 p-1.5 rounded-md z-10">
            {startIcon}
          </div>
        )}
        <select
          className={`appearance-none w-full bg-white border border-gray-200 rounded-lg pr-7 py-[7px] text-[#5C6E93] text-[14px] font-semibold focus:outline-none transition-all ${
            startIcon ? 'pl-9' : 'pl-3'
          }`}
          value={selected}
          onChange={onChange}
        >
          {options.map((option) => (
            <option key={option.id} value={option.id}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-1 flex items-center pr-1.5 pointer-events-none">
          <KeyboardDownArrow className="translate-x-0 transform(translate(20px))" />
        </div>
      </div>
    </div>
  );
};

export default DraftFilterSelect;
