import {
  TagLabel,
  Tag,
  TagCloseButton,
  TruncatedText
} from 'common/components';
import CloseNew from 'assets/CloseNew';
import { memo } from 'react';

const OutlineTag = ({
  text,
  startIcon,
  showRemoveButton = true,
  maxWidth = ['100%', '150px', '250px'],
  onTagClick = () => {},
  onTagRemove = () => {}
}) => {
  return (
    <Tag
      size="lg"
      bg="#fff"
      borderRadius="8px"
      className="relative max-w-max flex items-start border border-[#E7EFF5] cursor-pointer"
      onClick={(e) => {
        e.stopPropagation();
        onTagClick(e);
      }}
    >
      <div className="flex items-center gap-2">
        {startIcon && startIcon}
        <TagLabel className="text-[#454545] text-sm font-semibold">
          <TruncatedText cursor="pointer" text={text} maxWidth={maxWidth} />
        </TagLabel>
      </div>
      {showRemoveButton && (
        <TagCloseButton
          opacity={1}
          onClick={(e) => {
            e.stopPropagation();
            onTagRemove(e);
          }}
          className="absolute -top-2 -right-1"
        >
          <CloseNew fill="#F8CCDC" strokeWidth="3" stroke="#E83A7A" />
        </TagCloseButton>
      )}
    </Tag>
  );
};

export default memo(OutlineTag);
