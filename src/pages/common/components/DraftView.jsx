import { Button } from '@ksmartikm/ui-components';
import React from 'react';
import DraftIcons from 'assets/DraftIcons';
// import { useParams, useNavigate } from 'react-router-dom';
// import { BASE_PATH } from 'common/constants';
import DraftApprovedIcon from 'assets/DraftApprovedIcon';
import { getBackgroundColorForDrafts, getColorForDrafts } from '../helper';
import { DRAFT_STATUS } from '../constants';

const DraftView = ({
  draftDetails = [],
  setDraftItems = () => {},
  setOpen = () => {}
  // from = 'summary'
}) => {
  // const params = useParams();
  // const navigate = useNavigate();

  // const handleDraft = (val) => {
  //   navigate(`${BASE_PATH}/file/${params?.fileNo}/draft/${val?.draftId}?from=${from}`);
  // };

  return draftDetails?.map((item) => (
    <div className="mt-3" key={item?.draftNo}>
      <Button
        size="lg"
        style={{ background: getBackgroundColorForDrafts(item?.draftStage) }}
        onClick={(e) => {
          e.stopPropagation();
          const newDraftItems = { id: item?.draftId, ...item };
          setDraftItems(newDraftItems);
          setOpen(true);
        }}
        leftIcon={
          item?.draftStage === DRAFT_STATUS.APPROVED ? (
            <DraftApprovedIcon />
          ) : (
            <DraftIcons color={getColorForDrafts(item?.draftStage)} />
          )
        }
      >
        {' '}
        <span style={{ color: getColorForDrafts(item?.draftStage) }} className="text-[14px] font-medium">
          Draft {item?.draftNo}{' '}
        </span>
      </Button>
    </div>
  ));
};

export default DraftView;
