import {
  But<PERSON>, <PERSON><PERSON><PERSON>roller, FormWrapper, t
} from 'common/components';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useLocation } from 'react-router-dom';
import _ from 'lodash';
import {
  getFileExist,
  getFunctions,
  getLocalBodyPropertyType,
  getFunctionalGroup,
  getActionTriggered,
  getBuildingUsage,
  getOwnerShip,
  getWard,
  getVillage,
  getTaluk,
  getDesignation,
  getServiceValidation,
  getBillType,
  getEstablishmentType,
  getMission,
  getProfessionalTaxType,
  getTypeOfAudit,
  getLbBuilding,
  getAmountFromClaim,
  getOccupancy,
  getEstimateAmount,
  getBuildUpArea,
  getMeetingType,
  getOfficeType,
  getFund,
  getUserInfo,
  getRecoveryAccountHead,
  getHeight,
  getDoor,
  getWasteManagementType,
  getServicesDropdown,
  getLsgiType,
  getRegionType,
  getBuildingProjectType,
  getCountry,
  getState,
  getDistricts
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { useEffect, useState } from 'react';
import { FILE_NO } from 'common/regex';
import { removeDuplicates, validateFile, validateFileText } from 'utils/validateFile';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as fileActions from 'pages/file/details/actions';
import {
  CHILD_FILE, CUSTODIAN_CHANGE, IKM_FILE_ARISING, IKM_FILE_COUNTER, LEGACY_FILE
} from 'pages/file/details/constants';
import { getServicePostRoutes } from 'pages/file/details/selector';
import { DEFAULT_STATE } from 'common/constants';
import { RoutingKeySchema } from './validate';

const RoutingKeys = (props) => {
  const {
    serviceValidation,
    fileExist,
    checkFileNo,
    fetchFunctionalGroup,
    fetchFunctions,
    functions,
    functionalGroup,
    eFilePreview,
    setActionTriggered,
    actionTriggered,
    localBodyPropertyType,
    fetchBuildingUsage,
    buildingUsage,
    fetchOwnership,
    ownerShip,
    fetchVillage,
    fetchTalukOfficeCode,
    village,
    taluk,
    fetchLocalBodyPropertyType,
    // fetchServiceValidation,
    billType,
    establishmentType,
    mission,
    professionalTaxType,
    typeOfAudit,
    lbBuilding,
    amountFromClaim,
    fetchBillType,
    fetchEstablishmentType,
    fetchMission,
    fetchProfessionalTaxType,
    fetchTypeofAudit,
    fetchLbBuilding,
    fetchAmountFromClaim,
    designation,
    fetchDesignation,
    occupancy,
    fetchOccupancy,
    fetchEstimateAmount,
    estimateAmount,
    fetchBuildUpArea,
    buildUpArea,
    meetingType,
    officeType,
    fund,
    fetchMeetingType,
    fetchOfficeType,
    fetchFund,
    recoveryAccountHead,
    fetchRecoveryAccountHead,
    fetchHeight,
    height,

    fetchWasteManagementType,
    wasteManagementType,
    fetchServicesOptions,
    serviceDropdown,
    fetchServicePostRoutes,
    userInfo,
    // direct props
    routingSave = () => { },
    existingData = {},
    serviceInfo = {},
    activeIndex = false,
    isReturnToCitizen = false,
    officeCode = '',
    from = null,
    post = [],
    setRouteKeyObj = () => { },
    readOnly = [],
    handleFetchUser = () => { },
    servicePostRoutes,
    setServiceCodeDetails = () => { },
    serviceCodeDetails = '',
    fetchLsgiType,
    fetchRegionType,
    fetchBuildingProjectType,
    fetchStates,
    fetchDistricts,
    lsgiType,
    regionType,
    buildingProjectType,
    stateDropdown,
    districtDropdown
  } = props;

  const {
    handleSubmit,
    setValue,
    control,
    watch,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: {
      referenceNo: '',
      ownershipId: null,
      localBodyPropertyType: null,
      buildingUsage: null,
      buildingArea: null,
      functionalGroup: null,
      functions: null,
      ownerName: null,
      ksebPostNo: null,
      roadName: null,
      landMark: null,
      talukId: null,
      talukName: null,
      village: null,
      villageName: null,
      surveyNo: null,
      dateOfEvent: null,
      toDate: null,
      receiptNo: null,
      designation: null,
      billType: null,
      establishmentType: null,
      mission: null,
      professionalTaxType: null,
      typeOfAudit: null,
      lbBuilding: null,
      amountFromClaim: null,
      estimateAmount: null,
      occupancy: null,
      meetingType: null,
      officeType: null,
      fund: null,
      fieldsValidation: {},
      from: null,
      remarks: null,
      deductionHead: null,
      wasteManagementId: null,
      service: null,
      serviceObj: {},
      lsgiType: null,
      regionType: null,
      buildingProjectType: null,
      districts: null

    },
    resolver: yupResolver(RoutingKeySchema)
  });

  const location = useLocation();
  const referenceNo = watch('referenceNo');
  const [generalFieldValidation, setGeneralFieldValidation] = useState({});

  const referenceNoDetails = watch('referenceNo');
  const ownershipDetails = watch('ownership');
  const localBodyPropertyTypeIdDetails = watch('localBodyPropertyType');
  const buildingUsageIdDetails = watch('buildingUsage');
  const buildingAreaDetails = watch('buildingArea');
  const functionalGroupIdDetails = watch('functionalGroup');
  const functionDetails = watch('functions');
  const descriptionDetails = watch('description');
  const ownerNameDetails = watch('ownerName');
  const ksebPostNoDetails = watch('ksebPostNo');
  const roadNameDetails = watch('roadName');
  const landMarkDetails = watch('landMark');
  const talukIdDetails = watch('talukId');
  const villageDetails = watch('village');
  const surveyNoDetails = watch('surveyNo');
  const dateOfEventDetails = watch('dateOfEvent');
  const toDateDetails = watch('toDate');
  const receiptNoDetails = watch('receiptNo');
  const designationDetails = watch('designation');
  const billTypeDetails = watch('billType');
  const establishmentTypeDetails = watch('establishmentType');
  const missionDetails = watch('mission');
  const professionalTaxTypeDetails = watch('professionalTaxType');
  const typeOfAuditDetails = watch('typeOfAudit');
  const lbBuildingDetails = watch('lbBuilding');
  const amountFromClaimDetails = watch('amountFromClaim');
  const estimateAmountDetails = watch('estimateAmount');
  const occupancyDetails = watch('occupancy');
  const meetingTypeDetails = watch('meetingType');
  const officeTypeDetails = watch('officeType');
  const fundDetails = watch('fund');
  const deductionHeadDetails = watch('deductionHead');
  const wasteManagementId = watch('wasteManagementId');
  const lsgiTypeDetails = watch('lsgiType');
  const regionTypeDetails = watch('regionType');
  const buildingProjectTypeDetails = watch('buildingProjectType');
  const districtsDetails = watch('districts');

  useEffect(() => {
    fetchServicesOptions();
  }, []);

  useEffect(() => {
    if (from !== CUSTODIAN_CHANGE) {
      setRouteKeyObj({
        referenceNo: referenceNoDetails || null,
        ownership: ownershipDetails || null,
        localBodyPropertyType: localBodyPropertyTypeIdDetails || null,
        buildingUsage: buildingUsageIdDetails || null,
        buildingArea: buildingAreaDetails || null,
        functionalGroup: functionalGroupIdDetails || null,
        functions: functionDetails || null,
        description: descriptionDetails || null,
        ownerName: ownerNameDetails || null,
        ksebPostNo: ksebPostNoDetails || null,
        roadName: roadNameDetails || null,
        landMark: landMarkDetails || null,
        talukId: talukIdDetails || null,
        village: villageDetails,
        surveyNo: surveyNoDetails,
        dateOfEvent: dateOfEventDetails,
        toDate: toDateDetails,
        receiptNo: receiptNoDetails,
        designation: designationDetails || null,
        billType: billTypeDetails,
        establishmentType: establishmentTypeDetails || null,
        mission: missionDetails || null,
        professionalTaxType: professionalTaxTypeDetails || null,
        typeOfAudit: typeOfAuditDetails || null,
        lbBuilding: lbBuildingDetails || null,
        amountFromClaim: amountFromClaimDetails || null,
        estimateAmount: estimateAmountDetails || null,
        occupancy: occupancyDetails || null,
        meetingType: meetingTypeDetails || null,
        officeType: officeTypeDetails || null,
        fund: fundDetails || null,
        deductionHead: deductionHeadDetails || null,
        wasteManagementId: wasteManagementId || null
      });
    } else {
      setRouteKeyObj({
        establishmentType: establishmentTypeDetails || null,
        lsgiType: lsgiTypeDetails || null,
        regionType: regionTypeDetails || null,
        buildingProjectType: buildingProjectTypeDetails || null,
        fund: fundDetails || null,
        functionalGroup: functionalGroupIdDetails || null,
        amountFromClaim: amountFromClaimDetails || null,
        districts: districtsDetails || null
      });
    }
  }, [
    referenceNoDetails,
    ownershipDetails,
    localBodyPropertyTypeIdDetails,
    buildingUsageIdDetails,
    buildingAreaDetails,
    functionalGroupIdDetails,
    functionDetails,
    descriptionDetails,
    ownerNameDetails,
    ksebPostNoDetails,
    roadNameDetails,
    landMarkDetails,
    talukIdDetails,
    villageDetails,
    surveyNoDetails,
    dateOfEventDetails,
    toDateDetails,
    receiptNoDetails,
    designationDetails,
    billTypeDetails,
    establishmentTypeDetails,
    missionDetails,
    professionalTaxTypeDetails,
    typeOfAuditDetails,
    lbBuildingDetails,
    estimateAmountDetails,
    occupancyDetails,
    meetingTypeDetails,
    officeTypeDetails,
    fundDetails,
    deductionHeadDetails,
    wasteManagementId,
    lsgiTypeDetails,
    regionTypeDetails,
    buildingProjectTypeDetails,
    districtsDetails,
    amountFromClaimDetails
  ]);

  useEffect(() => {
    if (serviceInfo?.code) {
      // fetchServiceValidation(serviceInfo?.code);
      fetchServicePostRoutes({ serviceCode: serviceInfo?.code, officeId: officeCode });
    }
  }, [serviceInfo]);

  useEffect(() => {
    if (serviceInfo) {
      setValue('services', serviceInfo?.name);
    }
  }, [serviceInfo]);

  useEffect(() => {
    if ((from === CHILD_FILE || from === IKM_FILE_COUNTER || from === IKM_FILE_ARISING || from === LEGACY_FILE)) {
      setValue('from', from);
      if (servicePostRoutes?.routeKey2) {
        const routes = servicePostRoutes?.routeKey2.filter(
          (item) => item !== '' && item !== undefined && item !== null
        );
        const updatedList = {
          service: (from === IKM_FILE_COUNTER || from === IKM_FILE_ARISING || from === LEGACY_FILE) ? 0 : 1,
          establishmentType: routes.includes('1') ? 1 : 0,
          lsgiType: routes.includes('2') ? 1 : 0,
          regionType: routes.includes('3') ? 1 : 0,
          buildingProjectType: routes.includes('4') ? 1 : 0,
          fund: routes.includes('5') ? 1 : 0,
          functionalGroup: routes.includes('6') ? 1 : 0,
          amountFromClaim: routes.includes('7') ? 1 : 0,
          districts: routes.includes('8') ? 1 : 0
        };
        setValue('fieldsValidation', updatedList);
        setGeneralFieldValidation(updatedList);
        setValue('from', from);
      }
    } else if (serviceValidation && from !== CUSTODIAN_CHANGE) {
      const updatedList = {
        functionalGroup: serviceValidation?.functionalGroup || 0,
        establishmentType: serviceValidation?.establishmentType || 0,
        amountFromClaim: serviceValidation?.claimAmount || 0,
        fund: serviceValidation?.fundTypeNeed || 0,
        lsgiType: serviceValidation?.lsgiType || 0,
        regionType: serviceValidation?.regionType || 0,
        buildingProjectType: serviceValidation?.buildingProjectType || 0,
        districts: serviceValidation?.districts || 0
      };
      setValue('fieldsValidation', updatedList);
      setGeneralFieldValidation(updatedList);
      setValue('from', from);
    } else if (from === CUSTODIAN_CHANGE) {
      if (servicePostRoutes?.routeKey2) {
        const routes = servicePostRoutes?.routeKey2.filter(
          (item) => item !== '' && item !== undefined && item !== null
        );
        const updatedList = {
          establishmentType: routes.includes('1') ? 1 : 0,
          lsgiType: routes.includes('2') ? 1 : 0,
          regionType: routes.includes('3') ? 1 : 0,
          buildingProjectType: routes.includes('4') ? 1 : 0,
          fund: routes.includes('5') ? 1 : 0,
          functionalGroup: routes.includes('6') ? 1 : 0,
          amountFromClaim: routes.includes('7') ? 1 : 0,
          districts: routes.includes('8') ? 1 : 0
        };
        setValue('fieldsValidation', updatedList);
        setGeneralFieldValidation(updatedList);
        setValue('from', from);
      }
    }
  }, [JSON.stringify(serviceValidation), JSON.stringify(servicePostRoutes)]);

  const handleFieldChange = (field, data) => {
    if (data) {
      switch (field) {
        case 'fieldsValidation':
          setValue('fieldsValidation', data);
          break;
        case 'service':
          setValue('service', data?.code);
          setValue('serviceObj', data);
          fetchServicePostRoutes({ serviceCode: data?.code, officeId: userInfo?.id });
          setServiceCodeDetails(data?.code);
          break;
        case 'ownershipId':
          setValue('ownershipId', Number(data.id));
          break;
        case 'localBodyPropertyType':
          setValue('localBodyPropertyType', Number(data.id));
          break;
        case 'buildingArea':
          setValue('buildingArea', Number(data.id));
          break;
        case 'buildingUsage':
          setValue('buildingUsage', Number(data.id));
          break;
        case 'functionalGroup':
          setValue('functionalGroup', Number(data.functionalGroupId));
          fetchFunctions(Number(data.functionalGroupId));
          break;
        case 'functions':
          setValue('functions', Number(data.id));
          fetchDesignation({ serviceCode: eFilePreview?.serviceCode, officeId: officeCode });
          break;
        case 'taluk':
          setValue('talukId', data.id);
          setValue('talukName', data.name);
          break;
        case 'village':
          setValue('village', data.id);
          setValue('villageName', data.name);
          break;
        case 'designation':
          setValue('designation', data.id);
          break;
        case 'billType':
          setValue('billType', data.id);
          break;
        case 'establishmentType':
          setValue('establishmentType', data.id);
          break;
        case 'mission':
          setValue('mission', data.id);
          break;
        case 'professionalTaxType':
          setValue('professionalTaxType', data.id);
          break;
        case 'typeOfAudit':
          setValue('typeOfAudit', data.id);
          break;
        case 'lbBuilding':
          setValue('lbBuilding', data.id);
          break;
        case 'amountFromClaim':
          setValue('amountFromClaim', data.id);
          break;
        case 'occupancy':
          setValue('occupancy', data.id);
          break;
        case 'meetingType':
          setValue('meetingType', data.id);
          break;
        case 'officeType':
          setValue('officeType', data.id);
          break;
        case 'fund':
          setValue('fund', data.id);
          break;
        case 'deductionHead':
          setValue('deductionHead', data.headId);
          break;
        case 'height':
          setValue('height', data.id);
          break;
        case 'wasteManagementId':
          setValue('wasteManagementId', data.id);
          break;
        case 'lsgiType':
          setValue('lsgiType', data.id);
          break;
        case 'regionType':
          setValue('regionType', data.id);
          break;
        case 'buildingProjectType':
          setValue('buildingProjectType', data.id);
          break;
        case 'stateId':
          fetchDistricts(data.id);
          break;
        case 'districts':
          setValue('districts', data.id);
          break;
        default:
          break;
      }
    }
  };

  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'counterGeneralDetailsCreate' });
    routingSave(data);
  };

  useEffect(() => {
    if (referenceNo) {
      const found = FILE_NO.test(referenceNo);
      if (found) {
        checkFileNo(referenceNo);
      }
    }
  }, [referenceNo]);

  useEffect(() => {
    if (generalFieldValidation) {
      if (generalFieldValidation?.taluk === 1) {
        fetchTalukOfficeCode(officeCode);
      }

      if (generalFieldValidation?.village === 1) {
        fetchVillage(officeCode);
      }
      if (generalFieldValidation.localBodyPropertyType === 1) {
        fetchLocalBodyPropertyType();
      }
      if (generalFieldValidation.functionalGroup === 1) {
        fetchFunctionalGroup();
      }
      if (generalFieldValidation.buildingUsage === 1) {
        fetchBuildingUsage();
      }
      if (generalFieldValidation.ownershipId === 1) {
        fetchOwnership();
      }
      if (generalFieldValidation.designation === 1) {
        fetchDesignation({ serviceCode: eFilePreview?.serviceCode, officeId: officeCode });
      }
      if (generalFieldValidation.billType === 1) {
        fetchBillType();
      }
      if (generalFieldValidation.establishmentType === 1) {
        fetchEstablishmentType();
      }
      if (generalFieldValidation.mission === 1) {
        fetchMission();
      }
      if (generalFieldValidation.professionalTaxType === 1) {
        fetchProfessionalTaxType();
      }
      if (generalFieldValidation.typeOfAudit === 1) {
        fetchTypeofAudit();
      }
      if (generalFieldValidation.lbBuilding === 1) {
        fetchLbBuilding();
      }
      if (generalFieldValidation.amountFromClaim === 1) {
        fetchAmountFromClaim();
      }
      if (generalFieldValidation.estimateAmount === 1) {
        fetchEstimateAmount();
      }
      if (generalFieldValidation.occupancy === 1) {
        fetchOccupancy();
      }
      if (generalFieldValidation.buildingArea === 1) {
        fetchBuildUpArea();
      }
      if (generalFieldValidation.meetingType === 1) {
        fetchMeetingType();
      }
      if (generalFieldValidation.officeType === 1) {
        fetchOfficeType({ officeLbCode: officeCode });
      }
      if (generalFieldValidation.fund === 1) {
        fetchFund();
      }
      if (generalFieldValidation.deductionHead === 1) {
        fetchRecoveryAccountHead();
      }
      if (generalFieldValidation.height === 1) {
        fetchHeight();
      }
      if (generalFieldValidation.wasteManagementId === 1) {
        fetchWasteManagementType();
      }

      if (generalFieldValidation.lsgiType === 1) {
        fetchLsgiType();
      }

      if (generalFieldValidation.regionType === 1) {
        fetchRegionType();
      }

      if (generalFieldValidation.buildingProjectType === 1) {
        fetchBuildingProjectType();
      }

      if (generalFieldValidation.districts === 1) {
        fetchStates(DEFAULT_STATE?.id);
      }
    }
  }, [JSON.stringify(eFilePreview), JSON.stringify(generalFieldValidation)]);

  useEffect(() => {
    if (existingData && from !== CHILD_FILE) {
      setValue('referenceNo', existingData?.referenceNo);
      setValue('ownershipId', existingData?.ownershipId);
      setValue('localBodyPropertyType', existingData?.localBodyPropertyTypeId);
      setValue('buildingUsage', existingData?.buildingUsageId);
      setValue('buildingArea', existingData?.buildingArea);
      if (existingData?.functionalGroupId) {
        fetchFunctions(Number(existingData?.functionalGroupId));
      }
      setValue('functionalGroup', existingData?.functionalGroupId);
      setValue('functions', existingData?.functionId);
      setValue('description', existingData?.description);
      setValue('ownerName', existingData?.ownerName);
      setValue('ksebPostNo', existingData?.ksebPostNo);
      setValue('roadName', existingData?.roadName);
      setValue('landMark', existingData?.landMark);
      setValue('talukId', existingData?.talukId);
      setValue('talukName', existingData?.talukName);
      setValue('villageName', existingData?.villageName);
      setValue('village', existingData?.village);
      setValue('surveyNo', existingData?.surveyNo);
      setValue('dateOfEvent', existingData?.dateOfEvent);
      setValue('toDate', existingData?.toDate);
      setValue('receiptNo', existingData?.receiptNo);
      setValue('designation', existingData?.designation);
      setValue('billType', existingData?.billType);
      setValue('establishmentType', existingData?.establishmentType);
      setValue('mission', existingData?.mission);
      setValue('professionalTaxType', existingData?.professionalTaxType);
      setValue('typeOfAudit', existingData?.typeOfAudit);
      setValue('lbBuilding', existingData?.lbBuilding);
      setValue('amountFromClaim', existingData?.amountFromClaim);
      setValue('estimateAmount', existingData?.estimateAmount);
      setValue('occupancy', existingData?.occupancy);
      setValue('officeType', existingData?.officeType);
      setValue('meetingType', existingData?.meetingType);
      setValue('fund', existingData?.fund);
      setValue('height', existingData?.height);
      setValue('wasteManagementId', existingData?.wasteManagementId);
      setValue('deductionHead', existingData?.deductionHead);

      setValue('lsgiType', existingData?.lsgiType);
      setValue('regionType', existingData?.regionType);
      setValue('buildingProjectType', existingData?.buildingProjectType);
      setValue('districts', existingData?.districts);
      setValue('stateId', existingData?.stateId);
      if (existingData?.stateId) {
        fetchDistricts(existingData?.stateId);
      }

      if (existingData?.deductionHead) {
        fetchRecoveryAccountHead(Number(existingData?.deductionHead));
      }
    }
  }, [existingData]);

  return (
    <form onSubmit={handleSubmit(onSubmitForm)}>
      <FormWrapper py={3}>
        {from !== CUSTODIAN_CHANGE && from !== CHILD_FILE && (
          <div className="col-span-12">
            <div className="px-6 py-3 bg-slate-200 rounded-full font-medium text-blue-900 mb-5">
              {t('generalInformation')}
            </div>
          </div>
        )}

        {activeIndex && (
          <>
            <div hidden>
              <FormController
                name="fieldsValidation"
                variant="outlined"
                type="select"
                optionKey="id"
                control={control}
                errors={errors}
                options={[]}
                isClearable
                handleChange={(data) => handleFieldChange('fieldsValidation', data)}
              />
            </div>
            {from === CHILD_FILE && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="service"
                  type="select"
                  label={t('service')}
                  control={control}
                  errors={errors}
                  options={_.get(serviceDropdown, 'data', [])}
                  optionKey="code"
                  handleChange={(data) => {
                    handleFieldChange('service', data);
                  }}
                  isClearable
                  required={from === CHILD_FILE}
                />
              </div>
            )}

            {from === CUSTODIAN_CHANGE && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="services"
                  type="text"
                  label={t('services')}
                  placeholder={t('searchHere')}
                  control={control}
                  readOnly
                />
              </div>
            )}
            {generalFieldValidation.referenceNo === 1 && (
              <div className="col-span-12">
                <FormController
                  name="referenceNo"
                  variant="outlined"
                  type="text"
                  label={t('referenceNo')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('referenceNo', data)}
                  rightContent={validateFile(fileExist, referenceNo)}
                  required={generalFieldValidation.referenceNo === 1}
                  isDisabled={isReturnToCitizen}
                />
                {validateFileText(fileExist, referenceNo)}
              </div>
            )}

            {generalFieldValidation.ownershipId === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="ownershipId"
                  variant="outlined"
                  type="select"
                  label={t('residenceOwnership')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  options={_.get(ownerShip, 'data', [])}
                  handleChange={(data) => handleFieldChange('ownershipId', data)}
                  required={generalFieldValidation.ownershipId === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}
            {generalFieldValidation.localBodyPropertyType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="localBodyPropertyType"
                  variant="outlined"
                  type="select"
                  label={t('localBodyPropertyType')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  isClearable
                  options={_.get(localBodyPropertyType, 'data', [])}
                  handleChange={(data) => handleFieldChange('localBodyPropertyType', data)}
                  required={generalFieldValidation.localBodyPropertyType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}
            {generalFieldValidation.buildingUsage === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="buildingUsage"
                  variant="outlined"
                  type="select"
                  label={t('buildingUsage')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  isClearable
                  options={_.get(buildingUsage, 'data', [])}
                  handleChange={(data) => handleFieldChange('buildingUsage', data)}
                  required={generalFieldValidation.buildingUsage === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}
            {generalFieldValidation.buildingArea === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="buildingArea"
                  variant="outlined"
                  type="select"
                  label={t('buildUpArea')}
                  optionKey="id"
                  options={buildUpArea || []}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('buildingArea', data)}
                  required={generalFieldValidation.buildingArea === 1}
                  isDisabled={isReturnToCitizen || readOnly.includes('builtupArea')}
                />
              </div>
            )}
            {generalFieldValidation.functionalGroup === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="functionalGroup"
                  variant="outlined"
                  type="select"
                  label={t('functionalGroup')}
                  optionKey="functionalGroupId"
                  control={control}
                  errors={errors}
                  isClearable
                  options={_.get(functionalGroup, 'data', [])}
                  handleChange={(data) => handleFieldChange('functionalGroup', data)}
                  required={generalFieldValidation.functionalGroup === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.functions === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="functions"
                  variant="outlined"
                  type="select"
                  label={t('functions')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  isClearable
                  options={_.get(functions, 'data', [])}
                  handleChange={(data) => handleFieldChange('functions', data)}
                  required={generalFieldValidation.functions === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.designation === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="designation"
                  variant="outlined"
                  type="select"
                  label={t('designation')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  isClearable
                  options={designation || []}
                  handleChange={(data) => handleFieldChange('designation', data)}
                  required={generalFieldValidation.designation === 1}
                  isLoading={actionTriggered?.id === 'counter-applicant-designation' && actionTriggered?.loading}
                />
              </div>
            )}

            {generalFieldValidation.ownerName === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="ownerName"
                  variant="outlined"
                  label={t('ownerName')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('ownerName', data)}
                  required={generalFieldValidation.ownerName === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.ksebPostNo === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="ksebPostNo"
                  variant="outlined"
                  label={t('ksebPostNo')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('ksebPostNo', data)}
                  required={generalFieldValidation.ksebPostNo === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.roadName === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="roadName"
                  variant="outlined"
                  label={t('roadName')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('roadName', data)}
                  required={generalFieldValidation.roadName === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.landmark === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="landMark"
                  variant="outlined"
                  label={t('landmark')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('landmark', data)}
                  required={generalFieldValidation.landmark === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.taluk === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="talukId"
                  variant="outlined"
                  type="select"
                  label={t('taluk')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={taluk || []}
                  handleChange={(data) => handleFieldChange('taluk', data)}
                  required={generalFieldValidation.taluk === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.village === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="village"
                  variant="outlined"
                  type="select"
                  label={t('village')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={village || []}
                  handleChange={(data) => handleFieldChange('village', data)}
                  required={generalFieldValidation.village === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.surveyNumber === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="surveyNo"
                  variant="outlined"
                  label={t('surveyNumber')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('surveyNumber', data)}
                  required={generalFieldValidation.surveyNumber === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.dateOfEvent === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="dateOfEvent"
                  type="date"
                  variant="outlined"
                  label={t('fromDate')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('dateOfEvent', data)}
                  required={generalFieldValidation.dateOfEvent === 1}
                  isDisabled={isReturnToCitizen}
                  minDate={location?.pathname?.includes('legacyfiles') ? null : new Date()}
                />
              </div>
            )}

            {generalFieldValidation.dateOfEvent === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="toDate"
                  type="date"
                  variant="outlined"
                  label={t('toDate')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('toDate', data)}
                  required={generalFieldValidation.dateOfEvent === 1}
                  isDisabled={isReturnToCitizen}
                  minDate={location?.pathname?.includes('legacyfiles') ? null : new Date()}
                />
              </div>
            )}

            {generalFieldValidation.receiptNo === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="receiptNo"
                  variant="outlined"
                  label={t('receiptNo')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('receiptNo', data)}
                  required={generalFieldValidation.receiptNo === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.billType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="billType"
                  variant="outlined"
                  type="select"
                  label={t('billType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={billType || []}
                  handleChange={(data) => handleFieldChange('billType', data)}
                  required={generalFieldValidation.billType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.establishmentType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="establishmentType"
                  variant="outlined"
                  type="select"
                  label={t('establishmentType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={establishmentType || []}
                  handleChange={(data) => handleFieldChange('establishmentType', data)}
                  required={generalFieldValidation.establishmentType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.mission === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="mission"
                  variant="outlined"
                  type="select"
                  label={t('mission')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={mission || []}
                  handleChange={(data) => handleFieldChange('mission', data)}
                  required={generalFieldValidation.mission === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.professionalTaxType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="professionalTaxType"
                  variant="outlined"
                  type="select"
                  label={t('professionalTaxType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={professionalTaxType || []}
                  handleChange={(data) => handleFieldChange('professionalTaxType', data)}
                  required={generalFieldValidation.professionalTaxType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.typeOfAudit === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="typeOfAudit"
                  variant="outlined"
                  type="select"
                  label={t('typeOfAudit')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={typeOfAudit || []}
                  handleChange={(data) => handleFieldChange('typeOfAudit', data)}
                  required={generalFieldValidation.typeOfAudit === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.lbBuilding === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="lbBuilding"
                  variant="outlined"
                  type="select"
                  label={t('lbBuilding')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={lbBuilding || []}
                  handleChange={(data) => handleFieldChange('lbBuilding', data)}
                  required={generalFieldValidation.lbBuilding === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.amountFromClaim === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="amountFromClaim"
                  variant="outlined"
                  type="select"
                  label={t('amountFromClaim')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={amountFromClaim || []}
                  handleChange={(data) => handleFieldChange('amountFromClaim', data)}
                  required={generalFieldValidation.amountFromClaim === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.estimateAmount === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="estimateAmount"
                  variant="outlined"
                  type="select"
                  isClearable
                  optionKey="id"
                  options={estimateAmount || []}
                  label={t('estimateAmount')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('estimateAmount', data)}
                  required={generalFieldValidation.estimateAmount === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.occupancy === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="occupancy"
                  variant="outlined"
                  type="select"
                  label={t('occupancy')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={occupancy || []}
                  handleChange={(data) => handleFieldChange('occupancy', data)}
                  required={generalFieldValidation.occupancy === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.meetingType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="meetingType"
                  variant="outlined"
                  type="select"
                  label={t('meetingType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={meetingType || []}
                  handleChange={(data) => handleFieldChange('meetingType', data)}
                  required={generalFieldValidation.meetingType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.officeType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="officeType"
                  variant="outlined"
                  type="select"
                  label={t('officeType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={officeType || []}
                  handleChange={(data) => handleFieldChange('officeType', data)}
                  required={generalFieldValidation.officeType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.fund === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="fund"
                  variant="outlined"
                  type="select"
                  label={t('fund')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={fund || []}
                  handleChange={(data) => handleFieldChange('fund', data)}
                  required={generalFieldValidation.fund === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.deductionHead === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="deductionHead"
                  variant="outlined"
                  type="select"
                  label={t('deductionHead')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="headId"
                  options={recoveryAccountHead || []}
                  handleChange={(data) => handleFieldChange('deductionHead', data)}
                  required={generalFieldValidation.deductionHead === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.height === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="height"
                  variant="outlined"
                  type="select"
                  label={t('height')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={height || []}
                  handleChange={(data) => handleFieldChange('height', data)}
                  required={generalFieldValidation.height === 1}
                  isDisabled={isReturnToCitizen || readOnly.includes('height')}
                />
              </div>
            )}

            {generalFieldValidation.wasteManagementId === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="wasteManagementId"
                  variant="outlined"
                  type="select"
                  label={t('wasteManagementType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={wasteManagementType || []}
                  handleChange={(data) => handleFieldChange('wasteManagementId', data)}
                  required={generalFieldValidation.wasteManagementId === 1}
                  isDisabled={isReturnToCitizen || readOnly.includes('wasteManagementId')}
                />
              </div>
            )}

            {generalFieldValidation.lsgiType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="lsgiType"
                  variant="outlined"
                  type="select"
                  label={t('lsgiType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={lsgiType || []}
                  handleChange={(data) => handleFieldChange('lsgiType', data)}
                  required={generalFieldValidation.lsgiType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.regionType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="regionType"
                  variant="outlined"
                  type="select"
                  label={t('regionType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={regionType || []}
                  handleChange={(data) => handleFieldChange('regionType', data)}
                  required={generalFieldValidation.regionType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.buildingProjectType === 1 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="buildingProjectType"
                  variant="outlined"
                  type="select"
                  label={t('buildingProjectType')}
                  control={control}
                  errors={errors}
                  isClearable
                  optionKey="id"
                  options={buildingProjectType || []}
                  handleChange={(data) => handleFieldChange('buildingProjectType', data)}
                  required={generalFieldValidation.regionType === 1}
                  isDisabled={isReturnToCitizen}
                />
              </div>
            )}

            {generalFieldValidation.districts === 1 && (
              <>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="stateId"
                    type="select"
                    label={t('state')}
                    control={control}
                    errors={errors}
                    optionKey="id"
                    options={_.get(stateDropdown, 'data', [])}
                    handleChange={(data) => handleFieldChange('stateId', data)}
                    required
                    isClearable
                    isDisabled={isReturnToCitizen}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-4 col-span-12">
                  <FormController
                    name="districts"
                    variant="outlined"
                    type="select"
                    label={t('districts')}
                    control={control}
                    errors={errors}
                    isClearable
                    optionKey="id"
                    options={districtDropdown || []}
                    handleChange={(data) => handleFieldChange('districts', data)}
                    required={generalFieldValidation.districts === 1}
                    isDisabled={isReturnToCitizen}
                  />
                </div>
              </>
            )}

            {from !== CUSTODIAN_CHANGE && from !== CHILD_FILE && from !== LEGACY_FILE && (
              <div className="col-span-12">
                <FormController
                  name="description"
                  variant="outlined"
                  type="textarea"
                  label={t('description')}
                  control={control}
                  errors={errors}
                  style={{ height: '200px', lineHeight: '30px' }}
                  handleChange={(data) => handleFieldChange('description', data)}
                />
              </div>
            )}

            {(from === CUSTODIAN_CHANGE || from === CHILD_FILE) && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <Button
                  variant="secondary_outline"
                  isLoading={actionTriggered?.id === 'fetchUserDetails' && actionTriggered?.loading}
                  onClick={() => handleFetchUser()}
                  height={54}
                  isDisabled={from === CHILD_FILE && !serviceCodeDetails}
                >
                  {t('fetchUser')}
                </Button>
              </div>
            )}

            {(from === CUSTODIAN_CHANGE || from === CHILD_FILE) && removeDuplicates(post, 'penNo')?.length > 0 && (
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="user"
                  type="select"
                  label={t('user')}
                  placeholder={t('searchHere')}
                  control={control}
                  errors={errors}
                  options={removeDuplicates(post, 'penNo') || []}
                  optionKey="postId"
                  required
                  isClearable
                />
              </div>
            )}

            {(from === CUSTODIAN_CHANGE || from === CHILD_FILE) && (
              <div className="col-span-12">
                <FormController
                  name="remarks"
                  variant="outlined"
                  type="textarea"
                  label={t('remarks')}
                  control={control}
                  errors={errors}
                  style={{ height: '200px', lineHeight: '30px' }}
                  handleChange={(data) => handleFieldChange('remarks', data)}
                  required
                />
              </div>
            )}

            <div className="col-span-12 text-right">
              <Button
                type="submit"
                variant="secondary_outline"
                isLoading={actionTriggered?.id === 'efileGeneralDetailsCreate' && actionTriggered?.loading}
              >
                {from === CUSTODIAN_CHANGE || from === CHILD_FILE ? t('save') : t('proceed')}
              </Button>
            </div>
          </>
        )}
      </FormWrapper>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  wardDropdown: getWard,
  localBodyPropertyType: getLocalBodyPropertyType,
  functionalGroup: getFunctionalGroup,
  functions: getFunctions,
  fileExist: getFileExist,
  actionTriggered: getActionTriggered,
  buildingUsage: getBuildingUsage,
  ownerShip: getOwnerShip,
  village: getVillage,
  taluk: getTaluk,
  designation: getDesignation,
  serviceValidation: getServiceValidation,
  billType: getBillType,
  establishmentType: getEstablishmentType,
  mission: getMission,
  professionalTaxType: getProfessionalTaxType,
  typeOfAudit: getTypeOfAudit,
  lbBuilding: getLbBuilding,
  amountFromClaim: getAmountFromClaim,
  occupancy: getOccupancy,
  estimateAmount: getEstimateAmount,
  buildUpArea: getBuildUpArea,
  meetingType: getMeetingType,
  officeType: getOfficeType,
  fund: getFund,
  userInfo: getUserInfo,
  recoveryAccountHead: getRecoveryAccountHead,
  height: getHeight,
  door: getDoor,
  wasteManagementType: getWasteManagementType,
  serviceDropdown: getServicesDropdown,
  servicePostRoutes: getServicePostRoutes,
  lsgiType: getLsgiType,
  regionType: getRegionType,
  buildingProjectType: getBuildingProjectType,
  countryDropdown: getCountry,
  stateDropdown: getState,
  districtDropdown: getDistricts
});

const mapDispatchToProps = (dispatch) => ({
  fetchWardsByLocalBodyId: (data) => dispatch(commonActions.fetchWardsByLocalBodyId(data)),
  fetchLocalBodyPropertyType: (data) => dispatch(commonActions.fetchLocalBodyPropertyType(data)),
  fetchFunctionalGroup: (data) => dispatch(commonActions.fetchFunctionalGroup(data)),
  fetchFunctions: (data) => dispatch(commonActions.fetchFunctions(data)),
  checkFileNo: (data) => dispatch(commonActions.checkFileNo(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchBuildingUsage: (data) => dispatch(commonActions.fetchBuildingUsage(data)),
  fetchOwnership: (data) => dispatch(commonActions.fetchOwnership(data)),
  fetchTalukOfficeCode: (data) => dispatch(commonActions.fetchTalukOfficeCode(data)),
  fetchVillage: (data) => dispatch(commonActions.fetchVillage(data)),
  fetchDoorKey: (data) => dispatch(commonActions.fetchDoorKey(data)),
  fetchDoor: (data) => dispatch(commonActions.fetchDoor(data)),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  fetchBillType: (data) => dispatch(commonActions.fetchBillType(data)),
  fetchEstablishmentType: (data) => dispatch(commonActions.fetchEstablishmentType(data)),
  fetchMission: (data) => dispatch(commonActions.fetchMission(data)),
  fetchProfessionalTaxType: (data) => dispatch(commonActions.fetchProfessionalTaxType(data)),
  fetchTypeofAudit: (data) => dispatch(commonActions.fetchTypeofAudit(data)),
  fetchLbBuilding: (data) => dispatch(commonActions.fetchLbBuilding(data)),
  fetchAmountFromClaim: (data) => dispatch(commonActions.fetchAmountFromClaim(data)),
  fetchDesignation: (data) => dispatch(commonActions.fetchDesignation(data)),
  fetchOccupancy: (data) => dispatch(commonActions.fetchOccupancy(data)),
  fetchEstimateAmount: (data) => dispatch(commonActions.fetchEstimateAmount(data)),
  fetchBuildUpArea: (data) => dispatch(commonActions.fetchBuildUpArea(data)),
  fetchMeetingType: (data) => dispatch(commonActions.fetchMeetingType(data)),
  fetchOfficeType: (data) => dispatch(commonActions.fetchOfficeType(data)),
  fetchFund: (data) => dispatch(commonActions.fetchFund(data)),
  fetchRecoveryAccountHead: (data) => dispatch(commonActions.fetchRecoveryAccountHead(data)),
  fetchHeight: (data) => dispatch(commonActions.fetchHeight(data)),
  fetchWasteManagementType: (data) => dispatch(commonActions.fetchWasteManagementType(data)),
  fetchServicesOptions: () => dispatch(commonActions.fetchServicesDetails()),
  fetchServicePostRoutes: (data) => dispatch(fileActions.fetchServicePostRoutes(data)),
  fetchLsgiType: (data) => dispatch(commonActions.fetchLsgiType(data)),
  fetchRegionType: (data) => dispatch(commonActions.fetchRegionType(data)),
  fetchBuildingProjectType: (data) => dispatch(commonActions.fetchBuildingProjectType(data)),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchStates: () => dispatch(commonActions.fetchState())
});

export default connect(mapStateToProps, mapDispatchToProps)(RoutingKeys);
