import * as yup from 'yup';

import {
  ENG_NUMBER,
  FILE_NO
} from 'common/regex';

import { t } from 'common/components';
import { CHILD_FILE, CUSTODIAN_CHANGE } from 'pages/file/details/constants';

export const RoutingKeySchema = yup.object({
  referenceNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.referenceNo === 1) {
        return schema.required(t('isRequired', { type: t('referenceNo') })).matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
      }
      return schema.nullable().transform((val) => val || null).notRequired().matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
    }),
  service: yup.string()
    .when(['fieldsValidation', 'from'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.service === 1 || fieldsValidation[1] === CHILD_FILE) {
        return schema.required(t('isRequired', { type: t('Service') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ward: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ward === 1) {
        return schema.required(t('isRequired', { type: t('ward') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  doorNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.doorNo === 1) {
        return schema
          .max(5, 'Door No must be five digits')
          .required(t('isRequired', { type: t('doorNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  subNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.subNo === 1) {
        return schema.required(t('isRequired', { type: t('subNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownershipId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownershipId === 1) {
        return schema.required(t('isRequired', { type: t('ownershipId') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  localBodyPropertyType: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.localBodyPropertyType === 1) {
        return schema.required(t('isRequired', { type: t('localBodyPropertyType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingUsage: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingUsage === 1) {
        return schema.required(t('isRequired', { type: t('buildingUsage') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingArea: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingArea === 1) {
        return schema.required(t('isRequired', { type: t('buildingArea') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functionalGroup: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functionalGroup === 1) {
        return schema.required(t('isRequired', { type: t('functionalGroup') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functions: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functions === 1) {
        return schema.required(t('isRequired', { type: t('functions') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  description: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.description === 1) {
        return schema.required(t('isRequired', { type: t('description') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownerName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownerName === 1) {
        return schema.required(t('isRequired', { type: t('ownerName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ksebPostNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ksebPostNo === 1) {
        return schema.required(t('isRequired', { type: t('ksebPostNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  roadName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.roadName === 1) {
        return schema.required(t('isRequired', { type: t('roadName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  landMark: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.landmark === 1) {
        return schema.required(t('isRequired', { type: t('landmark') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  talukId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.taluk === 1) {
        return schema.required(t('isRequired', { type: t('taluk') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  village: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.village === 1) {
        return schema.required(t('isRequired', { type: t('village') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  surveyNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.surveyNumber === 1) {
        return schema.required(t('isRequired', { type: t('surveyNumber') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  dateOfEvent: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfEvent === 1) {
        return schema.required(t('isRequired', { type: t('dateOfEvent') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  receiptNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.receiptNo === 1) {
        return schema.required(t('isRequired', { type: t('receiptNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  toDate: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfEvent === 1) {
        return schema.required(t('isRequired', { type: t('toDate') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  designation: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.designation === 1) {
        return schema.required(t('isRequired', { type: t('designation') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  billType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.billType === 1) {
        return schema.required(t('isRequired', { type: t('billType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  establishmentType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.establishmentType === 1) {
        return schema.required(t('isRequired', { type: t('establishmentType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  mission: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.mission === 1) {
        return schema.required(t('isRequired', { type: t('mission') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  professionalTaxType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.professionalTaxType === 1) {
        return schema.required(t('isRequired', { type: t('professionalTaxType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  typeOfAudit: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.typeOfAudit === 1) {
        return schema.required(t('isRequired', { type: t('typeOfAudit') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  lbBuilding: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.lbBuilding === 1) {
        return schema.required(t('isRequired', { type: t('lbBuilding') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  amountFromClaim: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.amountFromClaim === 1) {
        return schema.required(t('isRequired', { type: t('amountFromClaim') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  estimateAmount: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.estimateAmount === 1) {
        return schema.required(t('isRequired', { type: t('estimateAmount') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  occupancy: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.occupancy === 1) {
        return schema.required(t('isRequired', { type: t('occupancy') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  remarks: yup.string()
    .when(['from'], (from, schema) => {
      if (from[0] === CUSTODIAN_CHANGE || from[0] === CHILD_FILE) {
        return schema.required(t('isRequired', { type: t('remarks') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  meetingType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.meetingType === 1) {
        return schema.required(t('isRequired', { type: t('meetingType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  officeType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.officeType === 1) {
        return schema.required(t('isRequired', { type: t('officeType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  fund: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.fund === 1) {
        return schema.required(t('isRequired', { type: t('fund') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  user: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.user === 1) {
        return schema.required(t('isRequired', { type: t('user') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  deductionHead: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.deductionHead === 1) {
        return schema.required(t('isRequired', { type: t('deductionHead') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  lsgiType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.lsgiType === 1) {
        return schema.required(t('isRequired', { type: t('lsgiType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  regionType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.regionType === 1) {
        return schema.required(t('isRequired', { type: t('regionType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingProjectType: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingProjectType === 1) {
        return schema.required(t('isRequired', { type: t('buildingProjectType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  districts: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.districts === 1) {
        return schema.required(t('isRequired', { type: t('districts') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })
}).required();

export const GeneralDetailSchema = yup.object({

  financialStatusId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.financialStatusId === 1) {
        return schema.required(t('isRequired', { type: t('financialStatus') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  category: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.category === 1) {
        return schema.required(t('isRequired', { type: t('category') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  accountNo: yup.string().nullable()
    .transform((val) => val || null)
    .matches(ENG_NUMBER, `${t('accountNo')} ${t('charAndNumbers')}`)
    .min(9, `${t('accountNo')} ${t('minimumNineOnly')}`)
    .max(18, `${t('accountNo')} ${t('maximumOnly')}`),
  bankNameId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.bankNameId === 1) {
        return schema.required(t('isRequired', { type: t('bankName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  bankBranchId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.bankBranchId === 1) {
        return schema.required(t('isRequired', { type: t('bankBranch') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ifsc: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ifsc === 1) {
        return schema.required(t('isRequired', { type: t('ifsc') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })
}).required();
