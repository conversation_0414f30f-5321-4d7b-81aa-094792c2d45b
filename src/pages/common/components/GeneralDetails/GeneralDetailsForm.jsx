import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, t, <PERSON><PERSON>, FormWrapper
} from 'common/components';
import _ from 'lodash';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import * as commonActions from 'pages/common/actions';
import {
  getActionTriggered,
  getBanks,
  getBranches,
  getCategory,
  getEducation,
  getFinancialStatus,
  getGender,
  getServiceValidation,
  getAccountType,
  getTreasuryType,
  getAccountId
} from 'pages/common/selectors';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { GeneralDetailSchema } from './validate';

const GeneralDetailsForm = (props) => {
  const {
    applicantData,
    banksOptions,
    fetchBanks,
    branchesOptions,
    fetchBranchByBank,
    fetchEducation,
    fetchCategory,
    category,
    fetchFinancialStatus,
    financialStatus,
    isAddNew,
    actionTriggered,
    serviceValidation,
    fetchServiceValidation,
    accountType,
    treasuryType,
    fetchAccountType,
    fetchTreasuryType,
    // direct props
    serviceInfo,
    from = 'applicant',
    addGeneralDetails,
    addressType = 1,
    fetchAccountId,
    accountIdDetails
  } = props;

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
    watch,
    reset
  } = useForm({
    mode: 'all',
    defaultValues: {
      category: null,
      financialStatusId: null,
      accountNo: '',
      bank: null,
      bankNameId: null,
      bankBranchId: null,
      ifsc: null,
      accountTypeId: null,
      treasuryTypeId: null,
      treasuryAccountNo: null,
      headOfAccount: null,
      fieldsValidation: {
        category: 0,
        financialStatusId: 0,
        ownershipId: 0,
        accountNo: 0,
        bank: 0,
        bankNameId: 0,
        branch: 0,
        branchName: 0,
        ifsc: 0,
        description: 0
      },
      accountId: null
    },
    resolver: yupResolver(GeneralDetailSchema)
  });

  const params = useParams();
  const [generalFieldValidation, setGeneralFieldValidation] = useState({});

  useEffect(() => {
    const updatedList = {
      category: serviceValidation?.category || 0,
      financialStatusId: serviceValidation?.financialStatus || 0,
      ownershipId: serviceValidation?.ownership || 0,
      accountNo: serviceValidation?.bankAccountNumber || 0,
      bank: serviceValidation?.bankAccountNumber || 0,
      bankNameId: serviceValidation?.bankAccountNumber || 0,
      branch: serviceValidation?.bankAccountNumber || 0,
      branchName: serviceValidation?.bankAccountNumber || 0,
      ifsc: serviceValidation?.bankAccountNumber || 0,
      description: 0
    };
    setGeneralFieldValidation(updatedList);
    setValue('fieldsValidation', updatedList);
  }, [serviceValidation, addressType]);

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  const handleFieldChange = (field, data) => {
    if (data) {
      switch (field) {
        case 'fieldsValidation':
          setValue('fieldsValidation', data);
          break;

        case 'category':
          setValue('category', Number(data.id));
          break;
        case 'financialStatusId':
          setValue('financialStatusId', Number(data.id));
          break;
        case 'bankNameId':
          fetchBranchByBank({ bankId: Number(data.id) });
          setValue('bankNameId', data.id);
          break;
        case 'bankBranchId':
          setValue('bankBranchId', data.id);
          setValue('ifsc', data.ifscCode);
          break;

        case 'accountTypeId':
          if (data?.id === 1 || data?.id === 2) {
            fetchAccountId({ lookupType: 'account_type' });
          }
          break;
        case 'accountId':
          setValue('accountId', data.id);
          break;
        default:
          break;
      }
    } else {
      switch (field) {
        case 'bankNameId':
          setValue('bankBranchId', null);
          setValue('ifsc', null);
          break;
        case 'bankBranchId':
          setValue('ifsc', null);
          break;
        default:
          break;
      }
    }
  };

  const resetData = () => {
    reset({
      category: null,
      financialStatusId: null,
      ownership: null,
      accountNo: '',
      bank: null,
      bankNameId: null,
      bankBranchId: null,
      ifsc: null,
      ward: null,
      doorNo: null,
      subNo: null,
      description: null,
      accountId: null
    });
  };

  useEffect(() => {
    if (!params || isAddNew) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  useEffect(() => {
    if (serviceInfo) {
      fetchServiceValidation(serviceInfo?.code);
    }
  }, [serviceInfo]);

  useEffect(() => {
    if (applicantData) {
      if (Object.keys(applicantData).length > 0) {
        setValue('category', applicantData.categoryId);
        setValue('financialStatusId', applicantData.financialStatusId);
        setValue('accountNo', applicantData.accountNo);
        setValue('bankNameId', applicantData.bankNameId);
        setValue('accountTypeId', applicantData.accountTypeId);
        setValue('treasuryTypeId', applicantData.treasuryTypeId);
        setValue('treasuryAccountNo', applicantData.treasuryAccountNo);
        setValue('headOfAccount', applicantData.headOfAccount);
        if (Number(applicantData.bankNameId)) {
          fetchBranchByBank({ bankId: Number(applicantData.bankNameId) });
        }
        if (Number(applicantData.treasuryType)) {
          fetchTreasuryType();
        }
        setValue('bankBranchId', applicantData.bankBranchId);
        setValue('ifsc', applicantData.ifsc);
        setValue('accountId', applicantData?.accountId);
        if (applicantData?.accountId) {
          fetchAccountId({ lookupType: 'account_type' });
        }
      } else {
        resetData();
      }
    } else {
      resetData();
    }
  }, [applicantData]);

  const onSubmitForm = (data) => {
    addGeneralDetails(data);
    if (from === 'beneficiary') {
      resetData();
    }
  };

  useEffect(() => {
    fetchBanks();
    fetchEducation();
    fetchCategory();
    fetchFinancialStatus();
    fetchAccountType();
    fetchTreasuryType();
  }, []);

  return (
    <form
      onSubmit={handleSubmit(onSubmitForm)}
    >

      <FormWrapper py="5">

        <div hidden>
          <FormController
            name="fieldsValidation"
            variant="outlined"
            type="select"
            optionKey="id"
            control={control}
            errors={errors}
            options={[]}
            handleChange={(data) => handleFieldChange('fieldsValidation', data)}
          />
        </div>

        {addressType !== 3
          && (
            <>

              <div className="lg:col-span-4 md:col-span-12 col-span-12">
                <FormController
                  name="category"
                  variant="outlined"
                  type="select"
                  label={t('category')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  options={_.get(category, 'data', [])}
                  isClearable
                  handleChange={(data) => handleFieldChange('category', data)}
                  required={generalFieldValidation.category === 1}
                  isLoading={actionTriggered?.id === 'counter-applicant-category' && actionTriggered?.loading}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-12 col-span-12">

                <FormController
                  name="financialStatusId"
                  variant="outlined"
                  type="select"
                  label={t('financialStatus')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  isClearable
                  options={_.get(financialStatus, 'data', [])}
                  handleChange={(data) => handleFieldChange('financialStatusId', data)}
                  required={generalFieldValidation.financialStatusId === 1}
                  isLoading={actionTriggered?.id === 'counter-applicant-financialstatus' && actionTriggered?.loading}
                />
              </div>
            </>
          )}

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="accountTypeId"
            variant="outlined"
            type="select"
            label={t('accountType')}
            optionKey="id"
            control={control}
            errors={errors}
            isClearable
            options={accountType?.filter((item) => item.id !== 3) || []}
            handleChange={(data) => handleFieldChange('accountTypeId', data)}
            required={generalFieldValidation.bankNameId === 1}
            isLoading={actionTriggered?.id === 'accountType' && actionTriggered?.loading}
          />
        </div>
        {
          (watch('accountTypeId') === 1 || watch('accountTypeId') === 2)
          && (
            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="accountId"
                variant="outlined"
                type="select"
                label={t('accountId')}
                optionKey="id"
                control={control}
                errors={errors}
                isClearable
                options={watch('accountTypeId') === 1 ? accountIdDetails?.filter((item) => item?.id === 1 || item?.id === 2) : accountIdDetails?.filter((item) => item?.id !== 1 && item?.id !== 2)}
                handleChange={(data) => handleFieldChange('accountId', data)}
                isLoading={actionTriggered?.id === 'accountId' && actionTriggered?.loading}
              />
            </div>
          )
        }

        {watch('accountTypeId') === 2 && (
          <>
            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="treasuryTypeId"
                variant="outlined"
                type="select"
                label={t('treasuryType')}
                optionKey="id"
                control={control}
                errors={errors}
                isClearable
                options={treasuryType || []}
                handleChange={(data) => handleFieldChange('treasuryType', data)}
                required={generalFieldValidation.bankNameId === 1}
                isLoading={actionTriggered?.id === 'treasuryType' && actionTriggered?.loading}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="treasuryAccountNo"
                variant="outlined"
                type="text"
                label={t('treasuryAccountNo')}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('treasuryAccountNo', data)}
                required={generalFieldValidation.teasuryAccountNo === 1}
                maxLength={18}
              />
            </div>
          </>
        )}
        {watch('accountTypeId') === 4 && (
          <div className="lg:col-span-4 md:col-span-12 col-span-12">
            <FormController
              name="headOfAccount"
              variant="outlined"
              type="text"
              label={t('headOfAccount')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('headOfAccount', data)}
              required={generalFieldValidation.headOfAccount === 1}
              maxLength={18}
            />
          </div>
        )}
        {watch('accountTypeId') === 1 && (
          <>

            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="bankNameId"
                variant="outlined"
                type="select"
                label={t('bank')}
                optionKey="id"
                control={control}
                errors={errors}
                isClearable
                options={_.get(banksOptions, 'data', [])}
                handleChange={(data) => handleFieldChange('bankNameId', data)}
                required={generalFieldValidation.bankNameId === 1}
                isLoading={actionTriggered?.id === 'counter-applicant-bank' && actionTriggered?.loading}
              />
            </div>

            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="bankBranchId"
                variant="outlined"
                type="select"
                label={t('branch')}
                optionKey="id"
                control={control}
                errors={errors}
                isClearable
                options={_.get(branchesOptions, 'data', [])}
                handleChange={(data) => handleFieldChange('bankBranchId', data)}
                required={generalFieldValidation.branch === 1}
                isLoading={actionTriggered?.id === 'counter-applicant-branch' && actionTriggered?.loading}
              />
            </div>

            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="ifsc"
                variant="outlined"
                type="text"
                label={t('ifsc')}
                control={control}
                errors={errors}
                readOnly
                handleChange={(data) => handleFieldChange('ifsc', data)}
                required={generalFieldValidation.ifsc === 1}
              />
            </div>

            <div className="lg:col-span-4 md:col-span-12 col-span-12">
              <FormController
                name="accountNo"
                variant="outlined"
                type="text"
                label={t('accountNo')}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('accountNo', data)}
                required={generalFieldValidation.accountNo === 1}
                // minLength={9}
                maxLength={18}
              />
            </div>

          </>
        )}

        <div className="col-span-12 text-right">
          <Button
            type="submit"
            variant="secondary_outline"
            className="shadow-md"
          >
            {from === 'beneficiary' ? t('save') : t('next')}
          </Button>
        </div>

      </FormWrapper>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  banksOptions: getBanks,
  branchesOptions: getBranches,
  educationOptions: getEducation,
  genderOptions: getGender,
  category: getCategory,
  financialStatus: getFinancialStatus,
  actionTriggered: getActionTriggered,
  serviceValidation: getServiceValidation,
  accountType: getAccountType,
  treasuryType: getTreasuryType,
  accountIdDetails: getAccountId
});

const mapDispatchToProps = (dispatch) => ({
  fetchBanks: () => dispatch(commonActions.fetchBanks()),
  fetchBranchByBank: (data) => dispatch(commonActions.fetchBranchByBank(data)),
  fetchEducation: () => dispatch(commonActions.fetchEducation()),
  fetchGender: () => dispatch(commonActions.fetchGender()),
  fetchCategory: () => dispatch(commonActions.fetchCategory()),
  fetchFinancialStatus: () => dispatch(commonActions.fetchFinancialStatus()),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  fetchAccountType: (data) => dispatch(commonActions.fetchAccountType(data)),
  fetchTreasuryType: (data) => dispatch(commonActions.fetchTreasuryType(data)),
  fetchAccountId: (data) => dispatch(commonActions.fetchAccountId(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(GeneralDetailsForm);
