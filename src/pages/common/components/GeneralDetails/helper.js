import _ from 'lodash';
import { DATE_FORMAT } from 'pages/common/constants';
import { LOCAL_BODY_TYPE_IDS } from 'pages/counter/new/constants';
import { convertToLocalDate, reFormattedDate } from 'utils/date';
import { numberNull } from 'utils/numberNull';

export const generalRoutingKeys = (data) => ({
  // 2: numberNull(Number(data.occupancy)),
  // 3: numberNull(Number(data.buildingUsage)),
  // 5: numberNull(Number(data.buildingArea)),
  // 6: numberNull(Number(doorkey?.id) || null),
  // 9: numberNull(Number(data.fund)),
  // 10: numberNull(Number(data.designation)),
  // 11: numberNull(Number(data.deductionHead)),
  // 13: numberNull(Number(data.officeType)),
  // 15: numberNull(Number(data.functionalGroup)),
  // 18: numberNull(Number(data.localBodyPropertyType)),
  // 19: numberNull(Number(data.meetingType)),
  // 20: numberNull(Number(data.functions)),
  // 21: numberNull(Number(data.billType)),
  // 22: numberNull(Number(data.establishmentType)),
  // 23: numberNull(Number(data.mission)),
  // 24: numberNull(Number(data.professionalTaxType)),
  // 25: numberNull(Number(data.typeOfAudit)),
  // 26: numberNull(Number(data.amountFromClaim)),
  // 27: numberNull(Number(data.estimateAmount)),
  // 29: numberNull(Number(data.wasteManagementId))
  1: numberNull(Number(data.establishmentType)),
  2: numberNull(Number(data.lsgiType)),
  3: numberNull(Number(data.regionType)),
  4: numberNull(Number(data.buildingProjectType)),
  5: numberNull(Number(data.fund)),
  6: numberNull(Number(data.functionalGroup)),
  7: numberNull(Number(data.amountFromClaim)),
  8: numberNull(Number(data.districts))
});

const sendFormat = (data) => {
  const dataFormat = data.map((item) => {
    return item.data;
  });
  return dataFormat;
};

const generalId = (data) => {
  if (data?.length > 0) {
    return data[0]?.id;
  } return null;
};

export const generalSaveData = (
  rounteKeyData,
  inwardId,
  existingData,
  data,
  userInfoId,
  from,
  fileNo = null,
  noteUpdateRequest = null,
  custodianPostId = null
) => ({
  inwardId,
  generalDetails: existingData?.length > 0 ? sendFormat(existingData) : [],
  localBodyPropertyTypeId: numberNull(Number(data.localBodyPropertyType)),
  buildingUsageId: numberNull(Number(data.buildingUsage)),
  buildingArea: data.buildingArea,
  functionalGroupId: numberNull(Number(data.functionalGroup)),
  functionId: numberNull(Number(data.functions)),
  ownershipId: numberNull(Number(data.ownershipId)),
  wardNo: numberNull(Number(data.ward)),
  doorNo: numberNull(Number(data.doorNo)),
  subNo: data.subNo,
  description: data.description ? data.description.replace(/\n/g, '') : null,
  referenceNo: data.referenceNo,
  ownerName: data.ownerName,
  ksebPostNo: data.ksebPostNo,
  roadName: data.roadName,
  landMark: data.landMark,
  talukId: numberNull(Number(data.talukId)),
  talukName: data.talukName,
  village: data.village,
  villageName: data.villageName,
  surveyNo: data.surveyNo,
  dateOfEvent: convertToLocalDate(data.dateOfEvent, DATE_FORMAT.DATE_LOCAL),
  eventEndDate: convertToLocalDate(data.toDate, DATE_FORMAT.DATE_LOCAL),
  receiptNo: data.receiptNo,
  designationId: numberNull(Number(data.designation)),
  billTypeId: numberNull(Number(data?.billType)),
  establishmentTypeId: numberNull(Number(data?.establishmentType)),
  missionId: numberNull(Number(data?.mission)),
  professionalTaxTypeId: numberNull(Number(data?.professionalTaxType)),
  typeOfAuditId: numberNull(Number(data?.typeOfAudit)),
  lbBuilding: data?.lbBuilding,
  amountFromClaimId: numberNull(Number(data?.amountFromClaim)),
  estimateAmountId: numberNull(Number(data?.estimateAmount)),
  occupancyId: numberNull(Number(data?.occupancy)),
  meetingTypeId: numberNull(Number(data?.meetingType)),
  officeTypeId: numberNull(Number(data?.officeType)),
  fundTypeId: numberNull(Number(data?.fund)),
  deductionHeadId: numberNull(Number(data?.deductionHead)),
  wasteManagementId: numberNull(Number(data?.wasteManagementId)),
  lsgiTypeId: numberNull(Number(data?.lsgiType)),
  regionTypeId: numberNull(Number(data?.regionType)),
  buildingProjectTypeId: numberNull(Number(data?.buildingProjectType)),
  districtId: numberNull(Number(data?.districts)),
  routeKey2: [_.omitBy(rounteKeyData, _.isNil)],
  routeKey1: data.ward ? { 1: numberNull(Number(data.ward)) } : null,
  userInfo: {
    officeId: userInfoId
  },
  from,
  fileNo,
  noteUpdateRequest,
  custodianPostId,
  generalDetailsId: from === 'arising' ? generalId(existingData) : null

});

export const existingGeneralData = (data) => ({
  referenceNo: data.referenceNo,
  wardNo: numberNull(Number(data.wardNo)),
  doorNo: data.doorNo,
  subNo: data.subNo,
  ownershipId: data.ownershipId,
  localBodyPropertyTypeId: data.localBodyPropertyTypeId,
  buildingUsageId: data.buildingUsageId,
  buildingArea: data.buildingArea ? data.buildingArea : numberNull(Number(data?.builtupArea)),
  functionalGroupId: data.functionalGroupId,
  functionId: data.functionId,
  description: data.description,
  ownerName: data.ownerName,
  ksebPostNo: data.ksebPostNo,
  roadName: data.roadName,
  landMark: data.landMark,
  talukId: data.talukId,
  talukName: data.talukName,
  villageName: data.villageName,
  village: data.village,
  surveyNo: data.surveyNo,
  dateOfEvent: reFormattedDate(data.dateOfEvent),
  toDate: reFormattedDate(data?.eventEndDate),
  receiptNo: data.receiptNo,
  designation: data?.designationId,
  billType: data?.billTypeId,
  establishmentType: data?.establishmentTypeId,
  mission: data?.missionId,
  professionalTaxType: data?.professionalTaxTypeId,
  typeOfAudit: data?.typeOfAuditId,
  lbBuilding: data?.lbBuilding,
  amountFromClaim: data?.amountFromClaimId,
  estimateAmount: data?.estimateAmountId,
  occupancy: data?.occupancyId,
  meetingType: data?.meetingTypeId,
  officeType: data?.officeTypeId,
  fund: data?.fundTypeId,
  deductionHead: data?.deductionHeadId,
  wasteManagementId: data?.wasteManagementId,
  height: numberNull(Number(data?.height)),
  lsgiType: data?.lsgiTypeId,
  regionType: data?.regionTypeId
  // buildingProjectType: buildingProjectTypeDetails || null,
  // districts: districtsDetails || null
});

export function findId(data) {
  if (data.applicantDetailsInsideLocalBodyId) {
    return data.applicantDetailsInsideLocalBodyId;
  } if (data.applicantDetailsOutsideLocalBodyId) {
    return data.applicantDetailsOutsideLocalBodyId;
  } return data.institutionDetailsId;
}

export function dataFact(existGeneral, index, inwardId, userInfoId, applicantDetails) {
  if (existGeneral.length) {
    if (existGeneral.length > 0) {
      const filteredObject = Object.keys(existGeneral[index]);
      let localbodyTypeKey;
      const findInside = filteredObject.findIndex(
        (item) => item === LOCAL_BODY_TYPE_IDS.APPLICATION_DETAILS_INSIDE_LOCAL_BODY_ID
      );
      const findOutSide = filteredObject.findIndex(
        (item) => item === LOCAL_BODY_TYPE_IDS.APPLICATION_DETAILS_OUTSIDE_LOCAL_BODY_ID
      );
      const findInstitution = filteredObject.findIndex((item) => item === LOCAL_BODY_TYPE_IDS.INSTITION_DETAILS_ID);
      if (findInside > -1) {
        localbodyTypeKey = LOCAL_BODY_TYPE_IDS.APPLICATION_DETAILS_INSIDE_LOCAL_BODY_ID;
      } else if (findOutSide > -1) {
        localbodyTypeKey = LOCAL_BODY_TYPE_IDS.APPLICATION_DETAILS_OUTSIDE_LOCAL_BODY_ID;
      } else if (findInstitution > -1) {
        localbodyTypeKey = LOCAL_BODY_TYPE_IDS.INSTITION_DETAILS_ID;
      }

      const saveData = {
        inwardId,
        [localbodyTypeKey]: findId(existGeneral[index]),
        id: existGeneral[index].id,
        genderId: Number(existGeneral[index].genderId) ? Number(existGeneral[index].genderId) : null,
        categoryId: existGeneral[index].categoryId,
        dateOfBirth: existGeneral[index].dateOfBirth,
        financialStatusId: existGeneral[index].financialStatusId,
        income: existGeneral[index].income,
        ownershipIdId: existGeneral[index].ownershipIdId,
        wardNo: existGeneral[index].ward,
        doorNo: existGeneral[index].doorNo,
        subNo: existGeneral[index].subNo,
        accountNo: existGeneral[index].accountNo,
        bank: existGeneral[index].bank,
        bankNameId: existGeneral[index].bankNameId,
        bankBranchId: existGeneral[index].bankBranchId,
        bankBranch: existGeneral[index].branchName,
        ifsc: existGeneral[index].ifsc,
        educationalQualificationId: existGeneral[index].educationalQualificationId,
        description: existGeneral[index].description,
        accountTypeId: existGeneral[index].accountTypeId,
        treasuryTypeId: existGeneral[index].treasuryTypeId,
        treasuryAccountNo: existGeneral[index].treasuryAccountNo,
        headOfAccount: existGeneral[index].headOfAccount,
        userInfo: {
          officeId: userInfoId
        },
        accountId: existGeneral[index].accountId,
        applicantDetailsId: applicantDetails[index].id || null
      };

      return saveData;
    }
  }
  return {};
}

export const findApplicantType = (addressType) => {
  switch (addressType) {
    case 1:
      return LOCAL_BODY_TYPE_IDS.APPLICANT_DETAILS_ID;
    case 2:
      return LOCAL_BODY_TYPE_IDS.INSTITION_DETAILS_ID;
    default:
      return LOCAL_BODY_TYPE_IDS.INSTITION_DETAILS_ID;
  }
};

export const addGeneralSaveData = (data, existing) => ({
  [findApplicantType(existing?.addressType)]: existing?.id,
  categoryId: data.category,
  financialStatusId: numberNull(Number(data.financialStatusId)),
  accountNo: data.accountNo,
  bankNameId: numberNull(Number(data.bankNameId)),
  bankBranchId: numberNull(Number(data.bankBranchId)),
  ifsc: data.ifsc,
  accountTypeId: data.accountTypeId,
  treasuryTypeId: data.treasuryTypeId,
  treasuryAccountNo: data.treasuryAccountNo,
  headOfAccount: data.headOfAccount,
  accountId: data?.accountId
});
