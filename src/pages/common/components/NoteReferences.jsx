import { Button } from '@ksmartikm/ui-components';
import React from 'react';
import { primary } from 'utils/color';

const NoteReferences = ({ noteReferences = [], handleRefNavigate = () => { } }) => {
  return (
    noteReferences?.length > 0 && (
      <div className="flex flex-wrap gap-1 pt-2">
        <div className="flex-none" style={{ color: primary }}>
          Ref. Note :
        </div>
        {noteReferences.map((item) => (
          <div className="flex items-center pr-2" key={item.documentName} aria-hidden onClick={(event) => handleRefNavigate(event, item)}>
            <Button variant="link" style={{ color: primary, height: '20px' }}>
              Note: {item?.noteOrApplicationNumber}
              {item?.documentName && `, Doc.: ${item.documentName}`}
            </Button>
          </div>
        ))}
      </div>
    )
  );
};

export default NoteReferences;
