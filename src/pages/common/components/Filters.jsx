import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>, FormController, t, InputGroup, Input, ErrorText
} from 'common/components';
import _ from 'lodash';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import * as commonActions from 'pages/common/actions';
import {
  getCounterOperator,
  getCounterOperatorWithRole,
  getDepartmentsForFilter,
  getEmployeeNameList,
  getModulesDropdown,
  getSearchListParams,
  getUserInfo
} from 'pages/common/selectors';
import { yupResolver } from '@hookform/resolvers/yup';
import { convertToLocalDate } from 'utils/date';
import { actions as commonSliceActions } from 'pages/common/slice';
import { EMPLOYEE_ROLES } from 'common/constants';
import { dropdownName } from 'utils/dropdownName';
import { removeDuplicates } from 'utils/validateFile';
import ToggleButton from 'common/components/Toggle';
import * as actions from '../actions';
import {
  getFilterParams, getSeats, getServicesSearchList, getStatus, getSubModulesSearchList
} from '../selectors';
import { FilterFormSchema } from '../validate';
import { actions as sliceActions } from '../slice';
import {
  DATE_FORMAT, FILTER_TYPE, MODULE_TYPE, X_STATE_CODE
} from '../constants';

const styles = {
  search: {
    input: {
      borderRadius: '20px',
      border: '1px solid #DEDEDE',
      height: '44px'
    },
    button: {
      background: 'none'
    }
  }
};

const Filters = ({
  fetchModulesOptions,
  modulesDropdown,
  fetchSubModulesByModuleId,
  subModulesDropdown,
  fetchServicesById,
  servicesDropdown,
  // fetchDepartments,
  // departmentDropdown,
  fetchSeats,
  seatsDropdown,
  fetchStatus,
  statusDropdown,
  setFilterParams,
  tableData = [],
  fetchCounterOperator,
  counterOperatorDropdown,
  filters,
  download = () => {},
  fileName,
  fetchEmployeeNameById,
  employeeNameDropdown,
  filterParams,
  setSearchListParams,
  searchFileParams,
  userInfo,
  setSubModulesSearchList,
  setServicesSearchList,
  setEmployeeNameList,
  setSeats,
  fetchCounterOperatorWithRole,
  CounterOperatorWithRole,
  setSelectedEmployees = () => {},
  setSelectedFromDate = () => {},
  setSelectedToDate = () => {},
  setPage = () => {},
  setLoginWiseFromDate = () => {},
  setLoginWiseToDate = () => {},
  fetchPostsByFunctionalGroups,
  setSelectedCheckBoxValue = () => {},
  isInsideOffice,
  setIsInsideOffice = () => {}
}) => {
  const {
    modules = false,
    subModule = false,
    service = false,
    department = false,
    seat = false,
    status = false,
    fromDate = true,
    toDate = true,
    counterOperator = false,
    fileNo = false,
    employeeName = false,
    generateReportFlag = true,
    cashDepartment = false,
    moduleType = false,
    cashDeclarationType = false
  } = filters;
  const [subId, setSubId] = useState('');
  const [fileNumber, setFileNumber] = useState(null);
  const [post, setPost] = useState([]);
  const [seatError, setSeatError] = useState([]);

  const [employeeNamesError, setEmployeeNamesError] = useState('');

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
      modules: null,
      subModule: null,
      service: null,
      department: null,
      seat: null,
      status: null,
      fromDate: moduleType || fileName === 'cashDeclaration' ? new Date() : null,
      toDate: moduleType || fileName === 'cashDeclaration' ? new Date() : null,
      counterOperator: null,
      fileNo: null,
      search: false,
      fileName,
      employeeName: null,
      functionalGroupIds: null,
      moduleType: 'All'
    },
    resolver: yupResolver(FilterFormSchema)
  });

  const moduleName = watch('modules');
  const subModuleName = watch('subModule');
  const serviceName = watch('service');
  const departmentName = watch('department');
  const seatName = watch('seat');
  const statusName = watch('status');
  const from = watch('fromDate');
  const to = watch('toDate');
  const counterOperatorName = watch('counterOperator');
  const employeeNames = watch('employeeName');
  const moduleTypes = watch('moduleType');
  const functionalGroupIds = watch('functionalGroupIds');

  useEffect(() => {
    if (employeeNameDropdown?.length > 0) {
      if (functionalGroupIds?.length === counterOperatorDropdown?.length) {
        const employees = removeDuplicates(dropdownName(employeeNameDropdown, 'employeeName'), 'penNo');
        const pens = employees.map((item) => item.penNo);
        setValue('employeeName', pens);
      } else {
        setValue('employeeName', []);
      }
    }
  }, [functionalGroupIds, employeeNameDropdown]);

  useEffect(() => {
    fetchModulesOptions();
    fetchStatus({ code: X_STATE_CODE });
    fetchCounterOperator();
    if (userInfo?.userRoles?.length > 0) {
      userInfo?.userRoles?.map((item) => {
        if (item?.code === 'GAGA14' && item?.roles?.includes(EMPLOYEE_ROLES.OPERATOR)) {
          fetchCounterOperatorWithRole({
            role: EMPLOYEE_ROLES.OPERATOR,
            serviceCode: 'GAGA14',
            officeId: userInfo?.id
          });
        }
        return null;
      });
    }
  }, [userInfo]);

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.MODULES:
        if (data) {
          fetchSubModulesByModuleId(data?.id);
          setSubId(data?.id);
        } else {
          setValue('subModule', '');
          setValue('service', '');
          setSubModulesSearchList([]);
          setServicesSearchList([]);
        }
        break;
      case FILTER_TYPE.SUB_MODULES:
        if (data) {
          fetchServicesById({ module: subId, subModule: data?.id });
        } else {
          setValue('service', '');
          setServicesSearchList([]);
        }
        break;
      case FILTER_TYPE.DEPARTMENT:
        if (data) {
          fetchEmployeeNameById({ functionalGroupId: data?.functionalGroupId, officeId: userInfo?.id });
          fetchSeats({ functionalGroupId: data?.functionalGroupId, officeId: userInfo?.id });
          setSeatError(t('isRequired', { type: t('seat') }));
        } else {
          setValue('employeeName', '');
          setValue('seat', '');
          setEmployeeNameList([]);
          setSeats([]);
          setSeatError('');
          setPost([]);
        }
        break;
      case FILTER_TYPE.FUNCTIONAL_GROUP_IDS:
        if (data?.length > 0) {
          fetchPostsByFunctionalGroups({ data, officeId: userInfo?.id });
          setEmployeeNamesError('Employee name required');
        } else {
          setEmployeeNamesError('');
        }
        break;
      case FILTER_TYPE.EMPLOYEE_NAME:
        if (data?.length > 0) {
          setValue('employeeName', data);
          setEmployeeNamesError('');
        } else {
          setEmployeeNamesError('');
        }

        break;
      case FILTER_TYPE.SEAT:
        if (data) {
          setSeatError('');
        } else {
          setSeatError(t('isRequired', { type: t('seat') }));
        }
        break;

      case FILTER_TYPE.MODULE_TYPE:
        if (data) {
          setSelectedCheckBoxValue(data?.name);
          setValue('moduleType', data?.name);
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (seatsDropdown?.length > 0) {
      const postArray = seatsDropdown?.filter((item) => item?.name !== null && item?.name !== '' && item?.name !== ' ');
      setPost(postArray);
    }
  }, [seatsDropdown]);

  const getInsideOutsideFlag = () => {
    if (isInsideOffice) {
      return 'false';
    }
    return 'true';
  };

  const onSubmitForm = (data) => {
    if (fileName.includes('report')) {
      setPage(0);
      setSearchListParams({
        ...searchFileParams,
        serviceName: data?.service,
        moduleCode: data.modules,
        submoduleCode: data.subModule,
        fileNo: fileNumber || null,
        applicantName: data.applicantName,
        department: data.department,
        postId: data.seat,
        fromDate: convertToLocalDate(data.fromDate, DATE_FORMAT.DATE_LOCAL),
        toDate: convertToLocalDate(data.toDate, DATE_FORMAT.DATE_LOCAL),
        search: true,
        keyword: null,
        holdUpToDate: null,
        fileStatus: data?.status,
        officeId: userInfo.id,
        page: 0,
        size: 10,
        stageNotIn: null,
        filingMode: null
      });
    } else {
      if (fileName === 'cashDeclaration') {
        if ((employeeNames?.length > 0 && functionalGroupIds?.length > 0) || functionalGroupIds?.length === 0) {
          setPage(0);
          let penNos;
          if (data?.employeeName?.length > 0) {
            penNos = data?.employeeName.join(',');
            setSelectedEmployees(data?.employeeName.join(','));
            setLoginWiseFromDate('');
            setLoginWiseToDate('');
          } else {
            penNos = userInfo?.userDetails?.pen;
            setSelectedEmployees(userInfo?.userDetails?.pen);
            setLoginWiseFromDate('');
            setLoginWiseToDate('');
          }
          if (data?.fromDate) {
            setSelectedFromDate(convertToLocalDate(data?.fromDate, DATE_FORMAT.DATE_LOCAL));
            setLoginWiseFromDate('');
            setLoginWiseToDate('');
          }
          if (data?.toDate) {
            setSelectedToDate(convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_LOCAL));
            setLoginWiseToDate('');
            setLoginWiseFromDate('');
          }
          let filingMode = null;
          if (data?.moduleType === 'Counter') {
            filingMode = 'counter';
          } else if (data?.moduleType === 'E-file') {
            filingMode = 'efile';
          }
          setFilterParams({
            ...filterParams,
            modules: data?.modules,
            subModule: data?.subModule,
            service: data?.service,
            department: data?.department,
            postId: data?.seat,
            status: data?.status,
            fromDate: data?.fromDate && convertToLocalDate(data?.fromDate, DATE_FORMAT.DATE_LOCAL),
            toDate: data?.toDate && convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_LOCAL),
            search: true,
            counterOperator: data?.counterOperator,
            fileNo: fileNumber || null,
            penNos,
            officeId: userInfo.id,
            page: 0,
            size: 10,
            stageNotIn: moduleType ? ['partial', 'discarded'] : null,
            filingMode,
            isOutsideOffice: cashDeclarationType ? getInsideOutsideFlag() : null
          });
        } else {
          setPage(0);
          let penNos;
          if (data?.employeeName?.length > 0) {
            penNos = data?.employeeName.join(',');
            setSelectedEmployees(data?.employeeName.join(','));
            setLoginWiseFromDate('');
            setLoginWiseToDate('');
          } else {
            penNos = userInfo?.userDetails?.pen;
            setSelectedEmployees(userInfo?.userDetails?.pen);
            setLoginWiseFromDate('');
            setLoginWiseToDate('');
          }
          if (data?.fromDate) {
            setSelectedFromDate(convertToLocalDate(data?.fromDate, DATE_FORMAT.DATE_LOCAL));
            setLoginWiseFromDate('');
            setLoginWiseToDate('');
          }
          if (data?.toDate) {
            setSelectedToDate(convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_LOCAL));
            setLoginWiseToDate('');
            setLoginWiseFromDate('');
          }
          let filingMode = null;
          if (data?.moduleType === 'Counter') {
            filingMode = 'counter';
          } else if (data?.moduleType === 'E-file') {
            filingMode = 'efile';
          }
          setFilterParams({
            ...filterParams,
            modules: data?.modules,
            subModule: data?.subModule,
            service: data?.service,
            department: data?.department,
            postId: data?.seat,
            status: data?.status,
            fromDate: data?.fromDate && convertToLocalDate(data?.fromDate, DATE_FORMAT.DATE_LOCAL),
            toDate: data?.toDate && convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_LOCAL),
            search: true,
            counterOperator: data?.counterOperator,
            fileNo: fileNumber || null,
            penNos,
            officeId: userInfo.id,
            page: 0,
            size: 10,
            stageNotIn: moduleType ? ['partial', 'discarded'] : null,
            filingMode,
            isOutsideOffice: cashDeclarationType ? getInsideOutsideFlag() : null
          });
        }
      }
      setPage(0);
      let penNos;
      if (data?.employeeName?.length > 0) {
        penNos = data?.employeeName.join(',');
        setSelectedEmployees(data?.employeeName.join(','));
        setLoginWiseFromDate('');
        setLoginWiseToDate('');
      } else {
        penNos = userInfo?.userDetails?.pen;
        setSelectedEmployees(userInfo?.userDetails?.pen);
        setLoginWiseFromDate('');
        setLoginWiseToDate('');
      }
      if (data?.fromDate) {
        setSelectedFromDate(convertToLocalDate(data?.fromDate, DATE_FORMAT.DATE_LOCAL));
        setLoginWiseFromDate('');
        setLoginWiseToDate('');
      }
      if (data?.toDate) {
        setSelectedToDate(convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_LOCAL));
        setLoginWiseToDate('');
        setLoginWiseFromDate('');
      }
      let filingMode = null;
      if (data?.moduleType === 'Counter') {
        filingMode = 'counter';
      } else if (data?.moduleType === 'E-file') {
        filingMode = 'efile';
      }
      setFilterParams({
        ...filterParams,
        modules: data?.modules,
        subModule: data?.subModule,
        service: data?.service,
        department: data?.department,
        postId: data?.seat,
        status: data?.status,
        fromDate: data?.fromDate && convertToLocalDate(data?.fromDate, DATE_FORMAT.DATE_LOCAL),
        toDate: data?.toDate && convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_LOCAL),
        search: true,
        counterOperator: data?.counterOperator,
        fileNo: fileNumber || null,
        penNos,
        officeId: userInfo.id,
        page: 0,
        size: 10,
        stageNotIn: moduleType ? ['partial', 'discarded'] : null,
        filingMode,
        isOutsideOffice: cashDeclarationType ? getInsideOutsideFlag() : null
      });
    }
    return null;
  };

  return (
    <form id={`${fileName}`} action="enter" onSubmit={handleSubmit(onSubmitForm)}>
      <div className="flex bg-white p-12 rounded-[10px]">
        <div className="grow">
          <div className={`grid ${cashDepartment ? 'grid-cols-5' : 'grid-cols-4'} gap-4`}>
            {cashDeclarationType && (
              <ToggleButton label={t('insideOffice')} togggle={isInsideOffice} setToggle={setIsInsideOffice} />
            )}
            {moduleType && (
              <FormController
                name="moduleType"
                type="select"
                label={t('moduleType')}
                control={control}
                errors={errors}
                options={MODULE_TYPE}
                handleChange={(data) => handleFieldChange(FILTER_TYPE.MODULE_TYPE, data)}
                optionKey="name"
              />
            )}
            {modules && (
              <FormController
                name="modules"
                type="select"
                label={t('module')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={_.get(modulesDropdown, 'data', [])}
                handleChange={(data) => handleFieldChange(FILTER_TYPE.MODULES, data)}
                optionKey="code"
                isClearable
              />
            )}
            {subModule && (
              <FormController
                name="subModule"
                type="select"
                label={t('subModule')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={subModulesDropdown}
                handleChange={(data) => handleFieldChange(FILTER_TYPE.SUB_MODULES, data)}
                optionKey="code"
                isClearable
              />
            )}
            {service && (
              <FormController
                name="service"
                type="select"
                label={t('services')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={servicesDropdown}
                handleChange={(data) => {
                  handleFieldChange('service', data);
                }}
                optionKey="name"
                isClearable
              />
            )}

            {cashDepartment && (
              // use for cash declaration
              <FormController
                name="functionalGroupIds"
                type="multi-select"
                label={t('functionalGroup')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={counterOperatorDropdown}
                handleChange={(data) => {
                  handleFieldChange(FILTER_TYPE.FUNCTIONAL_GROUP_IDS, data);
                }}
                optionKey="functionalGroupId"
              />
            )}

            {employeeName && (
              <div>
                <FormController
                  name="employeeName"
                  type="multi-select"
                  label={t('employeeName')}
                  placeholder={t('searchHere')}
                  control={control}
                  errors={errors}
                  options={removeDuplicates(dropdownName(employeeNameDropdown, 'employeeName'), 'penNo')}
                  handleChange={(data) => {
                    handleFieldChange(FILTER_TYPE.EMPLOYEE_NAME, data);
                  }}
                  optionKey="penNo"
                />
                {employeeNamesError && <ErrorText error={employeeNamesError} />}
              </div>
            )}

            {department && (
              <FormController
                name="department"
                type="select"
                label={t('functionalGroup')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={counterOperatorDropdown}
                handleChange={(data) => {
                  handleFieldChange(FILTER_TYPE.DEPARTMENT, data);
                }}
                optionKey="name"
                isClearable
                // isMulti
              />
            )}
            {seat && (
              <div>
                <FormController
                  name="seat"
                  type="select"
                  label={t('seat')}
                  placeholder={t('searchHere')}
                  control={control}
                  errors={errors}
                  options={post}
                  optionKey="postId"
                  isClearable
                  handleChange={(data) => {
                    handleFieldChange(FILTER_TYPE.SEAT, data);
                  }}
                />
                {seatError && <ErrorText error={seatError} />}
              </div>
            )}
            {status && (
              <FormController
                name="status"
                type="select"
                label={t('status')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={statusDropdown}
                optionKey="name"
                isClearable
              />
            )}
            {fromDate && (
              <FormController
                type="date"
                name="fromDate"
                label={t('concatLabel', { label: t('from'), type: t('date') })}
                control={control}
                dateFormat="dd-MM-yyyy"
                maxDate={new Date()}
                errors={errors}
              />
            )}
            {toDate && (
              <FormController
                type="date"
                name="toDate"
                label={t('concatLabel', { label: t('to'), type: t('date') })}
                control={control}
                dateFormat="dd-MM-yyyy"
                maxDate={new Date()}
                errors={errors}
              />
            )}
            {counterOperator && (
              <FormController
                name="counterOperator"
                type="select"
                label={t('counterOperator')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={CounterOperatorWithRole}
                optionKey="name"
                isClearable
              />
            )}
            {fileNo && (
              <div>
                <InputGroup style={styles.search}>
                  <Input
                    name="fileNo"
                    placeholder={t('fileNo')}
                    style={styles.search.input}
                    value={fileNumber}
                    onChange={(event) => {
                      setFileNumber(event.target.value);
                    }}
                  />
                  {/* <InputRightElement>
                      <SearchIcon />
                    </InputRightElement> */}
                </InputGroup>
              </div>
            )}
          </div>
        </div>
        <div className="grid grid-rows-1 gap-4 pl-20">
          <Button
            variant="primary_outline"
            type="submit"
            form={`${fileName}`}
            isDisabled={
              !moduleName
              && !subModuleName
              && !serviceName
              && !departmentName
              && !seatName
              && !statusName
              && !from
              && !to
              && !counterOperatorName
              && !fileNumber
              && !employeeNames
              && moduleTypes
            }
          >
            {t('search')}
          </Button>
          {generateReportFlag && (
            <Button variant="secondary" isDisabled={tableData?.content?.length === 0} onClick={download}>
              {t('generateReport')}
            </Button>
          )}
        </div>
      </div>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  modulesDropdown: getModulesDropdown,
  subModulesDropdown: getSubModulesSearchList,
  servicesDropdown: getServicesSearchList,
  departmentDropdown: getDepartmentsForFilter,
  seatsDropdown: getSeats,
  statusDropdown: getStatus,
  filterParams: getFilterParams,
  counterOperatorDropdown: getCounterOperator,
  employeeNameDropdown: getEmployeeNameList,
  userInfo: getUserInfo,
  searchFileParams: getSearchListParams,
  CounterOperatorWithRole: getCounterOperatorWithRole
});

const mapDispatchToProps = (dispatch) => ({
  fetchModulesOptions: (data) => dispatch(commonActions.fetchModuleDetails(data)),
  fetchSubModuleByIdSearch: (data) => dispatch(actions.fetchSubModuleByIdSearch(data)),
  fetchServicesById: (data) => dispatch(actions.fetchServicesById(data)),
  fetchDepartments: (data) => dispatch(actions.fetchDepartmentsForFilter(data)),
  fetchSeats: (data) => dispatch(actions.fetchSeats(data)),
  fetchStatus: (data) => dispatch(actions.fetchStatus(data)),
  setFilterParams: (data) => dispatch(sliceActions.setFilterParams(data)),
  fetchCounterOperator: (data) => dispatch(actions.fetchCounterOperator(data)),
  fetchSubModulesByModuleId: (data) => dispatch(actions.fetchSubModuleByModuleId(data)),
  fetchEmployeeNameById: (data) => dispatch(actions.fetchEmployeeNameById(data)),
  setSearchListParams: (data) => dispatch(sliceActions.setSearchListParams(data)),
  setSubModulesSearchList: (data) => dispatch(commonSliceActions.setSubModulesSearchList(data)),
  setServicesSearchList: (data) => dispatch(commonSliceActions.setServicesSearchList(data)),
  setEmployeeNameList: (data) => dispatch(commonSliceActions.setEmployeeNameList(data)),
  setSeats: (data) => dispatch(commonSliceActions.setSeats(data)),
  fetchCounterOperatorWithRole: (data) => dispatch(actions.fetchCounterOperatorWithRole(data)),
  fetchPostsByFunctionalGroups: (data) => dispatch(commonActions.fetchPostsByFunctionalGroups(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Filters);
