import { AddIconWithCircle } from 'assets/Svg';
import { t } from 'common/components';

const AddButton = ({ onClick, isError = false }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className="flex items-center gap-2 px-4 py-1.5 border-[1px] border-b-[2px] rounded-[8px] font-semibold text-sm hover:bg-gray-50 transition"
      style={{
        borderColor: isError ? 'red' : '#E8EFF4',
        color: isError ? 'red' : '#003450'
      }}
    >
      <AddIconWithCircle
        width="18"
        height="18"
        stroke={isError ? 'red' : '#003450'}
        strokeWidth="3.5"
      />
      {t('add')}
    </button>
  );
};

export default AddButton;
