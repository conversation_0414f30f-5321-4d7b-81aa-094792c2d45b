import { Tooltip, useOutsideClick } from '@ksmartikm/ui-components';
import { useState, useRef } from 'react';
import OutlineTag from './OutlineTag';

const TagGroup = ({
  tags,
  startIcon,
  maxVisibleTags = 2,
  maxWidth = ['100%', '80px', '130px'],
  onTagClick,
  onTagRemove,
  allowCloseOnRemove = false,
  removeButtonProps,
  CloseIcon,
  tagProps,
  divProps = {},
  showRemoveButton = () => true
}) => {
  const { className: divClassName = '', ...divPropsRest } = divProps;

  const ref = useRef(null);
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = () => setIsOpen(true);

  const toggleClose = () => setIsOpen(false);

  useOutsideClick({
    ref,
    handler: toggleClose
  });

  const visibleTags = tags?.slice(0, maxVisibleTags);
  const hiddenTags = tags?.slice(maxVisibleTags);

  const resolveStartIcon = (item) => {
    if (typeof startIcon === 'function') {
      return startIcon(item);
    }

    return startIcon;
  };

  return (
    <div className={`flex items-center gap-3 ${divClassName}`} {...divPropsRest}>
      {visibleTags?.map((tag, index) => (
        <OutlineTag
          CloseIcon={CloseIcon}
          tagProps={tagProps}
          removeButtonProps={removeButtonProps}
          showRemoveButton={showRemoveButton(tag)}
          key={tag?.text}
          text={tag?.text}
          maxWidth={maxWidth}
          startIcon={resolveStartIcon(tag)}
          onTagClick={() => onTagClick(tag, index)}
          onTagRemove={(e) => onTagRemove(index, tag, e)}
        />
      ))}
      {hiddenTags?.length > 0 && (
        <Tooltip
          ref={ref}
          isOpen={isOpen}
          hasArrow
          px={3}
          py={2}
          bg="#E6EFF5"
          placement="auto"
          pointerEvents="all"
          label={(
            <div className="flex gap-3 flex-wrap">
              {hiddenTags.map((tag, index) => (
                <OutlineTag
                  tagProps={tagProps}
                  CloseIcon={CloseIcon}
                  removeButtonProps={removeButtonProps}
                  showRemoveButton={showRemoveButton(tag)}
                  key={tag?.text}
                  text={tag?.text}
                  startIcon={resolveStartIcon(tag)}
                  onTagClick={() => {
                    onTagClick(tag, index + maxVisibleTags);
                    toggleClose();
                  }}
                  onTagRemove={(e) => {
                    onTagRemove(index + maxVisibleTags, tag, e);
                    if (allowCloseOnRemove) {
                      toggleClose();
                    }
                  }}
                />
              ))}
            </div>
          )}
        >
          <div>
            <OutlineTag
              tagProps={tagProps}
              CloseIcon={CloseIcon}
              removeButtonProps={removeButtonProps}
              showRemoveButton={false}
              text={`+${hiddenTags?.length}`}
              onTagClick={toggleOpen}
            />
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export default TagGroup;
