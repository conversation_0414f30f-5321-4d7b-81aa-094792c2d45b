import { Button, IconButton } from '@ksmartikm/ui-components';
import AttachmentIcon from 'assets/Attachment';
import React from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { BASE_PATH } from 'common/constants';
import { dark, light, secondary } from 'utils/color';
import CloseSolidIcon from 'assets/CloseSolid';
import { getNoteCardDetails } from '../selectors';

const DocumentView = ({
  notesDocsDetails = [],
  setDocumentId,
  from = 'summary',
  setIsOpenDocumentModal = () => { },
  isOpenDocumentModal = false,
  handleConfirmDelete = () => { },
  setNoteCardDetails,
  setIsOneDocumentSelect = () => { },
  noteNo = '',
  enableDelete = false
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();

  const handleDocument = (event, val) => {
    event.stopPropagation();
    setIsOneDocumentSelect({ docId: val?.content?.notesDocumentId || val?.content?.fileId, noteNo });
    setNoteCardDetails(notesDocsDetails);
    setDocumentId({ docId: val?.content?.notesDocumentId || val?.content?.fileId, from: val?.content?.notesDocumentId ? 'note' : 'inward' });
    if (from === 'summary' || from === 'create-draft') {
      setIsOpenDocumentModal(!isOpenDocumentModal);
    } else if (location.pathname.includes('/notes')) {
      navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=0`);
    }
  };

  return (
    notesDocsDetails?.map((item) => (
      <div className="rounded-lg flex items-center font-semibold relative mt-3" key={item?.documentName} style={{ color: dark, background: light }}>
        <Button
          display="flex"
          gap={3}
          variant="unstyled"
          p={3}
          onClick={(event) => handleDocument(event, item)}
          leftIcon={<AttachmentIcon color="#5C6E93" />}
        > <span className="text-[#5C6E93] text-[14px] font-semibold">{item?.documentName} </span>
          {
            !item?.content?.inwardId && enableDelete && <div className="absolute right-[-30px] top-[-20px]"> <IconButton variant="unstyled" onClick={(e) => handleConfirmDelete(item, e)} icon={<CloseSolidIcon color={secondary} />} /> </div>
          }
        </Button>
      </div>
    ))

  );
};

const mapStateToProps = createStructuredSelector({
  noteCardDetails: getNoteCardDetails
});

const mapDispatchToProps = (dispatch) => ({
  setDocumentId: (data) => dispatch(sliceActions.setDocumentId(data)),
  setNoteCardDetails: (data) => dispatch(sliceActions.setNoteCardDetails(data)),
  setIsOneDocumentSelect: (data) => dispatch(commonSliceActions.setIsOneDocumentSelect(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DocumentView);
