import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE } from 'common/constants';
import { ACTION_TYPES } from './actions';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  navigate: {
    active: false,
    to: ''
  },
  backButton: false,
  layout: {
    breadCrumbList: [{ title: 'Home', href: '/' }],
    formTitle: { title: 'welcome', variant: 'normal' },
    columns: { hideSidebar: false, sidebar: 'md:col-span-2', body: 'md:col-span-10' }
  },
  fileHeader: [],
  sidebarData: {
    data: [],
    completedSteps: [],
    activeStep: 0
  },
  commonConfig: {
    stateId: STATE.code,
    currentYear: 2024,
    locale: 'en'
  },
  country: {},
  countryById: {},
  state: {},
  district: {},
  serviceDropdown: {
    data: []
  },
  moduleDropdown: {
    data: []
  },
  subModuleDropdown: {
    data: []
  },
  fileType: {
    data: []
  },
  fileYear: {
    data: []
  },
  ward: {},
  postOffice: {},
  files: {},
  correspondType: {
    data: []
  },
  departments: {},
  seat: {},
  serviceDropdownList: {},
  institutionTypes: {},
  localBody: {},
  institution: {},
  banks: {},
  bankBranches: {},
  education: {},
  gender: {},
  filterParams: {
    modules: null,
    subModule: null,
    service: null,
    department: null,
    seat: null,
    status: null,
    fromDate: null,
    toDate: null,
    page: 0,
    size: 10,
    counterOperator: null,
    fileNo: null,
    search: false,
    sortDirection: 'desc'
  },
  searchListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc'
  },
  localBodyPropertyType: {},
  functionalGroup: {},
  functions: {},
  fileExist: {},
  actionTriggered: { loading: false, id: '' },
  acknowledgeTriggered: false,
  alertAction: {
    open: false,
    variant: 'alert',
    message: '',
    title: ''
  },
  triggerPrint: false,
  activeBlob: null,
  // user employee
  userInfo: {},
  userLocalBody: {},
  userOffices: [],
  documentPreview: null,
  otpStatus: false,
  tableLoader: { loading: false, id: '' },
  registerOpen: false,
  generalFieldValidation: {},
  localBodyTypeDetails: 1,
  inwardNextUserModal: false,
  digitalSignTrigger: false,
  digitalSignConfirmation: {},
  summaryCollapseMenuFlag: true
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setActiveAccordian: (state, { payload }) => {
      _.set(state, 'activeAccordian', payload);
    },
    navigateTo: (state, { payload }) => {
      _.set(state, 'navigate', { active: true, ...payload });
    },
    disableNavigate: (state) => {
      _.set(state, 'navigate.active', false);
    },
    setAddress: (state, { payload }) => {
      _.set(state, `addressData.${payload.type}`, { data: payload?.data, variant: payload?.variant });
    },
    setDistrict: (state, { payload }) => {
      _.set(state, `district.${payload.key}`, payload.value);
    },
    setFormComponentData: (state, { payload }) => {
      _.set(state, 'sidebarData.data', payload.data);
      _.set(state, 'sidebarData.steps', payload.steps);
    },
    setFormTitle: (state, { payload }) => {
      _.set(state, 'layout.formTitle', payload);
    },
    setBackButton: (state, { payload }) => {
      _.set(state, 'backButton', payload);
    },
    setFileHeader: (state, { payload }) => {
      _.set(state, 'fileHeader', payload);
    },
    setSelectedForm: (state, { payload }) => {
      _.set(state, 'sidebarData.selectedForm', payload.title);
    },
    setSidebarStatus: (state, { payload }) => {
      _.set(state, 'sidebarData.completedSteps', payload.completedSteps);
      _.set(state, 'sidebarData.activeStep', payload.activeStep);
      _.set(state, `ward.${payload.key}`, payload.value);
    },
    setFiles: (state, { payload }) => {
      _.set(state, 'files', payload);
    },
    setServicesSearchList: (state, { payload }) => {
      _.set(state, 'servicesSearchList', payload);
    },
    setDepartments: (state, { payload }) => {
      _.set(state, 'departments', payload);
    },
    setSeats: (state, { payload }) => {
      _.set(state, 'seats', payload);
    },
    setStatus: (state, { payload }) => {
      _.set(state, 'status', payload);
    },
    setFilterParams: (state, { payload }) => {
      _.set(state, 'filterParams', payload);
    },
    setSearchListParams: (state, { payload }) => {
      _.set(state, 'searchListParams', payload);
    },
    setCounterOperator: (state, { payload }) => {
      _.set(state, 'CounterOperator', payload);
    },
    clearDropdown: (state) => {
      _.set(state, 'serviceDropdown', initialState.serviceDropdown);
      _.set(state, 'moduleDropdown', initialState.moduleDropdown);
      _.set(state, 'subModuleDropdown', initialState.subModuleDropdown);
    },
    setActionTriggered: (state, { payload }) => {
      _.set(state, 'actionTriggered', payload);
    },
    setAcknowledgeTriggered: (state, { payload }) => {
      _.set(state, 'acknowledgeTriggered', payload);
    },
    setUserInfo: (state, { payload }) => {
      _.set(state, 'userInfo', payload);
    },
    setAlertAction: (state, { payload }) => {
      _.set(state, 'alertAction', payload);
    },
    setSubModulesSearchList: (state, { payload }) => {
      _.set(state, 'subModulesSearchList', payload);
    },

    setEmployeeNameList: (state, { payload }) => {
      _.set(state, 'employeeNameSearchList', payload);
    },
    setNotes: (state, { payload }) => {
      _.set(state, 'notesDetails', payload);
    },
    setUserOffices: (state, { payload }) => {
      _.set(state, 'userOffices', payload);
    },
    setDocumentPreview: (state, { payload }) => {
      _.set(state, 'documentPreview', payload);
    },
    // setDesignation: (state, { payload }) => {
    //   _.set(state, 'designation', payload);
    // },
    setOtpStatus: (state, { payload }) => {
      _.set(state, 'otpStatus', payload);
    },
    setLocale: (state, { payload }) => {
      _.set(state, 'commonConfig.locale', payload);
    },
    setTableLoader: (state, { payload }) => {
      _.set(state, 'tableLoader', payload);
    },
    setTriggerPrint: (state, { payload }) => {
      _.set(state, 'triggerPrint', payload);
    },
    setActiveBlob: (state, { payload }) => {
      _.set(state, 'activeBlob', payload);
    },
    setRegisterOpen: (state, { payload }) => {
      _.set(state, 'registerOpen', payload);
    },
    setGeneralFieldValidation: (state, { payload }) => {
      _.set(state, 'generalFieldValidation', payload);
    },
    setCounterOperatorWithRole: (state, { payload }) => {
      _.set(state, 'CounterOperatorWithRole', payload);
    },
    setLocalBodyType: (state, { payload }) => {
      _.set(state, 'localBodyTypeDetails', payload);
    },
    setInwardNextUserModal: (state, { payload }) => {
      _.set(state, 'inwardNextUserModal', payload);
    },
    setActiveBlobDetails: (state, { payload }) => {
      _.set(state, 'activeBlobDetails', payload);
    },
    setDigitalSignTrigger: (state, { payload }) => {
      _.set(state, 'digitalSignTrigger', payload);
    },
    setDigitalSignConfirmation: (state, { payload }) => {
      _.set(state, 'digitalSignConfirmation', payload);
    },

    setSummaryCollapseMenuFlag: (state, { payload }) => {
      _.set(state, 'summaryCollapseMenuFlag', payload);
    },
    setNoteCardDetails: (state, { payload }) => {
      _.set(state, 'noteCardDetails', payload);
    },
    setDocumentId: (state, { payload }) => {
      _.set(state, 'documentId', payload);
    },
    setDocumentNameFromNoteReferences: (state, { payload }) => {
      _.set(state, 'documentNameFromNoteReferences', payload);
    },
    setIsOneDocumentSelect: (state, { payload }) => {
      _.set(state, 'isOneDocumentSelect', payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.FETCH_STATE_SUCCESS, (state, { payload }) => {
        _.set(state, 'state', payload);
      })
      .addCase(ACTION_TYPES.FETCH_COUNTRY_SUCCESS, (state, { payload }) => {
        _.set(state, 'country', payload);
      })
      .addCase(ACTION_TYPES.FETCH_COUNTRY_BY_ID_SUCCESS, (state, { payload }) => {
        _.set(state, 'countryById', payload);
      })
      .addCase(ACTION_TYPES.FETCH_DISTRICTS_SUCCESS, (state, { payload }) => {
        _.set(state, 'district', payload);
      })
      .addCase(ACTION_TYPES.FETCH_SERVICES_SUCCESS, (state, { payload }) => {
        _.set(state, 'serviceDropdown', payload);
      })
      .addCase(ACTION_TYPES.FETCH_DFMS_SERVICES_SUCCESS, (state, { payload }) => {
        _.set(state, 'serviceDfmsDropdown', payload);
      })
      .addCase(ACTION_TYPES.FETCH_MODULES_SUCCESS, (state, { payload }) => {
        _.set(state, 'moduleDropdown', payload);
      })
      .addCase(ACTION_TYPES.FETCH_SUB_MODULES_SUCCESS, (state, { payload }) => {
        _.set(state, 'subModuleDropdown', payload);
      })
      .addCase(ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SUCCESS, (state, { payload }) => {
        _.set(state, 'subModule', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_MODULES_BY_ID_SUCCESS, (state, { payload }) => {
        _.set(state, 'module', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_FILETYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileType', payload);
      })
      .addCase(ACTION_TYPES.FETCH_FILEYEAR_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileYear', payload);
      })
      .addCase(ACTION_TYPES.FETCH_WARD_SUCCESS, (state, { payload }) => {
        _.set(state, 'ward', payload);
      })
      .addCase(ACTION_TYPES.FETCH_WARD_YEAR_SUCCESS, (state, { payload }) => {
        _.set(state, 'wardYear', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_OFFICE_SUCCESS, (state, { payload }) => {
        _.set(state, 'postOffice', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_OFFICE_BY_PIN_SUCCESS, (state, { payload }) => {
        _.set(state, 'postOffice', payload);
      })
      .addCase(ACTION_TYPES.FETCH_CORRESPONDTYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'correspondType', payload);
      })
      .addCase(ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS, (state, { payload }) => {
        _.set(state, 'departments', payload);
      })
      .addCase(ACTION_TYPES.FETCH_SEAT_SUCCESS, (state, { payload }) => {
        _.set(state, 'seat', payload);
      })
      .addCase(ACTION_TYPES.FETCH_SERVICE_SUCCESS, (state, { payload }) => {
        _.set(state, 'serviceDropdownList', payload);
      })
      .addCase(ACTION_TYPES.FETCH_INSTITUTION_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'institutionTypes', payload);
      })
      .addCase(ACTION_TYPES.FETCH_LOCAL_BODY_SUCCESS, (state, { payload }) => {
        _.set(state, 'localBody', payload);
      })
      .addCase(ACTION_TYPES.FETCH_INSTITUTIONS_SUCCESS, (state, { payload }) => {
        _.set(state, 'institutions', payload);
      })
      .addCase(ACTION_TYPES.FETCH_BANKS_SUCCESS, (state, { payload }) => {
        _.set(state, 'banks', payload);
      })
      .addCase(ACTION_TYPES.FETCH_BRANCHES_BY_BANK_SUCCESS, (state, { payload }) => {
        _.set(state, 'bankBranches', payload);
      })
      .addCase(ACTION_TYPES.FETCH_EDUCATION_SUCCESS, (state, { payload }) => {
        _.set(state, 'education', payload);
      })
      .addCase(ACTION_TYPES.FETCH_GENDER_SUCCESS, (state, { payload }) => {
        _.set(state, 'gender', payload);
      })
      .addCase(ACTION_TYPES.FETCH_LOCAL_BODY_PROPERTY_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'localBodyPropertyType', payload);
      })
      .addCase(ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_SUCCESS, (state, { payload }) => {
        _.set(state, 'functionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_FUNCTIONS_SUCCESS, (state, { payload }) => {
        _.set(state, 'functions', payload);
      })
      .addCase(ACTION_TYPES.CHECK_FILE_NO_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileExist', payload);
      })
      .addCase(ACTION_TYPES.FETCH_LOCAL_BODY_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'localBodyType', payload);
      })
      .addCase(ACTION_TYPES.FETCH_LOCAL_BODY_NAME_SUCCESS, (state, { payload }) => {
        _.set(state, 'localBodyTypeByDBylbCode', payload);
      })
      .addCase(ACTION_TYPES.FETCH_LOCAL_BODY_NAME_APPLICANT_SUCCESS, (state, { payload }) => {
        _.set(state, 'localBodyTypeByDBylbCodeApplicant', payload);
      })
      .addCase(ACTION_TYPES.FETCH_FINANCIAL_STATUS_SUCCESS, (state, { payload }) => {
        _.set(state, 'financialStatus', payload);
      })
      .addCase(ACTION_TYPES.FETCH_CATEGORY_SUCCESS, (state, { payload }) => {
        _.set(state, 'category', payload);
      })
      .addCase(ACTION_TYPES.FETCH_BUILDING_USAGE_SUCCESS, (state, { payload }) => {
        _.set(state, 'buildingUsage', payload);
      })
      .addCase(ACTION_TYPES.FETCH_OWNERSHIP_SUCCESS, (state, { payload }) => {
        _.set(state, 'ownerShip', payload);
      })
      .addCase(ACTION_TYPES.FETCH_LOCAL_BODY_BY_OFFICE_CODE_SUCCESS, (state, { payload }) => {
        _.set(state, 'userLocalBody', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_SERVICE_VALIDATION_SUCCESS, (state, { payload }) => {
        _.set(state, 'serviceValidation', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ROUTE_KEY_SUCCESS, (state, { payload }) => {
        _.set(state, 'routeKey', payload);
      })
      .addCase(ACTION_TYPES.FETCH_SERVICE_ACCOUNT_HEAD_SUCCESS, (state, { payload }) => {
        _.set(state, 'serviceAccountHead', payload?.data);
      })
      .addCase(ACTION_TYPES.GEN_OTP_SUCCESS, (state, { payload }) => {
        _.set(state, 'generateAadharOtp', payload?.data);
      })
      .addCase(ACTION_TYPES.VERIFY_OTP_SUCCESS, (state, { payload }) => {
        _.set(state, 'verifyAadharOtp', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_SMART_PROFILE_SUCCESS, (state, { payload }) => {
        _.set(state, 'smartDetails', payload?.data?.user);
      })
      .addCase(ACTION_TYPES.FETCH_FUNCTIONAL_GROUPS_SUCCESS, (state, { payload }) => {
        _.set(state, 'functionalGroups', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_VILLAGE_SUCCESS, (state, { payload }) => {
        _.set(state, 'village', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_TALUK_OFFICECODE_SUCCESS, (state, { payload }) => {
        _.set(state, 'taluk', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_DOOR_KEY_SUCCESS, (state, { payload }) => {
        _.set(state, 'doorKey', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_BILL_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'billType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ESTABLISHMENT_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'establishmentType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_MISSION_SUCCESS, (state, { payload }) => {
        _.set(state, 'mission', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_PROFESSION_TAX_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'professionalTaxType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_TYPE_OF_AUDIT_SUCCESS, (state, { payload }) => {
        _.set(state, 'typeOfAudit', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_LB_BUILDING_SUCCESS, (state, { payload }) => {
        _.set(state, 'lbBuilding', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_DESIGNATION_SUCCESS, (state, { payload }) => {
        _.set(state, 'designation', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_AMOUNT_FROM_CLAIM_SUCCESS, (state, { payload }) => {
        _.set(state, 'amountFromClaim', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_OCCUPANCY_SUCCESS, (state, { payload }) => {
        _.set(state, 'occupancy', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ESTIMATE_AMOUNT_SUCCESS, (state, { payload }) => {
        _.set(state, 'estimateAmount', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_BUILDUP_AREA_SUCCESS, (state, { payload }) => {
        _.set(state, 'buildUpArea', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_MEETING_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'meetingType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_OFFICE_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'officeType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_FUND_SUCCESS, (state, { payload }) => {
        _.set(state, 'fund', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ACCOUNT_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'accountType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_TREASURY_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'treasuryType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_RECOVERY_ACCOUNT_HEAD_SUCCESS, (state, { payload }) => {
        _.set(state, 'recoveryAccountHead', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_FUNC_GROUPS_SUCCESS, (state, { payload }) => {
        _.set(state, 'employeeNameSearchList', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_HEIGHT_SUCCESS, (state, { payload }) => {
        _.set(state, 'height', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_DOOR_SUCCESS, (state, { payload }) => {
        _.set(state, 'door', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_INWARD_USERS_SUCCESS, (state, { payload }) => {
        _.set(state, 'inwardUsers', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_WASTE_MANAGEMENT_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'wasteManagementType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_SIGN_PDF_SUCCESS, (state, { payload }) => {
        _.set(state, 'digitalSignedPdf', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ACCOUNT_ID_SUCCESS, (state, { payload }) => {
        _.set(state, 'accountId', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_POST_ID_BY_PEN_NO_SUCCESS, (state, { payload }) => {
        _.set(state, 'postIdByPenNoDetails', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_MODE_OF_DISPATCH_SUCCESS, (state, { payload }) => {
        _.set(state, 'modeOfDispatch', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_DISPATCH_CLERK_DETAILS_SUCCESS, (state, { payload }) => {
        _.set(state, 'dispatchClerkDetails', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_LSGI_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'lsgiType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_REGION_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'regionType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_BUILDING_PROJECT_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'buildingProjectType', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ALL_DOCUMENT_TYPES_SUCCESS, (state, { payload }) => {
        _.set(state, 'allDocumentsType', payload?.data);
      });
  }
});
export const { actions, reducer } = slice;

export const { setSidebarStatus, setFormComponentData, setDigitalSignTrigger } = actions;
