import { t } from 'common/components';
import { DRAFT_STATUS } from './constants';

export const applicantName = (data) => {
  const { row = {} } = data;
  if ((row?.addressType === 2 || row?.addressType === 3) && !row?.firstName) {
    return row?.institutionName;
  }
  return `${row?.firstName ? row?.firstName : ''} ${row?.middleName ? row?.middleName : ''} ${row?.lastName ? row?.lastName : ''}`;
};

export const checkType = (data) => {
  const { row = {} } = data;
  if (row?.addressType === 3 && !row?.firstName) {
    return t('institution');
  }
  return t('individual');
};

export const checkExistingApplicantType = (data) => {
  const { row = {} } = data;
  if (row?.firstName) {
    return 'individual';
  } return 'institution';
};

export const checkExistingApplicantTypeDisplay = (data) => {
  const { row = {} } = data;
  if (row?.firstName) {
    return t('individual');
  } return t('institution');
};

export const getBackgroundColorForDrafts = (data) => {
  switch (data) {
    case DRAFT_STATUS.CREATED:
      return 'rgba(188, 226, 247, 0.5)';
    case DRAFT_STATUS.VERIFIED:
      return 'rgba(251, 226, 255, 0.5)';
    case DRAFT_STATUS.APPROVED:
      return 'rgba(221, 246, 215, 1)';
    case DRAFT_STATUS.RECOMMENDED:
      return 'rgba(222, 215, 255, 1)';
    case DRAFT_STATUS.PENDING:
      return 'rgba(254, 245, 225, 1)';
    case DRAFT_STATUS.RETURNED:
      return 'rgba(76, 39, 25, 0.25)';
    case DRAFT_STATUS.REJECTED:
      return 'rgba(254, 212, 212, 1)';
    default:
      return 'rgba(188, 226, 247, 0.5)';
  }
};

export const getColorForDrafts = (data) => {
  switch (data) {
    case DRAFT_STATUS.CREATED:
      return '#00B2EB';
    case DRAFT_STATUS.VERIFIED:
      return '#E83A7A';
    case DRAFT_STATUS.APPROVED:
      return '#60b975';
    case DRAFT_STATUS.RECOMMENDED:
      return '#7B61FF';
    case DRAFT_STATUS.PENDING:
      return '#FFB200';
    case DRAFT_STATUS.RETURNED:
      return '#4C2719';
    case DRAFT_STATUS.REJECTED:
      return '#FC5555';
    default:
      return '#00B2EB';
  }
};
