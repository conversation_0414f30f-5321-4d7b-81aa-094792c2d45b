import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_USER_PROFILE: `${STATE_REDUCER_KEY}/FETCH_USER_PROFILE`,
  FETCH_USER_PROFILE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_USER_PROFILE_REQUEST`,
  FETCH_USER_PROFILE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_USER_PROFILE_SUCCESS`,
  FETCH_USER_PROFILE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_USER_PROFILE_FAILURE`,

  FETCH_SMART_PROFILE: `${STATE_REDUCER_KEY}/FETCH_SMART_PROFILE`,
  FETCH_SMART_PROFILE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SMART_PROFILE_REQUEST`,
  FETCH_SMART_PROFILE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SMART_PROFILE_SUCCESS`,
  FETCH_SMART_PROFILE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SMART_PROFILE_FAILURE`,

  FETCH_COUNTRY: `${STATE_REDUCER_KEY}/FETCH_COUNTRY`,
  FETCH_COUNTRY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_REQUEST`,
  FETCH_COUNTRY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_SUCCESS`,
  FETCH_COUNTRY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_FAILURE`,

  FETCH_COUNTRY_BY_ID: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_BY_ID`,
  FETCH_COUNTRY_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_BY_ID_REQUEST`,
  FETCH_COUNTRY_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_BY_ID_SUCCESS`,
  FETCH_COUNTRY_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_COUNTRY_BY_ID_FAILURE`,

  FETCH_STATE: `${STATE_REDUCER_KEY}/FETCH_STATE`,
  FETCH_STATE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_STATE_REQUEST`,
  FETCH_STATE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_STATE_SUCCESS`,
  FETCH_STATE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_STATE_FAILURE`,

  FETCH_DISTRICTS: `${STATE_REDUCER_KEY}/FETCH_DISTRICTS`,
  FETCH_DISTRICTS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DISTRICTS_REQUEST`,
  FETCH_DISTRICTS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DISTRICTS_SUCCESS`,
  FETCH_DISTRICTS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DISTRICTS_FAILURE`,

  FETCH_SERVICES: `${STATE_REDUCER_KEY}/FETCH_SERVICES`,
  FETCH_SERVICES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICES_REQUEST`,
  FETCH_SERVICES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICES_SUCCESS`,
  FETCH_SERVICES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICES_FAILURE`,

  FETCH_DFMS_SERVICES: `${STATE_REDUCER_KEY}/FETCH_DFMS_SERVICES`,
  FETCH_DFMS_SERVICES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DFMS_SERVICES_REQUEST`,
  FETCH_DFMS_SERVICES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DFMS_SERVICES_SUCCESS`,
  FETCH_DFMS_SERVICES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DFMS_SERVICES_FAILURE`,

  FETCH_MODULES: `${STATE_REDUCER_KEY}/FETCH_MODULES`,
  FETCH_MODULES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MODULES_REQUEST`,
  FETCH_MODULES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MODULES_SUCCESS`,
  FETCH_MODULES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MODULES_FAILURE`,

  FETCH_SUB_MODULES: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES`,
  FETCH_SUB_MODULES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_REQUEST`,
  FETCH_SUB_MODULES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_SUCCESS`,
  FETCH_SUB_MODULES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_FAILURE`,

  FETCH_FILETYPE: `${STATE_REDUCER_KEY}/FETCH_FILETYPE`,
  FETCH_FILETYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILETYPE_REQUEST`,
  FETCH_FILETYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILETYPE_SUCCESS`,
  FETCH_FILETYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILETYPE_FAILURE`,

  FETCH_WARD: `${STATE_REDUCER_KEY}/FETCH_WARD`,
  FETCH_WARD_REQUEST: `${STATE_REDUCER_KEY}/FETCH_WARD_REQUEST`,
  FETCH_WARD_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_WARD_SUCCESS`,
  FETCH_WARD_FAILURE: `${STATE_REDUCER_KEY}/FETCH_WARD_FAILURE`,

  FETCH_WARD_YEAR: `${STATE_REDUCER_KEY}/FETCH_WARD_YEAR`,
  FETCH_WARD_YEAR_REQUEST: `${STATE_REDUCER_KEY}/FETCH_WARD_YEAR_REQUEST`,
  FETCH_WARD_YEAR_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_WARD_YEAR_SUCCESS`,
  FETCH_WARD_YEAR_FAILURE: `${STATE_REDUCER_KEY}/FETCH_WARD_YEAR_FAILURE`,

  FETCH_POST_OFFICE: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE`,
  FETCH_POST_OFFICE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_REQUEST`,
  FETCH_POST_OFFICE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_SUCCESS`,
  FETCH_POST_OFFICE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_FAILURE`,

  FETCH_POST_OFFICE_BY_PIN: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_BY_PIN`,
  FETCH_POST_OFFICE_BY_PIN_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_BY_PIN_REQUEST`,
  FETCH_POST_OFFICE_BY_PIN_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_BY_PIN_SUCCESS`,
  FETCH_POST_OFFICE_BY_PIN_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_OFFICE_BY_PIN_FAILURE`,

  FETCH_FILES: `${STATE_REDUCER_KEY}/FETCH_FILES`,
  FETCH_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILES_REQUEST`,
  FETCH_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILES_SUCCESS`,
  FETCH_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILES_FAILURE`,

  FETCH_FILEYEAR: `${STATE_REDUCER_KEY}/FETCH_FILEYEAR`,
  FETCH_FILEYEAR_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILEYEAR_REQUEST`,
  FETCH_FILEYEAR_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILEYEAR_SUCCESS`,
  FETCH_FILEYEAR_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILEYEAR_FAILURE`,

  FETCH_CORRESPONDTYPE: `${STATE_REDUCER_KEY}/FETCH_CORRESPONDTYPE`,
  FETCH_CORRESPONDTYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_CORRESPONDTYPE_REQUEST`,
  FETCH_CORRESPONDTYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_CORRESPONDTYPE_SUCCESS`,
  FETCH_CORRESPONDTYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_CORRESPONDTYPE_FAILURE`,

  FETCH_DEPARTMENTS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS`,
  FETCH_DEPARTMENTS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_REQUEST`,
  FETCH_DEPARTMENTS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_SUCCESS`,
  FETCH_DEPARTMENTS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FAILURE`,

  FETCH_SEAT: `${STATE_REDUCER_KEY}/FETCH_SEAT`,
  FETCH_SEAT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SEAT_REQUEST`,
  FETCH_SEAT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SEAT_SUCCESS`,
  FETCH_SEAT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SEAT_FAILURE`,

  FETCH_SERVICE: `${STATE_REDUCER_KEY}/FETCH_SERVICES`,
  FETCH_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_REQUEST`,
  FETCH_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_SUCCESS`,
  FETCH_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_FAILURE`,

  FETCH_INSTITUTION_TYPE: `${STATE_REDUCER_KEY}/FETCH_INSTITUTION_TYPE`,
  FETCH_INSTITUTION_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INSTITUTION_TYPE_REQUEST`,
  FETCH_INSTITUTION_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INSTITUTION_TYPE_SUCCESS`,
  FETCH_INSTITUTION_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INSTITUTION_TYPE_FAILURE`,

  FETCH_LOCAL_BODY: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY`,
  FETCH_LOCAL_BODY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_REQUEST`,
  FETCH_LOCAL_BODY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_SUCCESS`,
  FETCH_LOCAL_BODY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_FAILURE`,

  FETCH_INSTITUTIONS: `${STATE_REDUCER_KEY}/FETCH_INSTITUTIONS`,
  FETCH_INSTITUTIONS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INSTITUTIONS_REQUEST`,
  FETCH_INSTITUTIONS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INSTITUTIONS_SUCCESS`,
  FETCH_INSTITUTIONS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INSTITUTIONS_FAILURE`,

  FETCH_BANKS: `${STATE_REDUCER_KEY}/FETCH_BANKS`,
  FETCH_BANKS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BANKS_REQUEST`,
  FETCH_BANKS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BANKS_SUCCESS`,
  FETCH_BANKS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BANKS_FAILURE`,

  FETCH_BRANCHES_BY_BANK: `${STATE_REDUCER_KEY}/FETCH_BRANCHES_BY_BANK`,
  FETCH_BRANCHES_BY_BANK_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BRANCHES_BY_BANK_REQUEST`,
  FETCH_BRANCHES_BY_BANK_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BRANCHES_BY_BANK_SUCCESS`,
  FETCH_BRANCHES_BY_BANK_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BRANCHES_BY_BANK_FAILURE`,

  FETCH_EDUCATION: `${STATE_REDUCER_KEY}/FETCH_EDUCATION`,
  FETCH_EDUCATION_REQUEST: `${STATE_REDUCER_KEY}/FETCH_EDUCATION_REQUEST`,
  FETCH_EDUCATION_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_EDUCATION_SUCCESS`,
  FETCH_EDUCATION_FAILURE: `${STATE_REDUCER_KEY}/FETCH_EDUCATION_FAILURE`,

  FETCH_GENDER: `${STATE_REDUCER_KEY}/FETCH_GENDER`,
  FETCH_GENDER_REQUEST: `${STATE_REDUCER_KEY}/FETCH_GENDER_REQUEST`,
  FETCH_GENDER_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_GENDER_SUCCESS`,
  FETCH_GENDER_FAILURE: `${STATE_REDUCER_KEY}/FETCH_GENDER_FAILURE`,

  DELETE_DOCUMENTS: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS`,
  DELETE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS_REQUEST`,
  DELETE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS_SUCCESS`,
  DELETE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS_FAILURE`,

  DELETE_NOTE_DOCUMENTS: `${STATE_REDUCER_KEY}/DELETE_NOTE_DOCUMENTS`,
  DELETE_NOTE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/DELETE_NOTE_DOCUMENTS_REQUEST`,
  DELETE_NOTE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_NOTE_DOCUMENTS_SUCCESS`,
  DELETE_NOTE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/DELETE_NOTE_DOCUMENTS_FAILURE`,

  FETCH_SUB_MODULES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID`,
  FETCH_SUB_MODULES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_REQUEST`,
  FETCH_SUB_MODULES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_SUCCESS`,
  FETCH_SUB_MODULES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_FAILURE`,

  FETCH_SERVICES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID`,
  FETCH_SERVICES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_REQUEST`,
  FETCH_SERVICES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_SUCCESS`,
  FETCH_SERVICES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_FAILURE`,

  FETCH_DEPARTMENTS_FOR_FILTER: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FOR_FILTER`,
  FETCH_DEPARTMENTS_FOR_FILTER_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FOR_FILTER_REQUEST`,
  FETCH_DEPARTMENTS_FOR_FILTER_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FOR_FILTER_SUCCESS`,
  FETCH_DEPARTMENTS_FOR_FILTER_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FOR_FILTER_FAILURE`,

  FETCH_SEATS: `${STATE_REDUCER_KEY}/FETCH_SEATS`,
  FETCH_SEATS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SEATS_REQUEST`,
  FETCH_SEATS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SEATS_SUCCESS`,
  FETCH_SEATS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SEATS_FAILURE`,

  FETCH_STATUS: `${STATE_REDUCER_KEY}/FETCH_STATUS`,
  FETCH_STATUS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_STATUS_REQUEST`,
  FETCH_STATUS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_STATUS_SUCCESS`,
  FETCH_STATUS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_STATUS_FAILURE`,

  FETCH_COUNTER_OPERATOR: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR`,
  FETCH_COUNTER_OPERATOR_REQUEST: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_REQUEST`,
  FETCH_COUNTER_OPERATOR_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_SUCCESS`,
  FETCH_COUNTER_OPERATOR_FAILURE: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_FAILURE`,

  FETCH_LOCAL_BODY_PROPERTY_TYPE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_PROPERTY_TYPE`,
  FETCH_LOCAL_BODY_PROPERTY_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_PROPERTY_TYPE_REQUEST`,
  FETCH_LOCAL_BODY_PROPERTY_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_PROPERTY_TYPE_SUCCESS`,
  FETCH_LOCAL_BODY_PROPERTY_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_PROPERTY_TYPE_FAILURE`,

  FETCH_FUNCTIONAL_GROUP: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP`,
  FETCH_FUNCTIONAL_GROUP_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP_REQUEST`,
  FETCH_FUNCTIONAL_GROUP_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP_SUCCESS`,
  FETCH_FUNCTIONAL_GROUP_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP_FAILURE`,

  FETCH_FUNCTIONS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS`,
  FETCH_FUNCTIONS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS_REQUEST`,
  FETCH_FUNCTIONS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS_SUCCESS`,
  FETCH_FUNCTIONS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS_FAILURE`,

  CHECK_FILE_NO: `${STATE_REDUCER_KEY}/CHECK_FILE_NO`,
  CHECK_FILE_NO_REQUEST: `${STATE_REDUCER_KEY}/CHECK_FILE_NO_REQUEST`,
  CHECK_FILE_NO_SUCCESS: `${STATE_REDUCER_KEY}/CHECK_FILE_NO_SUCCESS`,
  CHECK_FILE_NO_FAILURE: `${STATE_REDUCER_KEY}/CHECK_FILE_NO_FAILURE`,

  FETCH_LOCAL_BODY_TYPE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_TYPE`,
  FETCH_LOCAL_BODY_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_TYPE_REQUEST`,
  FETCH_LOCAL_BODY_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_TYPE_SUCCESS`,
  FETCH_LOCAL_BODY_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_TYPE_FAILURE`,

  FETCH_LOCAL_BODY_NAME: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME`,
  FETCH_LOCAL_BODY_NAME_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME_REQUEST`,
  FETCH_LOCAL_BODY_NAME_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME_SUCCESS`,
  FETCH_LOCAL_BODY_NAME_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME_FAILURE`,

  FETCH_LOCAL_BODY_NAME_APPLICANT: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_APPLICANT_NAME`,
  FETCH_LOCAL_BODY_NAME_APPLICANT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME_APPLICANT_REQUEST`,
  FETCH_LOCAL_BODY_NAME_APPLICANT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME_APPLICANT_SUCCESS`,
  FETCH_LOCAL_BODY_NAME_APPLICANT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_NAME_APPLICANT_FAILURE`,

  FETCH_FINANCIAL_STATUS: `${STATE_REDUCER_KEY}/FETCH_FINANCIAL_STATUS`,
  FETCH_FINANCIAL_STATUS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FINANCIAL_STATUS_REQUEST`,
  FETCH_FINANCIAL_STATUS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FINANCIAL_STATUS_SUCCESS`,
  FETCH_FINANCIAL_STATUS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FINANCIAL_STATUS_FAILURE`,

  FETCH_CATEGORY: `${STATE_REDUCER_KEY}/FETCH_CATEGORY`,
  FETCH_CATEGORY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_CATEGORY_REQUEST`,
  FETCH_CATEGORY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_CATEGORY_SUCCESS`,
  FETCH_CATEGORY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_CATEGORY_FAILURE`,

  FETCH_BUILDUP_AREA: `${STATE_REDUCER_KEY}/FETCH_BUILDUP_AREA`,
  FETCH_BUILDUP_AREA_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BUILDUP_AREA_REQUEST`,
  FETCH_BUILDUP_AREA_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BUILDUP_AREA_SUCCESS`,
  FETCH_BUILDUP_AREA_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BUILDUP_AREA_FAILURE`,

  FETCH_BUILDING_USAGE: `${STATE_REDUCER_KEY}/FETCH_BUILDING_USAGE`,
  FETCH_BUILDING_USAGE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BUILDING_USAGE_REQUEST`,
  FETCH_BUILDING_USAGE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BUILDING_USAGE_SUCCESS`,
  FETCH_BUILDING_USAGE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BUILDING_USAGE_FAILURE`,

  FETCH_OWNERSHIP: `${STATE_REDUCER_KEY}/FETCH_OWNERSHIP`,
  FETCH_OWNERSHIP_REQUEST: `${STATE_REDUCER_KEY}FETCH_OWNERSHIP_REQUEST`,
  FETCH_OWNERSHIP_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_OWNERSHIP_SUCCESS`,
  FETCH_OWNERSHIP_FAILURE: `${STATE_REDUCER_KEY}/FETCH_OWNERSHIP_FAILURE`,

  FETCH_SUB_MODULES_BY_MODULE_ID: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID`,
  FETCH_SUB_MODULES_BY_MODULE_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID_REQUEST`,
  FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS`,
  FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE`,

  FETCH_EMPLOYEE_NAME_BY_ID: `${STATE_REDUCER_KEY}/FETCH_EMPLOYEE_NAME_BY_ID`,
  FETCH_EMPLOYEE_NAME_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_EMPLOYEE_NAME_BY_ID_REQUEST`,
  FETCH_EMPLOYEE_NAME_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_EMPLOYEE_NAME_BY_ID_SUCCESS`,
  FETCH_EMPLOYEE_NAME_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_EMPLOYEE_NAME_BY_ID_FAILURE`,

  FETCH_LOCAL_BODY_BY_OFFICE_CODE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_BY_OFFICE_CODE`,
  FETCH_LOCAL_BODY_BY_OFFICE_CODE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_EMPLOYEE_NAME_BY_ID_REQUEST`,
  FETCH_LOCAL_BODY_BY_OFFICE_CODE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_BY_OFFICE_CODE_SUCCESS`,
  FETCH_LOCAL_BODY_BY_OFFICE_CODE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LOCAL_BODY_BY_OFFICE_CODE_FAILURE`,

  FETCH_SERVICE_VALIDATION: `${STATE_REDUCER_KEY}/FETCH_SERVICE_VALIDATION`,
  FETCH_SERVICE_VALIDATION_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_VALIDATION_REQUEST`,
  FETCH_SERVICE_VALIDATION_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_VALIDATION_SUCCESS`,
  FETCH_SERVICE_VALIDATION_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_VALIDATION_FAILURE`,

  FETCH_DESIGNATION: `${STATE_REDUCER_KEY}/FETCH_DESIGNATION`,
  FETCH_DESIGNATION_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DESIGNATION_REQUEST`,
  FETCH_DESIGNATION_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DESIGNATION_SUCCESS`,
  FETCH_DESIGNATION_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DESIGNATION_FAILURE`,

  FETCH_ROUTE_KEY: `${STATE_REDUCER_KEY}/FETCH_ROUTE_KEY`,
  FETCH_ROUTE_KEY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ROUTE_KEY_REQUEST`,
  FETCH_ROUTE_KEY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ROUTE_KEY_SUCCESS`,
  FETCH_ROUTE_KEY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ROUTE_KEY_FAILURE`,

  FETCH_SERVICE_ACCOUNT_HEAD: `${STATE_REDUCER_KEY}/FETCH_SERVICE_ACCOUNT_HEAD`,
  FETCH_SERVICE_ACCOUNT_HEAD_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_ACCOUNT_HEAD_REQUEST`,
  FETCH_SERVICE_ACCOUNT_HEAD_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_ACCOUNT_HEAD_SUCCESS`,
  FETCH_SERVICE_ACCOUNT_HEAD_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_ACCOUNT_HEAD_FAILURE`,

  GEN_OTP: `${STATE_REDUCER_KEY}/GEN_OTP`,
  GEN_OTP_REQUEST: `${STATE_REDUCER_KEY}/GEN_OTP_REQUEST`,
  GEN_OTP_SUCCESS: `${STATE_REDUCER_KEY}/GEN_OTP_SUCCESS`,
  GEN_OTP_FAILURE: `${STATE_REDUCER_KEY}/GEN_OTP_FAILURE`,

  VERIFY_OTP: `${STATE_REDUCER_KEY}/VERIFY_OTP`,
  VERIFY_OTP_REQUEST: `${STATE_REDUCER_KEY}/VERIFY_OTP_REQUEST`,
  VERIFY_OTP_SUCCESS: `${STATE_REDUCER_KEY}/VERIFY_OTP_SUCCESS`,
  VERIFY_OTP_FAILURE: `${STATE_REDUCER_KEY}/VERIFY_OTP_FAILURE`,

  FETCH_FUNCTIONAL_GROUPS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUPS`,
  FETCH_FUNCTIONAL_GROUPS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUPS_REQUEST`,
  FETCH_FUNCTIONAL_GROUPS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUPS_SUCCESS`,
  FETCH_FUNCTIONAL_GROUPS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUPS_FAILURE`,

  FETCH_TALUK_OFFICECODE: `${STATE_REDUCER_KEY}/FETCH_TALUK_OFFICECODE`,
  FETCH_TALUK_OFFICECODE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_TALUK_OFFICECODE_REQUEST`,
  FETCH_TALUK_OFFICECODE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_TALUK_OFFICECODE_SUCCESS`,
  FETCH_TALUK_OFFICECODE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_TALUK_OFFICECODE_FAILURE`,

  FETCH_VILLAGE: `${STATE_REDUCER_KEY}/FETCH_VILLAGE_OFFICECODE`,
  FETCH_VILLAGE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_VILLAGE_REQUEST`,
  FETCH_VILLAGE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_VILLAGE_SUCCESS`,
  FETCH_VILLAGE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_VILLAGE_FAILURE`,

  FETCH_DOOR: `${STATE_REDUCER_KEY}/FETCH_DOOR`,
  FETCH_DOOR_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DOOR_REQUEST`,
  FETCH_DOOR_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DOOR_SUCCESS`,
  FETCH_DOOR_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DOOR_FAILURE`,

  FETCH_DOOR_KEY: `${STATE_REDUCER_KEY}/FETCH_DOOR_KEY`,
  FETCH_DOOR_KEY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DOOR_KEY_REQUEST`,
  FETCH_DOOR_KEY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DOOR_KEY_SUCCESS`,
  FETCH_DOOR_KEY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DOOR_KEY_FAILURE`,

  LOGOUT_USER: `${STATE_REDUCER_KEY}/LOGOUT_USER`,

  FETCH_COUNTER_OPERATOR_WITH_ROLE: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_WITH_ROLE`,
  FETCH_COUNTER_OPERATOR_WITH_ROLE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_WITH_ROLE_REQUEST`,
  FETCH_COUNTER_OPERATOR_WITH_ROLE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_WITH_ROLE_SUCCESS`,
  FETCH_COUNTER_OPERATOR_WITH_ROLE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_COUNTER_OPERATOR_WITH_ROLE_FAILURE`,

  FETCH_BILL_TYPE: `${STATE_REDUCER_KEY}/FETCH_BILL_TYPE`,
  FETCH_BILL_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BILL_TYPE_REQUEST`,
  FETCH_BILL_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BILL_TYPE_SUCCESS`,
  FETCH_BILL_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BILL_TYPE_FAILURE`,

  FETCH_ESTABLISHMENT_TYPE: `${STATE_REDUCER_KEY}/FETCH_ESTABLISHMENT_TYPE`,
  FETCH_ESTABLISHMENT_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ESTABLISHMENT_TYPE_REQUEST`,
  FETCH_ESTABLISHMENT_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ESTABLISHMENT_TYPE_SUCCESS`,
  FETCH_ESTABLISHMENT_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ESTABLISHMENT_TYPE_FAILURE`,

  FETCH_MISSION: `${STATE_REDUCER_KEY}/FETCH_MISSION`,
  FETCH_MISSION_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MISSION_REQUEST`,
  FETCH_MISSION_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MISSION_SUCCESS`,
  FETCH_MISSION_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MISSION_FAILURE`,

  FETCH_PROFESSION_TAX_TYPE: `${STATE_REDUCER_KEY}/FETCH_PROFESSION_TAX_TYPE`,
  FETCH_PROFESSION_TAX_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PROFESSION_TAX_TYPE_REQUEST`,
  FETCH_PROFESSION_TAX_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PROFESSION_TAX_TYPE_SUCCESS`,
  FETCH_PROFESSION_TAX_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PROFESSION_TAX_TYPE_FAILURE`,

  FETCH_TYPE_OF_AUDIT: `${STATE_REDUCER_KEY}/FETCH_TYPE_OF_AUDIT`,
  FETCH_TYPE_OF_AUDIT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_TYPE_OF_AUDIT_REQUEST`,
  FETCH_TYPE_OF_AUDIT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_TYPE_OF_AUDIT_SUCCESS`,
  FETCH_TYPE_OF_AUDIT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_TYPE_OF_AUDIT_FAILURE`,

  FETCH_LB_BUILDING: `${STATE_REDUCER_KEY}/FETCH_LB_BUILDING`,
  FETCH_LB_BUILDING_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LB_BUILDING_REQUEST`,
  FETCH_LB_BUILDING_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LB_BUILDING_SUCCESS`,
  FETCH_LB_BUILDING_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LB_BUILDING_FAILURE`,

  FETCH_AMOUNT_FROM_CLAIM: `${STATE_REDUCER_KEY}/FETCH_AMOUNT_FROM_CLAIM`,
  FETCH_AMOUNT_FROM_CLAIM_REQUEST: `${STATE_REDUCER_KEY}/FETCH_AMOUNT_FROM_CLAIM_REQUEST`,
  FETCH_AMOUNT_FROM_CLAIM_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_AMOUNT_FROM_CLAIM_SUCCESS`,
  FETCH_AMOUNT_FROM_CLAIM_FAILURE: `${STATE_REDUCER_KEY}/FETCH_AMOUNT_FROM_CLAIM_FAILURE`,

  FETCH_ESTIMATE_AMOUNT: `${STATE_REDUCER_KEY}/FETCH_ESTIMATE_AMOUNT`,
  FETCH_ESTIMATE_AMOUNT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ESTIMATE_AMOUNT_REQUEST`,
  FETCH_ESTIMATE_AMOUNT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ESTIMATE_AMOUNT_SUCCESS`,
  FETCH_ESTIMATE_AMOUNT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ESTIMATE_AMOUNT_FAILURE`,

  FETCH_OCCUPANCY: `${STATE_REDUCER_KEY}/FETCH_OCCUPANCY`,
  FETCH_OCCUPANCY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_OCCUPANCY_REQUEST`,
  FETCH_OCCUPANCY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_OCCUPANCY_SUCCESS`,
  FETCH_OCCUPANCY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_OCCUPANCY_FAILURE`,

  FETCH_MEETING_TYPE: `${STATE_REDUCER_KEY}/FETCH_MEETING_TYPE`,
  FETCH_MEETING_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MEETING_TYPE_REQUEST`,
  FETCH_MEETING_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MEETING_TYPE_SUCCESS`,
  FETCH_MEETING_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MEETING_TYPE_FAILURE`,

  FETCH_OFFICE_TYPE: `${STATE_REDUCER_KEY}/FETCH_OFFICE_TYPE`,
  FETCH_OFFICE_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_OFFICE_TYPE_REQUEST`,
  FETCH_OFFICE_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_OFFICE_TYPE_SUCCESS`,
  FETCH_OFFICE_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_OFFICE_TYPE_FAILURE`,

  FETCH_FUND: `${STATE_REDUCER_KEY}/FETCH_FUND`,
  FETCH_FUND_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FUND_REQUEST`,
  FETCH_FUND_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FUND_SUCCESS`,
  FETCH_FUND_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FUND_FAILURE`,

  FETCH_MODULES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_BY_ID_MODULES`,
  FETCH_MODULES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MODULES_BY_ID_REQUEST`,
  FETCH_MODULES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MODULES_BY_ID_SUCCESS`,
  FETCH_MODULES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MODULES_BY_ID_FAILURE`,

  FETCH_VERIFICATION: `${STATE_REDUCER_KEY}/FETCH_VERIFICATION`,
  FETCH_VERIFICATION_REQUEST: `${STATE_REDUCER_KEY}/FETCH_VERIFICATION_REQUEST`,
  FETCH_VERIFICATION_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_VERIFICATION_SUCCESS`,
  FETCH_VERIFICATION_FAILURE: `${STATE_REDUCER_KEY}/FETCH_VERIFICATION_FAILURE`,

  LOGIN_CITIZEN: `${STATE_REDUCER_KEY}/LOGIN_CITIZEN`,
  LOGIN_CITIZEN_REQUEST: `${STATE_REDUCER_KEY}/LOGIN_CITIZEN_REQUEST`,
  LOGIN_CITIZEN_SUCCESS: `${STATE_REDUCER_KEY}/LOGIN_CITIZEN_SUCCESS`,
  LOGIN_CITIZEN_FAILURE: `${STATE_REDUCER_KEY}/LOGIN_CITIZEN_FAILURE`,

  SEND_OTP: `${STATE_REDUCER_KEY}/SEND_OTP`,
  SEND_OTP_REQUEST: `${STATE_REDUCER_KEY}/SEND_OTP_REQUEST`,
  SEND_OTP_SUCCESS: `${STATE_REDUCER_KEY}/SEND_OTP_SUCCESS`,
  SEND_OTP_FAILURE: `${STATE_REDUCER_KEY}/SEND_OTP_FAILURE`,

  FETCH_ACCOUNT_TYPE: `${STATE_REDUCER_KEY}/ACCOUNT_TYPE`,
  FETCH_ACCOUNT_TYPE_REQUEST: `${STATE_REDUCER_KEY}/ACCOUNT_TYPE_REQUEST`,
  FETCH_ACCOUNT_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/ACCOUNT_TYPE_SUCCESS`,
  FETCH_ACCOUNT_TYPE_FAILURE: `${STATE_REDUCER_KEY}/ACCOUNT_TYPE_FAILURE`,

  FETCH_TREASURY_TYPE: `${STATE_REDUCER_KEY}/FETCH_TREASURY_TYPE`,
  FETCH_TREASURY_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_TREASURY_TYPE_REQUEST`,
  FETCH_TREASURY_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_TREASURY_TYPE_SUCCESS`,
  FETCH_TREASURY_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_TREASURY_TYPE_FAILURE`,

  FETCH_RECOVERY_ACCOUNT_HEAD: `${STATE_REDUCER_KEY}/FETCH_RECOVERY_ACCOUNT_HEAD`,
  FETCH_RECOVERY_ACCOUNT_HEAD_REQUEST: `${STATE_REDUCER_KEY}/FETCH_RECOVERY_ACCOUNT_HEAD_REQUEST`,
  FETCH_RECOVERY_ACCOUNT_HEAD_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_RECOVERY_ACCOUNT_HEAD_SUCCESS`,
  FETCH_RECOVERY_ACCOUNT_HEAD_FAILURE: `${STATE_REDUCER_KEY}/FETCH_RECOVERY_ACCOUNT_HEAD_FAILURE`,

  FETCH_POST_BY_FUNC_GROUPS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUPS`,
  FETCH_POST_BY_FUNC_GROUPS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUPS_REQUEST`,
  FETCH_POST_BY_FUNC_GROUPS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUPS_SUCCESS`,
  FETCH_POST_BY_FUNC_GROUPS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUPS_FAILURE`,

  FETCH_HEIGHT: `${STATE_REDUCER_KEY}/FETCH_HEIGHT_GROUPS`,
  FETCH_HEIGHT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_HEIGHT_REQUEST`,
  FETCH_HEIGHT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_HEIGHT_SUCCESS`,
  FETCH_HEIGHT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_HEIGHT_FAILURE`,

  FETCH_INWARD_USERS: `${STATE_REDUCER_KEY}/FETCH_INWARD_USERS`,
  FETCH_INWARD_USERS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INWARD_USERS_REQUEST`,
  FETCH_INWARD_USERS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INWARD_USERST_SUCCESS`,
  FETCH_INWARD_USERS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INWARD_USERST_FAILURE`,

  FETCH_WASTE_MANAGEMENT_TYPE: `${STATE_REDUCER_KEY}/FETCH_WASTE_MANAGEMENT_TYPE`,
  FETCH_WASTE_MANAGEMENT_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_WASTE_MANAGEMENT_TYPE_REQUEST`,
  FETCH_WASTE_MANAGEMENT_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_WASTE_MANAGEMENT_TYPE_SUCCESS`,
  FETCH_WASTE_MANAGEMENT_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_WASTE_MANAGEMENT_TYPE_FAILURE`,

  FETCH_SIGN_PDF: `${STATE_REDUCER_KEY}/FETCH_SIGN_PDF`,
  FETCH_SIGN_PDF_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SIGN_PDF_REQUEST`,
  FETCH_SIGN_PDF_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SIGN_PDF_SUCCESS`,
  FETCH_SIGN_PDF_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SIGN_PDF_FAILURE`,

  SAVE_SIGNED_DRAFT: `${STATE_REDUCER_KEY}/SAVE_SIGNED_DRAFT`,
  SAVE_SIGNED_DRAFT_REQUEST: `${STATE_REDUCER_KEY}/SAVE_SIGNED_DRAFT_REQUEST`,
  SAVE_SIGNED_DRAFT_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_SIGNED_DRAFT_SUCCESS`,
  SAVE_SIGNED_DRAFT_FAILURE: `${STATE_REDUCER_KEY}/SAVE_SIGNED_DRAFT_FAILURE`,

  FETCH_ACCOUNT_ID: `${STATE_REDUCER_KEY}/FETCH_ACCOUNT_ID`,
  FETCH_ACCOUNT_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ACCOUNT_ID_REQUEST`,
  FETCH_ACCOUNT_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ACCOUNT_ID_SUCCESS`,
  FETCH_ACCOUNT_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ACCOUNT_ID_FAILURE`,

  FETCH_POST_ID_BY_PEN_NO: `${STATE_REDUCER_KEY}/FETCH_POST_ID_BY_PEN_NO`,
  FETCH_POST_ID_BY_PEN_NO_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_ID_BY_PEN_NO_REQUEST`,
  FETCH_POST_ID_BY_PEN_NO_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_ID_BY_PEN_NO_SUCCESS`,
  FETCH_POST_ID_BY_PEN_NO_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_ID_BY_PEN_NO_FAILURE`,

  FETCH_MODE_OF_DISPATCH: `${STATE_REDUCER_KEY}/FETCH_MODE_OF_DISPATCH`,
  FETCH_MODE_OF_DISPATCH_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MODE_OF_DISPATCH_REQUEST`,
  FETCH_MODE_OF_DISPATCH_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MODE_OF_DISPATCH_SUCCESS`,
  FETCH_MODE_OF_DISPATCH_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MODE_OF_DISPATCH_FAILURE`,

  FETCH_DISPATCH_CLERK_DETAILS: `${STATE_REDUCER_KEY}/FETCH_DISPATCH_CLERK_DETAILS`,
  FETCH_DISPATCH_CLERK_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DISPATCH_CLERK_DETAILS_REQUEST`,
  FETCH_DISPATCH_CLERK_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DISPATCH_CLERK_DETAILS_SUCCESS`,
  FETCH_DISPATCH_CLERK_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DISPATCH_CLERK_DETAILS_FAILURE`,

  FETCH_LSGI_TYPE: `${STATE_REDUCER_KEY}/FETCH_LSGI_TYPE`,
  FETCH_LSGI_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LSGI_TYPE_REQUEST`,
  FETCH_LSGI_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LSGI_TYPE_SUCCESS`,
  FETCH_LSGI_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LSGI_TYPE_FAILURE`,

  FETCH_REGION_TYPE: `${STATE_REDUCER_KEY}/FETCH_REGION_TYPE`,
  FETCH_REGION_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_REGION_TYPE_REQUEST`,
  FETCH_REGION_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_REGION_TYPE_SUCCESS`,
  FETCH_REGION_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_REGION_TYPE_FAILURE`,

  FETCH_BUILDING_PROJECT_TYPE: `${STATE_REDUCER_KEY}/FETCH_BUILDING_PROJECT_TYPE`,
  FETCH_BUILDING_PROJECT_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BUILDING_PROJECT_TYPE_REQUEST`,
  FETCH_BUILDING_PROJECT_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BUILDING_PROJECT_TYPE_SUCCESS`,
  FETCH_BUILDING_PROJECT_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BUILDING_PROJECT_TYPE_FAILURE`,

  FETCH_ALL_DOCUMENT_TYPES: `${STATE_REDUCER_KEY}/FETCH_ALL_DOCUMENT_TYPES`,
  FETCH_ALL_DOCUMENT_TYPES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ALL_DOCUMENT_TYPES_REQUEST`,
  FETCH_ALL_DOCUMENT_TYPES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ALL_DOCUMENT_TYPES_SUCCESS`,
  FETCH_ALL_DOCUMENT_TYPES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ALL_DOCUMENT_TYPES_FAILURE`

};
export const fetchSmartProfile = createAction(ACTION_TYPES.FETCH_SMART_PROFILE);
export const fetchCountry = createAction(ACTION_TYPES.FETCH_COUNTRY);
export const fetchCountryById = createAction(ACTION_TYPES.FETCH_COUNTRY_BY_ID);
export const fetchState = createAction(ACTION_TYPES.FETCH_STATE);
export const fetchDistricts = createAction(ACTION_TYPES.FETCH_DISTRICTS);
export const fetchServicesDetails = createAction(ACTION_TYPES.FETCH_SERVICES);
export const fetchModuleDetails = createAction(ACTION_TYPES.FETCH_MODULES);
export const fetchSubModuleDetails = createAction(ACTION_TYPES.FETCH_SUB_MODULES);
export const fetchFileTypeDetails = createAction(ACTION_TYPES.FETCH_FILETYPE);
export const fetchWardsByLocalBodyId = createAction(ACTION_TYPES.FETCH_WARD);
export const fetchPostOffice = createAction(ACTION_TYPES.FETCH_POST_OFFICE);
export const fetchPostOfficeByPin = createAction(ACTION_TYPES.FETCH_POST_OFFICE_BY_PIN);
export const fetchFiles = createAction(ACTION_TYPES.FETCH_FILES);
export const fetchFileYearDetails = createAction(ACTION_TYPES.FETCH_FILEYEAR);
export const fetchCorrespondTypeDetails = createAction(ACTION_TYPES.FETCH_CORRESPONDTYPE);
export const fetchDepartments = createAction(ACTION_TYPES.FETCH_DEPARTMENTS);
export const fetchSeatDetails = createAction(ACTION_TYPES.FETCH_SEAT);
export const fetchServiceDetails = createAction(ACTION_TYPES.FETCH_SERVICE);
export const fetchInstitutionTypes = createAction(ACTION_TYPES.FETCH_INSTITUTION_TYPE);
export const fetchLocalBody = createAction(ACTION_TYPES.FETCH_LOCAL_BODY);
export const fetchInstitutions = createAction(ACTION_TYPES.FETCH_INSTITUTIONS);
export const fetchBanks = createAction(ACTION_TYPES.FETCH_BANKS);
export const fetchBranchByBank = createAction(ACTION_TYPES.FETCH_BRANCHES_BY_BANK);
export const fetchEducation = createAction(ACTION_TYPES.FETCH_EDUCATION);
export const fetchGender = createAction(ACTION_TYPES.FETCH_GENDER);
export const deleteDocuments = createAction(ACTION_TYPES.DELETE_DOCUMENTS);
export const fetchSubModuleById = createAction(ACTION_TYPES.FETCH_SUB_MODULES_BY_ID);
export const fetchServicesById = createAction(ACTION_TYPES.FETCH_SERVICES_BY_ID);
export const fetchDepartmentsForFilter = createAction(ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER);
export const fetchSeats = createAction(ACTION_TYPES.FETCH_SEATS);
export const fetchStatus = createAction(ACTION_TYPES.FETCH_STATUS);
export const fetchCounterOperator = createAction(ACTION_TYPES.FETCH_COUNTER_OPERATOR);
export const fetchLocalBodyPropertyType = createAction(ACTION_TYPES.FETCH_LOCAL_BODY_PROPERTY_TYPE);
export const fetchFunctionalGroup = createAction(ACTION_TYPES.FETCH_FUNCTIONAL_GROUP);
export const fetchFunctions = createAction(ACTION_TYPES.FETCH_FUNCTIONS);
export const fetchLocalBodyType = createAction(ACTION_TYPES.FETCH_LOCAL_BODY_TYPE);
export const fetchLocalBodyByDistrictByType = createAction(ACTION_TYPES.FETCH_LOCAL_BODY_NAME);
export const fetchLocalBodyByDistrictByTypeApplicant = createAction(ACTION_TYPES.FETCH_LOCAL_BODY_NAME_APPLICANT);
export const checkFileNo = createAction(ACTION_TYPES.CHECK_FILE_NO);
export const fetchFinancialStatus = createAction(ACTION_TYPES.FETCH_FINANCIAL_STATUS);
export const fetchCategory = createAction(ACTION_TYPES.FETCH_CATEGORY);
export const fetchBuildingUsage = createAction(ACTION_TYPES.FETCH_BUILDING_USAGE);
export const fetchOwnership = createAction(ACTION_TYPES.FETCH_OWNERSHIP);
export const fetchSubModuleByModuleId = createAction(ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID);
export const fetchEmployeeNameById = createAction(ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID);
export const fetchLocalBodyByOfficeCode = createAction(ACTION_TYPES.FETCH_LOCAL_BODY_BY_OFFICE_CODE);
export const fetchServiceValidation = createAction(ACTION_TYPES.FETCH_SERVICE_VALIDATION);
export const fetchDesignation = createAction(ACTION_TYPES.FETCH_DESIGNATION);
export const fetchRouteKey = createAction(ACTION_TYPES.FETCH_ROUTE_KEY);
export const fetchServiceAccountHead = createAction(ACTION_TYPES.FETCH_SERVICE_ACCOUNT_HEAD);
export const generateAadharOtp = createAction(ACTION_TYPES.GEN_OTP);
export const verifyAadharOtp = createAction(ACTION_TYPES.VERIFY_OTP);
export const fetchDfmsServices = createAction(ACTION_TYPES.FETCH_DFMS_SERVICES);
export const fetchWardYear = createAction(ACTION_TYPES.FETCH_WARD_YEAR);
export const deleteNoteDocuments = createAction(ACTION_TYPES.DELETE_NOTE_DOCUMENTS);
export const fetchFunctionalGroups = createAction(ACTION_TYPES.FETCH_FUNCTIONAL_GROUPS);
export const fetchTalukOfficeCode = createAction(ACTION_TYPES.FETCH_TALUK_OFFICECODE);
export const fetchVillage = createAction(ACTION_TYPES.FETCH_VILLAGE);
export const logout = createAction(ACTION_TYPES.LOGOUT_USER);
export const fetchDoorKey = createAction(ACTION_TYPES.FETCH_DOOR_KEY);
export const fetchCounterOperatorWithRole = createAction(ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE);
export const fetchBillType = createAction(ACTION_TYPES.FETCH_BILL_TYPE);
export const fetchMission = createAction(ACTION_TYPES.FETCH_MISSION);
export const fetchProfessionalTaxType = createAction(ACTION_TYPES.FETCH_PROFESSION_TAX_TYPE);
export const fetchTypeofAudit = createAction(ACTION_TYPES.FETCH_TYPE_OF_AUDIT);
export const fetchLbBuilding = createAction(ACTION_TYPES.FETCH_LB_BUILDING);
export const fetchAmountFromClaim = createAction(ACTION_TYPES.FETCH_AMOUNT_FROM_CLAIM);
export const fetchOccupancy = createAction(ACTION_TYPES.FETCH_OCCUPANCY);
export const fetchEstimateAmount = createAction(ACTION_TYPES.FETCH_ESTIMATE_AMOUNT);
export const fetchBuildUpArea = createAction(ACTION_TYPES.FETCH_BUILDUP_AREA);
export const fetchMeetingType = createAction(ACTION_TYPES.FETCH_MEETING_TYPE);
export const fetchOfficeType = createAction(ACTION_TYPES.FETCH_OFFICE_TYPE);
export const fetchFund = createAction(ACTION_TYPES.FETCH_FUND);
export const fetchEstablishmentType = createAction(ACTION_TYPES.FETCH_ESTABLISHMENT_TYPE);
export const fetchModuleById = createAction(ACTION_TYPES.FETCH_MODULES_BY_ID);
export const fetchVerification = createAction(ACTION_TYPES.FETCH_VERIFICATION);
export const loginCitizen = createAction(ACTION_TYPES.LOGIN_CITIZEN);
export const sendOtp = createAction(ACTION_TYPES.SEND_OTP);
export const fetchAccountType = createAction(ACTION_TYPES.FETCH_ACCOUNT_TYPE);
export const fetchTreasuryType = createAction(ACTION_TYPES.FETCH_TREASURY_TYPE);
export const fetchRecoveryAccountHead = createAction(ACTION_TYPES.FETCH_RECOVERY_ACCOUNT_HEAD);
export const fetchPostsByFunctionalGroups = createAction(ACTION_TYPES.FETCH_POST_BY_FUNC_GROUPS);
export const fetchHeight = createAction(ACTION_TYPES.FETCH_HEIGHT);
export const fetchDoor = createAction(ACTION_TYPES.FETCH_DOOR);
export const fetchInwardUsers = createAction(ACTION_TYPES.FETCH_INWARD_USERS);
export const fetchWasteManagementType = createAction(ACTION_TYPES.FETCH_WASTE_MANAGEMENT_TYPE);
export const signPdf = createAction(ACTION_TYPES.FETCH_SIGN_PDF);
export const saveSignedDraft = createAction(ACTION_TYPES.SAVE_SIGNED_DRAFT);
export const fetchAccountId = createAction(ACTION_TYPES.FETCH_ACCOUNT_ID);
export const fetchPostIdByPenNo = createAction(ACTION_TYPES.FETCH_POST_ID_BY_PEN_NO);
export const fetchModeOfDispatch = createAction(ACTION_TYPES.FETCH_MODE_OF_DISPATCH);
export const fetchDispatchClerkDetails = createAction(ACTION_TYPES.FETCH_DISPATCH_CLERK_DETAILS);

export const fetchLsgiType = createAction(ACTION_TYPES.FETCH_LSGI_TYPE);
export const fetchRegionType = createAction(ACTION_TYPES.FETCH_REGION_TYPE);
export const fetchBuildingProjectType = createAction(ACTION_TYPES.FETCH_BUILDING_PROJECT_TYPE);

export const fecthAllDocumentTypes = createAction(ACTION_TYPES.FETCH_ALL_DOCUMENT_TYPES);
