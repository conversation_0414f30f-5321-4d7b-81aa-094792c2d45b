import {
  all, takeLatest, fork, take, put, select, call
} from 'redux-saga/effects';
import { Toast, t } from 'common/components';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { fetchApplication as FetchInward } from 'pages/counter/new/saga';
import {
  fetchPartialNotesDetails as FetchPartial, fetchFileDocuments as fetchAllDocs, fetchDraft as FetchDraft, fetchAllNotes as FetchAllNotes, fetchFileDetails as FetchFileDetails
} from 'pages/file/details/saga';
import { actions as summerySliceActions } from 'pages/file/details/slice';
import { fetchPartialNotes } from 'pages/file/details/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { fetchEFilePreview } from 'pages/citizen/e-file/saga';
import {
  BASE_PATH, HOME_UI_PATH, STORAGE_KEYS, USER_TYPE
} from 'common/constants';
import { routeRedirect } from 'utils/common';
import { setDataToStorage } from 'utils/encryption';
import { roleFormatter } from 'utils/user';
import { base64ToBlob } from 'utils/base64ToBlob';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { getCommonConfigSelector, getUserInfo } from './selectors';
import { NOTE_STATUS } from './constants';

const { successTost, errorTost } = Toast;

export function* fetchCountry({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchCountryApi, payload);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant-country' }));
}
export function* fetchCountryById({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchCountryById, payload);
}
export function* fetchState({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchStateApi, payload);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant-state' }));
}

export function* fetchDistricts({ payload = {} }) {
  const commonConfig = yield select(getCommonConfigSelector);
  const { stateCode = commonConfig.stateId } = payload;
  yield fork(handleAPIRequest, api.fetchDistrictsApi, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DISTRICTS_SUCCESS,
    ACTION_TYPES.FETCH_DISTRICTS_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant-district' }));
  if (type === ACTION_TYPES.FETCH_DISTRICTS_SUCCESS) {
    yield put(sliceActions.setDistrict({ key: stateCode, value: responsePayLoad }));
  }
}

export function* fetchDepartments({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDepartmentApi, payload);
}

export function* fetchServicesDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchServices, payload);
}

export function* fetchModuleDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchModules, payload);
}

export function* fetchSubModuleDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchSubModules, payload);
}

export function* fetchFileTypeDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFileType, payload);
}

export function* fetchFileYearDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFileYear, payload);
}

export function* fetchWard({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchWard, payload);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant-ward' }));
}

export function* fetchWardYear({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchWardYear, payload);
}

export function* fetchInstitutionTypes({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchInstitutionTypesApi, payload);
}

export function* fetchLocalBody({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLocalBodyApi, payload);
}

export function* fetchInstitutions({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchInstitutionsApi, payload);
}

export function* fetchPostOffice({ payload = {} }) {
  const { districtId } = payload;
  if (districtId) {
    yield call(handleAPIRequest, api.fetchPostOfficesByDId, payload);
  } else {
    yield call(handleAPIRequest, api.fetchPostOffices, payload);
  }
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant-postoffice' }));
}

export function* fetchPostOfficeByPin({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostOfficeByPin, payload);
}

export function* fetchFiles({ payload = {} }) {
  const updatedParams = _.omitBy(payload, _.isNil);
  const finalParams = _.omit(updatedParams, ['search']);
  yield fork(handleAPIRequest, api.fetchFiles, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_FILES_SUCCESS,
    ACTION_TYPES.FETCH_FILES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_FILES_SUCCESS) {
    yield put(sliceActions.setFiles(responsePayLoad.data));
    yield put(sliceActions.setActionTriggered({ loading: false, id: 'mergeFile/linkFile' }));
  }
}

export function* fetchCorrespondTypeDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchCorrespondType, payload);
}

export function* fetchSeatDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchSeat, payload);
}

export function* fetchServiceDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchService, payload);
}

export function* fetchBanks({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchBanks, payload);
}

export function* fetchBranchByBank({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchBranchByBank, payload);
}

export function* fetchEducation({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchEducation, payload);
}

export function* fetchGender({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchGender, payload);
}

export function* deleteDocuments({ payload = {} }) {
  const { source } = payload;
  yield fork(handleAPIRequest, api.deleteDocuments, payload);
  const { type } = yield take([
    ACTION_TYPES.DELETE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.DELETE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.DELETE_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('documentDeleteSuccess'), title: t('success'), backwardActionText: t('ok')
    }));
    if (source === 'efile') {
      yield call(fetchEFilePreview, { payload: payload.inwardId });
    } else {
      yield call(FetchInward, { payload: payload.inwardId });
    }
  } else {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('documentDeleteFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchSubModuleById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSubModuleById, payload);
}

export function* fetchModuleById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchModuleById, payload);
}

export function* fetchServiceById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchServicesById, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_SERVICES_BY_ID_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS) {
    yield put(sliceActions.setServicesSearchList(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchDepartmentsForFilter({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchDepartmentsForFilter, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER_SUCCESS,
    ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER_FAILURE]);
  if (type === ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER_SUCCESS) {
    yield put(sliceActions.setDepartments(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchSeats({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSeats, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SEATS_SUCCESS,
    ACTION_TYPES.FETCH_SEATS_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SEATS_SUCCESS) {
    const formatedArray = _.get(responsePayLoad, 'data', {})?.map((file) => {
      return { name: file?.postName, ...file };
    });
    yield put(sliceActions.setSeats(formatedArray));
  }
}

export function* fetchStatus({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchStatus, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_STATUS_SUCCESS,
    ACTION_TYPES.FETCH_STATUS_FAILURE]);
  if (type === ACTION_TYPES.FETCH_STATUS_SUCCESS) {
    yield put(sliceActions.setStatus(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchCounterOperator({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchCounterOperator, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_COUNTER_OPERATOR_SUCCESS,
    ACTION_TYPES.FETCH_COUNTER_OPERATOR_FAILURE]);
  if (type === ACTION_TYPES.FETCH_COUNTER_OPERATOR_SUCCESS) {
    yield put(sliceActions.setCounterOperator(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchCounterOperatorWithRole({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchCounterOperatorWithRole, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE_SUCCESS,
    ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE_FAILURE]);
  if (type === ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE_SUCCESS) {
    if (_.get(responsePayLoad, 'data', {})?.length > 0) {
      const formatedArray = _.get(responsePayLoad, 'data', {})?.map((file) => {
        return { name: file?.employeeName, ...file };
      });
      yield put(sliceActions.setCounterOperatorWithRole(formatedArray));
    } else {
      const newArray = [];
      newArray.push({ name: responsePayLoad?.data?.employeeName, ...responsePayLoad?.data });
      yield put(sliceActions.setCounterOperatorWithRole(newArray));
    }
  }
}

export function* fetchLocalBodyPropertyType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLocalBodyPropertyType, payload);
}

export function* fetchFunctionalGroup({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFunctionalGroup, payload);
}

export function* fetchFunctions({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFunctions, payload);
}

export function* checkFileNo({ payload = {} }) {
  yield call(handleAPIRequest, api.checkFileNo, payload);
}

export function* fetchLocalBodyType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLocalBodyType, payload);
}

export function* fetchLocalBodyByDistrictByType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLocalBodyByDistrictByType, payload);
}

export function* fetchLocalBodyByDistrictByTypeApplicant({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLocalBodyByDistrictByTypeApplicant, payload);
}

export function* fetchSubModulesByModuleId({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSubModulesByModuleId, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS,
    ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS) {
    yield put(sliceActions.setSubModulesSearchList(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchFinancialStatus({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFinancialStatus, payload);
}

export function* fetchCategory({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchCategory, payload);
}

export function* fetchBuildingUsage({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchBuildingUsage, payload);
}

export function* fetchOwnership({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchOwnership, payload);
}

export function* fetchEmployeeNameById({ payload = {} }) {
  const { functionalGroupId, officeId } = payload;
  yield fork(handleAPIRequest, api.fetchEmployeeNameById, { params: { functionalGroupId, officeId } });
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID_FAILURE]);
  if (type === ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID_SUCCESS) {
    const formatedArray = _.get(responsePayLoad, 'data', {})?.map((file) => {
      return { name: file?.employeeName, ...file };
    });
    yield put(sliceActions.setEmployeeNameList(formatedArray));
  }
}

export function* fetchLocalBodyByOfficeCode({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLocalBodyByOfficeCode, payload);
}

export function* fetchServiceValidation({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchServiceValidation, payload);
}

export function* fetchDesignation({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDesignation, payload);
  // const { payload: responsePayLoad = {}, type } = yield take([
  //   ACTION_TYPES.FETCH_DESIGNATION_SUCCESS,
  //   ACTION_TYPES.FETCH_DESIGNATION_FAILURE]);
  // if (type === ACTION_TYPES.FETCH_DESIGNATION_SUCCESS) {
  //   const formatedArray = _.get(responsePayLoad, 'data', {})?.map((file) => {
  //     return { name: file?.designation, ...file };
  //   });
  //   yield put(sliceActions.setDesignation(formatedArray));
  // }
}

export function* fetchRouteKey({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchRouteKey, payload);
}

export function* fetchServiceAccountHead({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchServiceAccountHead, payload);
}

export function* generateAadharOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.generateAadharOtp, payload);
  const { type } = yield take([
    ACTION_TYPES.GEN_OTP_SUCCESS,
    ACTION_TYPES.GEN_OTP_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'generateAadharOtp' }));
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'fetchKsmartId' }));
  if (type === ACTION_TYPES.GEN_OTP_SUCCESS) {
    yield put(commonSliceActions.setOtpStatus(true));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('aadharOtpSendSuccess'), title: t('success'), backwardActionText: t('ok')
    }));
  }
  if (type === ACTION_TYPES.GEN_OTP_FAILURE) {
    yield put(commonSliceActions.setOtpStatus(false));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('aadharOtpSendFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* verifyAadharOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.verifyAadharOtp, payload);
  const { type } = yield take([
    ACTION_TYPES.VERIFY_OTP_SUCCESS,
    ACTION_TYPES.VERIFY_OTP_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'validateAadharOtp' }));
  if (type === ACTION_TYPES.VERIFY_OTP_SUCCESS) {
    yield put(commonSliceActions.setOtpStatus(false));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('aadharOtpVerificationSuccess'), title: t('success'), backwardActionText: t('ok')
    }));
  }
  if (type === ACTION_TYPES.VERIFY_OTP_FAILURE) {
    yield put(commonSliceActions.setOtpStatus(true));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('aadharOtpVerificationFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchSmartProfile({ payload = {} }) {
  const { sendData, handleRegister } = payload;
  const { aadhaarNo, phoneNumber, userType } = sendData;
  const params = { aadhaarNumber: aadhaarNo, phoneNumber, userType };
  yield fork(handleAPIRequest, api.fetchSmartProfile, params);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SMART_PROFILE_SUCCESS,
    ACTION_TYPES.FETCH_SMART_PROFILE_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SMART_PROFILE_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'generateAadharOtp' }));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'fetchKsmartId' }));
    if (responsePayLoad?.data?.user?.length > 0) {
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'success', message: t('profileDetailsFetchedFromKSmart'), title: t('success'), backwardActionText: t('ok')
      }));
    } else {
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'warning', message: `${t('noDetailsFoundFor')} ${phoneNumber}! ${t('pleaseRegister')}`, title: t('noDataFound'), forwardActionText: t('register'), backwardActionText: t('cancel'), forwardAction: () => handleRegister()
      }));
    }
  } else {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('someIssueHappenedPleaseTryAgain'), title: t('Failed'), backwardActionText: t('ok')
    }));
  }
}

export function* deleteNoteDocuments({ payload = {} }) {
  const { notesSearchRequest, params } = payload;
  yield fork(handleAPIRequest, api.deleteNoteDocuments, params);
  const { type } = yield take([
    ACTION_TYPES.DELETE_NOTE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.DELETE_NOTE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.DELETE_NOTE_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('documentDeleteSuccess'), title: t('success'), backwardActionText: t('ok')
    }));
    yield call(FetchPartial, { payload: notesSearchRequest });
    yield call(fetchAllDocs, { payload: notesSearchRequest?.fileNo });
  } else {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('documentDeleteFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchDfmsServices({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDfmsServices, payload);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-service-dropdown' }));
}

export function* fetchFunctionalGroups({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFunctionalGroups, payload);
}

export function* fetchTalukOfficeCode({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchTalukOfficeCode, payload);
}

export function* fetchVillage({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchVillage, payload);
}

export function* fetchDoorKey({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDoorKey, payload);
}

export function* logoutUser() {
  localStorage.clear();
  yield call(routeRedirect(`${HOME_UI_PATH}/home/<USER>/login`));
}

export function* fetchBillType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchBillType, payload);
}

export function* fetchEstablishmentType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchEstablishmentType, payload);
}

export function* fetchMission({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchMission, payload);
}

export function* fetchProfessionalTaxType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchProfessionalTaxType, payload);
}

export function* fetchTypeofAudit({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchTypeofAudit, payload);
}

export function* fetchLbBuilding({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLbBuilding, payload);
}

export function* fetchAmountFromClaim({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchAmountFromClaim, payload);
}

export function* fetchOccupancy({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchOccupancy, payload);
}

export function* fetchEstimateAmount({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchEstimateAmount, payload);
}

export function* fetchBuildUpArea({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchBuildUpArea, payload);
}

export function* fetchMeetingType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchMeetingType, payload);
}

export function* fetchOfficeType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchOfficeType, payload);
}

export function* fetchFund({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFund, payload);
}

export function* fetchVerification({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchVerification, payload);
  const {
    payload: {
      data: {
        token, userType = USER_TYPE.EMPLOYEE, serviceDetails = [], offices = [], ...rest
      } = {}
    } = {}, type
  } = yield take([
    ACTION_TYPES.FETCH_VERIFICATION_SUCCESS,
    ACTION_TYPES.FETCH_VERIFICATION_FAILURE
  ]);

  if (type === ACTION_TYPES.FETCH_VERIFICATION_SUCCESS) {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    const { id: officeId = '' } = _.find(offices, { primary: true }) || {};
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    localStorage.setItem(STORAGE_KEYS.OFFICE_ID, officeId);
    setDataToStorage(STORAGE_KEYS.USER_DETAILS, { userType, offices, ...rest }, true);
    setDataToStorage(STORAGE_KEYS.USER_ROLES, roleFormatter(serviceDetails, userType), true);
    window.location.href = `${BASE_PATH}/dashboard`;
    yield call(successTost, { title: 'Success', description: t('success') });
  }
}

export function* loginCitizen({ payload = {} }) {
  yield fork(handleAPIRequest, api.loginCitizen, payload);
  const {
    payload: {
      data: {
        message,
        data: {
          token,
          userId,
          userType,
          userName,
          name,
          userRoleMappingList = [],
          organizationOwnership = [],
          organizationServiceType = [],
          organizationId,
          ...rest
        } = {}
      } = {}
    } = {},
    type
  } = yield take([
    ACTION_TYPES.LOGIN_CITIZEN_SUCCESS,
    ACTION_TYPES.LOGIN_CITIZEN_FAILURE]);
  if (type === ACTION_TYPES.LOGIN_CITIZEN_SUCCESS) {
    yield call(successTost, { title: t('success'), description: message });

    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);

    setDataToStorage(
      STORAGE_KEYS.USER_ROLES,
      roleFormatter(userType === USER_TYPE.CITIZEN ? [] : userRoleMappingList || [], userType),
      true
    );

    setDataToStorage(STORAGE_KEYS.USER_DETAILS, {
      userName: userName || name, userId, userType, ...rest
    }, true);

    if (userType === USER_TYPE.ORGANIZATION) {
      setDataToStorage(
        STORAGE_KEYS.ORGANIZATION_DETAIL,
        { organizationOwnership, organizationServiceType, organizationId },
        true
      );
    }
  }
}

export function* sendOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.sendOtp, payload);
  const { type } = yield take([
    ACTION_TYPES.SEND_OTP_SUCCESS,
    ACTION_TYPES.SEND_OTP_FAILURE]);
  if (type === ACTION_TYPES.SEND_OTP_SUCCESS) {
    yield call(successTost, { id: 'saved', title: t('success'), description: 'Otp Send' });
  } else {
    yield call(errorTost, { id: 'failed', title: t('failed'), description: 'Otp Failed' });
  }
}

export function* fetchAccountType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchAccountType, payload);
}

export function* fetchTreasuryType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchTreasuryType, payload);
}

export function* fetchRecoveryAccountHead({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchRecoveryAccountHead, payload);
}

export function* fetchPostsByFunctionalGroups({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostsByFunctionalGroups, payload);
}

export function* fetchHeight({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchHeight, payload);
}

export function* fetchDoor({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDoor, payload);
}

export function* fetchInwardUsers({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchInwardUsers, payload);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counterServiceCreate' }));
  const {
    type, payload: { error }
  } = yield take([
    ACTION_TYPES.FETCH_INWARD_USERS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_INWARD_USERS_FAILURE) {
    const { response } = error;
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: response.data.message,
      title: t('error'),
      backwardActionText: t('ok')
    }));
  }
}

export function* fetchWasteManagementType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchWasteManagementType, payload);
}

export function* saveSignedDraft({ payload = {} }) {
  const {
    navigateToActiveDraft = () => { }, blob, params: { fileNo, draftId }
  } = payload;
  const params = { blob, fileNo, draftId };
  yield fork(handleAPIRequest, api.saveSignedDraft, params);
  const { type = '' } = yield take([
    ACTION_TYPES.SAVE_SIGNED_DRAFT_SUCCESS,
    ACTION_TYPES.SAVE_SIGNED_DRAFT_FAILURE]);

  yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: true }));
  if (type === ACTION_TYPES.SAVE_SIGNED_DRAFT_SUCCESS) {
    yield put(commonSliceActions.setDigitalSignConfirmation({
      open: false
    }));

    const userInfo = yield select(getUserInfo);
    const notesSearchRequest = {
      fileNo: payload?.params?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      yield put(fetchPartialNotes(notesSearchRequest));
      yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: true }));
    }

    yield call(FetchDraft, { payload: { fileNo: payload?.params?.fileNo, status: 'ALL' } });
    yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: true }));

    yield call(FetchAllNotes, { payload: { fileNo: payload?.params?.fileNo, noteStatus: NOTE_STATUS.COMPLETED } });
    yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: true }));

    yield call(FetchFileDetails, { payload: { fileNo: payload?.params?.fileNo } });
    yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: true }));

    yield put(summerySliceActions.setDraftNumber(payload?.params?.draftNo));
    yield put(summerySliceActions.setActionOccured(true));
    yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: true }));

    // yield put(commonSliceActions.setAlertAction({
    //   open: true,
    //   variant: 'success',
    //   message: t('draftUpdatedwithDigitalSignature'),
    //   title: t('success'),
    //   backwardActionText: t('ok'),
    //   backwardAction: () => navigateToActiveDraft()
    // }));
    setTimeout(() => {
      navigateToActiveDraft();
    }, 200);
  } else {
    yield put(commonSliceActions.setActionTriggered({ id: 'confirm-ds-save', loading: false }));
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: t('draftUpdateFailed'),
      title: t('Error'),
      backwardActionText: t('ok')
    }));
  }
}

export function* fetchSignPdf({ payload = {} }) {
  const {
    pdfRequest,
    draftRequest,
    callBackEsign = () => { }
  } = payload;
  yield fork(handleAPIRequest, api.fetchSignPdf, pdfRequest);
  const { payload: responsePayLoad = {}, type = '' } = yield take([
    ACTION_TYPES.FETCH_SIGN_PDF_SUCCESS,
    ACTION_TYPES.FETCH_SIGN_PDF_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SIGN_PDF_SUCCESS) {
    const getBlob = base64ToBlob(responsePayLoad?.data?.base64, 'application/pdf');
    callBackEsign({ blob: getBlob, params: draftRequest });

    yield put(commonSliceActions.setDigitalSignConfirmation({
      open: false,
      payload: { blob: getBlob, params: draftRequest }
    }));

    // yield call(saveSignedDraft, { payload: { blob: getBlob, params: draftRequest } });
  }
  if (type === ACTION_TYPES.FETCH_SIGN_PDF_FAILURE) {
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: t('failedToDigitalSignPleaseTryAgain'),
      title: t('digitalSignature'),
      backwardActionText: t('ok')
    }));
    yield put(commonSliceActions.setActionTriggered({ id: 'digital-sign', loading: false }));
  }
}

export function* fetchAccountId({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchAccountId, { params: { lookupType: payload?.lookupType } });
}

export function* fetchPostIdByPenNo({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostIdByPenNo, payload);
}

export function* fetchModeOfDispatch() {
  yield call(handleAPIRequest, api.fetchModeOfDispatch);
}

export function* fetchDispatchClerkDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDispatchClerkDetails, payload);
}

export function* fetchLsgiType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchLsgiType, payload);
}

export function* fetchRegionType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchRegionType, payload);
}

export function* fetchBuildingProjectType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchBuildingProjectType, payload);
}

export function* fetchAllDocumentTypes({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchAllDocumentTypes, payload);
}

export default function* commonSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_COUNTRY, fetchCountry),
    takeLatest(ACTION_TYPES.FETCH_COUNTRY_BY_ID, fetchCountryById),
    takeLatest(ACTION_TYPES.FETCH_STATE, fetchState),
    takeLatest(ACTION_TYPES.FETCH_DISTRICTS, fetchDistricts),
    takeLatest(ACTION_TYPES.FETCH_SERVICES, fetchServicesDetails),
    takeLatest(ACTION_TYPES.FETCH_MODULES, fetchModuleDetails),
    takeLatest(ACTION_TYPES.FETCH_SUB_MODULES, fetchSubModuleDetails),
    takeLatest(ACTION_TYPES.FETCH_FILETYPE, fetchFileTypeDetails),
    takeLatest(ACTION_TYPES.FETCH_FILEYEAR, fetchFileYearDetails),
    takeLatest(ACTION_TYPES.FETCH_WARD, fetchWard),
    takeLatest(ACTION_TYPES.FETCH_WARD_YEAR, fetchWardYear),
    takeLatest(ACTION_TYPES.FETCH_POST_OFFICE, fetchPostOffice),
    takeLatest(ACTION_TYPES.FETCH_POST_OFFICE_BY_PIN, fetchPostOfficeByPin),
    takeLatest(ACTION_TYPES.FETCH_FILES, fetchFiles),
    takeLatest(ACTION_TYPES.FETCH_CORRESPONDTYPE, fetchCorrespondTypeDetails),
    takeLatest(ACTION_TYPES.FETCH_DEPARTMENTS, fetchDepartments),
    takeLatest(ACTION_TYPES.FETCH_SEAT, fetchSeatDetails),
    takeLatest(ACTION_TYPES.FETCH_INSTITUTION_TYPE, fetchInstitutionTypes),
    takeLatest(ACTION_TYPES.FETCH_LOCAL_BODY, fetchLocalBody),
    takeLatest(ACTION_TYPES.FETCH_INSTITUTIONS, fetchInstitutions),
    takeLatest(ACTION_TYPES.FETCH_BANKS, fetchBanks),
    takeLatest(ACTION_TYPES.FETCH_BRANCHES_BY_BANK, fetchBranchByBank),
    takeLatest(ACTION_TYPES.FETCH_EDUCATION, fetchEducation),
    takeLatest(ACTION_TYPES.FETCH_GENDER, fetchGender),
    takeLatest(ACTION_TYPES.DELETE_DOCUMENTS, deleteDocuments),
    takeLatest(ACTION_TYPES.FETCH_SUB_MODULES_BY_ID, fetchSubModuleById),
    takeLatest(ACTION_TYPES.FETCH_SERVICES_BY_ID, fetchServiceById),
    takeLatest(ACTION_TYPES.FETCH_DEPARTMENTS_FOR_FILTER, fetchDepartmentsForFilter),
    takeLatest(ACTION_TYPES.FETCH_SEATS, fetchSeats),
    takeLatest(ACTION_TYPES.FETCH_STATUS, fetchStatus),
    takeLatest(ACTION_TYPES.FETCH_COUNTER_OPERATOR, fetchCounterOperator),
    takeLatest(ACTION_TYPES.FETCH_LOCAL_BODY_PROPERTY_TYPE, fetchLocalBodyPropertyType),
    takeLatest(ACTION_TYPES.FETCH_FUNCTIONAL_GROUP, fetchFunctionalGroup),
    takeLatest(ACTION_TYPES.FETCH_FUNCTIONS, fetchFunctions),
    takeLatest(ACTION_TYPES.CHECK_FILE_NO, checkFileNo),
    takeLatest(ACTION_TYPES.FETCH_LOCAL_BODY_TYPE, fetchLocalBodyType),
    takeLatest(ACTION_TYPES.FETCH_LOCAL_BODY_NAME, fetchLocalBodyByDistrictByType),
    takeLatest(ACTION_TYPES.FETCH_LOCAL_BODY_NAME_APPLICANT, fetchLocalBodyByDistrictByTypeApplicant),
    takeLatest(ACTION_TYPES.FETCH_FINANCIAL_STATUS, fetchFinancialStatus),
    takeLatest(ACTION_TYPES.FETCH_CATEGORY, fetchCategory),
    takeLatest(ACTION_TYPES.FETCH_BUILDING_USAGE, fetchBuildingUsage),
    takeLatest(ACTION_TYPES.FETCH_OWNERSHIP, fetchOwnership),
    takeLatest(ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID, fetchSubModulesByModuleId),
    takeLatest(ACTION_TYPES.FETCH_EMPLOYEE_NAME_BY_ID, fetchEmployeeNameById),
    takeLatest(ACTION_TYPES.FETCH_LOCAL_BODY_BY_OFFICE_CODE, fetchLocalBodyByOfficeCode),
    takeLatest(ACTION_TYPES.FETCH_SERVICE_VALIDATION, fetchServiceValidation),
    takeLatest(ACTION_TYPES.FETCH_DESIGNATION, fetchDesignation),
    takeLatest(ACTION_TYPES.FETCH_ROUTE_KEY, fetchRouteKey),
    takeLatest(ACTION_TYPES.FETCH_SMART_PROFILE, fetchSmartProfile),
    takeLatest(ACTION_TYPES.FETCH_SERVICE_ACCOUNT_HEAD, fetchServiceAccountHead),
    takeLatest(ACTION_TYPES.GEN_OTP, generateAadharOtp),
    takeLatest(ACTION_TYPES.VERIFY_OTP, verifyAadharOtp),
    takeLatest(ACTION_TYPES.FETCH_DFMS_SERVICES, fetchDfmsServices),
    takeLatest(ACTION_TYPES.DELETE_NOTE_DOCUMENTS, deleteNoteDocuments),
    takeLatest(ACTION_TYPES.FETCH_FUNCTIONAL_GROUPS, fetchFunctionalGroups),
    takeLatest(ACTION_TYPES.FETCH_TALUK_OFFICECODE, fetchTalukOfficeCode),
    takeLatest(ACTION_TYPES.FETCH_VILLAGE, fetchVillage),
    takeLatest(ACTION_TYPES.LOGOUT_USER, logoutUser),
    takeLatest(ACTION_TYPES.FETCH_DOOR_KEY, fetchDoorKey),
    takeLatest(ACTION_TYPES.FETCH_COUNTER_OPERATOR_WITH_ROLE, fetchCounterOperatorWithRole),
    takeLatest(ACTION_TYPES.FETCH_BILL_TYPE, fetchBillType),
    takeLatest(ACTION_TYPES.FETCH_ESTABLISHMENT_TYPE, fetchEstablishmentType),
    takeLatest(ACTION_TYPES.FETCH_MISSION, fetchMission),
    takeLatest(ACTION_TYPES.FETCH_PROFESSION_TAX_TYPE, fetchProfessionalTaxType),
    takeLatest(ACTION_TYPES.FETCH_TYPE_OF_AUDIT, fetchTypeofAudit),
    takeLatest(ACTION_TYPES.FETCH_LB_BUILDING, fetchLbBuilding),
    takeLatest(ACTION_TYPES.FETCH_AMOUNT_FROM_CLAIM, fetchAmountFromClaim),
    takeLatest(ACTION_TYPES.FETCH_OCCUPANCY, fetchOccupancy),
    takeLatest(ACTION_TYPES.FETCH_ESTIMATE_AMOUNT, fetchEstimateAmount),
    takeLatest(ACTION_TYPES.FETCH_BUILDUP_AREA, fetchBuildUpArea),
    takeLatest(ACTION_TYPES.FETCH_MEETING_TYPE, fetchMeetingType),
    takeLatest(ACTION_TYPES.FETCH_OFFICE_TYPE, fetchOfficeType),
    takeLatest(ACTION_TYPES.FETCH_FUND, fetchFund),
    takeLatest(ACTION_TYPES.FETCH_MODULES_BY_ID, fetchModuleById),
    takeLatest(ACTION_TYPES.FETCH_VERIFICATION, fetchVerification),
    takeLatest(ACTION_TYPES.LOGIN_CITIZEN, loginCitizen),
    takeLatest(ACTION_TYPES.SEND_OTP, sendOtp),
    takeLatest(ACTION_TYPES.FETCH_ACCOUNT_TYPE, fetchAccountType),
    takeLatest(ACTION_TYPES.FETCH_TREASURY_TYPE, fetchTreasuryType),
    takeLatest(ACTION_TYPES.FETCH_RECOVERY_ACCOUNT_HEAD, fetchRecoveryAccountHead),
    takeLatest(ACTION_TYPES.FETCH_POST_BY_FUNC_GROUPS, fetchPostsByFunctionalGroups),
    takeLatest(ACTION_TYPES.FETCH_HEIGHT, fetchHeight),
    takeLatest(ACTION_TYPES.FETCH_DOOR, fetchDoor),
    takeLatest(ACTION_TYPES.FETCH_INWARD_USERS, fetchInwardUsers),
    takeLatest(ACTION_TYPES.FETCH_WASTE_MANAGEMENT_TYPE, fetchWasteManagementType),
    takeLatest(ACTION_TYPES.FETCH_SIGN_PDF, fetchSignPdf),
    takeLatest(ACTION_TYPES.SAVE_SIGNED_DRAFT, saveSignedDraft),
    takeLatest(ACTION_TYPES.FETCH_ACCOUNT_ID, fetchAccountId),
    takeLatest(ACTION_TYPES.FETCH_POST_ID_BY_PEN_NO, fetchPostIdByPenNo),
    takeLatest(ACTION_TYPES.FETCH_MODE_OF_DISPATCH, fetchModeOfDispatch),
    takeLatest(ACTION_TYPES.FETCH_DISPATCH_CLERK_DETAILS, fetchDispatchClerkDetails),

    takeLatest(ACTION_TYPES.FETCH_LSGI_TYPE, fetchLsgiType),
    takeLatest(ACTION_TYPES.FETCH_REGION_TYPE, fetchRegionType),
    takeLatest(ACTION_TYPES.FETCH_BUILDING_PROJECT_TYPE, fetchBuildingProjectType),
    takeLatest(ACTION_TYPES.FETCH_ALL_DOCUMENT_TYPES, fetchAllDocumentTypes)
  ]);
}
