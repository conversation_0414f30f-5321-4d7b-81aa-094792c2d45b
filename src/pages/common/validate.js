import { t } from 'common/components';
import * as yup from 'yup';

export const FilterFormSchema = yup.object({

  status: yup.string()
    .when(['fileName'], (fileName, schema) => {
      if (fileName[0] === 'fileStatus') {
        return schema.required(t('isRequired', { type: t('status') }));
      }
      return schema.notRequired();
    })
}).required();

export const PageNumberFormSchema = yup.object({
  page: yup.string().required(t('isRequired', { type: t('page') }))
}).required();
