import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getCommonData = (state) => state[STATE_REDUCER_KEY];

const activeAccordian = (state) => state?.activeAccordian;
export const getActiveAccordian = flow(getCommonData, activeAccordian);

const country = (state) => state.country;
export const getCountry = flow(getCommonData, country);

const countryById = (state) => state.countryById;
export const getCountryById = flow(getCommonData, countryById);

const states = (state) => state.state;
export const getState = flow(getCommonData, states);

const districts = (state) => state.district || [];
export const getDistricts = flow(getCommonData, districts);

const commonConfig = (state) => state.commonConfig;
export const getCommonConfigSelector = flow(getCommonData, commonConfig);

const sidebarData = (state) => state?.sidebarData || {};
export const getSidebarData = flow(getCommonData, sidebarData);

const completedSteps = (state) => state?.sidebarData.completedSteps || [];
export const getCompletedSteps = flow(getCommonData, completedSteps);

const ServiceDropdown = (state) => state?.serviceDropdown;
export const getServicesDropdown = flow(getCommonData, ServiceDropdown);

const serviceDfmsDropdown = (state) => state?.serviceDfmsDropdown;
export const getServiceDfmsDropdown = flow(getCommonData, serviceDfmsDropdown);

const ModuleDropdown = (state) => state?.moduleDropdown;
export const getModulesDropdown = flow(getCommonData, ModuleDropdown);

const SubModuleDropdown = (state) => state?.subModuleDropdown;
export const getSubModuleDropDown = flow(getCommonData, SubModuleDropdown);

const FileTypeDropDown = (state) => state?.fileType;
export const getFileTypeDropdown = flow(getCommonData, FileTypeDropDown);

const FileYearDropDown = (state) => state?.fileYear;
export const getFileYearDropdown = flow(getCommonData, FileYearDropDown);

const ward = (state) => state?.ward;
export const getWard = flow(getCommonData, ward);

const wardYear = (state) => state?.wardYear;
export const getWardYear = flow(getCommonData, wardYear);

const postOffice = (state) => state?.postOffice;
export const getPostOffice = flow(getCommonData, postOffice);

const files = (state) => state?.files;
export const getFiles = flow(getCommonData, files);

const CorrespondTypeDropDown = (state) => state?.correspondType;
export const getCorrespondTypeDropdown = flow(getCommonData, CorrespondTypeDropDown);

const departments = (state) => state.departments || [];
export const getDepartments = flow(getCommonData, departments);

const SeatDropDown = (state) => state?.seat;
export const getSeatDropdown = flow(getCommonData, SeatDropDown);

const ServiceDropdownList = (state) => state?.serviceDropdownList;
export const getServiceDropdown = flow(getCommonData, ServiceDropdownList);

const institutionTypes = (state) => state.institutionTypes;
export const getInstitutionTypes = flow(getCommonData, institutionTypes);

const localBody = (state) => state.localBody;
export const getLocalBody = flow(getCommonData, localBody);

const institutions = (state) => state.institutions;
export const getInstitutions = flow(getCommonData, institutions);

const banks = (state) => state.banks;
export const getBanks = flow(getCommonData, banks);

const bankBranches = (state) => state.bankBranches;
export const getBranches = flow(getCommonData, bankBranches);

const education = (state) => state.education;
export const getEducation = flow(getCommonData, education);

const gender = (state) => state.gender;
export const getGender = flow(getCommonData, gender);

const subModuleById = (state) => state?.subModule;
export const getSubModuleById = flow(getCommonData, subModuleById);

const moduleById = (state) => state?.module;
export const getModuleById = flow(getCommonData, moduleById);

const servicesSearchList = (state) => state?.servicesSearchList;
export const getServicesSearchList = flow(getCommonData, servicesSearchList);

const departmentsForFilter = (state) => state?.departments;
export const getDepartmentsForFilter = flow(getCommonData, departmentsForFilter);

const seats = (state) => state?.seats;
export const getSeats = flow(getCommonData, seats);

const status = (state) => state?.status;
export const getStatus = flow(getCommonData, status);

const filterParams = (state) => state?.filterParams;
export const getFilterParams = flow(getCommonData, filterParams);

const counterOperator = (state) => state?.CounterOperator;
export const getCounterOperator = flow(getCommonData, counterOperator);

const localBodyPropertyType = (state) => state?.localBodyPropertyType;
export const getLocalBodyPropertyType = flow(getCommonData, localBodyPropertyType);

const functionalGroup = (state) => state?.functionalGroup;
export const getFunctionalGroup = flow(getCommonData, functionalGroup);

const functions = (state) => state?.functions;
export const getFunctions = flow(getCommonData, functions);

const subModule = (state) => state?.subModule;
export const getSubModule = flow(getCommonData, subModule);

const fileExist = (state) => state?.fileExist;
export const getFileExist = flow(getCommonData, fileExist);

const actionTriggered = (state) => state?.actionTriggered;
export const getActionTriggered = flow(getCommonData, actionTriggered);

const acknowledgeTriggered = (state) => state?.acknowledgeTriggered;
export const getAcknowledgeTriggered = flow(getCommonData, acknowledgeTriggered);

const userInfo = (state) => state?.userInfo;
export const getUserInfo = flow(getCommonData, userInfo);

const alertAction = (state) => state?.alertAction;
export const getAlertAction = flow(getCommonData, alertAction);

const localBodyType = (state) => state?.localBodyType;
export const getLocalBodyType = flow(getCommonData, localBodyType);

const localBodyTypeByDBylbCode = (state) => state?.localBodyTypeByDBylbCode;
export const getLocalBodyTypeByDBylbCode = flow(getCommonData, localBodyTypeByDBylbCode);

const localBodyTypeByDBylbCodeApplicant = (state) => state?.localBodyTypeByDBylbCodeApplicant;
export const getLocalBodyTypeByDBylbCodeApplicant = flow(getCommonData, localBodyTypeByDBylbCodeApplicant);

const financialStatus = (state) => state?.financialStatus;
export const getFinancialStatus = flow(getCommonData, financialStatus);

const category = (state) => state?.category;
export const getCategory = flow(getCommonData, category);

const buildingUsage = (state) => state?.buildingUsage;
export const getBuildingUsage = flow(getCommonData, buildingUsage);

const ownerShip = (state) => state?.ownerShip;
export const getOwnerShip = flow(getCommonData, ownerShip);

const subModulesSearchList = (state) => state?.subModulesSearchList;
export const getSubModulesSearchList = flow(getCommonData, subModulesSearchList);

const employeeNameList = (state) => state?.employeeNameSearchList;
export const getEmployeeNameList = flow(getCommonData, employeeNameList);

const searchListParams = (state) => state?.searchListParams;
export const getSearchListParams = flow(getCommonData, searchListParams);

const noteDetails = (state) => state?.notesDetails;
export const getNoteDetails = flow(getCommonData, noteDetails);

const userLocalBody = (state) => state?.userLocalBody;
export const getUserLocalBody = flow(getCommonData, userLocalBody);

const userOffices = (state) => state?.userOffices;
export const getUserOffices = flow(getCommonData, userOffices);

const serviceValidation = (state) => state?.serviceValidation;
export const getServiceValidation = flow(getCommonData, serviceValidation);

const documentPreview = (state) => state?.documentPreview;
export const getDocumentPreview = flow(getCommonData, documentPreview);

const designation = (state) => state?.designation;
export const getDesignation = flow(getCommonData, designation);

const routeKey = (state) => state?.routeKey;
export const getRouteKey = flow(getCommonData, routeKey);

const serviceAccountHead = (state) => state?.serviceAccountHead;
export const getServiceAccountHead = flow(getCommonData, serviceAccountHead);

const otpStatus = (state) => state?.otpStatus;
export const getOtpStatus = flow(getCommonData, otpStatus);

const generateAadharOtp = (state) => state?.generateAadharOtp;
export const getGenerateAadharOtp = flow(getCommonData, generateAadharOtp);

const verifyAadharOtp = (state) => state?.verifyAadharOtp;
export const getVerifyAadharOtp = flow(getCommonData, verifyAadharOtp);

const smartDetails = (state) => state?.smartDetails;
export const getSmartDetails = flow(getCommonData, smartDetails);

const functionalGroups = (state) => state?.functionalGroups;
export const getFunctionalGroups = flow(getCommonData, functionalGroups);

const backButton = (state) => state?.backButton || {};
export const getBackButton = flow(getCommonData, backButton);

const tableLoader = (state) => state?.tableLoader;
export const getTableLoader = flow(getCommonData, tableLoader);

const triggerPrint = (state) => state?.triggerPrint;
export const getTriggerPrint = flow(getCommonData, triggerPrint);

const activeBlob = (state) => state?.activeBlob;
export const getActiveBlob = flow(getCommonData, activeBlob);

const registerOpen = (state) => state?.registerOpen;
export const getRegisterOpen = flow(getCommonData, registerOpen);

const village = (state) => state?.village;
export const getVillage = flow(getCommonData, village);

const taluk = (state) => state?.taluk;
export const getTaluk = flow(getCommonData, taluk);

const doorKey = (state) => state?.doorKey;
export const getDoorKey = flow(getCommonData, doorKey);

const generalFieldValidation = (state) => state?.generalFieldValidation;
export const getGeneralFieldValidation = flow(getCommonData, generalFieldValidation);

const counterOperatorWithRole = (state) => state?.CounterOperatorWithRole;
export const getCounterOperatorWithRole = flow(getCommonData, counterOperatorWithRole);

const billType = (state) => state?.billType;
export const getBillType = flow(getCommonData, billType);

const establishmentType = (state) => state?.establishmentType;
export const getEstablishmentType = flow(getCommonData, establishmentType);

const mission = (state) => state?.mission;
export const getMission = flow(getCommonData, mission);

const professionalTaxType = (state) => state?.professionalTaxType;
export const getProfessionalTaxType = flow(getCommonData, professionalTaxType);

const typeOfAudit = (state) => state?.typeOfAudit;
export const getTypeOfAudit = flow(getCommonData, typeOfAudit);

const lbBuilding = (state) => state?.lbBuilding;
export const getLbBuilding = flow(getCommonData, lbBuilding);

const amountFromClaim = (state) => state?.amountFromClaim;
export const getAmountFromClaim = flow(getCommonData, amountFromClaim);

const occupancy = (state) => state?.occupancy;
export const getOccupancy = flow(getCommonData, occupancy);

const estimateAmount = (state) => state?.estimateAmount;
export const getEstimateAmount = flow(getCommonData, estimateAmount);

const buildUpArea = (state) => state?.buildUpArea;
export const getBuildUpArea = flow(getCommonData, buildUpArea);

const meetingType = (state) => state?.meetingType;
export const getMeetingType = flow(getCommonData, meetingType);

const officeType = (state) => state?.officeType;
export const getOfficeType = flow(getCommonData, officeType);

const fund = (state) => state?.fund;
export const getFund = flow(getCommonData, fund);

const localBodyTypeDetails = (state) => state?.localBodyTypeDetails;
export const getLocalBodyTypeDetails = flow(getCommonData, localBodyTypeDetails);

const accountType = (state) => state?.accountType;
export const getAccountType = flow(getCommonData, accountType);

const treasuryType = (state) => state?.treasuryType;
export const getTreasuryType = flow(getCommonData, treasuryType);

const recoveryAccountHead = (state) => state?.recoveryAccountHead;
export const getRecoveryAccountHead = flow(getCommonData, recoveryAccountHead);

const height = (state) => state?.height;
export const getHeight = flow(getCommonData, height);

const door = (state) => state?.door;
export const getDoor = flow(getCommonData, door);

const inwardUsers = (state) => state?.inwardUsers;
export const getInwardUsers = flow(getCommonData, inwardUsers);

const inwardNextUserModal = (state) => state?.inwardNextUserModal;
export const getInwardNextUserModal = flow(getCommonData, inwardNextUserModal);

const wasteManagementType = (state) => state?.wasteManagementType;
export const getWasteManagementType = flow(getCommonData, wasteManagementType);

const activeBlobDetails = (state) => state?.activeBlobDetails;
export const getActiveBlobDetails = flow(getCommonData, activeBlobDetails);

const digitalSignedPdf = (state) => state?.digitalSignedPdf;
export const getDigitalSignedPdf = flow(getCommonData, digitalSignedPdf);

const accountId = (state) => state?.accountId;
export const getAccountId = flow(getCommonData, accountId);

const digitalSignTrigger = (state) => state?.digitalSignTrigger;
export const getDigitalSignTrigger = flow(getCommonData, digitalSignTrigger);

const postIdByPenNoDetails = (state) => state?.postIdByPenNoDetails;
export const getPostIdByPenNoDetails = flow(getCommonData, postIdByPenNoDetails);

const digitalSignConfirmation = (state) => state?.digitalSignConfirmation;
export const getDigitalSignConfirmation = flow(getCommonData, digitalSignConfirmation);

const modeOfDispatch = (state) => state?.modeOfDispatch;
export const getModeOfDispatch = flow(getCommonData, modeOfDispatch);

const dispatchClerkDetails = (state) => state?.dispatchClerkDetails;
export const getDispatchClerkDetails = flow(getCommonData, dispatchClerkDetails);

const lsgiType = (state) => state?.lsgiType;
export const getLsgiType = flow(getCommonData, lsgiType);

const regionType = (state) => state?.regionType;
export const getRegionType = flow(getCommonData, regionType);

const buildingProjectType = (state) => state?.buildingProjectType;
export const getBuildingProjectType = flow(getCommonData, buildingProjectType);

const summaryCollapseMenuFlag = (state) => state?.summaryCollapseMenuFlag;
export const getSummaryCollapseMenuFlag = flow(getCommonData, summaryCollapseMenuFlag);

const noteCardDetails = (state) => state?.noteCardDetails;
export const getNoteCardDetails = flow(getCommonData, noteCardDetails);

const documentId = (state) => state?.documentId;
export const getDocumentId = flow(getCommonData, documentId);

const documentNameFromNoteReferences = (state) => state?.documentNameFromNoteReferences;
export const getDocumentNameFromNoteReferences = flow(getCommonData, documentNameFromNoteReferences);

const allDocumentsType = (state) => state?.allDocumentsType;
export const getAllDocumentsType = flow(getCommonData, allDocumentsType);

const isOneDocumentSelect = (state) => state?.isOneDocumentSelect;
export const getIsOneDocumentSelect = flow(getCommonData, isOneDocumentSelect);
