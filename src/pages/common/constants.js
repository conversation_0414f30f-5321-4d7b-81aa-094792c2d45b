import PendingIcon from 'assets/Pending';
import CreatedIcon from 'assets/Created';
import VerifiedIcon from 'assets/Verified';
import ApprovedIcon from 'assets/Approved';
import RecommendIcon from 'assets/RecommendIcon';
import RejectIcon from 'assets/Rejected';
import ReturnedIcon from 'assets/Returned';

export const STATE_REDUCER_KEY = 'common';

export const DATE_FORMAT = {
  DATE_LOCAL: 'DD-MM-YYYY',
  TIME_LOCAL: 'h:mm A',
  DATE_TIME: 'DD-MM-YYYY hh:mm:ss',
  LOCAL_DATE_REVERSE: 'YYYY-MM-DD',
  DATE_LOCAL_STANDARD: 'DD/MM/YYYY'
};

export const X_STATE_CODE = 'KL';

export const FILING_TYPE = {
  COUNTER: 'counter',
  EFILE: 'e-file'
};

export const FILTER_TYPE = {
  SEARCH_KEY_WORD: 'searchKeyword',
  DATE: 'date',
  SORT: 'sort',
  STATUS: 'status',
  FILE_NO: 'fileNo',
  SORT_BY_STATUS: 'sortByStatus',
  PRIORITY: 'priority',
  MODULES: 'modules',
  SUB_MODULES: 'subModules',
  SERVICES: 'services',
  WORK_FLOW: 'workflow',
  REFERENCE_NO: 'referenceNo',
  WARD: 'ward',
  FUNCTIONAL_GROUP: 'functionalGroup',
  FUNCTIONS: 'functions',
  DEPARTMENT: 'department',
  POST: 'post',
  APPLICANT_NAME: 'applicantName',
  SEAT: 'seat',
  EMPLOYEE_NAME: 'employeeName',
  OFFICE_TYPE: 'officeType',
  POST_BY_LOCATION: 'postByLocation',
  ROUTE_TYPE: 'routeType',
  FUNCTIONAL_GROUP_IDS: 'functionalGroupIds',
  USER: 'user',
  MODULE_TYPE: 'moduleType'
};

export const FILE_STATUS = {
  RUNNING: 'running',
  HOLD: 'hold',
  VERY_URGENT: 'veryUrgent',
  URGENT: 'urgent',
  PENDING: 'pending',
  DELAYED: 'delayed',
  NORMAL: 'normal',
  RETURN_TO_CITIZEN: 'return-to-citizen'
};

export const NOTE_STATUS = {
  PARTIAL: 'PARTIAL',
  COMPLETED: 'COMPLETED'
};

export const FILE_STATUS_FOR_API_PARAMS = {
  RUNNING: 'RUNNING',
  CLOSED: 'CLOSED',
  APPROVED: 'APPROVED',
  ROUTE_CHANGE: 'FORWARD_PLUS',
  HOLD: 'HOLD',
  RETURN_TO_CITIZEN: 'RETURN_TO_CITIZEN',
  APPLIED: 'APPLIED',
  REJECTED: 'REJECTED',
  PARKED: 'PARKED',
  SUSPENDED: 'SUSPENDED'
};

export const DRAFT_STATUS = {
  PENDING: 'PENDING',
  CREATED: 'CREATED',
  VERIFIED: 'VERIFIED',
  APPROVED: 'APPROVED',
  RETURNED: 'RETURNED',
  REJECTED: 'REJECT',
  RECOMMENDED: 'RECOMMENDED'
};

export const FILE_ROLE = {
  OPERATOR: 'OPERATOR',
  VERIFIER: 'VERIFIER',
  APPROVER: 'APPROVER',
  AUTHORIZER: 'AUTHORIZER',
  ENQUIRY_OFFICER: 'ENQUIRY_OFFICER',
  RECOMMENDING_OFFICER: 'RECOMMENDING_OFFICER',
  ROUTE_CHANGE_ROLE: 'FORWARD_PLUS_ROLE',
  ADMINISTRATOR: 'ADMINISTRATOR'
};

export const INWARD_STATUS = {
  COMPLETED: 'completed',
  INWARD_PROCESSING: 'INWARD_PROCESSING',
  RECORD_RECEIVED: 'RECORD_RECEIVED',
  HANDED_OVER_TO_MESSENGER: 'HANDED_OVER_TO_MESSENGER',
  OPERATOR_ACCEPTED: 'OPERATOR_ACCEPTED',
  OPERATOR_REJECTED: 'OPERATOR_REJECTED',
  OPERATOR_HANDED_OVER_TO_MESSENGER: 'OPERATOR_HANDED_OVER_TO_MESSENGER',
  RECORD_ACCEPTED: 'RECORD_ACCEPTED',
  RECORD_REJECTED: 'RECORD_REJECTED',
  MOVED_TO_RACK: 'MOVED_TO_RACK'
};

export const MAX_FILE_SIZE_BYTES = 5000000;

export const REPORTS = [
  {
    id: 1,
    name: 'File Tracking',
    route: 'ui/file-management/services/reports/file-tracking'
  },
  {
    id: 2,
    name: 'File Status',
    route: 'ui/file-management/services/reports/file-status-report'
  },
  {
    id: 3,
    name: 'Distribution Register',
    route: 'ui/file-management/services/reports/distribution-register-report'
  },
  // {
  //   id: 4,
  //   name: 'Pending File',
  //   route: 'ui/file-management/services/reports/pending-file-report'
  // },
  {
    id: 5,
    name: 'File Abstract',
    route: 'ui/file-management/services/reports/file-abstract-report'
  },
  {
    id: 6,
    name: 'Cash Declaration',
    route: 'ui/file-management/services/reports/cash-declaration'
  }
  // {
  //   id: 7,
  //   name: 'Personal Register',
  //   route: 'ui/file-management/services/reports/personal-register'
  // }
];

export const SOURCE_TYPE = [
  // {
  //   id: 1, name: 'Module'
  // },
  {
    id: 2, name: 'Counter'
  },
  {
    id: 3, name: 'Arising'
  },
  // {
  //   id: 4, name: 'eFile'
  // },
  {
    id: 5, name: 'Legacy'
  }
];

export const MODULE_TYPE = [
  {
    id: 1, name: 'All'
  },
  {
    id: 2, name: 'Counter'
  }
  // {
  //   id: 3, name: 'E-file'
  // }
];

export const DISPATCH = [
  {
    id: 1,
    name: 'Send Letter',
    route: 'ui/file-management/services/dispatch/send-letter'
  },
  {
    id: 2,
    name: 'Outbox',
    route: 'ui/file-management/services/dispatch/dispatch-outbox'
  }
];

export const CASH_DECLARATION_CHARACTERRS = [
  'e',
  'E',
  '+',
  '-',
  'a',
  'A',
  'b',
  'B',
  'c',
  'C',
  'd',
  'D',
  'f',
  'F',
  'g',
  'G',
  'h',
  'H',
  'i',
  'I',
  'j',
  'J',
  'k',
  'K',
  'l',
  'L',
  'm',
  'M',
  'n',
  'N',
  'o',
  'O',
  'p',
  'P',
  'q',
  'Q',
  'r',
  'R',
  's',
  'S',
  't',
  'T',
  'u',
  'U',
  'v',
  'V',
  'w',
  'W',
  'x',
  'X',
  'y',
  'Y',
  'z',
  'Z'
];

export const NOTE_CREATED_BY = {
  OPERATOR: 'OPERATOR',
  VERIFIER: 'VERIFIER',
  APPROVER: 'APPROVER',
  RECOMMENDING_OFFICER: 'RECOMMENDING_OFFICER'
};

export const E_SIGN_STATUS = {
  SUBMIT: 'SUBMIT'
};

export const DRAFT_STATUS_CONFIG = {
  [DRAFT_STATUS.PENDING]: {
    color: '#FBBA23',
    Icon: PendingIcon,
    label: 'pending'
  },
  [DRAFT_STATUS.CREATED]: {
    color: '#00B2EB',
    Icon: CreatedIcon,
    label: 'created'
  },
  [DRAFT_STATUS.VERIFIED]: {
    color: '#E83A7A',
    Icon: VerifiedIcon,
    label: 'verified'
  },
  [DRAFT_STATUS.APPROVED]: {
    color: '#1EBE72',
    Icon: ApprovedIcon,
    label: 'approved'
  },
  [DRAFT_STATUS.RETURNED]: {
    color: '#4C2719',
    Icon: ReturnedIcon,
    label: 'needReview'
  },
  [DRAFT_STATUS.REJECTED]: {
    color: '#FC5555',
    Icon: RejectIcon,
    label: 'rejected'
  },
  [DRAFT_STATUS.RECOMMENDED]: {
    color: '#7B61FF',
    Icon: RecommendIcon,
    label: 'recommended'
  }
};

export const DRAFT_FILTERS = [
  {
    id: 'PENDING',
    label: 'Pending Drafts'
  },
  {
    id: 'APPROVED',
    label: 'Approved Drafts'
  },
  {
    id: 'ALL',
    label: 'All Drafts'
  }
];
