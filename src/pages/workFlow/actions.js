import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_DEPARTMENTS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS`,
  FETCH_DEPARTMENTS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_REQUEST`,
  FETCH_DEPARTMENTS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_SUCCESS`,
  FETCH_DEPARTMENTS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FAILURE`,

  FETCH_POST_BY_FUNC_GROUP: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUP`,
  FETCH_POST_BY_FUNC_GROUP_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUP_REQUEST`,
  FETCH_POST_BY_FUNC_GROUP_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUP_SUCCESS`,
  FETCH_POST_BY_FUNC_GROUP_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_FUNC_GROUP_FAILURE`,

  FETCH_WORKFLOW: `${STATE_REDUCER_KEY}/FETCH_WORKFLOW`,
  FETCH_WORKFLOW_REQUEST: `${STATE_REDUCER_KEY}/FETCH_WORKFLOW_REQUEST`,
  FETCH_WORKFLOW_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_WORKFLOW_SUCCESS`,
  FETCH_WORKFLOW_FAILURE: `${STATE_REDUCER_KEY}/FETCH_WORKFLOW_FAILURE`,

  SAVE_HOLD_FILE: `${STATE_REDUCER_KEY}/SAVE_HOLD_FILE`,
  SAVE_HOLD_FILE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_HOLD_FILE_REQUEST`,
  SAVE_HOLD_FILE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_HOLD_FILE_SUCCESS`,
  SAVE_HOLD_FILE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_HOLD_FILE_FAILURE`,

  ACTION_FILE: `${STATE_REDUCER_KEY}/ACTION_FILE`,
  ACTION_FILE_REQUEST: `${STATE_REDUCER_KEY}/ACTION_FILE_REQUEST`,
  ACTION_FILE_SUCCESS: `${STATE_REDUCER_KEY}/ACTION_FILE_SUCCESS`,
  ACTION_FILE_FAILURE: `${STATE_REDUCER_KEY}/ACTION_FILE_FAILURE`,

  FETCH_FILE_DISPOSAL_TYPE: `${STATE_REDUCER_KEY}/FETCH_ACTION_FILE`,
  FETCH_FILE_DISPOSAL_TYPE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILE_DISPOSAL_TYPE_REQUEST`,
  FETCH_FILE_DISPOSAL_TYPE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILE_DISPOSAL_TYPE_SUCCESS`,
  FETCH_FILE_DISPOSAL_TYPE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILE_DISPOSAL_TYPE_FAILURE`,

  FETCH_IS_GROUPED_SERVICE: `${STATE_REDUCER_KEY}/FETCH_IS_GROUPED_SERVICE`,
  FETCH_IS_GROUPED_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_IS_GROUPED_SERVICE_REQUEST`,
  FETCH_IS_GROUPED_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_IS_GROUPED_SERVICE_SUCCESS`,
  FETCH_IS_GROUPED_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_IS_GROUPED_SERVICE_FAILURE`,

  FETCH_NEXT_ROLE_DETAILS: `${STATE_REDUCER_KEY}/FETCH_NEXT_ROLE_DETAILS`,
  FETCH_NEXT_ROLE_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_NEXT_ROLE_DETAILS_REQUEST`,
  FETCH_NEXT_ROLE_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_NEXT_ROLE_DETAILS_SUCCESS`,
  FETCH_NEXT_ROLE_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_NEXT_ROLE_DETAILS_FAILURE`,

  FETCH_POST_BY_LOCATION: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION`,
  FETCH_POST_BY_LOCATION_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_REQUEST`,
  FETCH_POST_BY_LOCATION_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_SUCCESS`,
  FETCH_POST_BY_LOCATION_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_FAILURE`,

  SEND_FILE_FOR_DISPOSAL_OR_NOT: `${STATE_REDUCER_KEY}/SEND_FILE_FOR_DISPOSAL_OR_NOT`,
  SEND_FILE_FOR_DISPOSAL_OR_NOT_REQUEST: `${STATE_REDUCER_KEY}/SEND_FILE_FOR_DISPOSAL_OR_NOT_REQUEST`,
  SEND_FILE_FOR_DISPOSAL_OR_NOT_SUCCESS: `${STATE_REDUCER_KEY}/SEND_FILE_FOR_DISPOSAL_OR_NOT_SUCCESS`,
  SEND_FILE_FOR_DISPOSAL_OR_NOT_FAILURE: `${STATE_REDUCER_KEY}/SEND_FILE_FOR_DISPOSAL_OR_NOT_FAILURE`,

  DISPOSE_FILES: `${STATE_REDUCER_KEY}/DISPOSE_FILES`,
  DISPOSE_FILES_REQUEST: `${STATE_REDUCER_KEY}/DISPOSE_FILES_REQUEST`,
  DISPOSE_FILES_SUCCESS: `${STATE_REDUCER_KEY}/DISPOSE_FILES_SUCCESS`,
  DISPOSE_FILES_FAILURE: `${STATE_REDUCER_KEY}/DISPOSE_FILES_FAILURE`,

  FETCH_OFFICE_WISE_POST_DETAILS: `${STATE_REDUCER_KEY}/FETCH_OFFICE_WISE_POST_DETAILS`,
  FETCH_OFFICE_WISE_POST_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_OFFICE_WISE_POST_DETAILS_REQUEST`,
  FETCH_OFFICE_WISE_POST_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_OFFICE_WISE_POST_DETAILS_SUCCESS`,
  FETCH_OFFICE_WISE_POST_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_OFFICE_WISE_POST_DETAILS_FAILURE`

};

export const fetchDepartments = createAction(ACTION_TYPES.FETCH_DEPARTMENTS);
export const fetchPostByFunctionalGroup = createAction(ACTION_TYPES.FETCH_POST_BY_FUNC_GROUP);
export const fetchWorkflowDetails = createAction(ACTION_TYPES.FETCH_WORKFLOW);
export const saveHoldFile = createAction(ACTION_TYPES.SAVE_HOLD_FILE);
export const actionFile = createAction(ACTION_TYPES.ACTION_FILE);
export const fetchFileDisposalType = createAction(ACTION_TYPES.FETCH_FILE_DISPOSAL_TYPE);
export const fetchIsGroupedService = createAction(ACTION_TYPES.FETCH_IS_GROUPED_SERVICE);
export const fetchNextRoleDetails = createAction(ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS);
export const fetchPostBytLocation = createAction(ACTION_TYPES.FETCH_POST_BY_LOCATION);
export const sendFileForDisposalOrNot = createAction(ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT);
export const disposeFilesAction = createAction(ACTION_TYPES.DISPOSE_FILES);
export const fetchOfficeWisePostDetails = createAction(ACTION_TYPES.FETCH_OFFICE_WISE_POST_DETAILS);
