import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getWorkFlowData = (state) => state[STATE_REDUCER_KEY];

const departments = (state) => state.departments || [];
export const getDepartments = flow(getWorkFlowData, departments);

const postByFunctionalGroup = (state) => state.postByFunctionalGroup || [];
export const getPostByFunctionalGroup = flow(getWorkFlowData, postByFunctionalGroup);

const WorkflowDropdownList = (state) => state?.workflowDropdownList;
export const getWorkflowDropdown = flow(getWorkFlowData, WorkflowDropdownList);

const closeFileConfirm = (state) => state?.closeFileConfirm;
export const getCloseFileConfirm = flow(getWorkFlowData, closeFileConfirm);

const fileCloseType = (state) => state?.fileCloseType;
export const getFileCloseType = flow(getWorkFlowData, fileCloseType);

const isGroupedService = (state) => state?.isGroupedService;
export const getIsGroupedService = flow(getWorkFlowData, isGroupedService);

const nextRoleDetails = (state) => state?.nextRoleDetails;
export const getNextRoleDetails = flow(getWorkFlowData, nextRoleDetails);

const officeWisePostDetails = (state) => state?.officeWisePostDetails;
export const getOfficeWisePostDetails = flow(getWorkFlowData, officeWisePostDetails);
