import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchDepartmentApi = () => {
  return {
    url: API_URL.COMMON.FETCH_FUNCTIONAL_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DEPARTMENTS_REQUEST,
        ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS,
        ACTION_TYPES.FETCH_DEPARTMENTS_FAILURE
      ]
    }
  };
};

export const fetchPostByFunctionalGroup = (params) => {
  return {
    url: API_URL.COMMON.POST_BY_FUNC_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_FUNC_GROUP_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_FUNC_GROUP_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_FUNC_GROUP_FAILURE
      ],
      params
    }
  };
};

export const fetchWorkflow = (data) => {
  return {
    url: API_URL.COMMON.WORKFLOW.replace(':fileNo', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_WORKFLOW_REQUEST,
        ACTION_TYPES.FETCH_WORKFLOW_SUCCESS,
        ACTION_TYPES.FETCH_WORKFLOW_FAILURE
      ]
    }
  };
};

export const saveHoldFileData = (data) => {
  return {
    url: API_URL.COMMON.SAVE_HOLD_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_HOLD_FILE_REQUEST,
        ACTION_TYPES.SAVE_HOLD_FILE_SUCCESS,
        ACTION_TYPES.SAVE_HOLD_FILE_FAILURE
      ],
      data
    }
  };
};

export const actionFile = (data) => {
  return {
    url: API_URL.COMMON.ACTION_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.ACTION_FILE_REQUEST,
        ACTION_TYPES.ACTION_FILE_SUCCESS,
        ACTION_TYPES.ACTION_FILE_FAILURE
      ],
      data
    }
  };
};

export const fetchFileDisposalType = () => {
  return {
    url: API_URL.COMMON.FILE_DISPOSAL_TYPE,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILE_DISPOSAL_TYPE_REQUEST,
        ACTION_TYPES.FETCH_FILE_DISPOSAL_TYPE_SUCCESS,
        ACTION_TYPES.FETCH_FILE_DISPOSAL_TYPE_FAILURE
      ]
    }
  };
};

export const fetchIsGroupedService = (data) => {
  return {
    url: API_URL.COMMON.FETCH_IS_GROUPED_SERVICE.replace(':fileNo', data),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_IS_GROUPED_SERVICE_REQUEST,
        ACTION_TYPES.FETCH_IS_GROUPED_SERVICE_SUCCESS,
        ACTION_TYPES.FETCH_IS_GROUPED_SERVICE_FAILURE
      ]
    }
  };
};

export const fetchNextRoleDetails = (data) => {
  const { fileNo, action } = data;
  const params = {
    action
  };
  return {
    url: API_URL.COMMON.FETCH_NEXT_ROLE_DETAILS.replace(':fileNo', fileNo),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const fetchPostBytLocation = (data) => {
  return {
    url: API_URL.COMMON.FETCH_POST_BY_LOCATION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_LOCATION_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_LOCATION_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_LOCATION_FAILURE
      ],
      params: data
    }
  };
};

export const sendFileForDisposalOrNot = (data) => {
  return {
    url: API_URL.COMMON.UPDATE_FILE_FOR_DISPOSAL_OR_NOT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT_REQUEST,
        ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT_SUCCESS,
        ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT_FAILURE
      ],
      data
    }
  };
};

export const disposeFiles = (data) => {
  return {
    url: API_URL.DASHBOARD.DISPOSE_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DISPOSE_FILES_REQUEST,
        ACTION_TYPES.DISPOSE_FILES_SUCCESS,
        ACTION_TYPES.DISPOSE_FILES_FAILURE
      ],
      data
    }
  };
};

export const fetchOfficeWisePostDetails = (params) => {
  return {
    url: API_URL.COMMON.FETCH_OFFICE_WISE_POST_DETAILS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_OFFICE_WISE_POST_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_OFFICE_WISE_POST_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_OFFICE_WISE_POST_DETAILS_FAILURE
      ],
      params
    }
  };
};
