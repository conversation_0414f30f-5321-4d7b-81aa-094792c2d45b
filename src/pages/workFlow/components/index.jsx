import React, { useEffect, useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import _ from 'lodash';
import {
  <PERSON><PERSON><PERSON>roller, t, Button, IconButton
} from 'common/components';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import * as actionsFile from 'pages/file/details/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  DATE_FORMAT, FILE_ROLE, FILTER_TYPE, NOTE_STATUS
} from 'pages/common/constants';
import {
  getDraftExistsOrNot, getDraftList, getFinanceExistsOrNot, getPartialNotes
} from 'pages/file/details/selector';
import { getActionTriggered, getOfficeType, getUserInfo } from 'pages/common/selectors';
import BackButton from 'common/components/BackButton/BackButton';
import * as commonActions from 'pages/common/actions';
import { convertToLocalDate, convertToLocalDateTime } from 'utils/date';
import RouteKeys from 'pages/file/details/components/summaryDetails/RouteKeys';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import NoteLayout from 'common/components/NoteLayout';
import { baseApiURL } from 'utils/http';
import { STORAGE_KEYS } from 'common/constants';
import { API_URL } from 'common';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import {
  getDepartments, getIsGroupedService, getNextRoleDetails, getOfficeWisePostDetails, getPostByFunctionalGroup, getWorkflowDropdown
} from '../selectors.js';
import HoldFile from './HoldFile';
import { WORKFLOW_ACTIONS, WORKFLOW_DIALOG_APPROVE_STAGE } from '../constants';
import { workFlowSchema } from '../validate';
import ApproveConfirmation from './ApproveConfirmation';
import WorkflowConfirmation from './WorkflowConfirmation';
import ConfirmationModalDisposeFiles from './ConfirmationModalDisposeFiles';
import { SettingsFrame } from '../../../assets/Svg';

const WorkFlow = ({
  workflowDropdown,
  fetchWorkflow,
  fetchDepartments,
  departmentsDropdown,
  saveHoldFile,
  actionFile,
  fetchPartialNotes,
  setAlertAction,
  fetchPostByFunctionalGroup,
  postByFunctionalGroup,
  userInfo,
  fileDetails,
  fetchServiceValidation,
  fetchIsGroupedService,
  defaultAction = null,
  // draftExistsOrNot,
  financeExistsOrNot,
  fetchOfficeType,
  fetchPostBytLocation,
  fetchNextRoleDetails,
  nextRoleDetails,
  setActionTriggered,
  sendFileForDisposalOrNot,
  actionTriggered,
  // draftList,
  partialNote,
  from = 'summary',

  fetchOfficeWisePostDetails,
  officeWisePostDetails
}) => {
  const params = useParams();
  const [nextRole, setNextRole] = useState([]);
  const [nextWorkFlowRole, setNextWorkFlowRole] = useState({ fileNo: params?.fileNo });
  const {
    control,
    handleSubmit,
    getValues,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: {
      actions: '',
      department: '',
      assignee: '',
      officeType: '',
      postByLocation: '',
      user: null
    },
    resolver: yupResolver(workFlowSchema),
    context: { nextRole, nextWorkFlowRole }
  });

  const [workFlow, setWorkFlow] = useState([]);
  const [post, setPost] = useState([]);
  const [postId, setPostId] = useState(null);
  const [routeChange, setRouteChange] = useState(false);
  const [open, setOpen] = useState(false);
  const [dialogStage, setDialogStage] = useState(WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM);
  const [isAdministrator, setIsAdministrator] = useState(false);
  const [workflowConfirm, setWorkflowConfirm] = useState(false);
  const [forDisposal, setForDisposal] = useState(false);
  const [confirmDisposeFlag, setConfirmDisposeFlag] = useState(false);
  // const [draftPendingListNumbers, setDraftPendingListNumbers] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isFunctionalGroupShow, setIsFunctionalGroupShow] = useState(false);

  useEffect(() => {
    if (fileDetails) {
      fetchServiceValidation(fileDetails?.serviceCode);
    }
  }, [fileDetails]);

  useEffect(() => {
    if (userInfo?.id) {
      fetchOfficeWisePostDetails({ officeId: userInfo?.id });
    }
  }, [userInfo]);

  useEffect(() => {
    if (workFlow?.length > 0) {
      if (workFlow?.length === 1 && JSON.stringify(workFlow).includes('Forwarded By Administrator')) {
        setIsAdministrator(true);
      }
    }
  }, [JSON.stringify(workFlow)]);

  useEffect(() => {
    if (defaultAction) {
      if (defaultAction === 'routechange') {
        setValue('actions', WORKFLOW_ACTIONS.ROUTE_CHANGE);
        setRouteChange(true);
        fetchDepartments();
      }
    }
  }, [workFlow]);

  // const checkDraftExistOrNot = (status) => {
  //   if (status === true) {
  //     return true;
  //   }
  //   return false;
  // };

  const checkFinanceExistOrNot = (statusArray) => {
    const finalStatus = statusArray?.some((obj) => obj.status);
    const finalRecStatus = statusArray?.some((obj) => obj.recStatus === 'AUTHORISE');
    if (finalRecStatus) {
      return false;
    }
    return finalStatus;
  };

  useEffect(() => {
    if (params.fileNo) {
      fetchWorkflow(params.fileNo);
      fetchIsGroupedService(params.fileNo);
      // FIX: Commented for fetching ALL draft issue fix
      // fetchDraft({ fileNo: params?.fileNo, status: 'PENDING' });
    }
  }, [params.fileNo]);

  // console.log('draftList=====', draftList);

  useEffect(() => {
    const notesSearchRequest = {
      fileNo: params?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      fetchPartialNotes(notesSearchRequest);
    }
  }, [params.fileNo]);

  useEffect(() => {
    if (workflowDropdown.data?.length) {
      const workFlowInitialStep = workflowDropdown?.data
        ?.filter((item) => item.action !== '')
        .map((wf) => ({ ...wf, name: wf.action }));
      if (fileDetails?.source !== 4) {
        setWorkFlow(workFlowInitialStep.filter((item) => item.action !== 'Return to Citizen'));
      } else {
        setWorkFlow(workFlowInitialStep);
      }
    }
  }, [workflowDropdown, fileDetails]);

  useEffect(() => {
    setValue('assignee', null);
    if (!isFunctionalGroupShow && officeWisePostDetails && officeWisePostDetails?.data?.length > 0) {
      setPost(
        officeWisePostDetails?.data?.map((ed) => ({
          ...ed,
          name: `${ed.employeeName ? ed.employeeName : ''} - ${ed.designation} - ${ed.penNo ? ed.penNo : ''}- ${ed.postnameEng ? ed.postnameEng : ed.postNameInEng}`
        }))
      );
    }
    if (isFunctionalGroupShow && postByFunctionalGroup.data?.length > 0) {
      setPost(
        postByFunctionalGroup?.data?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName ? ps.employeeName : ''} - ${ps.designation} - ${ps.penNo ? ps.penNo : ''}- ${ps.postnameEng ? ps.postnameEng : ps.postNameInEng}`
        }))
      );
    }
  }, [postByFunctionalGroup, JSON.stringify(isFunctionalGroupShow), officeWisePostDetails]);

  useEffect(() => {
    if (nextRoleDetails?.length > 0) {
      setNextRole(
        nextRoleDetails?.map((ps) => ({
          id: ps?.postId,
          name: `${ps?.employeeName ? ps?.employeeName : ''} - ${ps?.penNo ? ps?.penNo : ''} - ${ps?.designation ? ps?.designation : ''} - ${ps?.seat ? ps?.seat : ''} - ${ps?.role ? ps?.role : ''}`,
          employeeName: ps?.employeeName,
          fileId: ps?.fileId,
          fileNo: ps?.fileNo,
          penNo: ps?.penNo,
          role: ps?.role,
          designation: ps?.designation,
          seat: ps?.seat
        }))
      );
    }
  }, [nextRoleDetails]);

  const updateSelecterFocus = (selectRef) => {
    const selectorRef = document.getElementById(`${selectRef}`);
    if (selectorRef) {
      const parentElement = selectorRef.parentElement.querySelector('.KSmart__control');
      if (parentElement) {
        parentElement.classList.add('KSmart__control--menu-is-open');
        parentElement.classList.add('KSmart__control--is-focused');
        const inputElement = parentElement.querySelector('input');
        if (inputElement) {
          inputElement.focus();
          inputElement.setAttribute('aria-expanded', 'true');
          const inputId = inputElement.id;
          if (inputId) {
            inputElement.setAttribute('aria-controls', `${inputId.replace('-input', '-listbox')}`);
            inputElement.setAttribute('aria-activedescendant', `${inputId.replace('-input', '-option-0')}`);
          }
        }
      }
    }
  };

  useEffect(() => {
    if (nextRole?.length === 1) {
      setValue('user', nextRole[0]?.id);
      setNextWorkFlowRole({
        id: nextRole[0]?.id, fileNo: nextRole[0]?.fileNo, forwardingTo: nextRole[0]?.employeeName, designation: nextRole[0]?.designation, role: nextRole[0]?.role, pen: nextRole[0]?.penNo, seat: nextRole[0]?.seat, dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextWorkFlowRole({});
      updateSelecterFocus('userSelectRef');
    }
  }, [nextRole]);

  useEffect(() => {
    if (routeChange) {
      if (isFunctionalGroupShow) {
        updateSelecterFocus('departmentSelectRef');
      } else {
        updateSelecterFocus('allAssigneeSelectRef');
      }
    }
  }, [watch('actions'), JSON.stringify(routeChange), JSON.stringify(isFunctionalGroupShow)]);

  useEffect(() => {
    if (routeChange && isFunctionalGroupShow && watch('department') !== '') {
      const assigneeSelectRef = document.getElementById('assigneeSelectRef');
      if (assigneeSelectRef) {
        const parentElement = assigneeSelectRef.parentElement.querySelector('.KSmart__control');
        if (parentElement) {
          parentElement.classList.add('KSmart__control--is-focused');
          parentElement.classList.add('KSmart__control--menu-is-open');
          const inputElement = parentElement.querySelector('input');
          if (inputElement) {
            inputElement.focus();
          }
        }
      }
    }
  }, [watch('department')]);

  const handleChange = (data, field) => {
    switch (field) {
      case FILTER_TYPE.WORK_FLOW:
        setValue('workflow', data.action);
        setNextRole([]);
        setValue('user', null);
        if (data.action === WORKFLOW_ACTIONS.ROUTE_CHANGE) {
          setRouteChange(true);
        } else {
          fetchNextRoleDetails({ fileNo: params?.fileNo, action: data.action });
          setRouteChange(false);
        }

        break;
      case FILTER_TYPE.DEPARTMENT:
        setValue('department', data.functionalGroupId);
        setValue('assignee', null);
        fetchPostByFunctionalGroup({ functionalGroupId: data.functionalGroupId, officeId: userInfo?.id });
        break;
      case FILTER_TYPE.ROUTE_TYPE:
        setValue('officeType', null);
        setValue('assignee', null);
        setValue('department', null);
        if (data?.id === 2) {
          fetchDepartments();
        } else {
          fetchOfficeType({ officeLbCode: userInfo?.id });
        }
        break;
      case FILTER_TYPE.OFFICE_TYPE:
        if (data) {
          setValue('officeType', data.id);
        } else {
          setValue('assignee', null);
        }
        fetchPostBytLocation({ location: data.id });
        break;
      case FILTER_TYPE.POST:
        setValue('assignee', data.id);
        setNextWorkFlowRole({
          id: data?.id, fileNo: params?.fileNo, forwardingTo: data?.employeeName, designation: data?.designation, role: data?.role || null, pen: data?.penNo, seat: data?.postnameEng, dateAndTime: convertToLocalDateTime(new Date().toISOString())
        });
        if (data.postId) {
          setPostId(data.postId);
        } else {
          setPostId(data.id);
        }
        break;
      case FILTER_TYPE.USER:
        if (data) {
          setNextWorkFlowRole({
            id: data?.id, fileNo: data?.fileNo, forwardingTo: data?.employeeName, designation: data?.designation, role: data?.role, pen: data?.penNo, seat: data?.seat, dateAndTime: convertToLocalDateTime(new Date().toISOString())
          });
        } else {
          // setNextRole([]);
          setNextWorkFlowRole({});
        }

        break;
      default:
        break;
    }
    if (field === FILTER_TYPE.WORK_FLOW) {
      setValue('workflow', data.action);
      if (data.action === WORKFLOW_ACTIONS.ROUTE_CHANGE) {
        setRouteChange(true);
        fetchDepartments();
      } else {
        setRouteChange(false);
      }
    }
  };

  // useEffect(() => {
  //   if (draftList) {
  //     const filteredDraftNos = draftList?.filter((draft) => draft?.draftNo !== 0)?.map((draft) => draft?.draftNo);
  //     const draftNoResult = filteredDraftNos?.length > 1 ? filteredDraftNos?.join(', ') : filteredDraftNos[0];
  //     setDraftPendingListNumbers(draftNoResult);
  //   }
  // }, [draftList]);

  const handleClose = () => {
    setOpen(false);
  };

  const formatRoleWithoutCaps = (role) => {
    // if (Array.isArray(role)) {
    //   return role.length > 0 ? null : null;
    // }

    if (typeof role === 'string' && role.length > 0) {
      return role;
    }

    return null;
  };
  const actWorkFlow = () => {
    setActionTriggered({ loading: true, id: 'file-flow' });
    setWorkflowConfirm(false);
    const sendData = {
      workflowActionRequest: {
        fileNo: params.fileNo,
        action: getValues('actions'),
        postId: getValues('actions') === WORKFLOW_ACTIONS.ROUTE_CHANGE ? postId : nextWorkFlowRole?.id,
        fileFinalStage: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? 'na' : null,
        disposalType: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? 'na' : null,
        disposalMode: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? 'na' : null,
        // role: nextWorkFlowRole?.role
        role: formatRoleWithoutCaps(nextWorkFlowRole?.role)
      }
    };
    actionFile(sendData);
  };

  const onSubmitForm = async () => {
    const localNote = JSON.parse(localStorage.getItem('notes-unsaved')) || '';
    const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    const draftExistResponse = await (fetch(`${baseApiURL}/${API_URL.INBOX.DRAFT_EXISTS_OR_NOT.replace(':fileNo', params?.fileNo)}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`
      }
    }));

    const response = await (fetch(`${baseApiURL}/${API_URL.INBOX.FETCH_PENDING_DRAFT_NO.replace(':fileNo', params?.fileNo)}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`
      }
    }));

    const res = await response.json();
    const existRes = await draftExistResponse.json();
    const draftNoResult = res?.payload?.length > 1 ? res?.payload?.join(', ') : res?.payload?.[0];

    if (existRes?.payload === true) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: !draftNoResult || res?.error ? `${t('pendingDraftExists')}, Draft No(s) : Error` : `${t('pendingDraftExists')}, Draft No(s) : ${draftNoResult}`,
        title: t('draft'),
        backwardActionText: t('ok'),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else if (checkFinanceExistOrNot(financeExistsOrNot) && !isAdministrator && getValues('actions') !== WORKFLOW_ACTIONS.ROUTE_CHANGE) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('financialTransactionPending'),
        title: t('financial'),
        backwardActionText: t('ok'),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else if ((localNote?.fileNo && localNote?.note) || partialNote?.notes) {
      let content = {};
      if ((localNote?.fileNo === params?.fileNo) && localNote?.note !== '') {
        content = {
          fileNo: params.fileNo,
          notes: localNote?.note
        };
      } else if (partialNote?.notes) {
        content = {
          fileNo: params.fileNo,
          notes: partialNote?.notes
        };
      }
      const responseVal = await fetch(
        `${baseApiURL}/file-management-services/v2/notes/save`,
        {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(content)
        }
      );
      if (responseVal?.ok) {
        // setTimeout(() => {
        //   localStorage.removeItem('notes-unsaved');
        // }, 500);
        const notesSearchRequest = {
          fileNo: params?.fileNo,
          assigner: userInfo?.assigner,
          noteStatus: NOTE_STATUS.PARTIAL
        };
        if (userInfo?.assigner) {
          fetchPartialNotes(notesSearchRequest);
        }
        setWorkflowConfirm(true);
      }
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('fileCanBeSubmittedOnlyAfterSavingTheNote'),
        title: t('noteIsEmpty'),
        backwardActionText: t('ok'),
        closeOnEsc: false,
        closeOnOverlayClick: false
      });
    }
  };

  const approveWithCloseFile = (type, mode, date) => {
    const sendData = {
      workflowActionRequest: {
        fileNo: params.fileNo,
        action: getValues('actions'),
        postId: getValues('actions') === WORKFLOW_ACTIONS.ROUTE_CHANGE ? postId : nextWorkFlowRole?.id,
        fileFinalStage: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? 'yes' : null,
        disposalType: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? type : null,
        disposalMode: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? mode : null,
        holdUpto:
          getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? convertToLocalDate(date, DATE_FORMAT.DATE_TIME) : null,
        role: nextWorkFlowRole?.role || null
      }
    };
    actionFile(sendData);
  };

  const approveWithoutCloseFile = () => {
    const sendData = {
      workflowActionRequest: {
        fileNo: params.fileNo,
        action: getValues('actions'),
        postId: getValues('actions') === WORKFLOW_ACTIONS.ROUTE_CHANGE ? postId : nextWorkFlowRole?.id,
        fileFinalStage: getValues('actions') === WORKFLOW_ACTIONS.APPROVE ? 'no' : null,
        role: nextWorkFlowRole?.role
      }
    };
    actionFile(sendData);
  };

  const handleWorkFlowClose = () => {
    setWorkflowConfirm(false);
  };

  // For File Disposal and Exclude from disposal start
  useEffect(() => {
    if (fileDetails?.stage === 'FOR_DISPOSAL') {
      setForDisposal(true);
      setValue('forDisposal', true);
    } else {
      setForDisposal(false);
      setValue('forDisposal', false);
    }
  }, [fileDetails]);

  const onDisposeFile = (type) => {
    let responseBody = {};
    if (type === 'forDisposal') {
      responseBody = {
        fileNo: params?.fileNo,
        disposeStatus: true
      };
      sendFileForDisposalOrNot({
        type, responseBody
      });
    } else if (type === 'forNotDisposal') {
      responseBody = {
        fileNo: params?.fileNo,
        disposeStatus: false
      };
      sendFileForDisposalOrNot({ type, responseBody });
    }
  };

  const handleDisposalCheck = () => {
    if (forDisposal === false) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('areYouSureToSendThisFileForDisposal'),
        title: t('warning'),
        backwardActionText: t('cancel'),
        forwardActionText: t('confirm'),
        closeOnEsc: false,
        closeOnOverlayClick: false,
        forwardAction: () => onDisposeFile('forDisposal')
      });
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('doYouWantToUncheckThisFileFromDispose'),
        title: t('warning'),
        backwardActionText: t('cancel'),
        forwardActionText: t('confirm'),
        closeOnEsc: false,
        closeOnOverlayClick: false,
        forwardAction: () => onDisposeFile('forNotDisposal')
      });
    }
  };
  // For File Disposal and Exclude from disposal end

  const confirm = () => {
    setConfirmDisposeFlag(true);
    setAlertAction(false);
  };

  const confirmDisposeFile = () => {
    if (params?.fileNo) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: 'Are you sure want to dispose',
        title: 'Confirm',
        backwardActionText: t('no'),
        forwardActionText: t('yes'),
        forwardAction: () => confirm()
      });
    }
  };

  const handleCloseDisposeConfirm = () => {
    setConfirmDisposeFlag(false);
  };

  const handleMenuOpen = () => {
    setIsMenuOpen(true);
  };
  const handleMenuClose = () => setIsMenuOpen(false);
  // const parentDivRef = useRef(null);
  useEffect(() => {
    if (isMenuOpen) {
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: 'instant'
      });
    }
  }, [isMenuOpen]);

  return (
    <div className="bg-white rounded-lg mt-2 mb-2">
      <form id="workflow" action="enter" onSubmit={handleSubmit(onSubmitForm)}>
        <NoteLayout
          // title={t('actions')}
          content={(
            <div className="bg-white rounded-lg mt-3 mb-2">
              <div className="col-span-12">
                <div className={`grid ${isFunctionalGroupShow ? 'grid-cols-[3fr_3fr_3fr_0fr]' : 'grid-cols-[3fr_3fr_0fr]'} gap-5 p-5`}>
                  <FormController
                    name="actions"
                    type="select"
                      // label={from === 'note-file' ? t('actions') : ''}
                    label={t('actions')}
                    placeholder={t('select')}
                    control={control}
                    errors={errors}
                    options={workFlow}
                    optionKey="action"
                    handleChange={(data) => handleChange(data, FILTER_TYPE.WORK_FLOW)}
                    isDisabled={defaultAction === 'routechange'}
                    menuPlacement="top"
                    menuPosition="fixed"
                    maxMenuHeight={160}
                    zIndex={99999}
                    onMenuOpen={handleMenuOpen}
                    onMenuClose={handleMenuClose}
                  />

                  {
                    watch('actions') && !routeChange && watch('actions') !== WORKFLOW_ACTIONS.RETURN_TO_CITIZEN && nextRole?.filter((item) => item?.id !== null).length > 0
                      ? (
                        <FormController
                          id="userSelectRef"
                          name="user"
                          type="select"
                          // value={nextUser?.postId}
                          label={t('forwardTo')}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="id"
                          handleChange={(data) => handleChange(data, FILTER_TYPE.USER)}
                          options={nextRole}
                          isClearable
                          menuPlacement="top"
                          menuPosition="fixed"
                          maxMenuHeight={160}
                          zIndex={99999}
                          onMenuOpen={handleMenuOpen}
                          onMenuClose={handleMenuClose}
                        // style={customStyles}
                        />
                      )
                      : (
                        ''
                      )
                  }

                  {watch('actions') && routeChange ? (
                    <>

                      {isFunctionalGroupShow ? (
                        <>
                          <FormController
                            id="departmentSelectRef"
                            name="department"
                            type="select"
                            label={t('functionalGroup')}
                            placeholder={t('select')}
                            control={control}
                            errors={errors}
                            optionKey="functionalGroupId"
                            handleChange={(data) => handleChange(data, FILTER_TYPE.DEPARTMENT)}
                            options={_.get(departmentsDropdown, 'data', [])}
                            menuPlacement="top"
                            menuPosition="fixed"
                            maxMenuHeight={160}
                            zIndex={99999}
                            onMenuOpen={handleMenuOpen}
                            onMenuClose={handleMenuClose}
                          // style={customStyles}
                          />

                          <FormController
                            id="assigneeSelectRef"
                            name="assignee"
                            type="select"
                            label={t('forwardTo')}
                            placeholder={t('select')}
                            control={control}
                            errors={errors}
                            optionKey="id"
                            handleChange={(data) => handleChange(data, FILTER_TYPE.POST)}
                            options={post?.filter((item) => item?.penNo !== null)}
                            menuPlacement="top"
                            menuPosition="fixed"
                            maxMenuHeight={160}
                            zIndex={99999}
                            onMenuOpen={handleMenuOpen}
                            onMenuClose={handleMenuClose}
                          // style={customStyles}
                          />
                        </>
                      ) : (
                        <FormController
                          id="allAssigneeSelectRef"
                          name="assignee"
                          type="select"
                          label={t('forwardTo')}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="id"
                          handleChange={(data) => handleChange(data, FILTER_TYPE.POST)}
                          options={post?.filter((item) => item?.penNo !== null)}
                          menuPlacement="top"
                          menuPosition="fixed"
                          maxMenuHeight={160}
                          zIndex={99999}
                          onMenuOpen={handleMenuOpen}
                          onMenuClose={handleMenuClose}
                        // style={customStyles}
                        />
                      )}

                      <div className="mt-2">
                        <IconButton
                          className="items-center justify-center p-4"
                          variant="unstyled"
                          onClick={() => {
                            setIsFunctionalGroupShow(!isFunctionalGroupShow);
                          }}
                          icon={<SettingsFrame />}
                        />
                      </div>
                    </>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            </div>
          )}
          moreMenu={false}
          from={from}
        />

        <div className={`mt-2 flex ${from === 'note-file' ? 'px-8 py-5' : 'p-4'}`}>

          <div className="flex-grow">
            <RouteKeys />
          </div>
          <div className="flex">
            {
              fileDetails?.postId === fileDetails?.custodian?.id && (
                <Button
                  variant="link"
                  name="forDisposal"
                  style={{ textDecoration: 'none' }}
                  leftIcon={forDisposal ? <CheckedBox /> : <UnCheckedBox />}
                  onClick={() => handleDisposalCheck()}
                  className="mr-10"
                  isDisabled={watch('actions') !== WORKFLOW_ACTIONS.FORWARD}
                >
                  {t('forDispose')}
                </Button>
              )
            }
            <BackButton />
            {(fileDetails?.role === FILE_ROLE.VERIFIER || fileDetails?.role === FILE_ROLE.RECOMMENDING_OFFICER || fileDetails?.role === FILE_ROLE.OPERATOR) && (
              <Button variant="secondary_outline" className="mx-2" type="submit" onClick={() => setOpen(true)}>
                {t('park')}
              </Button>
            )}
            {(fileDetails?.role === FILE_ROLE.VERIFIER || fileDetails?.role === FILE_ROLE.APPROVER) && (fileDetails?.stage === 'FOR_DISPOSAL') && (
              <Button
                variant="secondary"
                className="mx-2"
                onClick={() => confirmDisposeFile()}
                isLoading={actionTriggered?.id === 'dispose-file' && actionTriggered?.loading}
              >
                {t('dispose')}
              </Button>
            )}
            <Button variant="secondary" className="mx-2" type="submit">
              {t('submit')}
            </Button>
          </div>
        </div>
      </form>
      <HoldFile open={open} handleClose={handleClose} params={params} saveHoldFile={saveHoldFile} />
      <ApproveConfirmation
        approveWithoutCloseFile={approveWithoutCloseFile}
        approveWithCloseFile={approveWithCloseFile}
        dialogStage={dialogStage}
        setDialogStage={setDialogStage}
      />
      {/* <CertificateDialog open={openC} close={handleCloseC} previewData={baseCode} loading={loading} /> */}
      <WorkflowConfirmation open={workflowConfirm} close={handleWorkFlowClose} handleSelect={actWorkFlow} workflowObj={nextWorkFlowRole} returnToCitizen={getValues('actions') === WORKFLOW_ACTIONS.RETURN_TO_CITIZEN} />
      {
        confirmDisposeFlag && (
          <ConfirmationModalDisposeFiles
            open={confirmDisposeFlag}
            fileNo={params?.fileNo}
            handleClose={handleCloseDisposeConfirm}
          />
        )
      }
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  workflowDropdown: getWorkflowDropdown,
  departmentsDropdown: getDepartments,
  userInfo: getUserInfo,
  postByFunctionalGroup: getPostByFunctionalGroup,
  isGroupedService: getIsGroupedService,
  draftExistsOrNot: getDraftExistsOrNot,
  financeExistsOrNot: getFinanceExistsOrNot,
  officeType: getOfficeType,
  nextRoleDetails: getNextRoleDetails,
  actionTriggered: getActionTriggered,
  draftList: getDraftList,
  partialNote: getPartialNotes,

  officeWisePostDetails: getOfficeWisePostDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchDepartments: () => dispatch(actions.fetchDepartments()),
  fetchPostByFunctionalGroup: (data) => dispatch(actions.fetchPostByFunctionalGroup(data)),
  fetchWorkflow: (data) => dispatch(actions.fetchWorkflowDetails(data)),
  saveHoldFile: (data) => dispatch(actions.saveHoldFile(data)),
  actionFile: (data) => dispatch(actions.actionFile(data)),
  fetchPartialNotes: (data) => dispatch(actionsFile.fetchPartialNotes(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  setCloseFileConfirm: (data) => dispatch(sliceActions.setCloseFileConfirm(data)),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  fetchIsGroupedService: (data) => dispatch(actions.fetchIsGroupedService(data)),
  fetchOfficeType: (data) => dispatch(commonActions.fetchOfficeType(data)),
  fetchPostBytLocation: (data) => dispatch(actions.fetchPostBytLocation(data)),
  fetchNextRoleDetails: (data) => dispatch(actions.fetchNextRoleDetails(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  sendFileForDisposalOrNot: (data) => dispatch(actions.sendFileForDisposalOrNot(data)),
  fetchDraft: (data) => dispatch(actionsFile.fetchDraft(data)),

  fetchOfficeWisePostDetails: (data) => dispatch(actions.fetchOfficeWisePostDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(WorkFlow);
