import React, { useState } from 'react';
import {
  t, <PERSON><PERSON>, <PERSON><PERSON>,
  Modal<PERSON>lay,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dalBody,
  FormController
} from 'common/components';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { HOME_PATH } from 'common/constants';
import { unHoldSchema } from '../validate';

function ModalForm({ onSubmitForm }) {
  const {
    handleSubmit,
    control,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: {
      reason: ''
    },
    resolver: yupResolver(unHoldSchema)
  });

  return (
    <form
      id="unHold-file-form"
      action="enter"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <FormController
        name="reason"
        variant="outlined"
        type="textarea"
        label={t('reason')}
        control={control}
        errors={errors}
      />

    </form>
  );
}

export default function UnHoldFile({
  openUnHoldFile, handleCloseUnHoldFile, params, saveUnHoldFile, fileDetails
}) {
  const [showReason, setShowReason] = useState(false);

  const backwardAction = () => {
    window.location.href = HOME_PATH;
  };

  const onSubmitForm = (data) => {
    const saveData = {
      fileNo: params?.fileNo,
      unHoldReason: data?.reason
    };
    saveUnHoldFile({ saveData, backwardAction });
    handleCloseUnHoldFile();
  };

  const handleShowReason = () => {
    setShowReason(true);
  };

  return (

    <Modal isOpen={openUnHoldFile} size="xl" onClose={handleCloseUnHoldFile} className="custom-form-modal" closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader p={0}>
          <span size="xl">
            <h4 className="rounded-t-lg bg-[#E7EFF5] p-[14px] text-center text-[#09327B]">
              {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage !== FILE_STATUS_FOR_API_PARAMS.PARKED && t('moveToCustodian')}
              {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage === FILE_STATUS_FOR_API_PARAMS.PARKED && t('revoke')}
            </h4>
          </span>
        </ModalHeader>
        <ModalBody p={10}>
          {showReason ? (
            <ModalForm onSubmitForm={onSubmitForm} />
          ) : (
            <div className="text-center text-[24px] font-[600] leading-[40px]">
              {t('doYouWantTo')}
              <span className="text-[#00B2EC]"> {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage !== FILE_STATUS_FOR_API_PARAMS.PARKED && t('moveToCustodian')}
                {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage === FILE_STATUS_FOR_API_PARAMS.PARKED && t('revoke')}
              </span>
              <div>{t('thisFile')}</div>
            </div>
          )}

        </ModalBody>

        <ModalFooter p={0}>
          <div className="flex justify-center w-[100%] bg-[#E7EFF5] rounded-b-lg p-[14px]">
            <Button
              variant="secondary_outline"
              className="mx-2"
              size="sm"
              mr={3}
              onClick={handleCloseUnHoldFile}
            >
              {t('no')}
            </Button>
            {showReason
              ? (
                <Button variant="secondary" size="sm" type="submit" form="unHold-file-form">
                  {t('submit')}
                </Button>
              )
              : (
                <Button variant="secondary" size="sm" onClick={() => handleShowReason()}>
                  {t('yes')}
                </Button>
              )}
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
