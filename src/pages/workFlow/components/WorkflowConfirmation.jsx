import React from 'react';

import {
  <PERSON><PERSON>,
  <PERSON>dal,
  ModalContent,
  ModalBody,
  ModalOverlay,
  t
} from 'common/components';
import { dateTimeNow } from 'utils/date';
import { formatRole } from '../helper';

const WorkflowConfirmation = ({
  open, close, closeOnOverlayClick = false, closeOnEsc = false, handleSelect = () => { }, workflowObj = {}, returnToCitizen = false
}) => {
  const {
    fileNo = '', forwardingTo = '', designation = '', role = '', pen = '', seat = ''
  } = workflowObj;

  return (
    <Modal isOpen={open} size="3xl" closeOnOverlayClick={closeOnOverlayClick} onClose={close} closeOnEsc={closeOnEsc} className="custom-form-modal">
      <ModalOverlay />
      <ModalContent className="form-modal" p={0}>
        <ModalBody p={0}>
          <div className="text-[#09327B] text-[24px] font-[600] text-center mt-[19px]">
            {t('fileMovement')}
          </div>
          <div className="text-[#E82C78] text-[14px] font-[600] text-center leading-[25px]">
            {t('fileNo')} : {fileNo}
          </div>
          {returnToCitizen ? <div className="bg-[#F2F2F2] rounded-[8px] leading-[20px] text-center p-5 mt-5 mb-2">{t('fileReturningToCitizen')}</div>
            : (
              <table className="w-full mt-[14px]">
                <tbody>
                  <tr className="bg-[#E7EFF5] rounded-[8px]">
                    <td className="pt-[20px] pr-[30px] pb-[20px] pl-[30px] text-right">
                      <span className="text-[#232F50] text-[16px] font-[400]">{t('forwardTo')}</span>
                    </td>
                    <td className="pl-[30px] w-[50%]">
                      <span className="text-[#09327B] text-[18px] font-[500]">{forwardingTo || ''}</span>
                    </td>
                  </tr>
                  <tr className="bg-white rounded-[8px]">
                    <td className="pt-[20px] pr-[30px] pb-[20px] pl-[30px] text-right">
                      <span className="text-[#232F50] text-[16px] font-[400]">{t('designation')}</span>
                    </td>
                    <td className="pl-[30px] w-[50%]">
                      <span className="text-[#09327B] text-[18px] font-[500]">{designation || ''}</span>
                    </td>
                  </tr>
                  <tr className="bg-[#E7EFF5] rounded-[8px]">
                    <td className="pt-[20px] pr-[30px] pb-[20px] pl-[30px] text-right">
                      <span className="text-[#232F50] text-[16px] font-[400]">{t('role')}</span>
                    </td>
                    <td className="pl-[30px] w-[50%]">
                      <span className="text-[#09327B] text-[18px] font-[500]">{formatRole(role)}</span>
                    </td>
                  </tr>
                  <tr className="bg-white rounded-[8px]">
                    <td className="pt-[20px] pr-[30px] pb-[20px] pl-[30px] text-right">
                      <span className="text-[#232F50] text-[16px] font-[400]">{t('penCap')}</span>
                    </td>
                    <td className="pl-[30px] w-[50%]">
                      <span className="text-[#09327B] text-[18px] font-[500]">{pen || ''}</span>
                    </td>
                  </tr>
                  <tr className="bg-[#E7EFF5] rounded-[8px]">
                    <td className="pt-[20px] pr-[30px] pb-[20px] pl-[30px] text-right">
                      <span className="text-[#232F50] text-[16px] font-[400]">{t('seat')}</span>
                    </td>
                    <td className="pl-[30px] w-[50%]">
                      <span className="text-[#09327B] text-[18px] font-[500]">{seat || ''}</span>
                    </td>
                  </tr>
                  <tr className="bg-white rounded-[8px]">
                    <td className="pt-[20px] pr-[30px] pb-[20px] pl-[30px] text-right">
                      <span className="text-[#232F50] text-[16px] font-[400]">{t('dateAndTime')}</span>
                    </td>
                    <td className="pl-[30px] w-[50%]">
                      <span className="text-[#09327B] text-[18px] font-[500]">{dateTimeNow()}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            )}
        </ModalBody>

        <div className="col-span-12 flex justify-center space-x-2 p-[30px]">
          <Button
            variant="secondary_outline"
            size="xs"
            mr={3}
            onClick={close}
          >
            {t('close')}
          </Button>
          <Button
            variant="secondary"
            type="submit"
            size="xs"
            onClick={handleSelect}
            style={{ width: '109.45px' }}
          >
            {t('ok')}
          </Button>
        </div>
      </ModalContent>
    </Modal>
  );
};

export default WorkflowConfirmation;
