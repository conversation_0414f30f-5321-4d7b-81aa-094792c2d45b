import React from 'react';
import {
  Modal, ModalBody, ModalContent, Modal<PERSON>ooter, ModalOverlay, t, Button, PdfViewer
} from 'common/components';

const CertificateDialog = ({
  open,
  close = () => {},
  previewData,
  loading
}) => {
  return (
    <Modal size="4xl" isOpen={open}>
      <ModalOverlay />
      <ModalContent borderRadius={16} style={{ width: '645px' }} className="bg-white">

        <ModalBody py={6}>
          {loading ? 'loading'
            : (
              <PdfViewer
                data={previewData}
                type="scroll"
                variant="normal"
              />
            )}
        </ModalBody>
        <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center gap-6" />
            <div className="flex items-center gap-2 cursor-pointer">
              <Button
                variant="secondary"
                onClick={close}
              >
                {t('close')}
              </Button>
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CertificateDialog;
