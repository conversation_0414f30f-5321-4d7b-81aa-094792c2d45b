import {
  <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalHeader, ModalOverlay
} from '@ksmartikm/ui-components';
import { FormController, t } from 'common/components';
import React from 'react';
import { useForm } from 'react-hook-form';
import { dark, light } from 'utils/color';
import { yupResolver } from '@hookform/resolvers/yup';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DisposeFileSchema } from '../validate';
import * as actions from '../actions';

function ModalForm({ onSubmitForm }) {
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      mode: 'all',
      disposeReason: ''
    },
    resolver: yupResolver(DisposeFileSchema)
  });

  return (
    <form
      id="dispose-file-form"
      action="enter"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <div className=" col-span-12">
        <FormController
          name="disposeReason"
          type="textarea"
          label={t('disposeReason')}
          placeholder={t('disposeReason')}
          control={control}
          errors={errors}
        />
      </div>
    </form>
  );
}

const ConfirmationModalDisposeFiles = ({
  open, handleClose, fileNo, setActionTriggered, disposeFilesAction
}) => {
  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'dispose-file' });
    const response = {
      fileNos: [fileNo],
      noteUpdateRequest: {
        noteText: data?.disposeReason
      }
    };
    disposeFilesAction(response);
    handleClose();
  };

  return (
    <Modal isOpen={open} size="xl" onClose={handleClose} className="custom-form-modal" closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader className="rounded-full text-center m-5" style={{ background: light, color: dark }}>
          <h4 size="md">
            {t('forDisposal')}
          </h4>
        </ModalHeader>

        <ModalBody>
          <div className="px-5 pt-0 pb-5">
            <ModalForm onSubmitForm={onSubmitForm} />
          </div>
        </ModalBody>

        <ModalFooter style={{ background: light, color: dark }}>
          <div className="col-span-12 flex justify-end items-center space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              mr={3}
              onClick={handleClose}
            >
              {t('cancel')}
            </Button>
            <Button variant="secondary" size="sm" type="submit" form="dispose-file-form">
              {t('save')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({

});

const mapDispatchToProps = (dispatch) => ({
  disposeFilesAction: (data) => dispatch(actions.disposeFilesAction(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ConfirmationModalDisposeFiles);
