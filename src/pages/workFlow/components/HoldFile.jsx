import React from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  FormController, t, Button, Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody
} from 'common/components';

import { useForm } from 'react-hook-form';
import { convertToLocalDate } from 'utils/date';
import { dark, light } from 'utils/color';
import { DATE_FORMAT } from 'pages/common/constants';
import { HoldFileSchema } from '../validate';

function ModalForm({ onSubmitForm }) {
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      mode: 'all',
      holdReason: '',
      toDate: undefined
    },
    resolver: yupResolver(HoldFileSchema)
  });

  return (
    <form
      id="hold-file-form"
      action="enter"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <div className=" col-span-12">
        <FormController
          name="holdReason"
          type="textarea"
          label={t('parkingReason')}
          placeholder={t('holdReason')}
          control={control}
          errors={errors}
        />
      </div>
      <div className="col-span-12 pt-[17px]">
        <FormController
          name="toDate"
          type="date"
          label={t('toDate')}
          placeholder={t('toDate')}
          control={control}
          errors={errors}
          minDate={new Date()}
          id="toDate"
        />
      </div>
    </form>
  );
}

export default function HoldFile({
  open, handleClose, params, saveHoldFile
}) {
  const onSubmitForm = (data) => {
    const saveData = {
      fileNo: params?.fileNo,
      holdReason: data?.holdReason,
      holdUpto: convertToLocalDate(data?.toDate, DATE_FORMAT.DATE_TIME),
      isForParking: true
    };
    saveHoldFile(saveData);
    handleClose();
  };

  return (

    <Modal isOpen={open} size="xl" onClose={handleClose} className="custom-form-modal" closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader className="rounded-full text-center m-5" style={{ background: light, color: dark }}>
          <h4 size="md">
            {t('parkFile')}
          </h4>
        </ModalHeader>

        <ModalBody>
          <div className="px-5 pt-0 pb-5">
            <ModalForm onSubmitForm={onSubmitForm} />
          </div>
        </ModalBody>

        <ModalFooter style={{ background: light, color: dark }}>
          <div className="col-span-12 flex justify-end items-center space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              mr={3}
              onClick={handleClose}
            >
              {t('cancel')}
            </Button>
            <Button variant="secondary" size="sm" type="submit" form="hold-file-form">
              {t('save')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
