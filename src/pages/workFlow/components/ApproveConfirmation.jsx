import { <PERSON><PERSON><PERSON>t, FormLabel } from '@ksmartikm/ui-components';
import { FormController, t } from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { useState, useEffect } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import _ from 'lodash';
import DisposalTypeIcon from 'assets/DisposalType';
import { primary } from 'utils/color';
import { getServiceValidation } from 'pages/common/selectors';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import { getCloseFileConfirm, getFileCloseType } from '../selectors';
import {
  WORKFLOW_DIALOG_APPROVE_STAGE, WORKFLOW_DISPOSAL_FIELDS, WORKFLOW_DISPOSAL_TYPE, WORKFLOW_DISPOSAL_TYPE_OPTIONS
} from '../constants';
import { closeConfirmSchema } from '../validate';

const Confirm = () => {
  return (
    <div className="px-10 pt-5 pb-10">
      <div className="text-center mb-4">
        <DisposalTypeIcon width="48px" height="48px" color={primary} />
      </div>
      <div className="text-center text-[24px] font-[600] leading-[40px]">
        {t('doYouWantToDisposeTheFile')}
      </div>
    </div>
  );
};

const ConfirmYes = ({
  fileCloseType,
  serviceValidation,
  setSelectedType,
  setSelectedMode,
  setHoldDate
}) => {
  const {
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: {
      fileCloseType: null,
      fileCloseTypeAction: 1,
      toDate: undefined
    },
    resolver: yupResolver(closeConfirmSchema)
  });

  useEffect(() => {
    if (serviceValidation) {
      setValue('fileCloseType', serviceValidation?.disposalType);
      setSelectedType(serviceValidation?.disposalType);
    }
  }, [serviceValidation]);

  const handleFieldChange = (data, field) => {
    switch (field) {
      case WORKFLOW_DISPOSAL_FIELDS.FILE_CLOSE_TYPE:
        setValue('fileCloseType', Number(data.id));
        setSelectedType(Number(data.id));
        break;
      case WORKFLOW_DISPOSAL_FIELDS.FILE_CLOSE_TYPE_ACTION:
        setValue('fileCloseTypeAction', data);
        setSelectedMode(data);
        break;
      case WORKFLOW_DISPOSAL_FIELDS.FILE_HOLD_DATE:
        setValue('toDate', data);
        setHoldDate(data);
        break;
      default:
        break;
    }
  };

  const onSubmitForm = () => {

  };

  return (
    <p>
      <form
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div className="py-10 px-10 grid gap-5">
          <div className="col-span-12">
            <FormLabel label={t('modeofDisposal')} />
            <FormController
              name="fileCloseTypeAction"
              type="radio"
              optionKey="id"
              control={control}
              errors={errors}
              options={WORKFLOW_DISPOSAL_TYPE}
              handleChange={(data) => handleFieldChange(data, WORKFLOW_DISPOSAL_FIELDS.FILE_CLOSE_TYPE_ACTION)}
            />
          </div>
          {watch('fileCloseTypeAction') === WORKFLOW_DISPOSAL_TYPE_OPTIONS.TEMPORARY
            && (
              <div className="col-span-12">
                <FormController
                  name="toDate"
                  type="date"
                  label={t('toDate')}
                  placeholder={t('toDate')}
                  control={control}
                  errors={errors}
                  id="toDate"
                  handleChange={(data) => handleFieldChange(data, WORKFLOW_DISPOSAL_FIELDS.FILE_HOLD_DATE)}
                  minDate={new Date()}
                />
              </div>
            )}
          {watch('fileCloseTypeAction') === WORKFLOW_DISPOSAL_TYPE_OPTIONS.PERMANENT
            && (
              <div className="col-span-12">
                <FormController
                  name="fileCloseType"
                  variant="outlined"
                  type="select"
                  label={t('fileDisposalType')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  options={_.get(fileCloseType, 'data', [])}
                  handleChange={(data) => handleFieldChange(data, WORKFLOW_DISPOSAL_FIELDS.FILE_CLOSE_TYPE)}
                />
              </div>
            )}
        </div>
      </form>
    </p>
  );
};

const ApproveConfirmation = (props) => {
  const {
    closeFileConfirm,
    setCloseFileConfirm,
    fetchFileDisposalType,
    fileCloseType,
    approveWithoutCloseFile,
    approveWithCloseFile,
    serviceValidation,
    dialogStage,
    setDialogStage
  } = props;

  const [selectedType, setSelectedType] = useState('');
  const [selectedMode, setSelectedMode] = useState('');
  const [holdDate, setHoldDate] = useState(null);

  const handleClose = () => {
    setDialogStage(WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM);
    setCloseFileConfirm(false);
  };

  const yesFunc = () => {
    fetchFileDisposalType();
    setDialogStage(WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM_YES);
  };

  const triggerWorkFlow = (data) => {
    if (data === 'permanantDisposal') {
      approveWithCloseFile(selectedType, selectedMode, holdDate);
    } else {
      approveWithoutCloseFile();
    }
    setCloseFileConfirm(false);
  };

  const contentFunc = () => {
    switch (dialogStage) {
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM:
        return <Confirm />;
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM_YES:
        return <ConfirmYes fileCloseType={fileCloseType} serviceValidation={serviceValidation} setSelectedType={setSelectedType} setSelectedMode={setSelectedMode} selectedMode={selectedMode} setHoldDate={setHoldDate} />;
      default:
        return <Confirm />;
    }
  };

  const actionFuncTextForward = () => {
    switch (dialogStage) {
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM:
        return t('yes');
      case WORKFLOW_DIALOG_APPROVE_STAGE.DYSPOSAL_TYPE:
        return t('permanantDisposal');
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM_YES:
        return t('submit');
      default:
        return ('permanantDisposal');
    }
  };

  const actionFuncTextBackward = () => {
    switch (dialogStage) {
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM:
        return t('no');
      case WORKFLOW_DIALOG_APPROVE_STAGE.DYSPOSAL_TYPE:
        return t('temporaryDisposal');
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM_YES:
        return t('cancel');
      default:
        return ('temporaryDisposal');
    }
  };

  const actionFunc = () => {
    switch (dialogStage) {
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM:
        return yesFunc();
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM_YES:
        return triggerWorkFlow('permanantDisposal');
      case WORKFLOW_DIALOG_APPROVE_STAGE.CONFIRM_NO:
        return triggerWorkFlow('temporaryDisposal');
      default:
        return yesFunc();
    }
  };

  return (
    <CustomAlert
      title={t('confirmation')}
      open={closeFileConfirm}
      close={handleClose}
      backwardActionText={actionFuncTextBackward()}
      forwardActionText={actionFuncTextForward()}
      content={contentFunc()}
      modalSize="xl"
      actionForward={() => actionFunc()}
      actionBackward={() => { triggerWorkFlow('temporaryDisposal'); }}
      closeOnOverlayClick
      closeOnEsc
      haveClose
    />
  );
};

const mapStateToProps = createStructuredSelector({
  closeFileConfirm: getCloseFileConfirm,
  fileCloseType: getFileCloseType,
  serviceValidation: getServiceValidation
});

const mapDispatchToProps = (dispatch) => ({
  setCloseFileConfirm: (data) => dispatch(sliceActions.setCloseFileConfirm(data)),
  fetchFileDisposalType: () => dispatch(actions.fetchFileDisposalType())
});

export default connect(mapStateToProps, mapDispatchToProps)(ApproveConfirmation);
