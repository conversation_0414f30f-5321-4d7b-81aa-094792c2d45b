import {
  all, takeLatest, call, fork, take, put
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { t } from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
// import { HOME_PATH } from 'common/constants';
import { routeRedirect } from 'utils/common';
import { fetchFileDetails } from 'pages/file/details/saga';
import { ACTION_TYPES } from './actions';
import * as api from './api';

export function* fetchDepartments({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchDepartmentApi, payload);
}
export function* fetchPostByFunctionalGroup({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostByFunctionalGroup, payload);
}
export function* fetchWorkflowDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchWorkflow, payload);
}

export function* saveHoldFileData({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveHoldFileData, payload);
  const { type } = yield take([
    ACTION_TYPES.SAVE_HOLD_FILE_SUCCESS,
    ACTION_TYPES.SAVE_HOLD_FILE_FAILURE]);
  if (type === ACTION_TYPES.SAVE_HOLD_FILE_SUCCESS) {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('fileSuccessfullyParked'), title: t('success'), backwardActionText: t('ok'), backwardAction: () => routeRedirect('ui/home/<USER>/dashboard/files'), closeOnEsc: false, closeOnOverlayClick: false
    }));
  } else {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('fileFailedtoParkedPleaseTryAgain'), title: t('Failed'), backwardActionText: t('ok'), closeOnEsc: false, closeOnOverlayClick: false
    }));
  }
}

export function* actionFile({ payload = {} }) {
  yield fork(handleAPIRequest, api.actionFile, payload);
  const {
    type, payload: { error }
  } = yield take([
    ACTION_TYPES.ACTION_FILE_SUCCESS,
    ACTION_TYPES.ACTION_FILE_FAILURE]);
  if (type === ACTION_TYPES.ACTION_FILE_SUCCESS) {
    routeRedirect('ui/home/<USER>/dashboard/files');
    // const successMessage = `${t('workFlowActionSuccess')}`;
    // yield put(commonSliceActions.setAlertAction({
    //   open: true, variant: 'success', message: successMessage, title: t('workFlowAction'), backwardActionText: t('ok'), backwardAction: () => routeRedirect('ui/home/<USER>/dashboard'), closeOnEsc: false, closeOnOverlayClick: false
    // }));
    // setTimeout(() => {
    localStorage.removeItem('notes-unsaved');
    // }, 500);
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'file-flow' }));
  }
  if (type === ACTION_TYPES.ACTION_FILE_FAILURE) {
    const { response } = error;
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'file-flow' }));
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: response.data.message,
      title: t('workFlowAction'),
      backwardActionText: t('ok')
    }));
  }
}

// export function* actionFile({ payload = {} }) {
//   yield fork(handleAPIRequest, api.actionFile, payload);
//   const {
//     type, payload: { error }
//   } = yield take([
//     ACTION_TYPES.ACTION_FILE_SUCCESS,
//     ACTION_TYPES.ACTION_FILE_FAILURE]);
//   if (type === ACTION_TYPES.ACTION_FILE_SUCCESS) {
//     yield put(commonSliceActions.setAlertAction({
//       open: true, variant: 'success', message: t('workFlowActionSuccess'), title: t('workFlowAction'), backwardActionText: t('ok')
//     }));
//     window.location.href = HOME_PATH;
//   }
//   if (type === ACTION_TYPES.ACTION_FILE_FAILURE) {
//     const { response } = error;
//     yield put(commonSliceActions.setAlertAction({
//       open: true,
//       variant: 'error',
//       message: response.data.message,
//       title: t('workFlowAction'),
//       backwardActionText: t('ok')
//     }));
//   }
// }

export function* fetchFileDisposalType({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFileDisposalType, payload);
}

export function* fetchIsGroupedService({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchIsGroupedService, payload);
}

export function* fetchNextRoleDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchNextRoleDetails, payload);
  const {
    type, payload: { error }
  } = yield take([
    ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_FAILURE) {
    const { response } = error;
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: response?.data?.message,
      title: t('error'),
      backwardActionText: t('ok')
    }));
  }
}

export function* fetchPostBytLocation({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostBytLocation, payload);
}

export function* sendFileForDisposalOrNot({ payload = {} }) {
  yield fork(handleAPIRequest, api.sendFileForDisposalOrNot, payload?.responseBody);
  const { type } = yield take([
    ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT_SUCCESS,
    ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT_FAILURE]);
  if (type === ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT_SUCCESS) {
    yield call(fetchFileDetails, { payload: payload?.responseBody?.fileNo });

    if (payload?.type === 'forDisposal') {
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'success', message: t('successfullySendThisFileForDisposal'), title: t('success'), backwardActionText: t('ok'), closeOnEsc: false, closeOnOverlayClick: false
      }));
    } else if (payload?.type === 'forNotDisposal') {
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'success', message: t('successfullyUncheckThisFileFromDispose'), title: t('success'), backwardActionText: t('ok'), closeOnEsc: false, closeOnOverlayClick: false
      }));
    }
  } else if (payload?.type === 'forDisposal') {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('sendThisFileForDisposalFailed'), title: t('error'), backwardActionText: t('ok'), closeOnEsc: false, closeOnOverlayClick: false
    }));
  } else if (payload?.type === 'forNotDisposal') {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('uncheckThisFileFromDisposeFailed'), title: t('error'), backwardActionText: t('ok'), closeOnEsc: false, closeOnOverlayClick: false
    }));
  }
}

export function* disposeFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.disposeFiles, payload);
  const { type } = yield take([
    ACTION_TYPES.DISPOSE_FILES_SUCCESS,
    ACTION_TYPES.DISPOSE_FILES_FAILURE
  ]);
  if (type === ACTION_TYPES.DISPOSE_FILES_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'dispose-file' }));
    yield call(fetchFileDetails, { payload: payload?.fileNos[0] });

    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: t('successfullyDisposed'),
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect('ui/home/<USER>/dashboard/dispose')
      })
    );
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'dispose-file' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: t('DisposedFailed'),
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* fetchOfficeWisePostDetails({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchOfficeWisePostDetails, payload);
}

export default function* inboxSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_DEPARTMENTS, fetchDepartments),
    takeLatest(ACTION_TYPES.FETCH_WORKFLOW, fetchWorkflowDetails),
    takeLatest(ACTION_TYPES.SAVE_HOLD_FILE, saveHoldFileData),
    takeLatest(ACTION_TYPES.ACTION_FILE, actionFile),
    takeLatest(ACTION_TYPES.FETCH_POST_BY_FUNC_GROUP, fetchPostByFunctionalGroup),
    takeLatest(ACTION_TYPES.FETCH_FILE_DISPOSAL_TYPE, fetchFileDisposalType),
    takeLatest(ACTION_TYPES.FETCH_IS_GROUPED_SERVICE, fetchIsGroupedService),
    takeLatest(ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS, fetchNextRoleDetails),
    takeLatest(ACTION_TYPES.FETCH_POST_BY_LOCATION, fetchPostBytLocation),

    takeLatest(ACTION_TYPES.SEND_FILE_FOR_DISPOSAL_OR_NOT, sendFileForDisposalOrNot),
    takeLatest(ACTION_TYPES.DISPOSE_FILES, disposeFiles),
    takeLatest(ACTION_TYPES.FETCH_OFFICE_WISE_POST_DETAILS, fetchOfficeWisePostDetails)
  ]);
}
