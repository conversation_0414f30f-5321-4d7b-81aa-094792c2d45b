import { baseApiURL } from 'utils/http';

export const STATE_REDUCER_KEY = 'workflow';

export const BPL_CERTICATE_URL = `${baseApiURL}/file-management-services/generate-certificate`;

export const STATUS = {
  RUNNING: 'RUNNING'
};

export const WORKFLOW_ACTIONS = {
  ROUTE_CHANGE: 'Forward Plus',
  APPROVE: 'Approve',
  REJECT: 'Reject',
  RETURN_TO_CITIZEN: 'Return to Citizen',
  FORWARD: 'Forward'
};

export const WORKFLOW_DIALOG_APPROVE_STAGE = {
  CONFIRM: 'CONFIRM',
  DYSPOSAL_TYPE: 'DYSPOSAL_TYPE',
  CONFIRM_YES: 'CONFIRM_YES',
  CONFIRM_NO: 'CONFIRM_NO'
};

export const WORKFLOW_DISPOSAL_TYPE = [
  { id: 'permanent', name: 'Permanent' },
  { id: 'temporary', name: 'Temporary' }
];
export const WORKFLOW_DISPOSAL_TYPE_OPTIONS = {
  PERMANENT: 'permanent',
  TEMPORARY: 'temporary'
};

export const DEFAULT_WORKFLOW_DISPOSAL_TYPE = 1;

export const WORKFLOW_DISPOSAL_FIELDS = {
  FILE_CLOSE_TYPE_ACTION: 'fileCloseTypeAction',
  FILE_CLOSE_TYPE: 'fileCloseType',
  FILE_HOLD_DATE: 'fileHoldDate'
};

export const WORKFLOW_ROUTE_CHANGE_OPTIONS = [
  { id: 1, name: 'Zonal/Circle' },
  { id: 2, name: 'Functional Groups' }
];
