import * as yup from 'yup';

import { t } from 'common/components';
import { WORKFLOW_ACTIONS } from './constants';

const today = new Date();
today.setHours(0, 0, 0, 0);

export const HoldFileSchema = yup.object().shape({

  holdReason: yup.string().required('Please enter parking reason'),
  toDate: yup.date()
    .required('Please select To Date')
    .min(today, 'Date must be today or later.')
});

export const workFlowSchema = yup.object().shape({
  actions: yup.string().required('Please select Actions'),
  // department: yup.string()
  //   .when(['actions', 'routeType'], (actions, schema) => {
  //     if (actions[0] === WORKFLOW_ACTIONS.ROUTE_CHANGE) {
  //       return schema.required('Please select Functional Group');
  //     }
  //     return schema.notRequired();
  //   }),
  officeType: yup.string()
    .when(['actions', 'routeType'], (actions, schema) => {
      if (actions[0] === WORKFLOW_ACTIONS.ROUTE_CHANGE && actions[1] === 1) {
        return schema.required(t('isRequired', { type: `${t('officeType')}` }));
      }
      return schema.notRequired();
    }),
  assignee: yup.string()
    .when(['actions'], (actions, schema) => {
      if (actions[0] === WORKFLOW_ACTIONS.ROUTE_CHANGE) {
        return schema.required(t('isRequired', { type: `${t('forwardTo')}` }));
      }
      return schema.notRequired();
    }),
  user: yup.string().nullable()
    .when(['actions', '$nextRole', '$nextWorkFlowRole'], {
      is: (actions, nextRole, nextWorkFlowRole) => {
        if ((actions[0] !== WORKFLOW_ACTIONS.ROUTE_CHANGE || actions[0] !== WORKFLOW_ACTIONS.RETURN_TO_CITIZEN) && nextRole?.length > 1 && Object.keys(nextWorkFlowRole).length === 0) {
          return true;
        } if ((actions[0] !== WORKFLOW_ACTIONS.ROUTE_CHANGE || actions[0] !== WORKFLOW_ACTIONS.RETURN_TO_CITIZEN) && nextRole?.length === 1 && Object.keys(nextWorkFlowRole).length === 0) {
          return true;
        }
        return false;
      },
      then: (schema) => schema.required(t('isRequired', { type: t('forwardTo') })),
      otherwise: (schema) => schema.optional()
    })
});

export const closeConfirmSchema = yup.object().shape({
  fileCloseType: yup.string().required(t('isRequired', { type: t('holdReason') }))
});

export const unHoldSchema = yup.object().shape({
  reason: yup.string().required(t('isRequired', { type: t('reason') }))
});

export const DisposeFileSchema = yup.object().shape({
  disposeReason: yup.string().required(t('isRequired', { type: t('disposeReason') }))
});
