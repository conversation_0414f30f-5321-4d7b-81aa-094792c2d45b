import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { ACTION_TYPES } from './actions';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  departments: {},
  workflowDropdownList: {},
  closeFileConfirm: false
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setCloseFileConfirm: (state, { payload }) => {
      _.set(state, 'closeFileConfirm', payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS, (state, { payload }) => {
        _.set(state, 'departments', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_FUNC_GROUP_SUCCESS, (state, { payload }) => {
        _.set(state, 'postByFunctionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_WORKFLOW_SUCCESS, (state, { payload }) => {
        _.set(state, 'workflowDropdownList', payload);
      })
      .addCase(ACTION_TYPES.FETCH_FILE_DISPOSAL_TYPE_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileCloseType', payload);
      })
      .addCase(ACTION_TYPES.FETCH_IS_GROUPED_SERVICE_SUCCESS, (state, { payload }) => {
        _.set(state, 'isGroupedService', payload);
      })
      .addCase(ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_SUCCESS, (state, { payload }) => {
        _.set(state, 'nextRoleDetails', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_NEXT_ROLE_DETAILS_FAILURE, (state) => {
        _.set(state, 'nextRoleDetails', []);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_LOCATION_SUCCESS, (state, { payload }) => {
        _.set(state, 'postByFunctionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_OFFICE_WISE_POST_DETAILS_SUCCESS, (state, { payload }) => {
        _.set(state, 'officeWisePostDetails', payload);
      });
  }
});
export const { actions, reducer } = slice;
