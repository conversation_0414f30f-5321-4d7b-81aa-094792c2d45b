import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_PUBLIC_FILE_LOG_REPORT: `${STATE_REDUCER_KEY}/FETCH_PUBLIC_FILE_LOG_REPORT`,
  FETCH_PUBLIC_FILE_LOG_REPORT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PUBLIC_FILE_LOG_REPORT_REQUEST`,
  FETCH_PUBLIC_FILE_LOG_REPORT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PUBLIC_FILE_LOG_REPORT_SUCCESS`,
  FETCH_PUBLIC_FILE_LOG_REPORT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PUBLIC_FILE_LOG_REPORT_FAILURE`

};

export const fetchPublicFileLogList = createAction(ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT);
