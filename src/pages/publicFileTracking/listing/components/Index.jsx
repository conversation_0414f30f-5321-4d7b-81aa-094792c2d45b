import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  <PERSON><PERSON><PERSON><PERSON>on, FormController, FormWrapper, t
} from 'common/components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getTableLoader } from 'pages/common/selectors';
import { But<PERSON>, Spinner } from '@ksmartikm/ui-components';
import { useForm } from 'react-hook-form';
import { getTableData } from '../selectors';
import * as actions from '../actions';
import LogStatusModal from './LogStatusModal';

const Index = ({
  tableData, fetchPublicFileLogList, setTableLoader, tableLoader
}) => {
  const {
    control, handleSubmit
  } = useForm({
    mode: 'all',
    defaultValues: {
      fileNo: '',
      applicationNo: ''
    }
  });

  const [fileNumber, setFileNumber] = useState(null);
  const [applicationNo, setApplicationNo] = useState(null);
  const [openFileStatusModal, setOpenFileStatusModal] = useState(false);
  const [modalData, setModalData] = useState([]);

  const handleCloseFileStatusModal = () => {
    setOpenFileStatusModal(false);
  };

  const backToHome = () => {
    window.location.href = `${window?.location?.origin}/ui/web-portal`;
  };

  const onSubmitForm = () => {
    if (fileNumber && !applicationNo) {
      setTableLoader({ loading: true, id: 'public-file-log' });
      fetchPublicFileLogList({ fileNo: fileNumber });
    } else if (applicationNo && !fileNumber) {
      setTableLoader({ loading: true, id: 'public-file-log' });
      fetchPublicFileLogList({ applicationNumber: applicationNo });
    } else {
      setTableLoader({ loading: true, id: 'public-file-log' });
      fetchPublicFileLogList({ applicationNumber: applicationNo, fileNo: fileNumber });
    }
  };

  useEffect(() => {
    if (tableData) {
      if (Object.keys(tableData).length > 0) {
        setModalData(tableData);
        setOpenFileStatusModal(true);
      } else {
        setModalData({});
      }
    }
  }, [tableData]);

  const handleFieldChange = (field, data) => {
    switch (field) {
      case 'fileNo':
        setFileNumber(data.target.value);
        break;
      case 'applicationNo':
        setApplicationNo(data.target.value);
        break;
      default:
        break;
    }
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('fileTracking')}
        </div>
      </div>

      <form
        id="public-file-tracking"
        action="enter"
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div className="flex gap-4 bg-white p-5 rounded-[10px]">
          <FormWrapper>
            <div className="lg:col-span-3 md:col-span-6 col-span-12">
              <FormController
                name="fileNo"
                type="text"
                placeholder="File No : 1-2024"
                label={t('fileNo')}
                control={control}
                handleChange={(data) => handleFieldChange('fileNo', data)}
              />
            </div>
            <div className="lg:col-span-3 md:col-span-6 col-span-12">
              <FormController
                name="applicationNo"
                type="text"
                label={t('applicationNumber')}
                control={control}
                handleChange={(data) => handleFieldChange('applicationNo', data)}
              />
            </div>
            <div className="lg:col-span-3 md:col-span-6 col-span-12">

              <Button
                type="submit"
                variant="secondary_outline"
                className="shadow-md"
                form="public-file-tracking"
                isLoading={tableLoader?.loading && tableLoader?.id === 'public-file-log'}
                isDisabled={!applicationNo && !fileNumber}
              >
                {t('search')}
              </Button>
            </div>

          </FormWrapper>
        </div>
      </form>
      {
        tableLoader?.loading && tableLoader?.id === 'public-file-log' ? <Spinner style={{ marginTop: '230px', marginLeft: 'calc(50% - 30px)' }} />

          : <LogStatusModal openFileStatusModal={openFileStatusModal} handleCloseFileStatusModal={handleCloseFileStatusModal} modalData={modalData} />
      }
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  tableData: getTableData,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchPublicFileLogList: (data) => dispatch(actions.fetchPublicFileLogList(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
