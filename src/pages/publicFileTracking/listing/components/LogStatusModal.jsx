import React from 'react';
import {
  t, FormModal
} from 'common/components';
import './FileLog.css';
import { STATUS } from 'common/regex';
import _ from 'lodash';
import { TickIcon } from 'assets/TickIcon';
import { convertToLocalTimeZone } from 'utils/date';
import FileLogUserIcon from 'assets/FileLogUserIcon';
import FileLogTimeIcon from 'assets/FileLogTimeIcon';
import FileLogFileIcon from 'assets/FileLogFileIcon';
import FileLogCurrentStatusIcon from 'assets/FileLogCurrentStatusIcon';
import FileLogArrowEnd from 'assets/FileLogArrowEnd';
import NoNotesIcon from 'assets/NoNotesIcon';

const TimelineItem = ({
  data, isCurrentUser, val
}) => {
  const showActionDetails = (statusVal) => {
    if (statusVal === 'REJECTED') {
      return 'and Rejected on';
    } if (statusVal === 'RETURNED') {
      return 'and Returned on';
    } if (statusVal === 'RETURN_TO_CITIZEN') {
      return 'and Return to citizen on';
    } if (statusVal === 'APPROVED') {
      return 'and Approved on';
    } if (statusVal === 'ROUTE_CHANGE') {
      return 'and Route changed on';
    } if (statusVal === 'RETURN_TO_CUSTODIAN') {
      return 'and Return to custodian on';
    } if (statusVal === 'ENQUIRY') {
      return 'and Forwarded for enquiry on';
    }
    return 'and forwarded on';
  };

  const formatedDate = (statusVal, arrayVal) => {
    if (statusVal === 'RECEIVED') {
      return convertToLocalTimeZone(data?.fileReceivedDate);
    } if (data?.stage === 'APPLIED' && arrayVal?.fileActionLogs?.length === 1) {
      return convertToLocalTimeZone(val?.fileDate);
    }
    return convertToLocalTimeZone(data?.actionTakenDate);
  };

  const formatApplicantName = (allData, outSideData) => {
    if (isCurrentUser && outSideData?.stage === 'RETURN_TO_CITIZEN') {
      return outSideData?.applicantName;
    } if (allData?.stage === 'APPLIED') {
      return outSideData?.applicantName;
    }
    return _.capitalize(allData?.employeeName?.replace(STATUS, ' '));
  };

  const formatDesignation = (allData, outSideData) => {
    if (isCurrentUser && outSideData?.stage === 'RETURN_TO_CITIZEN') {
      return <span className="p-[7px]">has</span>;
    } if (allData?.stage === 'APPLIED') {
      return <span className="p-[7px]">has</span>;
    }
    return <span className="p-[7px]">{_.capitalize(data?.designation?.replace(STATUS, ' '))}(Seat {data?.postnameEng}) has</span>;
  };

  return (
    <li className="timeline-item relative">
      {isCurrentUser && (
        <div
          className="absolute"
          style={{
            background: 'white', width: '110px', top: 0, height: 'calc(100% - 78px - 25%)', left: '-55px'
          }}
        />
      )}
      <div className="bg-white pt-[10px] pb-[10px] px-[10px] rounded-[20px] absolute log-circle w-[76px]">
        <div className="relative">
          {isCurrentUser
            ? (
              <FileLogCurrentStatusIcon style={{ margin: 'auto' }} />
            )
            : <TickIcon style={{ margin: 'auto' }} />}
          <div className="text-[#959595] not-italic leading-normal pt-[4px] text-center  bg-white pb-[10px] text-[10px]"> <span className="font-bold text-[12px]">{data?.stage === 'APPLIED' && val?.fileActionLogs?.length === 1 ? convertToLocalTimeZone(val?.fileDate) : convertToLocalTimeZone(data?.fileReceivedDate)}</span></div>

          <FileLogArrowEnd style={{
            margin: 'auto', position: 'absolute', bottom: isCurrentUser ? '86px' : '73px', left: isCurrentUser ? 'calc(50% - 4px)' : 'calc(50% - 5px)', transform: 'rotate(180deg)'
          }}
          />
        </div>

      </div>

      <div className="w-full rounded-[16px] border-[#EFF2F5] border-2 mb-[10px] px-[10px] py-[20px]" style={{ background: '#FBFCFE' }}>

        {data?.stage !== 'APPLIED' && data?.stage !== 'RECEIVED' && (
          <div className="flex gap-2 px-3 items-center mb-[20px]">
            <FileLogFileIcon />
            <div className="flex-grow text-[#454545] text-[13px] font-semibold">
              <div className="w-full">
                File received at this seat on  <span className="text-[#09327B]">{convertToLocalTimeZone(data?.fileReceivedDate)}</span> {showActionDetails(data?.stage)} <span className="text-[#09327B]">{convertToLocalTimeZone(data?.actionTakenDate)}</span>
              </div>
            </div>
          </div>
        )}
        <div className="flex gap-2 px-3 mb-[20px] items-center">
          <FileLogUserIcon />
          <div className="flex-grow text-[#454545] text-[13px] font-semibold">
            <div className="w-full">
              <span className="text-[#09327B]">{formatApplicantName(data, val)}</span>,
              {formatDesignation(data, val)}
              <span className="text-[#00B2EC] text-[14px]">{_.capitalize(data?.stage?.replace(STATUS, ' '))}</span>
            </div>
            <div className="w-full text-[#454545] text-[13px] font-semibold">
              this file on  <span className="text-[#09327B]">{formatedDate(data?.stage, val)}</span>
            </div>
          </div>
        </div>

        {data?.stage !== 'APPLIED' && data?.stage !== 'RECEIVED' && (
          <div className="flex gap-2 px-3 items-center">
            <FileLogTimeIcon />
            <div className="flex-grow text-[#454545] text-[13px] font-semibold">
              <div className="w-full">
                The Total time taken at this seat is <span className="text-[#E82C78]">{data?.duration === '1 days' ? '1 day' : data?.duration}</span>
              </div>
            </div>
          </div>
        )}

      </div>
    </li>
  );
};
const Timeline = ({ val }) => {
  const parsedVal = JSON.parse(JSON.stringify(val?.fileActionLogs)) || [];
  const updatedVal = parsedVal;

  const currentUserObj = {
    employeeName: val?.currentUser?.employeeName,
    fileReceivedDate: val?.currentUserFileReceivedAt,
    postnameEng: val?.currentUser?.postNameInEng,
    stage: 'RECEIVED',
    designation: val?.currentUser?.designation
  };
  return (
    <>
      <div className="flex px-5">
        <div className="flex-grow">
          <span className="text-[#878787] text-[20px] font-semibold">{t('fileNo')}</span> : <span className="text-[#E82C78] text-[20px] font-semibold">{val?.fileNo}</span> <span className="text-[#000000] text-[20px] font-semibold p-[5px]">/</span> <span className="text-[#09327B] text-[20px] font-semibold">{val?.localBodyName}</span>
        </div>
      </div>
      <ul className="timeline-container h-[490px] overflow-y-auto">

        {updatedVal?.map((data, index) => (
          <TimelineItem data={data} key={updatedVal?.length} isCurrentStage={Number(updatedVal?.length) - 1} isCurrentUser={false} val={val} index={index} />
        ))}
        <TimelineItem data={currentUserObj} isCurrentStage={Number(updatedVal?.length) - 1} isCurrentUser val={val} index={Number(updatedVal?.length) - 1} />
      </ul>
    </>

  );
};

const LogtatusModal = ({
  openFileStatusModal, handleCloseFileStatusModal, modalData
}) => {
  return (
    <FormModal
      modalTitle={t('fileTracking')}
      open={openFileStatusModal}
      close={() => {
        handleCloseFileStatusModal();
      }}
      modalSize="4xl"
      content={modalData?.fileActionLogs?.length > 0 ? (
        <div>
          <Timeline val={modalData} />
        </div>
      )
        : (
          <div className="p-10 text-center">
            <NoNotesIcon width="100px" height="100px" className="m-auto" />
            <h4>{t('noRecordsFound')}</h4>
          </div>
        )}
      formId="pull-form"
      type="button"
      closeButtonText={t('close')}
      closeOnOverlayClick={false}
      closeOnEsc={false}
      actionButtonText=""
    />
  );
};

export default LogtatusModal;
