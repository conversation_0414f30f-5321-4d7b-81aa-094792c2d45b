import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchPublicFileLogList = (params) => {
  return {
    url: API_URL.PUBLIC_FILE_TRACKING,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT_REQUEST,
        ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT_SUCCESS,
        ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT_FAILURE
      ],
      params
    }
  };
};
