import {
  all, fork, put, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'common/components';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

export function* fetchPublicFileLogList({ payload }) {
  yield fork(handleAPIRequest, api.fetchPublicFileLogList, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT_SUCCESS,
    ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT_FAILURE]);
  if (type === ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT_SUCCESS) {
    yield put(sliceActions.setTableData(_.get(responsePayLoad, 'data', {})));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'public-file-log' }));
  } else {
    const { error: { response: { data: { message = '' } = {} } = {} } = {} } = responsePayLoad;
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'warning',
      message,
      title: t('warning'),
      backwardActionText: t('ok')
    }));
    yield put(sliceActions.setTableData([]));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'public-file-log' }));
  }
}

export default function* reportsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_PUBLIC_FILE_LOG_REPORT, fetchPublicFileLogList)
  ]);
}
