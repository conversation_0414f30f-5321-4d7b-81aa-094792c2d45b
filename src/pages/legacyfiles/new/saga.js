import {
  all, takeLatest, call, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { Toast, t } from 'common/components';
import _ from 'lodash';
import { getSidebarData, getUserInfo } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { fetchApplication } from 'pages/counter/new/saga';
import { MODULE_PATH } from 'common/constants';
import { routeRedirect } from 'utils/common';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { getApiParams } from './selectors';
import { LEGACYFILES_APPLICATION_KEYS } from './constants';

const { successTost, errorTost } = Toast;

export function* fetchLegacyFile({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchLegacyFile, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_LEGACY_FILE_SUCCESS,
    ACTION_TYPES.FETCH_LEGACY_FILE_FAILURE]);
  if (type === ACTION_TYPES.FETCH_LEGACY_FILE_SUCCESS) {
    yield put(sliceActions.setLegacyFileList(_.get(responsePayLoad, 'data', {})));
  }
}

export function* saveApplication({ payload = {} }) {
  const apiParams = yield select(getApiParams);
  const formComponentData = yield select(getSidebarData);
  yield fork(handleAPIRequest, api.saveApplicationServiceApi, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.SAVE_APPLICATION_SERVICE_FAILURE]);
  if (type === ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'legacyServiceCreate' }));
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('serviceSuccessfullySaved') });
    yield put(commonSliceActions.setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service));
    yield call(fetchLegacyFile, { payload: apiParams });
    yield put(commonSliceActions.setSidebarStatus({ activeStep: 1, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    yield put(commonSliceActions.navigateTo({ to: `${MODULE_PATH}/legacyfiles/create/${responsePayLoad?.data?.inwardId}` }));
  } else {
    const { error: { response: { data: { errors = {}, message = '' } = {} } = {} } = {} } = responsePayLoad;
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'legacyServiceCreate' }));
    if (errors?.lastCorrespondenceDate) {
      yield call(errorTost, { id: t('error'), title: t('error'), description: errors?.lastCorrespondenceDate });
    }
    if (errors?.fileStartDate) {
      yield call(errorTost, { id: t('error'), title: t('error'), description: errors?.fileStartDate });
    }
    if (errors?.fileEndDate) {
      yield call(errorTost, { id: t('error'), title: t('error'), description: errors?.fileEndDate });
    }
    if (message) {
      yield call(errorTost, { id: t('error'), title: t('error'), description: message });
    }
  }
}

export function* updateApplication({ payload = {} }) {
  const apiParams = yield select(getApiParams);
  yield fork(handleAPIRequest, api.updateApplicationServiceApi, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.UPDATE_APPLICATION_SERVICE_FAILURE]);
  const formComponentData = yield select(getSidebarData);
  if (type === ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'legacyServiceCreate' }));
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('legacySuccessfullyUpdated') });
    yield put(commonSliceActions.setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service));
    yield call(fetchLegacyFile, { payload: apiParams });
    yield put(commonSliceActions.setSidebarStatus({ activeStep: 1, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'legacyServiceCreate' }));
    yield call(errorTost, { id: t('error'), title: t('error'), description: responsePayLoad?.message });
  }
}

export function* deleteLegacyFile({ payload = {} }) {
  const apiParams = yield select(getApiParams);
  yield fork(handleAPIRequest, api.deleteLegacyFile, payload);
  const { type } = yield take([
    ACTION_TYPES.DELETE_LEGACY_FILE_SUCCESS,
    ACTION_TYPES.DELETE_LEGACY_FILE_FAILURE]);
  if (type === ACTION_TYPES.DELETE_LEGACY_FILE_SUCCESS) {
    yield call(fetchLegacyFile, { payload: apiParams });
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: t('legacySuccessfullyDeleted'),
        title: t('success'),
        backwardActionText: t('ok')
      })
    );
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: t('legacyDeletedFailed'),
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* createLegacyFile({ payload = {} }) {
  const apiParams = yield select(getApiParams);
  const userInfo = yield select(getUserInfo);
  yield fork(handleAPIRequest, api.createLegacyFile, payload);
  const { type } = yield take([
    ACTION_TYPES.CREATE_LEGACY_FILE_SUCCESS,
    ACTION_TYPES.CREATE_LEGACY_FILE_FAILURE]);
  if (type === ACTION_TYPES.CREATE_LEGACY_FILE_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'legacyFileCreate' }));
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileSuccessfullyCreated') });
    yield call(fetchLegacyFile, { payload: { ...apiParams, officeId: userInfo?.id } });
  } else {
    yield call(errorTost, { id: t('error'), title: t('error'), description: t('legacy') });
  }
}

export function* uploadLegacyFileDocument({ payload = {} }) {
  const { legacyDocs, request, documentsType } = payload;
  const formData = new FormData();
  formData.append('legacyDocs', legacyDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));
  yield fork(handleAPIRequest, api.uploadLegacyFileDocument, formData);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT_SUCCESS,
    ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT_FAILURE]);
  if (type === ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: documentsType }));
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileSuccessfullyUpload') });
    yield call(fetchApplication, { payload: responsePayLoad?.data?.inwardId });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: documentsType }));
    yield call(errorTost, { id: t('error'), title: t('error'), description: t('fileUpload') });
  }
}

export function* saveComplete({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveComplete, payload);
  const { type } = yield take([
    ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
    ACTION_TYPES.COMPLETE_SAVE_FAILURE]);
  if (type === ACTION_TYPES.COMPLETE_SAVE_SUCCESS) {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: 'Legacy file suceessfully submitted',
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect('ui/file-management/application/legacyfiles/create')
      })
    );
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'legacyCreate' }));
  }
}

export function* deleteDocuments({ payload = {} }) {
  yield fork(handleAPIRequest, api.deleteDocuments, payload);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.DELETE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.DELETE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.DELETE_DOCUMENTS_SUCCESS) {
    const { apiResponse: { data: { message = '' } } = {} } = responsePayLoad;
    yield call(fetchApplication, { payload: payload?.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: message });
  } else {
    const { error: { response: { data: { error = {} } } = {} } = {} } = responsePayLoad;
    yield call(errorTost, { id: t('Deleted'), title: t('error'), description: error });
  }
}

export default function* legacyfilesSaga() {
  yield all([
    takeLatest(ACTION_TYPES.SAVE_APPLICATION_SERVICE, saveApplication),
    takeLatest(ACTION_TYPES.UPDATE_APPLICATION_SERVICE, updateApplication),
    takeLatest(ACTION_TYPES.FETCH_LEGACY_FILE, fetchLegacyFile),
    takeLatest(ACTION_TYPES.DELETE_LEGACY_FILE, deleteLegacyFile),
    takeLatest(ACTION_TYPES.CREATE_LEGACY_FILE, createLegacyFile),
    takeLatest(ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT, uploadLegacyFileDocument),
    takeLatest(ACTION_TYPES.COMPLETE_SAVE, saveComplete),
    takeLatest(ACTION_TYPES.DELETE_DOCUMENTS, deleteDocuments)

  ]);
}
