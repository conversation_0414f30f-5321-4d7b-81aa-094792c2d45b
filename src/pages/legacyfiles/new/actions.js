import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  SAVE_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE`,
  SAVE_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_REQUEST`,
  SAVE_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_SUCCESS`,
  SAVE_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_FAILURE`,

  UPDATE_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE`,
  UPDATE_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE_REQUEST`,
  UPDATE_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE_SUCCESS`,
  UPDATE_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE_FAILURE`,

  FETCH_LEGACY_FILE: `${STATE_REDUCER_KEY}/FETCH_LEGACY_FILE`,
  FETCH_LEGACY_FILE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_LEGACY_FILE_REQUEST`,
  FETCH_LEGACY_FILE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_LEGACY_FILE_SUCCESS`,
  FETCH_LEGACY_FILE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_LEGACY_FILE_FAILURE`,

  DELETE_LEGACY_FILE: `${STATE_REDUCER_KEY}/DELETE_LEGACY_FILE`,
  DELETE_LEGACY_FILE_REQUEST: `${STATE_REDUCER_KEY}/DELETE_LEGACY_FILE_REQUEST`,
  DELETE_LEGACY_FILE_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_LEGACY_FILE_SUCCESS`,
  DELETE_LEGACY_FILE_FAILURE: `${STATE_REDUCER_KEY}/DELETE_LEGACY_FILE_FAILURE`,

  CREATE_LEGACY_FILE: `${STATE_REDUCER_KEY}/CREATE_LEGACY_FILE`,
  CREATE_LEGACY_FILE_REQUEST: `${STATE_REDUCER_KEY}/CREATE_LEGACY_FILE_REQUEST`,
  CREATE_LEGACY_FILE_SUCCESS: `${STATE_REDUCER_KEY}/CREATE_LEGACY_FILE_SUCCESS`,
  CREATE_LEGACY_FILE_FAILURE: `${STATE_REDUCER_KEY}/CREATE_LEGACY_FILE_FAILURE`,

  UPLOAD_LEGACY_FILE_DOCUMENT: `${STATE_REDUCER_KEY}/UPLOAD_LEGACY_FILE_DOCUMENT`,
  UPLOAD_LEGACY_FILE_DOCUMENT_REQUEST: `${STATE_REDUCER_KEY}/UPLOAD_LEGACY_FILE_DOCUMENT_REQUEST`,
  UPLOAD_LEGACY_FILE_DOCUMENT_SUCCESS: `${STATE_REDUCER_KEY}/UPLOAD_LEGACY_FILE_DOCUMENT_SUCCESS`,
  UPLOAD_LEGACY_FILE_DOCUMENT_FAILURE: `${STATE_REDUCER_KEY}/UPLOAD_LEGACY_FILE_DOCUMENT_FAILURE`,

  COMPLETE_SAVE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE`,
  COMPLETE_SAVE_REQUEST: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_REQUEST`,
  COMPLETE_SAVE_SUCCESS: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_SUCCESS`,
  COMPLETE_SAVE_FAILURE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_FAILURE`,

  DELETE_DOCUMENTS: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS`,
  DELETE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS_REQUEST`,
  DELETE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS_SUCCESS`,
  DELETE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/DELETE_DOCUMENTS_FAILURE`

};

// save application legacyfiles form
export const saveApplicationService = createAction(ACTION_TYPES.SAVE_APPLICATION_SERVICE);
export const updateApplicationService = createAction(ACTION_TYPES.UPDATE_APPLICATION_SERVICE);

export const fetchLegacyFile = createAction(ACTION_TYPES.FETCH_LEGACY_FILE);
export const deleteLegacyFile = createAction(ACTION_TYPES.DELETE_LEGACY_FILE);
export const createFileForLegacy = createAction(ACTION_TYPES.CREATE_LEGACY_FILE);
export const uploadDocument = createAction(ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT);
export const saveComplete = createAction(ACTION_TYPES.COMPLETE_SAVE);
export const deleteDocuments = createAction(ACTION_TYPES.DELETE_DOCUMENTS);
