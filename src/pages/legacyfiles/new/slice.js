import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  subModule: {},
  module: {},
  subModulesSearch: {},
  servicesSearchList: {},
  advanceSearchResult: {},
  activeLegacyFilesFormData: {},

  apiParams: {
    pages: 0,
    sortDirections: 'desc',
    sizes: 10,
    filingMode: 'legacy',
    fileNo: null,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID)
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {

    setActiveLegacyFilesFormData: (state, { payload }) => {
      _.set(state, 'activeLegacyFilesFormData', payload);
    },
    setLegacyFileList: (state, { payload }) => {
      _.set(state, 'setLegacyFileList', payload);
    },
    setLegacyFileListById: (state, { payload }) => {
      _.set(state, 'setLegacyFileListById', payload);
    },
    setApiParams: (state, { payload }) => {
      _.set(state, 'apiParams', payload);
    }
  }
});

export const { actions, reducer } = slice;
