import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const saveApplicationServiceApi = (data) => {
  return {
    url: API_URL.LEGACYFILES.SAVE_APPLICATION,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_FAILURE
      ],
      data
    }
  };
};

export const updateApplicationServiceApi = (values) => {
  const { values: data, id } = values;
  return {
    url: API_URL.LEGACYFILES.UPDATE_APPLICATION.replace(':id', id),
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.UPDATE_APPLICATION_SERVICE_FAILURE
      ],
      data
    }
  };
};

export const fetchLegacyFile = (data) => {
  const {
    filingMode, fileNo, sortDirections = 'desc', pages = 0, sizes = 10, legacyFileNo = '', officeId = ''
  } = data;

  const paramData = `?filingMode=${filingMode}&fileNo=${fileNo}&sortDirection=${sortDirections}&page=${pages}&size=${sizes}&legacyFileNo=${legacyFileNo}&officeId=${officeId}`;

  return {
    url: API_URL.LEGACYFILES.FETCH_LEGACY_FILE.replace('?query', paramData),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_LEGACY_FILE_REQUEST,
        ACTION_TYPES.FETCH_LEGACY_FILE_SUCCESS,
        ACTION_TYPES.FETCH_LEGACY_FILE_FAILURE
      ]
    },
    data
  };
};

export const deleteLegacyFile = (data) => {
  const {
    id
  } = data;
  return {
    url: API_URL.LEGACYFILES.DELETE_LEGACY_FILE.replace(':id', id),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_LEGACY_FILE_REQUEST,
        ACTION_TYPES.DELETE_LEGACY_FILE_SUCCESS,
        ACTION_TYPES.DELETE_LEGACY_FILE_FAILURE
      ]
    },
    data
  };
};

export const createLegacyFile = (data) => {
  return {
    url: API_URL.LEGACYFILES.CREATE_LEGACY_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.CREATE_LEGACY_FILE_REQUEST,
        ACTION_TYPES.CREATE_LEGACY_FILE_SUCCESS,
        ACTION_TYPES.CREATE_LEGACY_FILE_FAILURE
      ],
      data
    }
  };
};

export const uploadLegacyFileDocument = (data) => {
  return {
    url: API_URL.LEGACYFILES.UPLOAD_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT_REQUEST,
        ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT_SUCCESS,
        ACTION_TYPES.UPLOAD_LEGACY_FILE_DOCUMENT_FAILURE
      ],
      data
    }
  };
};

export const saveComplete = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_COMPLETE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.COMPLETE_SAVE_REQUEST,
        ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
        ACTION_TYPES.COMPLETE_SAVE_FAILURE
      ],
      data
    }
  };
};

export const deleteDocuments = (data) => {
  return {
    url: API_URL.LEGACYFILES.DELETE_DOCUMENT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_DOCUMENTS_REQUEST,
        ACTION_TYPES.DELETE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.DELETE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};
