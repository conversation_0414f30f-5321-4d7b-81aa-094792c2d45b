import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getDoorKey, getUserInfo } from 'pages/common/selectors';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';

import { actions as commonSliceActions } from 'pages/common/slice';
import RoutingKeys from 'pages/common/components/GeneralDetails/RoutingKeys';
import { getActiveApplicationData } from 'pages/counter/new/selectors';
import { existingGeneralData, generalRoutingKeys, generalSaveData } from 'pages/common/components/GeneralDetails/helper';
import * as actions from 'pages/counter/new/actions';
import _ from 'lodash';
import { LEGACY_FILE } from 'pages/file/details/constants';

const GeneralDetails = (props) => {
  const {
    userInfo,
    setActionTriggered,
    updateGeneralDetails,
    activeApplicationData,
    doorkey
  } = props;

  const params = useParams();
  const [serviceInfo, setServiceInfo] = useState({});
  const [existingGeneral, setExistingGeneral] = useState({});

  useEffect(() => {
    if (activeApplicationData?.generalDetailsResponses) {
      const generalDetailsResponses = activeApplicationData?.generalDetailsResponses;
      setExistingGeneral(existingGeneralData(generalDetailsResponses));
    }
  }, [activeApplicationData]);

  const routingSave = (data) => {
    setActionTriggered({ loading: true, id: 'GeneralDetailsCreate' });
    const keyData = generalRoutingKeys(data, doorkey);
    const rounteKeyData = _.omitBy(keyData, _.isNil);
    const saveData = generalSaveData(
      rounteKeyData,
      params?.id,
      [],
      data,
      userInfo?.id,
      'legacy'
    );

    updateGeneralDetails(_.omitBy(saveData, _.isNil));
  };

  useEffect(() => {
    if (activeApplicationData) {
      setServiceInfo({ code: activeApplicationData?.serviceCode, name: activeApplicationData?.serviceName });
    }
  }, [activeApplicationData]);

  return (
    <div className="pt-5">

      <RoutingKeys
        routingSave={routingSave}
        activeIndex
        serviceInfo={serviceInfo}
        officeCode={userInfo?.id}
        existingData={existingGeneral}
        from={LEGACY_FILE}
      />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  doorkey: getDoorKey,
  activeApplicationData: getActiveApplicationData
});

const mapDispatchToProps = (dispatch) => ({
  updateGeneralDetails: (data) => dispatch(actions.saveGeneralDetails(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(GeneralDetails);
