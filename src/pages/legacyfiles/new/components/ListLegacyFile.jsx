import React, { useState, useEffect } from 'react';
import {
  t, InputGroup, Input, InputRightElement, Button, Toast, IconButton
} from 'common/components';
import { Edit, Trash } from 'assets/Svg';
import { useNavigate } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { CommonTable } from 'common/components/Table';
import SearchIcon from 'assets/SearchIcon';
import { BASE_PATH } from 'common/constants';
import { FILTER_TYPE } from 'pages/common/constants';
import { useForm } from 'react-hook-form';
import GridIcon from 'assets/GridIcon';
import { getActionTriggered, getTableLoader, getUserInfo } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as actions from '../actions';
import { getApiParams, getLegacyList } from '../selectors';
import { actions as sliceActions } from '../slice';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    input: {
      borderRadius: '20px',
      border: '1px solid #E4E4E4'
    },
    button: {
      background: 'none'
    },
    width: '187px'
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #E4E4E4',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '120px'
  },
  sort: {
    display: 'flex'
  }
};

const ListLegacyFile = ({
  fetchLegacyFile, legacyFileListDetails, deleteLegacyFile, apiParams, setApiParams, setsetLegacyFileListById,
  setAllTableData, allTableData, createFileForLegacy, actionTriggered,
  setActionTriggered, setTableLoader, tableLoader, userInfo, setAlertAction
}) => {
  const {
    getValues
  } = useForm({
    mode: 'all'
  });
  const [tableData, setTableData] = useState([]);
  const [fileNumber, setFileNumber] = useState('');
  const navigate = useNavigate();
  const activeRows = [{}];
  const [page, setPage] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [sort, setSort] = useState(10);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const { errorTost } = Toast;

  const deleteLegacyData = (data) => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: t('areYouSureWantToDelete'),
      title: t('confirm'),
      backwardActionText: t('cancel'),
      forwardActionText: t('confirm'),
      forwardAction: () => deleteLegacyFile({ id: data?.inwardId })
    });
  };

  const editLegacyData = (data) => {
    navigate(`${BASE_PATH}/application/legacyfiles/create/${data.inwardId}`);
  };

  const handleFileStatus = (val) => (
    <div className={`pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-center capitalize ${val?.row?.stage === 'completed' ? 'text-[#48be6c] bg-[#c8ffda]' : 'text-[#D69E2E] bg-[#FEFCBF]'}`}>
      {val?.row?.stage ? val?.row?.stage : 'partial'}
    </div>
  );

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData?.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleLegacyFileNo = (fileData) => {
    let legacyFileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      legacyFileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData?.legacyFileNo}</div>;
    }
    return <div className="block">{legacyFileNo}</div>;
  };

  const handleFileStartDate = (fileData) => {
    let fileStartDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileStartDate = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData?.fileStartDate}</div>;
    }
    return <div className="block">{fileStartDate}</div>;
  };
  const headers = [
    {
      type: 'radio'
    },
    {
      header: t('legacyFileNo'),
      alignment: 'left',
      field: 'legacyFileNo',
      cell: (field) => handleLegacyFileNo(field)
    },
    {
      header: t('date'),
      alignment: 'left',
      field: 'fileStartDate',
      cell: (field) => handleFileStartDate(field)
    },
    {
      header: t('servicee'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('status'),
      alignment: 'left',
      field: 'stage',
      cell: handleFileStatus
    },
    {
      header: t('action'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          label: t('edit'),
          icon: <Edit />,
          onClick: (data) => editLegacyData(data)
        },
        {
          label: t('delete'),
          icon: <Trash />,
          onClick: (data) => deleteLegacyData(data)
        }
      ]
    }
  ];

  const triggerSearch = (field, data) => {
    switch (field) {
      case FILTER_TYPE.FILE_NO:
        setFileNumber(data);
        setApiParams({
          ...apiParams, legacyFileNo: data, pages: 0, officeId: userInfo?.id
        });
        break;
      case FILTER_TYPE.SORT:
        setApiParams({ ...apiParams, sizes: data, officeId: userInfo?.id });
        setSort(data);
        break;
      default:
        break;
    }
  };

  const onPageClick = (data) => {
    setPage(data);
    setApiParams({ ...apiParams, pages: data, officeId: userInfo?.id });
  };

  useEffect(() => {
    if (apiParams) {
      setTableLoader({ loading: true, id: 'legacy-file-table' });
      fetchLegacyFile({ ...apiParams, officeId: userInfo?.id });
    }
  }, [apiParams]);

  useEffect(() => {
    if (legacyFileListDetails) {
      setTableLoader({ loading: false, id: 'legacy-file-table' });
      if (Object.keys(legacyFileListDetails).length > 0) {
        setTableData(legacyFileListDetails.content);
        setTotalItems(parseInt(`${legacyFileListDetails.totalPages}0`, 10));
        setNumberOfElements(Number(legacyFileListDetails.numberOfElements));
        setsetLegacyFileListById({});
        if (getValues('legacyFileNo')) {
          setPage(0);
        }
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [legacyFileListDetails]);

  const onRowCheck = (data) => {
    setAllTableData(data);
  };

  const handleCreateFile = () => {
    if (allTableData?.inwardId && allTableData?.stage === 'completed') {
      const inwardIds = [];
      inwardIds.push(allTableData?.inwardId);
      const createFileData = {
        inwardIds
      };
      setActionTriggered({ loading: true, id: 'legacyFileCreate' });
      setTableLoader({ loading: true, id: 'legacy-file-table' });
      createFileForLegacy(createFileData);
    } else if (allTableData?.inwardId && allTableData?.stage !== 'completed') {
      errorTost({
        title: t('incompleted'),
        description: t('selectedLegacyFileisPartialStage')
      });
    } else {
      errorTost({
        title: t('legacyNotSelected'),
        description: t('pleaseSelectAnLegacyNumber')
      });
    }
  };

  return (
    <div className="bg-white rounded">
      <div className="flex justify-end pt-5 pr-5 gap-4">
        {/* <div style={styles.sort}>
          <div style={styles.label}>Sort:</div>
          <select onChange={(event) => triggerSearch(FILTER_TYPE.SORT, event.target.value)} value={sort} style={styles.select}>
            <option value={5}>5 Items</option>
            <option value={10}>10 Items</option>
            <option value={25}>25 Items</option>
          </select>
        </div> */}
        <InputGroup style={styles.search}>
          <Input
            name="legacyFileNo"
            placeholder={t('fileNo')}
            style={styles.search.input}
            value={fileNumber}
            onChange={(event) => {
              setFileNumber(event.target.value);
            }}
          />
          <InputRightElement>
            <IconButton onClick={() => triggerSearch(FILTER_TYPE.FILE_NO, fileNumber)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
          </InputRightElement>
        </InputGroup>
        <div>
          <Button
            variant="primary_outline"
            leftIcon={<GridIcon color="#00B2EC" />}
            onClick={handleCreateFile}
            style={{ maxWidth: '110px', height: '40px' }}
            isLoading={actionTriggered?.id === 'legacyFileCreate' && actionTriggered?.loading}
          >
            {t('create')}
          </Button>
        </div>
      </div>

      <div className="col-span-12 p-[20px]">
        <CommonTable
          columns={headers}
          tableData={tableData}
          activeRows={activeRows}
          onRowRadioSelected={onRowCheck}
          itemsPerPage={sort}
          totalItems={totalItems}
          currentPage={page}
          onPageClick={onPageClick}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'legacy-file-table'}
          numberOfElements={numberOfElements}
        />
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  legacyFileListDetails: getLegacyList,
  apiParams: getApiParams,
  actionTriggered: getActionTriggered,
  tableLoader: getTableLoader,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchLegacyFile: (data) => dispatch(actions.fetchLegacyFile(data)),
  deleteLegacyFile: (data) => dispatch(actions.deleteLegacyFile(data)),
  setApiParams: (data) => dispatch(sliceActions.setApiParams(data)),
  setsetLegacyFileListById: (data) => dispatch(sliceActions.setLegacyFileListById(data)),
  createFileForLegacy: (data) => dispatch(actions.createFileForLegacy(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(ListLegacyFile);
