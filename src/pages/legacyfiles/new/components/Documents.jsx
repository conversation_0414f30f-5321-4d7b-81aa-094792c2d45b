import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON>per, FormController, t, To<PERSON>, ErrorText
} from 'common/components';
import { useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { getActionTriggered, getSidebarData, getUserInfo } from 'pages/common/selectors';
import PreviewThumbnail from 'common/components/DocumentPreview/previewThumbnail';
import { actions as commonSliceActions } from 'pages/common/slice';
import { documenturl } from 'pages/counter/new/constants';
import { getActiveApplicationData } from 'pages/counter/new/selectors';
import { STORAGE_KEYS } from 'common/constants';
import * as actions from '../actions';
import { DOCUMENT_TYPES, LEGACYFILES_APPLICATION_KEYS } from '../constants';

const Documents = ({
  uploadDocument,
  deleteDocuments,
  userInfo,
  setActiveAccordian,
  setSidebarStatus,
  formComponentData,
  setActionTriggered,
  actionTriggered,
  activeApplicationData,
  setAlertAction
}) => {
  const params = useParams();
  const [fileDocsPreview, setFileDocsPreview] = useState([]);
  const [loading, setLoading] = useState(true);
  const [docPreview, setDocPreview] = useState(null);
  const [fileNoteValidateShow, setFileNoteValidateShow] = useState('');
  const [correspondenceValidateShow, setCorrespondenceValidateShow] = useState('');

  const correspondRef = useRef(null);
  const fileNoteRef = useRef(null);

  const { errorTost } = Toast;

  const {
    control, formState: { errors }, getValues,
    setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
      fileNoteDocs: '',
      correspondDocs: '',
      fileNoteName: '',
      correspondenceName: ''
    }
  });

  const onFileSelected = (data, type) => {
    if (type === DOCUMENT_TYPES.FILE_NOTE_DOCS) {
      if ((getValues('fileNoteName') === '' || getValues('fileNoteName') === undefined)) {
        setFileNoteValidateShow(true);
      } else {
        setFileNoteValidateShow(false);
        const request = {
          inwardId: params?.id,
          documentType: 'note',
          documentName: getValues('fileNoteName'),
          userInfo: {
            officeId: userInfo?.id
          }
        };
        setActionTriggered({ loading: true, id: 'legacy-file-note-doc-upload' });
        uploadDocument({ request, legacyDocs: data, documentsType: 'legacy-file-note-doc-upload' });
        setValue('fileNoteName', '');
        setValue('fileNoteDocs', '');
      }
    } else if (type === DOCUMENT_TYPES.CORRESPOND_DOCS) {
      if ((getValues('correspondenceName') === '' || getValues('correspondenceName') === undefined)) {
        setCorrespondenceValidateShow(true);
        setValue('correspondenceName', '');
        setValue('correspondDocs', '');
      } else {
        setCorrespondenceValidateShow(false);
        const request = {
          inwardId: params?.id,
          documentType: 'correspondence',
          documentName: getValues('correspondenceName'),
          userInfo: {
            officeId: userInfo?.id
          }
        };
        setActionTriggered({ loading: true, id: 'legacy-correspond-doc-upload' });
        uploadDocument({ request, legacyDocs: data, documentsType: 'legacy-correspond-doc-upload' });
        setValue('correspondenceName', '');
        setValue('correspondDocs', '');
      }
    } else {
      errorTost({
        title: t('missingSections'),
        description: `${t('pleaseComplete')} ${t('the')} ${t('service')} ${t('section')}`
      });
      setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service);
      setSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
    }
  };

  useEffect(() => {
    if (activeApplicationData) {
      if (activeApplicationData?.legacyDocumentSearchResponses?.length > 0) {
        setFileDocsPreview(activeApplicationData?.legacyDocumentSearchResponses);
      } else {
        setFileDocsPreview([]);
      }
    }
  }, [activeApplicationData]);

  const onHandleRemove = (data) => {
    const sendData = {
      inwardId: params?.id,
      legacyFileDocumentsId: data?.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    deleteDocuments(sendData);
    setAlertAction({
      open: false
    });
  };

  function getDocument(url, token, body) {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  }

  const handlePreview = (data, type, typeId) => {
    const sendData = {
      inwardId: params?.id,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: data.documentId
    };

    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };

  const handleFieldChange = (field) => {
    switch (field) {
      case 'fileNoteName':
        setFileNoteValidateShow(false);
        break;
      case 'correspondenceName':
        setCorrespondenceValidateShow(false);
        break;

      default:
        break;
    }
  };

  return (
    <FormWrapper>
      <div className="col-span-12">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-6">
            <FormController
              name="fileNoteName"
              type="text"
              label={t('note')}
              placeholder={t('note')}
              control={control}
              errors={errors}
              handleChange={() => handleFieldChange('fileNoteName')}
            />
            {
              fileNoteValidateShow
              && <ErrorText error={t('documentNameisRequired')} />
            }
          </div>
          <div className="col-span-6">
            <FormController
              name="fileNoteDocs"
              type="file"
              label={t('attachFileNotes')}
              placeholder={t('dropOrChooseFilesToUpload')}
              control={control}
              errors={errors}
              handleChange={(data) => {
                onFileSelected(data, 'fileNoteDocs');
                fileNoteRef.current.value = '';
              }}
              accept="image,pdf"
              loading={actionTriggered?.id === 'legacy-file-note-doc-upload' && actionTriggered?.loading}
              ref={fileNoteRef}
            />
          </div>

        </div>
      </div>
      <div className="col-span-12">
        <div className="col-span-4 flex flex-wrap gap-3">
          {fileDocsPreview?.filter((item) => item.documentType === 'note').map((item) => (
            <PreviewThumbnail
              key={item.id}
              handlePreviewRemove={(data) => onHandleRemove(data)}
              handlePreview={(data) => handlePreview(data, 3, 0)}
              item={item}
              preview={docPreview}
              fileType={item?.documentContentType}
              documenturl={documenturl}
              setLoading={setLoading}
              loading={loading}
              documentName={item?.documentName}
            />
          ))}
        </div>
      </div>
      <div className="col-span-12">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-6">
            <FormController
              name="correspondenceName"
              type="text"
              label={t('correspondenceName')}
              placeholder={t('correspondenceName')}
              control={control}
              errors={errors}
              handleChange={() => handleFieldChange('correspondenceName')}
            />
            {
              correspondenceValidateShow
              && <ErrorText error={t('documentNameisRequired')} />
            }
          </div>
          <div className="col-span-6">
            <FormController
              name="correspondDocs"
              type="file"
              label={t('attachCorrespondence')}
              placeholder={t('dropOrChooseFilesToUpload')}
              control={control}
              errors={errors}
              handleChange={(data) => {
                onFileSelected(data, 'correspondDocs');
                correspondRef.current.value = '';
              }}
              accept="image,pdf"
              loading={actionTriggered?.id === 'legacy-correspond-doc-upload' && actionTriggered?.loading}
              ref={correspondRef}
            />
          </div>

        </div>
      </div>
      <div className="col-span-12">
        <div className="col-span-4 flex flex-wrap gap-3">
          {fileDocsPreview?.filter((item) => item.documentType === 'correspondence').map((item) => (
            <PreviewThumbnail
              key={item.id}
              handlePreviewRemove={(data) => onHandleRemove(data)}
              handlePreview={(data) => handlePreview(data, 3, 0)}
              item={item}
              preview={docPreview}
              fileType={item?.documentContentType}
              documenturl={documenturl}
              setLoading={setLoading}
              loading={loading}
              documentName={item?.documentName}
            />
          ))}
        </div>
      </div>
    </FormWrapper>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  formComponentData: getSidebarData,
  actionTriggered: getActionTriggered,
  activeApplicationData: getActiveApplicationData
});

const mapDispatchToProps = (dispatch) => ({
  uploadDocument: (data) => dispatch(actions.uploadDocument(data)),
  deleteDocuments: (data) => dispatch(actions.deleteDocuments(data)),
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Documents);
