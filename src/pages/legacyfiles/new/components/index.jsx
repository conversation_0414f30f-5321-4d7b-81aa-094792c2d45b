import { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  AccordionComponent, t, But<PERSON>, VerticalStepper,
  CustomTab,
  Select
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  getActionTriggered, getInwardNextUserModal, getInwardUsers, getSidebarData, getUserInfo
} from 'pages/common/selectors.js';

import * as commonActions from 'pages/common/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as counterActions from 'pages/counter/new/actions';
import { MODULE_PATH } from 'common/constants';
import { getActiveApplicationData } from 'pages/counter/new/selectors';
import InwardUserConfirmation from 'common/components/WorkflowConfirmation';
import { convertToLocalDateTime } from 'utils/date';
import { removeDuplicates } from 'utils/validateFile';
import Documents from './Documents';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import { SIDEBAR_KEYS, LEGACYFILES_APPLICATION_KEYS_INDEX, LEGACYFILES_APPLICATION_KEYS } from '../constants';
import ListLegacyFile from './ListLegacyFile';
import GeneralDetails from './GeneralDetails';
import CreateLegacyFile from './CreateLegacyFile';

const LegacyFilesNew = (props) => {
  const {
    setActiveAccordian,
    formComponentData,
    setLegacyFilesApplicationComponentDetails,
    setLegacyFilesApplicationSidebarStatus,
    activeApplicationData,
    setActiveLegacyFilesFormData,
    fetchApplication,
    setFormTitle,
    saveComplete,
    userInfo,
    actionTriggered,
    setActionTriggered,
    setAlertAction,
    fetchInwardUsers,
    inwardUsers,
    fetchServiceValidation,
    inwardNextUserModal,
    setInwardNextUserModal
  } = props;

  const params = useParams();
  let sidebarData = [];
  const [activeIndex, setActiveIndex] = useState(0);
  const [allTableData, setAllTableData] = useState();
  const [isSubmit, setIsSubmit] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const [nextRole, setNextRole] = useState([]);
  const [nextUser, setNextUser] = useState({});

  useEffect(() => {
    if (params?.id) {
      fetchApplication(params?.id);
    }
  }, [params?.id]);

  useEffect(() => {
    setFormTitle({ title: t('legacy'), variant: 'normal' });
    setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service);
    setLegacyFilesApplicationSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
  }, []);

  const onClickSidebarItem = (currentStep) => {
    const formFieldKeys = Object.keys(LEGACYFILES_APPLICATION_KEYS);
    const currentFormFieldKey = formFieldKeys[Number(currentStep - 1)];
    setLegacyFilesApplicationSidebarStatus({ activeStep: currentStep, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
    switch (LEGACYFILES_APPLICATION_KEYS[currentFormFieldKey]) {
      case LEGACYFILES_APPLICATION_KEYS.Service:
        setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service);
        break;
      case LEGACYFILES_APPLICATION_KEYS.Document:
        setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Document);
        break;
      default:
        return false;
    }
    return true;
  };

  const accordionData = [
    { title: t('services'), content: <CreateLegacyFile isSubmit={isSubmit} setIsSubmit={setIsSubmit} />, onPress: () => onClickSidebarItem(LEGACYFILES_APPLICATION_KEYS_INDEX.Service) },
    { title: t('generalDetails'), content: <GeneralDetails />, onPress: () => onClickSidebarItem(LEGACYFILES_APPLICATION_KEYS_INDEX.General) },
    { title: t('documents'), content: <Documents />, onPress: () => onClickSidebarItem(LEGACYFILES_APPLICATION_KEYS_INDEX.Document) }
  ];

  const SIDEBAR_STEPPER_STEPS = {
    APPLICATION:
      [
        { title: t('services'), onClick: () => onClickSidebarItem(LEGACYFILES_APPLICATION_KEYS_INDEX.Service) },
        { title: t('generalDetails'), onClick: () => onClickSidebarItem(LEGACYFILES_APPLICATION_KEYS_INDEX.General) },
        { title: t('documents'), onClick: () => onClickSidebarItem(LEGACYFILES_APPLICATION_KEYS_INDEX.Document) }
      ]
  };

  const getSidebarAccordionData = () => {
    return ([{
      title: t('application'),
      key: SIDEBAR_KEYS.APPLICATION,
      content: <VerticalStepper
        steps={SIDEBAR_STEPPER_STEPS.APPLICATION}
        activeStep={Number(formComponentData.activeStep)}
        completedSteps={formComponentData?.completedSteps}
      />
    },
    {
      title: t('concatLabel', { label: t('required'), type: t('documents') }),
      key: SIDEBAR_KEYS.REQUIRED_DOCUMENTS,
      content: ''
    },
    { title: t('guidelines'), key: SIDEBAR_KEYS.GUIDELINES, content: '' },
    { title: t('quickAccess'), key: SIDEBAR_KEYS.QUICK_ACCESS, content: '' },
    { title: t('process'), key: SIDEBAR_KEYS.PROCESS, content: '' }
    ]);
  };

  useEffect(() => {
    sidebarData = getSidebarAccordionData();
    setLegacyFilesApplicationComponentDetails({ data: sidebarData, steps: SIDEBAR_STEPPER_STEPS.APPLICATION });
  }, [formComponentData?.activeStep]);

  const save = () => {
    const steps = [];
    setIsSubmit(true);
    if (activeApplicationData) {
      if (activeApplicationData?.inwardId) {
        const find = steps.findIndex((item) => item === 'service');
        if (find === -1) {
          steps.push('service');
        }
      }
      if (activeApplicationData?.generalDetailsResponses) {
        const find = steps.findIndex((item) => item === 'general');
        if (find === -1) {
          steps.push('general');
        }
      }
    }

    if (steps.length > 0 && params?.id) {
      const findService = steps.findIndex((item) => item === 'service');
      const findGeneral = steps.findIndex((item) => item === 'general');
      if (findService === -1) {
        setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service);
        setLegacyFilesApplicationSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('service')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
      } else if (findGeneral === -1) {
        setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.General);
        setLegacyFilesApplicationSidebarStatus({ activeStep: 1, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('general')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
      } else {
        setActionTriggered({ loading: true, id: '' });
        if (Object.keys(nextUser).length === 0) {
          setAlertAction({
            open: true,
            variant: 'warning',
            message: 'Please select Forward to',
            title: 'Warning',
            backwardActionText: t('ok'),
            closeOnOverlayClick: false,
            closeOnEsc: false
          });
        } else {
          setInwardNextUserModal(true);
        }
      }
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseComplete')} ${t('the')} ${t('service')} ${t('section')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
      setActiveAccordian(LEGACYFILES_APPLICATION_KEYS.Service);
      setLegacyFilesApplicationSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
    }
  };

  const handleSave = () => {
    const saveData = {
      inwardId: params?.id,
      filestoreId: 0,
      userInfo,
      postId: nextUser?.postId
    };
    setActionTriggered({ loading: true, id: 'legacyCreate' });
    saveComplete(saveData);
    setActiveLegacyFilesFormData({});
  };

  useEffect(() => {
    if (location) {
      if ((location.pathname).includes('/create')) {
        setActiveIndex(0);
      } else if ((location.pathname).includes('/list')) {
        setActiveIndex(1);
      } else if ((location.pathname).includes(`/${params.id}`)) {
        setActiveIndex(0);
      }
    }
  }, [location]);

  const handleTabsChange = (data) => {
    const { index } = data;
    setActiveIndex(index);
    if (index === 0) {
      navigate(`${MODULE_PATH}/legacyfiles/create`);
    }
    if (index === 1) {
      navigate(`${MODULE_PATH}/legacyfiles/list`);
      setActiveAccordian(LEGACYFILES_APPLICATION_KEYS_INDEX.Service);
      setLegacyFilesApplicationSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
    }
  };

  const tabs = [
    {
      title: t('createLegacyFile')
    },
    {
      title: t('legacyFileList')
    }
  ];

  const handleChange = (data) => {
    if (data) {
      setNextUser({
        postId: data?.id, inwardNo: activeApplicationData?.inwardNo, forwardingTo: data?.employeeName, designation: data?.designation, role: data?.role || null, penNo: data?.penNo, seat: data?.seat, dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextUser({});
      setInwardNextUserModal(false);
    }
  };

  useEffect(() => {
    if (inwardUsers?.length > 0) {
      setNextRole(
        inwardUsers?.map((ps) => ({
          id: ps?.postId,
          name: `${ps?.employeeName ? ps?.employeeName : ''} - ${ps?.penNo ? ps?.penNo : ''} - ${ps?.designation ? ps?.designation : ''} - ${ps?.seat ? ps?.seat : ''}`,
          employeeName: ps?.employeeName,
          fileId: ps?.fileId,
          fileNo: ps?.fileNo,
          penNo: ps?.penNo,
          role: ps?.role,
          designation: ps?.designation,
          seat: ps?.seat
        }))
      );
    }
  }, [JSON.stringify(inwardUsers)]);

  useEffect(() => {
    if (nextRole?.length === 1) {
      setNextUser({
        postId: nextRole[0]?.id, inwardNo: activeApplicationData?.inwardNo, forwardingTo: nextRole[0]?.employeeName, designation: nextRole[0]?.designation, role: nextRole[0]?.role || null, penNo: nextRole[0]?.penNo, seat: nextRole[0]?.seat, dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextUser({});
    }
  }, [nextRole]);

  useEffect(() => {
    if (activeApplicationData?.serviceCode) {
      fetchServiceValidation(activeApplicationData?.serviceCode);
    }
    if (activeApplicationData?.generalDetailsResponses) {
      fetchInwardUsers(params?.id);
    }
  }, [activeApplicationData]);

  const handleClose = () => {
    setInwardNextUserModal(false);
  };

  return (
    <div className="pb-[60px] ">
      <div className="bg-white p-5 mb-3 rounded-lg no-panel">
        <CustomTab data={tabs} handleChange={handleTabsChange} currentIndex={activeIndex} />
      </div>
      {activeIndex === 1 ? (
        <ListLegacyFile
          setAllTableData={setAllTableData}
          allTableData={allTableData}
        />
      ) : (
        <AccordionComponent
          data={accordionData}
          allowMultiple={false}
          currentIndexes={[formComponentData.activeStep]}
        />
      )}
      <div>

        <div className="grid grid-cols-12 gap-x-5 my-5 bg-white rounded-lg py-5 px-10">
          <div className="lg:col-span-8 md:col-span-8 col-span-12" />
          <div className="lg:col-span-4 md:col-span-4 col-span-12 flex gap-3 items-center justify-end">
            {nextRole?.filter((item) => item?.id !== null).length > 0 && (
              <Select
                name="user"
                type="select"
                value={nextUser?.postId}
                label={t('forwardTo')}
                placeholder={t('select')}
                optionKey="id"
                onChange={(data) => handleChange(data)}
                options={removeDuplicates(nextRole, 'penNo') || []}
                isClearable
              />
            )}
            {
              location.pathname.includes('legacyfiles/create')
              && (
                <Button type="submit" form="hook-form" variant="secondary" onClick={save} isLoading={actionTriggered?.id === 'legacyCreate' && actionTriggered?.loading}>
                  {t('submit')}
                </Button>
              )
            }
          </div>
        </div>
        <InwardUserConfirmation open={inwardNextUserModal} close={handleClose} handleSelect={handleSave} nextUser={nextUser} isLoading={actionTriggered?.id === 'arisingCreate' && actionTriggered?.loading} />

      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  formComponentData: getSidebarData,
  activeApplicationData: getActiveApplicationData,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  inwardUsers: getInwardUsers,
  inwardNextUserModal: getInwardNextUserModal
});

const mapDispatchToProps = (dispatch) => ({
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  setLegacyFilesApplicationComponentDetails: (data) => dispatch(commonSliceActions.setFormComponentData(data)),
  setLegacyFilesApplicationSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActiveLegacyFilesFormData: (data) => dispatch(sliceActions.setActiveLegacyFilesFormData(data)),
  fetchApplication: (data) => dispatch(counterActions.fetchApplicationService(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  saveComplete: (data) => dispatch(actions.saveComplete(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchInwardUsers: (data) => dispatch(commonActions.fetchInwardUsers(data)),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  setInwardNextUserModal: (data) => dispatch(commonSliceActions.setInwardNextUserModal(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(LegacyFilesNew);
