import React, { useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, Form<PERSON><PERSON>roller, t, But<PERSON>
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  getServicesDropdown,
  getFileTypeDropdown,
  getFileYearDropdown,
  getCorrespondTypeDropdown,
  getUserInfo,
  getActionTriggered,
  getModuleById,
  getSubModule
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { convertToLocalDate, reFormattedDate } from 'utils/date';
import { useParams } from 'react-router-dom';
import { DATE_FORMAT } from 'pages/common/constants';
import * as actionsCounter from 'pages/counter/new/actions';
import { getActiveApplicationData, getServiceByCodeDetails } from 'pages/counter/new/selectors';
import * as actions from '../actions';
import { ServiceFormSchema } from '../validate';

const CreateLegacyFile = (props) => {
  const params = useParams();

  const {
    fetchServicesOptions,
    serviceDropdown,
    fetchSubModuleById,
    fetchFileTypeOptions,
    fileTypeDropdown,
    saveApplicationService,
    subModule,
    moduleById,
    fetchCorrespondTypeOptions,
    correspondTypeDropdown,
    activeApplicationData,
    updateApplicationService,
    fetchServiceByServiceCode,
    userInfo,
    actionTriggered,
    setActionTriggered,
    serviceByCodeDetails,
    fetchModuleById
  } = props;
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
    setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
      services: null,
      modules: '',
      subModule: '',
      fileType: 1,
      title: '',
      description: '',
      filenumber: '',
      fileYear: '',
      fileStartDate: '',
      lastpagenumber: '',
      fileNoteEndDate: '',
      correspondType: '',
      lastCorrespondDate: '',
      serviceCode: '',
      legacyLastNoteNo: ''
    },
    resolver: yupResolver(ServiceFormSchema)
  });
  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'legacyServiceCreate' });
    if (params?.id) {
      const updateData = {
        serviceCode: data.services,
        title: data.title,
        description: data.description,
        legacyFileNo: data.filenumber,
        year: new Date(data.fileYear).getFullYear(),
        fileStartDate: convertToLocalDate(data.fileStartDate, DATE_FORMAT.DATE_LOCAL),
        lastPageNo: Number(data.lastpagenumber),
        fileEndDate: convertToLocalDate(data.fileNoteEndDate, DATE_FORMAT.DATE_LOCAL),
        lastCorrespondenceType: data.correspondType,
        lastCorrespondenceDate: convertToLocalDate(data.lastCorrespondDate, DATE_FORMAT.DATE_LOCAL),
        fileType: data.fileType,
        userInfo: {
          officeId: userInfo?.id
        },
        legacyLastNoteNo: Number(data.legacyLastNoteNo)
      };
      updateApplicationService({ values: updateData, id: params.id });
    } else {
      const saveData = {
        serviceCode: data.services,
        title: data.title,
        description: data.description,
        legacyFileNo: data.filenumber,
        year: new Date(data.fileYear).getFullYear(),
        fileStartDate: convertToLocalDate(data.fileStartDate, DATE_FORMAT.DATE_LOCAL),
        lastPageNo: Number(data.lastpagenumber),
        fileEndDate: convertToLocalDate(data.fileNoteEndDate, DATE_FORMAT.DATE_LOCAL),
        lastCorrespondenceType: data.correspondType,
        lastCorrespondenceDate: convertToLocalDate(data.lastCorrespondDate, DATE_FORMAT.DATE_LOCAL),
        fileType: data.fileType,
        userInfo: {
          officeId: userInfo?.id
        },
        legacyLastNoteNo: Number(data.legacyLastNoteNo)
      };
      saveApplicationService(saveData);
    }
  };

  useEffect(() => {
    fetchServicesOptions();
    fetchFileTypeOptions();
    fetchCorrespondTypeOptions();
  }, []);

  const handleFieldChange = (field, data) => {
    switch (field) {
      case 'services':
        if (data) {
          fetchSubModuleById(data.subModuleId);
          fetchModuleById(data.moduleId);
          setValue('serviceCode', data.code);
        } else {
          setValue('subModule', '');
          setValue('modules', '');
          setValue('serviceCode', null);
        }
        break;
      case 'serviceCode':
        setValue('serviceCode', data.target.value.toUpperCase());
        if (data.target.value.length > 4) {
          fetchServiceByServiceCode(data.target.value);
          setValue('subModule', '');
          setValue('modules', '');
          setValue('services', '');
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (subModule) {
      if (Object.keys(subModule).length > 0) {
        setValue('subModule', subModule.name);
      }
    } else {
      setValue('subModule', '');
    }
  }, [subModule]);

  useEffect(() => {
    if (moduleById) {
      if (Object.keys(moduleById)?.length > 0) {
        setValue('modules', moduleById?.name);
      }
    } else {
      setValue('modules', '');
    }
  }, [moduleById]);

  useEffect(() => {
    if (serviceByCodeDetails?.length > 0) {
      fetchSubModuleById(serviceByCodeDetails[0]?.subModuleId);
      fetchModuleById(serviceByCodeDetails[0]?.moduleId);
      setValue('services', serviceByCodeDetails[0]?.code);
      setValue('serviceCode', serviceByCodeDetails[0]?.code);
    }
  }, [serviceByCodeDetails]);

  const resetForm = () => {
    reset({
      services: null,
      modules: '',
      subModule: '',
      fileType: 1,
      title: '',
      description: '',
      filenumber: '',
      fileYear: null,
      fileStartDate: null,
      lastpagenumber: '',
      fileNoteEndDate: null,
      correspondType: '',
      lastCorrespondDate: null,
      legacyLastNoteNo: ''
    });
  };

  useEffect(() => {
    if (params?.id) {
      if (!_.isEmpty(activeApplicationData)) {
        if (activeApplicationData?.inwardId) {
          setValue('services', activeApplicationData?.serviceCode);
          setValue('serviceCode', activeApplicationData?.serviceCode);
          fetchServiceByServiceCode(activeApplicationData?.serviceCode);
          setValue('title', activeApplicationData?.title);
          setValue('description', activeApplicationData?.description);
          setValue('fileYear', activeApplicationData?.year?.toString());
          setValue('fileStartDate', activeApplicationData?.fileStartDate ? reFormattedDate(activeApplicationData?.fileStartDate) : '');
          setValue('fileNoteEndDate', activeApplicationData?.fileEndDate ? reFormattedDate(activeApplicationData?.fileEndDate) : '');
          setValue('lastpagenumber', activeApplicationData?.lastPageNo);
          setValue('filenumber', activeApplicationData?.legacyFileNo);
          setValue('lastCorrespondDate', activeApplicationData?.lastCorrespondenceDate ? reFormattedDate(activeApplicationData?.lastCorrespondenceDate) : '');
          setValue('correspondType', activeApplicationData?.lastCorrespondenceType);
          setValue('fileType', activeApplicationData?.fileType);
          setValue('legacyLastNoteNo', activeApplicationData?.legacyLastNoteNo);
        } else {
          resetForm();
        }
      } else {
        resetForm();
      }
    } else {
      resetForm();
    }
  }, [JSON.stringify(activeApplicationData)]);

  return (
    <form
      id="service-form"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <FormWrapper>

        <div className="lg:col-span-8 md:col-span-8 col-span-12">
          <FormController
            name="services"
            type="select"
            label={t('services')}
            control={control}
            errors={errors}
            options={_.get(serviceDropdown, 'data', [])}
            handleChange={(data) => {
              handleFieldChange('services', data);
            }}
            required
            isDisabled={params?.id}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="serviceCode"
            type="text"
            label={t('serviceCode')}
            control={control}
            readOnly
            errors={errors}
            handleChange={(data) => handleFieldChange('serviceCode', data)}
            required
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="modules"
            type="text"
            label={t('module')}
            control={control}
            errors={errors}
            disabled
            handleChange={(data) => handleFieldChange('moduleId', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="subModule"
            type="text"
            label={t('subModule')}
            control={control}
            errors={errors}
            disabled
            handleChange={(data) => handleFieldChange('subModuleId', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="fileType"
            type="select"
            label={t('priority')}
            control={control}
            errors={errors}
            optionKey="id"
            options={_.get(fileTypeDropdown, 'data', [])}
            defaultValue="1"
            required
          />
        </div>
        <div className="col-span-12">
          <FormController
            name="title"
            type="text"
            label={t('title')}
            control={control}
            errors={errors}
            required
          />
        </div>
        <div className="col-span-12">
          <FormController
            name="description"
            type="textarea"
            label={t('description')}
            control={control}
            errors={errors}
            required
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="filenumber"
            type="text"
            label={t('fileNumber')}
            control={control}
            errors={errors}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="fileYear"
            variant="outlined"
            type="select"
            label={t('year')}
            optionKey="name"
            control={control}
            errors={errors}
            required
            options={_.range(1980, new Date().getFullYear() + 1).reverse().map((yr) => ({ name: yr.toString(), value: yr.toString() }))}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            type="date"
            name="fileStartDate"
            label={t('fileNoteStartDate')}
            control={control}
            errors={errors}
            dateFormat="dd-MM-yyyy"
            maxDate={new Date()}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="lastpagenumber"
            type="text"
            label={t('noteFileLastPageNumber')}
            control={control}
            errors={errors}
            required
            onKeyPress={(event) => {
              if (/[ ]/.test(event.key)) {
                event.preventDefault();
              }
            }}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="legacyLastNoteNo"
            type="text"
            label={t('noteFileLastParaNumber')}
            control={control}
            errors={errors}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            type="date"
            name="fileNoteEndDate"
            label={t('fileNoteEndDate')}
            control={control}
            errors={errors}
            dateFormat="dd-MM-yyyy"
            minDate={watch('fileStartDate')}
            maxDate={new Date()}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="correspondType"
            type="select"
            label={t('lastCorrespondenceType')}
            control={control}
            errors={errors}
            optionKey="id"
            options={_.get(correspondTypeDropdown, 'data', [])}
            defaultValue="1"
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            type="date"
            name="lastCorrespondDate"
            label={t('lastCorrespondenceDate')}
            control={control}
            errors={errors}
            dateFormat="dd-MM-yyyy"
            minDate={watch('fileStartDate')}
            maxDate={watch('fileNoteEndDate')}
          />
        </div>
        {
          params.id
            ? (
              <div className="col-span-12 text-right">
                <Button
                  type="submit"
                  variant="secondary_outline"
                  className="shadow-md"
                  form="service-form"
                  isLoading={actionTriggered?.id === 'legacyServiceCreate' && actionTriggered?.loading}
                >
                  {t('update')}
                </Button>
              </div>
            )
            : (
              <div className="col-span-12 text-right">
                <Button
                  type="submit"
                  variant="secondary_outline"
                  className="shadow-md"
                  form="service-form"
                  isLoading={actionTriggered?.id === 'legacyServiceCreate' && actionTriggered?.loading}
                >
                  {t('proceed')}
                </Button>
              </div>
            )
        }

      </FormWrapper>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  serviceDropdown: getServicesDropdown,
  fileTypeDropdown: getFileTypeDropdown,
  fileYearDropdown: getFileYearDropdown,
  moduleById: getModuleById,
  subModule: getSubModule,
  correspondTypeDropdown: getCorrespondTypeDropdown,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  serviceByCodeDetails: getServiceByCodeDetails,
  activeApplicationData: getActiveApplicationData
});

const mapDispatchToProps = (dispatch) => ({
  fetchServicesOptions: () => dispatch(commonActions.fetchServicesDetails()),
  fetchSubModuleById: (data) => dispatch(commonActions.fetchSubModuleById(data)),
  fetchModuleById: (data) => dispatch(commonActions.fetchModuleById(data)),
  fetchFileTypeOptions: () => dispatch(commonActions.fetchFileTypeDetails()),
  saveApplicationService: (data) => dispatch(actions.saveApplicationService(data)),
  fetchCorrespondTypeOptions: () => dispatch(commonActions.fetchCorrespondTypeDetails()),
  updateApplicationService: (data) => dispatch(actions.updateApplicationService(data)),
  fetchServiceByServiceCode: (data) => dispatch(actionsCounter.fetchServiceByServiceCode(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(CreateLegacyFile);
