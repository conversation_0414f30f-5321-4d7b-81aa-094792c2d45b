import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getLegacyFilesNew = (state) => state[STATE_REDUCER_KEY];

const legacyFileList = (state) => state?.setLegacyFileList;
export const getLegacyList = flow(getLegacyFilesNew, legacyFileList);

const legacyFileListById = (state) => state?.setLegacyFileListById;
export const getLegacyListById = flow(getLegacyFilesNew, legacyFileListById);

const apiParams = (state) => state?.apiParams;
export const getApiParams = flow(getLegacyFilesNew, apiParams);
