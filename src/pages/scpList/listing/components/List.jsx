import {
  t,
  Input, InputGroup, InputRightElement, IconButton
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import TableView from 'assets/TableView';
import { DATE_FORMAT, FILTER_TYPE } from 'pages/common/constants';
import SearchIcon from 'assets/SearchIcon';
import { convertToLocalDate } from 'utils/date';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { dark } from 'utils/color';
import BackArrow from 'assets/BackIcon';
import { BASE_UI_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { STATUS } from 'common/regex';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import { getScpTableListData, getScpTableListParams } from '../selectors';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    paddingRight: '9px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const List = ({
  userInfo, fetchScpTableList, setScpTableListParams, scpTableList, scpListParams,
  setTableLoader, tableLoader
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [search, setSearch] = useState('');
  const [date, setDate] = useState();
  const today = new Date().toISOString().split('T')[0];
  const [numberOfElements, setNumberOfElements] = useState(0);

  const viewActions = (data) => {
    window.location = `${BASE_UI_PATH}${data?.url}`;
  };

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      status = (
        <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
          {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
        </div>
      );
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNumber = (
        <>
          <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
          <div className="text-[14px] font-[700] text-[#B5B5B5]">{cellData?.fileDate}</div>
        </>
      );
    }
    return <div className="block">{fileNumber}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.applicantName}</div>
      );
    }
    return <div className="block">{applicantName}</div>;
  };

  const actionsTable = (row) => {
    return (
      <div className="flex gap-3">
        <IconButton variant="ghost" onClick={() => viewActions(row?.row)} icon={<TableView />} />
      </div>
    );
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('nameOfApplicant'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('status'),
      field: 'currentStage',
      cell: (field) => handleFileStatus(field)
    },
    {
      header: t('Actions'),
      alignment: 'left',
      cell: (field) => actionsTable(field)
    }

  ];

  useEffect(() => {
    if (scpListParams) {
      setTableLoader({ loading: true, id: 'scp-list-table' });
      fetchScpTableList();
    }
  }, [scpListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setScpTableListParams({
      ...scpListParams,
      page: data,
      officeId: userInfo.id
    });
  };

  useEffect(() => {
    if (scpTableList) {
      setTableLoader({ loading: false, id: 'scp-list-table' });
      if (Object.keys(scpTableList).length > 0) {
        setTableData(scpTableList.content);
        setTotalItems(Number(`${scpTableList.totalPages}0`));
        setNumberOfElements(Number(scpTableList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [scpTableList]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setScpTableListParams({
          ...scpListParams,
          keyword: data || null,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setScpTableListParams({
          ...scpListParams,
          createdAt: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          page: 0
        });
        setDate(data);
        break;
      default:
        break;
    }
  };

  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('scpList')}
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
              max={today}
            />
          </InputGroup>
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'scp-list-table'}
        />
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  scpTableList: getScpTableListData,
  scpListParams: getScpTableListParams,
  tableLoader: getTableLoader

});

const mapDispatchToProps = (dispatch) => ({
  fetchScpTableList: (data) => dispatch(actions.fetchScpTableList(data)),
  setScpTableListParams: (data) => dispatch(sliceActions.setScpTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
