import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_SCP_LIST: `${STATE_REDUCER_KEY}/FETCH_SCP_LIST`,
  FETCH_SCP_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SCP_LIST_REQUEST`,
  FETCH_SCP_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SCP_LIST_SUCCESS`,
  FETCH_SCP_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SCP_LIST_FAILURE`

};
export const fetchScpTableList = createAction(ACTION_TYPES.FETCH_SCP_LIST);
