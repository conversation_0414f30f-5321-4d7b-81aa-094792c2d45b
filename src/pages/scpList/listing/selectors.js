import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getArchivedDetails = (state) => state[STATE_REDUCER_KEY];

const scpTableListData = (state) => state?.scpTableList;
export const getScpTableListData = flow(getArchivedDetails, scpTableListData);

const scpTableListParams = (state) => state?.scpTableListParams;
export const getScpTableListParams = flow(getArchivedDetails, scpTableListParams);
