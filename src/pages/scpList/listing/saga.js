import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import { getScpTableListParams } from './selectors';

export function* fetchScpTableList() {
  const apiParams = yield select(getScpTableListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchScpTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SCP_LIST_SUCCESS,
    ACTION_TYPES.FETCH_SCP_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SCP_LIST_SUCCESS) {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'scp-list-table' }));
    yield put(sliceActions.setScpTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'scp-list-table' }));
  }
}

export default function* counterSaga() {
  yield all([

    takeLatest(ACTION_TYPES.FETCH_SCP_LIST, fetchScpTableList)
  ]);
}
