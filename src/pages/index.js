import * as common from './common';
import * as counter from './counter';
import * as legacyfiles from './legacyfiles';
import * as file from './file';
import * as arising from './arising';
import * as reports from './reports';
import * as searchFile from './searchFile';
import * as workflow from './workFlow';
import * as citizen from './citizen';
import * as archivedFile from './archivedFile';
import * as processedFile from './ProcessesdFile';
import * as temporaryDisposedFile from './temporaryDisposedFile';
import * as inwardSearch from './inwardSearch';
import * as scpList from './scpList';
import * as publicFileTracking from './publicFileTracking';
import * as profile from './profile';
import * as reassignList from './reassignFiles';
import * as kswift from './kswift';
import * as dispatchNew from './Dispatch-latest';
import * as recordRoom from './recordRoom';
import * as myFiles from './myFiles';
import * as eSignUserRequests from './eSignRequests';

const commonData = { common };
const workFlowData = { workflow };
const dispatchData = { dispatchNew };
export {

  commonData, counter, legacyfiles, arising, file, reports, searchFile,
  workFlowData, citizen, archivedFile, processedFile, temporaryDisposedFile, inwardSearch,
  scpList, publicFileTracking, profile, reassignList, kswift, dispatchData, recordRoom,
  myFiles, eSignUserRequests
};
