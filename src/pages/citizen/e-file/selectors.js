import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getCounterNew = (state) => state[STATE_REDUCER_KEY];

const activeAccordian = (state) => state?.activeAccordian;
export const getActiveAccordian = flow(getCounterNew, activeAccordian);

const eFilePreview = (state) => state?.eFilePreview;
export const getEFilePreview = flow(getCounterNew, eFilePreview);

const activeApplicationData = (state) => state?.activeCounterFormData;
export const getActiveApplicationData = flow(getCounterNew, activeApplicationData);

const activeDocumentsDetails = (state) => state?.documentDetails?.data;
export const getDocumentsDetails = flow(getCounterNew, activeDocumentsDetails);

const setDocumentDropdownTypes = (state) => state?.documentTypes;
export const getDocumentDropdownTypes = flow(getCounterNew, setDocumentDropdownTypes);

const jointApplicant = (state) => state?.jointApplicant;
export const getJointApplicant = flow(getCounterNew, jointApplicant);

const setActiveInwardId = (state) => state?.activeInwardId;
export const getActiveInwardId = flow(getCounterNew, setActiveInwardId);

const generalFieldValidation = (state) => state?.generalFieldValidation;
export const getGeneralFieldValidation = flow(getCounterNew, generalFieldValidation);

const documentPreview = (state) => state?.documentPreview;
export const getDocumentPreview = flow(getCounterNew, documentPreview);

const user = (state) => state?.user;
export const getUserData = flow(getCounterNew, user);

const otpSendResponse = (state) => state?.otpSendResponse;
export const getOtpSendResponse = flow(getCounterNew, otpSendResponse);
