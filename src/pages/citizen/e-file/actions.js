import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  CREATE_E_FILE: `${STATE_REDUCER_KEY}/CREATE_E_FILE`,
  CREATE_E_FILE_REQUEST: `${STATE_REDUCER_KEY}/CREATE_E_FILE_REQUEST`,
  CREATE_E_FILE_SUCCESS: `${STATE_REDUCER_KEY}/CREATE_E_FILE_SUCCESS`,
  CREATE_E_FILE_FAILURE: `${STATE_REDUCER_KEY}/CREATE_E_FILE_FAILURE`,

  SAVE_E_FILE_APPLICANT_DETAILS: `${STATE_REDUCER_KEY}/SAVE_E_FILE_APPLICANT_DETAILS`,
  SAVE_E_FILE_APPLICANT_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_E_FILE_APPLICANT_DETAILS_REQUEST`,
  SAVE_E_FILE_APPLICANT_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_E_FILE_APPLICANT_DETAILS_SUCCESS`,
  SAVE_E_FILE_APPLICANT_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_E_FILE_APPLICANT_DETAILS_FAILURE`,

  E_FILE_PREVIEW: `${STATE_REDUCER_KEY}/E_FILE_PREVIEW`,
  E_FILE_PREVIEW_REQUEST: `${STATE_REDUCER_KEY}/E_FILE_PREVIEW_REQUEST`,
  E_FILE_PREVIEW_SUCCESS: `${STATE_REDUCER_KEY}/E_FILE_PREVIEW_SUCCESS`,
  E_FILE_PREVIEW_FAILURE: `${STATE_REDUCER_KEY}/E_FILE_PREVIEW_FAILURE`,

  FETCH_FILES: `${STATE_REDUCER_KEY}/FETCH_FILES`,
  FETCH_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILES_REQUEST`,
  FETCH_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILES_SUCCESS`,
  FETCH_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILES_FAILURE`,

  SAVE_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS`,
  SAVE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_REQUEST`,
  SAVE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_SUCCESS`,
  SAVE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_FAILURE`,

  SAVE_MANDATORY_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS`,
  SAVE_MANDATORY_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_REQUEST`,
  SAVE_MANDATORY_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_SUCCESS`,
  SAVE_MANDATORY_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_FAILURE`,

  FETCH_DOCUMENT_TYPES: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES`,
  FETCH_DOCUMENT_TYPES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES_REQUEST`,
  FETCH_DOCUMENT_TYPES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES_SUCCESS`,
  FETCH_DOCUMENT_TYPES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DOCUMENT_TYPES_FAILURE`,

  SAVE_GENERAL_DETAILS: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS`,
  SAVE_GENERAL_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS_REQUEST`,
  SAVE_GENERAL_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS_SUCCESS`,
  SAVE_GENERAL_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_GENERAL_DETAILS_FAILURE`,

  COMPLETE_SAVE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE`,
  COMPLETE_SAVE_REQUEST: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_REQUEST`,
  COMPLETE_SAVE_SUCCESS: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_SUCCESS`,
  COMPLETE_SAVE_FAILURE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_FAILURE`,

  DELETE_APPLICANT: `${STATE_REDUCER_KEY}/DELETE_APPLICANT`,
  DELETE_APPLICANT_REQUEST: `${STATE_REDUCER_KEY}/DELETE_APPLICANT_REQUEST`,
  DELETE_APPLICANT_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_APPLICANT_SUCCESS`,
  DELETE_APPLICANT_FAILURE: `${STATE_REDUCER_KEY}/DELETE_APPLICANT_FAILURE`,

  EFILE_DECLARATION: `${STATE_REDUCER_KEY}/EFILE_DECLARATION`,
  EFILE_DECLARATION_REQUEST: `${STATE_REDUCER_KEY}/EFILE_DECLARATION_REQUEST`,
  EFILE_DECLARATION_SUCCESS: `${STATE_REDUCER_KEY}/EFILE_DECLARATION_SUCCESS`,
  EFILE_DECLARATION_FAILURE: `${STATE_REDUCER_KEY}/EFILE_DECLARATION_FAILURE`,

  GET_USER: `${STATE_REDUCER_KEY}/GET_USER`,
  GET_USER_REQUEST: `${STATE_REDUCER_KEY}/GET_USER_REQUEST`,
  GET_USER_SUCCESS: `${STATE_REDUCER_KEY}/GET_USER_SUCCESS`,
  GET_USER_FAILURE: `${STATE_REDUCER_KEY}/GET_USER_FAILURE`,

  EPAY_GENERATE: `${STATE_REDUCER_KEY}/EPAY_GENERATE`,
  EPAY_GENERATE_REQUEST: `${STATE_REDUCER_KEY}/EPAY_GENERATE_REQUEST`,
  EPAY_GENERATE_SUCCESS: `${STATE_REDUCER_KEY}/EPAY_GENERATE_SUCCESS`,
  EPAY_GENERATE_FAILURE: `${STATE_REDUCER_KEY}/EPAY_GENERATE_FAILURE`,

  SEND_OTP: `${STATE_REDUCER_KEY}/SEND_OTP`,
  SEND_OTP_REQUEST: `${STATE_REDUCER_KEY}/SEND_OTP_REQUEST`,
  SEND_OTP_SUCCESS: `${STATE_REDUCER_KEY}/SEND_OTP_SUCCESS`,
  SEND_OTP_FAILURE: `${STATE_REDUCER_KEY}/SEND_OTP_FAILURE`,

  VERIFY_OTP: `${STATE_REDUCER_KEY}/VERIFY_OTP`,
  VERIFY_OTP_REQUEST: `${STATE_REDUCER_KEY}/VERIFY_OTP_REQUEST`,
  VERIFY_OTP_SUCCESS: `${STATE_REDUCER_KEY}/VERIFY_OTP_SUCCESS`,
  VERIFY_OTP_FAILURE: `${STATE_REDUCER_KEY}/VERIFY_OTP_FAILURE`,

  RESUBMIT_EFILE: `${STATE_REDUCER_KEY}/RESUBMIT_EFILE`,
  RESUBMIT_EFILE_REQUEST: `${STATE_REDUCER_KEY}/RESUBMIT_EFILE_REQUEST`,
  RESUBMIT_EFILE_SUCCESS: `${STATE_REDUCER_KEY}/RESUBMIT_EFILE_SUCCESS`,
  RESUBMIT_EFILE_FAILURE: `${STATE_REDUCER_KEY}/RESUBMIT_EFILE_FAILURE`,

  CREATE_FILE: `${STATE_REDUCER_KEY}/CREATE_FILE`,
  CREATE_FILE_REQUEST: `${STATE_REDUCER_KEY}/CREATE_FILE_REQUEST`,
  CREATE_FILE_SUCCESS: `${STATE_REDUCER_KEY}/CREATE_FILE_SUCCESS`,
  CREATE_FILE_FAILURE: `${STATE_REDUCER_KEY}/CREATE_FILE_FAILURE`,

  FETCH_INWARDS: `${STATE_REDUCER_KEY}/FETCH_INWARDS`,
  FETCH_INWARDS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INWARDS_REQUEST`,
  FETCH_INWARDS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INWARDS_SUCCESS`,
  FETCH_INWARDS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INWARDS_FAILURE`

};

export const createEFile = createAction(ACTION_TYPES.CREATE_E_FILE);
export const saveEFileApplicantDetails = createAction(ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS);
export const fetchEFilePreview = createAction(ACTION_TYPES.E_FILE_PREVIEW);

export const fetchFiles = createAction(ACTION_TYPES.FETCH_FILES);
export const saveDocuments = createAction(ACTION_TYPES.SAVE_DOCUMENTS);
export const saveMandatoryDocuments = createAction(ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS);
export const getDocumentTypes = createAction(ACTION_TYPES.FETCH_DOCUMENT_TYPES);
export const saveGeneralDetails = createAction(ACTION_TYPES.SAVE_GENERAL_DETAILS);
export const saveComplete = createAction(ACTION_TYPES.COMPLETE_SAVE);
export const deleteApplicant = createAction(ACTION_TYPES.DELETE_APPLICANT);
export const saveEfileDeclaration = createAction(ACTION_TYPES.EFILE_DECLARATION);
export const epayGenerate = createAction(ACTION_TYPES.EPAY_GENERATE);

export const getUser = createAction(ACTION_TYPES.GET_USER);
export const sendOtp = createAction(ACTION_TYPES.SEND_OTP);
export const verifyOtp = createAction(ACTION_TYPES.VERIFY_OTP);
export const resubmitEfile = createAction(ACTION_TYPES.RESUBMIT_EFILE);
export const createFile = createAction(ACTION_TYPES.CREATE_FILE);
export const fetchInwards = createAction(ACTION_TYPES.FETCH_INWARDS);
