import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';
import { ACTION_TYPES } from './actions';

const initialState = {
  activeAccordian: 'Service',
  subModule: {},
  module: {},
  subModulesSearch: {},
  servicesSearchList: {},
  advanceSearchResult: {},
  activeCounterFormData: {},
  activeInwardId: '',
  jointApplicant: [],
  documentTypes: [],
  generalFieldValidation: {},
  documentPreview: {}
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {

    setActiveCounterFormData: (state, { payload }) => {
      _.set(state, 'activeCounterFormData', payload);
    },
    setDocumentDetails: (state, { payload }) => {
      _.set(state, 'documentDetails', payload);
    },
    setDocumentTypes: (state, { payload }) => {
      _.set(state, 'documentTypes', payload);
    },
    setActiveInwardId: (state, { payload }) => {
      _.set(state, 'activeInwardId', payload);
    },
    setJointApplicant: (state, { payload }) => {
      _.set(state, 'jointApplicant', payload);
    },
    setGeneralFieldValidation: (state, { payload }) => {
      _.set(state, 'generalFieldValidation', payload);
    },
    setApplications: (state, { payload }) => {
      _.set(state, 'inwards', payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.E_FILE_PREVIEW_SUCCESS, (state, { payload }) => {
        _.set(state, 'eFilePreview', payload.data);
      })
      .addCase(ACTION_TYPES.GET_USER_SUCCESS, (state, { payload }) => {
        _.set(state, 'user', payload.data);
      })
      .addCase(ACTION_TYPES.SEND_OTP_SUCCESS, (state, { payload }) => {
        _.set(state, 'otpSendResponse', payload.data);
      });
  }
});

export const { actions, reducer } = slice;

export const { setActiveAccordian } = actions;
