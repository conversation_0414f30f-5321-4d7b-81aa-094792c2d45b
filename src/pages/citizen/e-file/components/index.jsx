import { useEffect, useState } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import {
  AccordionComponent, t, Button, VerticalStepper
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  getAcknowledgeTriggered,
  getActionTriggered,
  getCompletedSteps,
  getServiceAccountHead,
  getSidebarData,
  getUserInfo
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { secondary } from 'utils/color';
import * as commonActions from 'pages/common/actions';
import * as counterActions from 'pages/counter/new/actions';
import { getRequiredDocsCount, getServiceByCodeDetails } from 'pages/counter/new/selectors';
import Acknowledgement from 'common/components/Aknowledgement';
import DownloadAck from 'common/components/Aknowledgement/DownloadAck';
import { USER_TYPE } from 'common/constants';
import { KSWIFT_HOME_URL } from 'pages/kswift/login/constants';
import ApplicantDetails from './ApplicantDetails';
import Documents from './Document';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import {
  SIDEBAR_KEYS,
  E_FILE_KEYS_INDEX,
  E_FILE_KEYS,
  inwardAknowledgementUrl,
  inwardAknowledgementDownloadUrl,
  APPLICATION_STAGE
} from '../constants';
import { getDocumentDropdownTypes, getEFilePreview } from '../selectors';
import GeneralDetails from './GeneralDetails';
import './style.css';
import LocalBody from './LocalBody';
import Declaration from './Declaration';
import LocalBodyPreview from './Preview/LocalBody';
import ApplicantPreview from './Preview/ApplicantDetails';
import GeneralPreview from './Preview/GeneralDetail';
import DocumentPreview from './Preview/Document';
import OtpVerfication from './OtpVerfication';
import OrgApplicantDetails from './OrgApplicantDetails';
import OrgApplicantDetailPreview from './Preview/OrgApplicantDetailPreview';

const CounterNew = (props) => {
  const {
    setFormTitle,
    setActiveAccordian,
    formComponentData,
    setFormComponentData,
    setSidebarStatus,
    eFilePreview,
    setActiveCounterFormData,
    fetchEFilePreview,
    saveComplete,
    acknowledgeTriggered,
    setAcknowledgeTriggered,
    userInfo,
    fetchServiceValidation,
    setAlertAction,
    requiredDocsCount,
    fetchServiceAccountHead,
    serviceAccountHead,
    setActionTriggered,
    actionTriggered,
    serviceByCodeDetails,
    fetchServiceByServiceCode,
    getUser,
    epayGenerate,
    documentTypeDropdown,
    completedSteps,
    resubmitEfile
  } = props;

  const params = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  let sidebarData = [];
  const [preview, setPreview] = useState(false);
  const [haveReceipt, setHaveReceipt] = useState(false);
  const [receiptOk, setReceiptOk] = useState(false);
  const [checkingPayment, setCheckingPayment] = useState(true);
  const [openAck, setOpenAck] = useState(false);

  useEffect(() => {
    if (params) {
      if (params?.serviceCode) {
        fetchServiceByServiceCode(params?.serviceCode);
      }
    }
  }, [params]);

  useEffect(() => {
    if (userInfo) {
      if (userInfo?.userDetails?.userId) {
        getUser(userInfo?.userDetails?.userId);
      }
    }
  }, [JSON.stringify(userInfo)]);

  useEffect(() => {
    setFormTitle({ title: serviceByCodeDetails ? serviceByCodeDetails[0]?.name : '', variant: 'normal' });
  }, [JSON.stringify(serviceByCodeDetails)]);

  useEffect(() => {
    if (params?.id) {
      fetchEFilePreview(params?.id);
    } else {
      setActiveCounterFormData({});
    }
  }, [params?.id]);

  useEffect(() => {
    if (serviceAccountHead?.receiptAtTheTimeOfApplication) {
      setCheckingPayment(false);
      setHaveReceipt(serviceAccountHead?.receiptAtTheTimeOfApplication === 1);
    } else if (serviceAccountHead?.receiptAtTheTimeOfApplication === 0) {
      setCheckingPayment(false);
    }
  }, [serviceAccountHead]);

  const onClickSidebarItem = (currentStep) => {
    const formFieldKeys = Object.keys(E_FILE_KEYS);
    const currentFormFieldKey = formFieldKeys[Number(currentStep - 1)];
    setSidebarStatus({
      activeStep: currentStep,
      completedSteps
    });
    switch (E_FILE_KEYS[currentFormFieldKey]) {
      case E_FILE_KEYS.LocalBody:
        setActiveAccordian(E_FILE_KEYS.Service);
        break;
      case E_FILE_KEYS.Applicant:
        setActiveAccordian(E_FILE_KEYS.Applicant);
        break;
      case E_FILE_KEYS.Document:
        setActiveAccordian(E_FILE_KEYS.Document);
        break;
      case E_FILE_KEYS.Preview:
        setActiveAccordian(E_FILE_KEYS.Preview);
        break;
      case E_FILE_KEYS.Declaration:
        setActiveAccordian(E_FILE_KEYS.Declaration);
        break;
      case E_FILE_KEYS.Verification:
        setActiveAccordian(E_FILE_KEYS.Verification);
        break;
      default:
        return false;
    }
    return true;
  };

  const checkApplicantType = () => {
    if (userInfo?.userDetails?.userType === USER_TYPE.ORGANIZATION) {
      return <OrgApplicantDetails formActiveData={eFilePreview} />;
    }
    return <ApplicantDetails formActiveData={eFilePreview} />;
  };

  const checkApplicantPreview = () => {
    if (userInfo?.userDetails?.userType === USER_TYPE.ORGANIZATION) {
      return <OrgApplicantDetailPreview data={eFilePreview} />;
    }
    return <ApplicantPreview columns={4} data={eFilePreview} />;
  };

  const accordionData = [
    {
      title: t('submittingTo'),
      content: preview ? (
        <LocalBodyPreview columns={4} data={eFilePreview} />
      ) : (
        <LocalBody formActiveData={eFilePreview} />
      ),
      onPress: preview ? null : () => onClickSidebarItem(E_FILE_KEYS_INDEX.LocalBody),
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.LocalBody)
    },
    {
      title: t('concatLabel', { label: t('applicant'), type: t('details') }),
      content: preview ? checkApplicantPreview() : checkApplicantType(),
      onPress: preview ? null : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Applicant),
      onClick:
        (preview && eFilePreview?.stage === APPLICATION_STAGE.PARTIAL)
          || (preview && eFilePreview?.stage === APPLICATION_STAGE.RETURN_TO_CITIZEN)
          ? () => onClickSidebarItem(E_FILE_KEYS_INDEX.Applicant)
          : null,
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.Applicant)
    },
    {
      title: t('concatLabel', { label: t('general'), type: t('details') }),
      content: preview ? (
        <GeneralPreview columns={4} data={eFilePreview} />
      ) : (
        <GeneralDetails formActiveData={eFilePreview} />
      ),
      onPress: preview ? null : () => onClickSidebarItem(E_FILE_KEYS_INDEX.General),
      onClick:
        (preview && eFilePreview?.stage === APPLICATION_STAGE.PARTIAL)
          || (preview && eFilePreview?.stage === APPLICATION_STAGE.RETURN_TO_CITIZEN)
          ? () => onClickSidebarItem(E_FILE_KEYS_INDEX.General)
          : null,
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.General)
    },
    {
      title: t('documents'),
      content: preview ? (
        <DocumentPreview
          userInfo={userInfo}
          data={eFilePreview}
          documentTypeDropdown={documentTypeDropdown}
          applicationStatus={eFilePreview?.stage}
        />
      ) : (
        <Documents formActiveData={eFilePreview} />
      ),
      onPress: preview ? null : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Document),
      onClick:
        (preview && eFilePreview?.stage === APPLICATION_STAGE.PARTIAL)
          || (preview && eFilePreview?.stage === APPLICATION_STAGE.RETURN_TO_CITIZEN)
          ? () => onClickSidebarItem(E_FILE_KEYS_INDEX.Document)
          : null,
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.Document)
    },
    {
      title: preview ? null : t('preview'),
      onPress: preview ? null : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Preview),
      hidden: !!preview,
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.Preview)
    },
    {
      title: t('declaration'),
      content: <Declaration formActiveData={eFilePreview} />,
      onPress: () => onClickSidebarItem(E_FILE_KEYS_INDEX.Declaration),
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.Declaration),
      hidden:
        eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
        && params?.id
        && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
    },
    {
      title: t('otpVerification'),
      content: <OtpVerfication />,
      onPress: () => onClickSidebarItem(E_FILE_KEYS_INDEX.Verification),
      isCompleted: completedSteps.includes(E_FILE_KEYS_INDEX.Verification),
      hidden:
        eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
        && params?.id
        && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
    }
  ];

  const SIDEBAR_STEPPER_STEPS = {
    APPLICATION: [
      {
        title: t('localBody'),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.LocalBody)
      },
      {
        title: t('concatLabel', { label: t('applicant'), type: t('details') }),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Applicant)
      },
      {
        title: t('concatLabel', { label: t('general'), type: t('details') }),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.General)
      },
      {
        title: t('documents'),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Document)
      },
      {
        title: t('preview'),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Preview)
      },
      {
        title: t('declaration'),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Declaration)
      },
      {
        title: t('otpVerification'),
        onClick:
          eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
            && params?.id
            && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
            ? null
            : () => onClickSidebarItem(E_FILE_KEYS_INDEX.Verification)
      }
    ]
  };

  const getSidebarAccordionData = () => {
    return [
      {
        title: t('application'),
        key: SIDEBAR_KEYS.APPLICATION,
        content: (
          <VerticalStepper
            steps={SIDEBAR_STEPPER_STEPS.APPLICATION}
            activeStep={Number(formComponentData.activeStep)}
            completedSteps={formComponentData?.completedSteps}
            isDisabled
          />
        )
      },
      {
        title: t('concatLabel', { label: t('required'), type: t('documents') }),
        key: SIDEBAR_KEYS.REQUIRED_DOCUMENTS,
        content: '',
        isDisabled: true
      },
      {
        title: t('guidelines'),
        key: SIDEBAR_KEYS.GUIDELINES,
        content: '',
        isDisabled: true
      },
      {
        title: t('quickAccess'),
        key: SIDEBAR_KEYS.QUICK_ACCESS,
        content: '',
        isDisabled: true
      },
      {
        title: t('process'),
        key: SIDEBAR_KEYS.PROCESS,
        content: '',
        isDisabled: true
      }
    ];
  };

  useEffect(() => {
    if (eFilePreview?.kswiftId) {
      navigate(
        `/ui/file-management/citizen/e-file/${params?.serviceCode}/${params?.id}?kswiftId=${eFilePreview?.kswiftId}`
      );
    }
  }, [eFilePreview]);

  useEffect(() => {
    sidebarData = getSidebarAccordionData();
    setFormComponentData({ data: sidebarData, steps: SIDEBAR_STEPPER_STEPS.APPLICATION });
    if (formComponentData?.activeStep === E_FILE_KEYS_INDEX.Preview) {
      setPreview(true);
    } else {
      setPreview(false);
    }
  }, [formComponentData?.activeStep]);

  useEffect(() => {
    if (params?.serviceCode) {
      setCheckingPayment(true);
      fetchServiceValidation(params?.serviceCode);
      fetchServiceAccountHead(params?.serviceCode);
      if (Number(params?.payStatus) === 4) {
        setReceiptOk(true);
      }
    }
  }, [params]);

  useEffect(() => {
    if (eFilePreview?.serviceCode) {
      if (eFilePreview?.payStatus === 4) {
        setReceiptOk(true);
      }
    }
  }, [JSON.stringify(eFilePreview)]);

  useEffect(() => {
    if (eFilePreview) {
      if (
        eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
        && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
      ) {
        setPreview(true);
      }
      if (eFilePreview?.inwardId) {
        const copyCompleted = JSON.parse(JSON.stringify(completedSteps)) || [];
        const find = copyCompleted.findIndex((item) => item === E_FILE_KEYS_INDEX.LocalBody);
        if (find === -1) {
          copyCompleted.push(E_FILE_KEYS_INDEX.LocalBody);
          setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Applicant, completedSteps: copyCompleted });
        }
      }
      if (
        eFilePreview?.applicantDetailsResponses?.length > 0
        || eFilePreview?.institutionDetailsResponses?.length > 0
      ) {
        const copyCompleted = JSON.parse(JSON.stringify(completedSteps)) || [];
        const find = copyCompleted.findIndex((item) => item === E_FILE_KEYS_INDEX.Applicant);
        if (find === -1) {
          copyCompleted.push(E_FILE_KEYS_INDEX.Applicant);
          setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.General, completedSteps: copyCompleted });
        }
      }
      if (
        eFilePreview?.applicantDetailsResponses?.length > 0
        || eFilePreview?.institutionDetailsResponses?.length > 0
      ) {
        if (
          eFilePreview?.generalDetailsResponses?.details?.length
          >= (eFilePreview?.applicantDetailsResponses?.length || eFilePreview?.institutionDetailsResponses?.length > 0)
        ) {
          const copyCompleted = JSON.parse(JSON.stringify(completedSteps)) || [];
          const find = copyCompleted.findIndex((item) => item === E_FILE_KEYS_INDEX.General);
          if (find === -1) {
            copyCompleted.push(E_FILE_KEYS_INDEX.General);
            setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Document, completedSteps: copyCompleted });
          }
        }
      }
      if (requiredDocsCount === 0 || eFilePreview?.documentsTypeUpload?.length === requiredDocsCount) {
        const copyCompleted = JSON.parse(JSON.stringify(completedSteps)) || [];
        const find = copyCompleted.findIndex((item) => item === E_FILE_KEYS_INDEX.Document);
        if (find === -1) {
          copyCompleted.push(E_FILE_KEYS_INDEX.Document);
          setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Document, completedSteps: copyCompleted });
        }
      }
      if (eFilePreview?.declaration) {
        const copyCompleted = JSON.parse(JSON.stringify(completedSteps)) || [];
        const find = copyCompleted.findIndex((item) => item === E_FILE_KEYS_INDEX.Declaration);
        if (find === -1) {
          copyCompleted.push(E_FILE_KEYS_INDEX.Preview);
          copyCompleted.push(E_FILE_KEYS_INDEX.Declaration);
          setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Verification, completedSteps: copyCompleted });
        }
      }
      if (eFilePreview?.otpValidationStatus) {
        const copyCompleted = JSON.parse(JSON.stringify(completedSteps)) || [];
        const find = copyCompleted.findIndex((item) => item === E_FILE_KEYS_INDEX.Verification);
        if (find === -1) {
          copyCompleted.push(E_FILE_KEYS_INDEX.Verification);
          setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Verification, completedSteps: copyCompleted });
        }
      }
    }
  }, [JSON.stringify(eFilePreview), JSON.stringify(completedSteps)]);

  const save = (actionType) => {
    setActionTriggered({ loading: true, id: 'efile-Create' });

    if (completedSteps.length > 0) {
      if (!completedSteps.includes(E_FILE_KEYS_INDEX.LocalBody)) {
        setSidebarStatus({
          activeStep: E_FILE_KEYS_INDEX.LocalBody,
          completedSteps
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('localBody')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('service_details');
        setActionTriggered({ loading: false, id: 'efile-Create' });
      } else if (!completedSteps.includes(E_FILE_KEYS_INDEX.Applicant)) {
        setSidebarStatus({
          activeStep: E_FILE_KEYS_INDEX.Applicant,
          completedSteps
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('applicant')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('applicant_details');
        setActionTriggered({ loading: false, id: 'efile-Create' });
      } else if (!completedSteps.includes(E_FILE_KEYS_INDEX.General)) {
        setSidebarStatus({
          activeStep: E_FILE_KEYS_INDEX.General,
          completedSteps
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('general')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('general_details');
        setActionTriggered({ loading: false, id: 'efile-Create' });
      } else if (!completedSteps.includes(E_FILE_KEYS_INDEX.Document)) {
        setSidebarStatus({
          activeStep: E_FILE_KEYS_INDEX.Document,
          completedSteps
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('document')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('documents_details');
        setActionTriggered({ loading: false, id: 'efile-Create' });
      } else if (!completedSteps.includes(E_FILE_KEYS_INDEX.Declaration)) {
        setSidebarStatus({
          activeStep: E_FILE_KEYS_INDEX.Declaration,
          completedSteps
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('declaration')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('declaration_details');
        setActionTriggered({ loading: false, id: 'efile-Create' });
      } else if (!completedSteps.includes(E_FILE_KEYS_INDEX.Verification)) {
        setSidebarStatus({
          activeStep: E_FILE_KEYS_INDEX.Verification,
          completedSteps
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('verification')} ${t('section')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        scrollToTop('verification_details');
        setActionTriggered({ loading: false, id: 'efile-Create' });
      } else if (eFilePreview?.otpValidationStatus) {
        if (actionType === 'receipt') {
          const sendData = {
            officeId: eFilePreview?.localBodyDetails?.officeCode,
            inwardId: eFilePreview?.inwardId
          };
          epayGenerate(sendData);
        } else {
          const saveData = {
            inwardId: params?.id,
            filePayload: {
              inwardIds: [params?.id],
              title: serviceByCodeDetails ? serviceByCodeDetails[0]?.name : '',
              description: eFilePreview?.generalDetailsResponses?.description
                ? eFilePreview?.generalDetailsResponses?.description
                : '',
              source: 4
            }
          };
          saveComplete(saveData);
        }
      } else {
        setActiveAccordian(E_FILE_KEYS.Declaration);
        setSidebarStatus({
          activeStep: 5
        });
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('pleaseComplete')} ${t('the')} ${t('self')} ${t('declaration')}`,
          title: t('missingSections'),
          backwardActionText: t('ok')
        });
        setActionTriggered({ loading: false, id: 'efile-Create' });
      }
    } else {
      setSidebarStatus({
        activeStep: 0
      });
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseComplete')} ${t('the')} ${t('above')} ${t('section')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
      scrollToTop('service_details');
      setActiveAccordian(E_FILE_KEYS.Service);

      setActionTriggered({ loading: false, id: 'efile-Create' });
    }
  };

  useEffect(() => {
    if (searchParams?.get('payment') === 'ok') {
      setTimeout(() => {
        scrollToTop('efile-payment-ok');
      }, 300);
    }
  }, [searchParams]);

  const handleReset = () => {
    setAcknowledgeTriggered(false);
    if (searchParams?.get('kswiftId')) {
      localStorage.clear();
      window.location.href = KSWIFT_HOME_URL;
    } else {
      window.location.href = `${window?.location?.origin}/ui/home/<USER>/dashboard`;
    }
  };

  const anknowledgePreview = {
    date: eFilePreview?.inwardDate,
    time: '',
    district: '',
    localBody: '',
    title: serviceByCodeDetails ? serviceByCodeDetails[0]?.name : '',
    description: `Efile Application Submited Successfully against 
    ${serviceByCodeDetails ? serviceByCodeDetails[0]?.name : ''} 
      with reference inward number ${eFilePreview?.inwardNo}`
  };

  const printData = `${inwardAknowledgementUrl}${eFilePreview?.localBodyDetails?.officeCode}/${params.id}`;
  const urlDownload = `${inwardAknowledgementDownloadUrl}${params.id}`;

  const handleDownload = () => {
    setOpenAck(true);
  };

  const handleClose = () => {
    setOpenAck(false);
  };

  const confirmReSubmit = () => {
    const sendData = {
      fileNo: eFilePreview?.fileNo,
      title: '',
      description: ''
    };
    resubmitEfile(sendData);
  };

  const reSubmit = () => {
    if (!completedSteps.includes(E_FILE_KEYS_INDEX.Verification)) {
      setSidebarStatus({
        activeStep: E_FILE_KEYS_INDEX.Verification,
        completedSteps
      });
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseComplete')} ${t('the')} ${t('verification')} ${t('section')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
      scrollToTop('verification_details');
      setActionTriggered({ loading: false, id: 'efile-Create' });
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('areYoursureTo')}<br/> ${t('resubmitFile')}: ${eFilePreview?.fileNo}`,
        title: t('confirm'),
        backwardActionText: t('close'),
        forwardActionText: t('confirm'),
        forwardAction: () => confirmReSubmit()
      });
    }
  };

  const saveActions = eFilePreview?.stage === APPLICATION_STAGE.RETURN_TO_CITIZEN ? (
    <Button type="submit" form="hook-form" onClick={() => reSubmit()} variant="secondary">
      {t('reSubmit')}
    </Button>
  ) : (
    <Button type="submit" form="hook-form" onClick={() => save('final')} variant="secondary">
      {t('submit')}
    </Button>
  );

  return (
    <div className="pb-10 mb-10">
      {formComponentData?.activeStep === E_FILE_KEYS_INDEX.Preview && (
        <div className="grid justify-center rounded-md mb-3 bg-white">
          <div className="rounded py-3 my-2 px-10 font-semibold" style={{ background: secondary, color: '#fff' }}>
            Preview
          </div>
        </div>
      )}
      <AccordionComponent
        data={accordionData}
        allowMultiple={false}
        currentIndexes={
          (formComponentData.activeStep === E_FILE_KEYS_INDEX.Preview && params.id)
            || (eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
              && params?.id
              && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN)
            ? [0, 1, 2, 3]
            : [formComponentData.activeStep]
        }
        offset={200}
      />
      <div id="efile-payment-ok" />
      {eFilePreview?.stage !== APPLICATION_STAGE.PARTIAL
        && eFilePreview?.stage !== APPLICATION_STAGE.RETURN_TO_CITIZEN
        && params?.id ? (
          <div className="col-span-12 flex justify-end items-center space-x-4 pt-5">
            <Button
              type="submit"
              form="hook-form"
              onClick={() => handleDownload()}
              variant="secondary"
              isLoading={actionTriggered?.id === 'download-ack' && actionTriggered?.loading}
            >
              {t('downloadAcknowledgement')}
            </Button>
          </div>
        ) : (
          !checkingPayment && (
          <div className="col-span-12 flex justify-end items-center space-x-4 pt-5">
            {haveReceipt && !receiptOk ? (
              <Button
                type="submit"
                form="hook-form"
                onClick={() => save('receipt')}
                variant="secondary_outline"
                isLoading={actionTriggered?.id === 'counterCreate' && actionTriggered?.loading}
              >
                {t('payNow')}
              </Button>
            ) : (
              saveActions
            )}
          </div>
          )
        )}
      <Acknowledgement
        open={acknowledgeTriggered}
        handleClick={handleReset}
        print={printData}
        data={anknowledgePreview}
      />
      <DownloadAck open={openAck} downloadUrl={urlDownload} handleClose={handleClose} />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  formComponentData: getSidebarData,
  eFilePreview: getEFilePreview,
  acknowledgeTriggered: getAcknowledgeTriggered,
  serviceByCodeDetails: getServiceByCodeDetails,
  userInfo: getUserInfo,
  documentTypeDropdown: getDocumentDropdownTypes,
  requiredDocsCount: getRequiredDocsCount,
  completedSteps: getCompletedSteps,
  serviceAccountHead: getServiceAccountHead,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  setFormComponentData: (data) => dispatch(commonSliceActions.setFormComponentData(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActiveCounterFormData: (data) => dispatch(sliceActions.setActiveCounterFormData(data)),
  fetchEFilePreview: (data) => dispatch(actions.fetchEFilePreview(data)),
  saveComplete: (data) => dispatch(actions.saveComplete(data)),
  setAcknowledgeTriggered: (data) => dispatch(commonSliceActions.setAcknowledgeTriggered(data)),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchServiceAccountHead: (data) => dispatch(commonActions.fetchServiceAccountHead(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchServiceByServiceCode: (data) => dispatch(counterActions.fetchServiceByServiceCode(data)),
  getUser: (data) => dispatch(actions.getUser(data)),
  epayGenerate: (data) => dispatch(actions.epayGenerate(data)),
  resubmitEfile: (data) => dispatch(actions.resubmitEfile(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(CounterNew);
