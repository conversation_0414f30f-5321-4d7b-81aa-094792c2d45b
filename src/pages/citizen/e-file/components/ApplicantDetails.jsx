import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import _ from 'lodash';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, t, Button, IconButton
} from 'common/components';
import WhatsappIcon from 'assets/Whatsapp';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as commonActions from 'pages/common/actions';
import {
  getCountry,
  getState,
  getDistricts,
  getPostOffice,
  getSidebarData,
  getUserInfo,
  getLocalBodyType,
  getUserLocalBody,
  getGenerateAadharOtp,
  getVerifyAadharOtp,
  getOtpStatus,
  getSmartDetails,
  getActionTriggered,
  getWardYear,
  getLocalBodyTypeByDBylbCodeApplicant
} from 'pages/common/selectors';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DEFAULT_COUNTRY, DEFAULT_STATE, USER_TYPE } from 'common/constants';
import { CommonTable } from 'common/components/Table';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import Edit from 'assets/Edit';
import Delete from 'assets/delete';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { numberNull } from 'utils/numberNull';
import { docLength, docNumber } from 'pages/counter/new/components/helper';
import {
  DOOR_SUB_NUM, DOOR__NUM_ONLY, EMAIL_ONLY, ENG_ONLY, ML_ONLY, MOBILE_ONLY, SMALL_LETTERS
} from 'common/regex';
import { formateWard } from 'utils/ward';
import Registration from 'common/components/Registration';
import { nameValidation } from 'utils/validateFile';
import { getJointApplicant, getUserData } from '../selectors';
import { ApplicatDetailsFormSchema } from '../validate';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import { applicantDefaultValues, formatApplicantDetails, formateDocumentType } from './helper';
import { getPostOfficePreview } from './Preview/helper';

const ApplicantDetails = (props) => {
  const {
    fetchCountry,
    countryDropdown,
    fetchStates,
    stateDropdown,
    fetchDistricts,
    districtDropdown,
    wardDropdown,
    fetchWard,
    postOfficeDropdown,
    fetchPostOffice,
    saveEFileApplicantDetails,
    formActiveData,
    jointApplicantData,
    setJointApplicant,
    deleteApplicant,
    localBodyType,
    localBodyTypeByDBylbCode,
    fetchLocalBodyByDistrictByTypeApplicant,
    userLocalBody,
    setActionTriggered,
    fetchSmartProfile,
    setAlertAction,
    verifyAadharOtp,
    generateAadharOtpRes,
    actionTriggered,
    otpStatus,
    smartDetails,
    setRegisterOpen,
    fetchPostOfficeByPin,
    userData
  } = props;

  const [jointApplications, setJointApplications] = useState([]);
  const [editState, setEditState] = useState(false);
  const [editIndex, setEditIndex] = useState('');
  const [editId, setEditId] = useState(null);
  const [wardFormated, setWardFormated] = useState([]);
  const [localBodyTypeApplicant, setLocalBodyTypeApplicant] = useState([]);
  const [joinStatus, setJoinStatus] = useState(false);
  const [appliedForStatus, setAppliedForStatus] = useState(false);

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
    getValues,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: applicantDefaultValues,
    resolver: yupResolver(ApplicatDetailsFormSchema)
  });

  const params = useParams();
  const countrySelected = watch('countryId');
  const stateSelected = watch('stateId');
  const documentType = watch('documentType');
  const isWhatsappSame = watch('isWhatsappSame');
  const jointApplication = watch('jointApplication');
  const mobileNo = watch('mobileNo');
  const whatsapp = watch('whatsapp');

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  useEffect(() => {
    if (localBodyType?.data?.length > 0) {
      const lbType = localBodyType?.data;
      const submitingTop = lbType.filter((item) => item.code !== 'LB_TYPE_DISTRICT_PANCHAYATH' && item.code !== 'LB_TYPE_BLOCK_PANCHAYATH');
      setLocalBodyTypeApplicant(submitingTop);
    }
  }, [localBodyType]);

  useEffect(() => {
    if (wardDropdown?.data?.length > 0) {
      const wards = formateWard(wardDropdown?.data);
      setWardFormated(wards);
      setActionTriggered({ loading: false, id: 'counter-applicant-ward' });
    }
  }, [wardDropdown]);

  const resetData = () => {
    reset({ ...applicantDefaultValues, jointApplication: !!jointApplication }, { keepValues: false });
    setJointApplications([]);
  };

  useEffect(() => {
    if ((userData && formActiveData?.applicantDetailsResponses?.length === 0 && !appliedForStatus) || (userData && !params?.id && !appliedForStatus)) {
      if (Object.keys(userData?.data).length > 0) {
        const data = userData?.data;
        if (watch('mobileNo') === data?.phoneNumber || !watch('mobileNo')) {
          const fetchedFirstname = data?.name?.replace(/\./g, ' ');
          const spaceRemovedFirstName = fetchedFirstname?.replace(/ +(?= )/g, '');
          setValue('firstName', spaceRemovedFirstName);
          setValue('emailId', data.email);
          setValue('mobileNo', data.phoneNumber);
          setValue('whatsapp', data.whatsappNumber);
          setValue('documentNo', data.aadhaarNo);
          if (data.aadhaarId) {
            if (Object.keys(data.aadhaarId).length > 0) {
              setValue('pincode', data.aadhaarId.pincode);
              if (data.aadhaarId.pincode) {
                fetchPostOfficeByPin(data.aadhaarId.pincode);
              }
              setValue('mainPlace', data.aadhaarId.vtc);
              setValue('houseName', data.aadhaarId.houseEng);
            }
          }
        }
      }
    }
  }, [JSON.stringify(userData), appliedForStatus]);

  useEffect(() => {
    if (smartDetails?.length > 0) {
      const smartData = smartDetails[0];
      const fetchedFirstname = smartData?.name?.replace(/\./g, ' ');
      const spaceRemovedFirstName = fetchedFirstname?.replace(/ +(?= )/g, '');
      if (watch('mobileNo') === smartData?.phoneNumber || !watch('mobileNo')) {
        setValue('firstName', spaceRemovedFirstName);
        setValue('emailId', smartData.email);
        setValue('mobileNo', smartData.phoneNumber);
        setValue('whatsapp', smartData.whatsappNumber);
        setValue('documentNo', smartData.aadhaarNo);
        if (smartData.aadhaarId) {
          if (Object.keys(smartData.aadhaarId).length > 0) {
            setValue('pincode', smartData.aadhaarId.pincode);
            if (smartData.aadhaarId.pincode) {
              fetchPostOfficeByPin(smartData.aadhaarId.pincode);
            }
            setValue('mainPlace', smartData.aadhaarId.vtc);
            setValue('houseName', smartData.aadhaarId.houseEng);
          }
        }
      }
    }
  }, [JSON.stringify(smartDetails), appliedForStatus]);

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  const onDeleteActions = (data, index) => {
    if (data.id) {
      deleteApplicant({
        inwardId: params?.id,
        applicantId: data.id
      });
    } else {
      const existingArray = JSON.parse(JSON.stringify(jointApplications));
      existingArray.splice(index, 1);
      setEditId('');
      setEditIndex(null);
      setEditState(false);
      setJointApplicant(existingArray);
    }
  };

  const setDocument = (data) => {
    if (data.documentType) {
      return data.documentNo;
    }
    if (data.aadharNo) {
      return data.aadharNo;
    }
    if (data.udid) {
      return data.udid;
    }
    return data.passport;
  };

  const onEditActions = (data, index) => {
    scrollToTop('applicant_details');
    setEditState(true);
    setEditIndex(index);
    setEditId(data.id);
    setValue('countryId', Number(data.countryId));
    setValue('stateId', Number(data.stateId));
    setValue('districtId', Number(data.districtId));
    setValue('documentType', formateDocumentType(data));
    setValue('documentNo', setDocument(data));
    setValue('firstName', data.firstName);
    setValue('middleName', data.middleName);
    setValue('lastName', data.lastName);
    setValue('localFirstName', data.localFirstName);
    setValue('localMiddleName', data.localMiddleName);
    setValue('localLastName', data.localLastName);
    setValue('houseName', data.houseName);
    setValue('localHouseName', data.localHouseName);
    setValue('wardName', data.wardName);
    setValue('doorNo', data.doorNo);
    setValue('subNo', data.subNo);
    setValue('postOffice', Number(data.postOffice));
    setValue('pincode', data.pincode);
    setValue('street', data.street);
    setValue('localPlace', data.localPlace);
    setValue('mainPlace', Number(data.countryId) !== DEFAULT_COUNTRY.id ? data.city : data.mainPlace);
    setValue('localStreet', data.localStreet);
    setValue('localLocalPlace', data.localLocalPlace);
    setValue('localMainPlace', data.localMainPlace);
    setValue('emailId', data.emailId);
    setValue('mobileNo', data.mobileNo);
    setValue('isWhatsappSame', data.isWhatsappSame);
    setValue('whatsapp', data.whatsappNo);
    fetchLocalBodyByDistrictByTypeApplicant({ districtId: Number(data.districtId), lbTypeId: data.localBodyTypeId });
    setValue('localBodyNameId', data.localBodyNameId);
    setValue('localBodyTypeId', data.localBodyTypeId);
    setValue('postOfficeName', getPostOfficePreview(Number(data.postOffice), data.postOfficeList));
  };

  const tableActions = (row) => {
    return (
      <>
        <IconButton variant="unstyled" onClick={() => onDeleteActions(row?.row, row.rowIndex)} icon={<Delete />} />
        <IconButton variant="unstyled" onClick={() => onEditActions(row?.row, row.rowIndex)} icon={<Edit />} />
      </>
    );
  };

  const columns = [
    {
      header: 'Sl.No.',
      cell: ({ rowIndex }) => rowIndex + 1,
      alignment: 'left'
    },
    {
      header: 'Name',
      field: 'firstName',
      alignment: 'left'
    },
    {
      header: 'Phone Number',
      field: 'mobileNo',
      alignment: 'left'
    },
    {
      header: 'Action',
      alignment: 'left',
      cell: tableActions
    }
  ];

  const handleJointCheck = () => {
    setJoinStatus(!joinStatus);
    setValue('jointApplication', !joinStatus);
  };

  useEffect(() => {
    if (joinStatus && jointApplications.length > 0) {
      setAppliedForStatus(true);
      setEditId(null);
      setEditState(false);
    }
  }, [joinStatus, JSON.stringify(jointApplications)]);

  const handleAppliedForOthers = () => {
    setAppliedForStatus(!appliedForStatus);
  };

  useEffect(() => {
    if (appliedForStatus) {
      setValue('firstName', null);
      setValue('emailId', null);
      setValue('mobileNo', null);
      setValue('whatsapp', null);
      setValue('documentNo', null);
      setValue('pincode', null);
      setValue('street', null);
      setValue('mainPlace', null);
      setValue('houseName', null);
    }
  }, [appliedForStatus]);

  useEffect(() => {
    if (userLocalBody) {
      if (getValues('localbodyType') === 1) {
        setValue('countryId', DEFAULT_COUNTRY.id);
        setValue('stateId', userLocalBody?.stateId);
        setValue('districtId', userLocalBody?.districtId);
      }
    }
  }, [userLocalBody]);

  // const sendAadharOtp = () => {
  //   setActionTriggered({ loading: true, id: 'generateAadharOtp' });
  //   if (AADHAAR.test(getValues('documentNo'))) {
  //     const sendData = {
  //       aadhaarNo: getValues('documentNo'),
  //       project: 'KSMART',
  //       module: 'PENSION'
  //     };
  //     fetchSmartProfile(sendData);
  //   } else {
  //     setAlertAction({
  //       open: true,
  //       variant: 'warning',
  //       message: t('aadharNotValid'),
  //       title: t('warning'),
  //       backwardActionText: t('ok')
  //     });
  //   }
  // };

  const verifyAadharOtpFuc = () => {
    setActionTriggered({ loading: true, id: 'validateAadharOtp' });
    const sendData = {
      aadhaarNo: getValues('documentNo'),
      otp: getValues('validateAadhar'),
      txn: generateAadharOtpRes?.txn,
      project: 'KSMART',
      module: 'PENSION'
    };
    verifyAadharOtp(sendData);
  };

  const handleRegister = () => {
    setAlertAction(false);
    setRegisterOpen(true);
  };

  const fetchKsmartId = () => {
    setActionTriggered({ loading: true, id: 'fetchKsmartId' });
    const sendData = {
      phoneNumber: getValues('mobileNo'),
      project: 'KSMART',
      module: 'PENSION',
      userType: USER_TYPE.CITIZEN
    };
    fetchSmartProfile({ sendData, handleRegister });
  };

  const handleFieldChange = (field, data) => {
    const malayalamOnly = data?.target?.value?.replace(ML_ONLY, '');
    const nameValidate = nameValidation(data?.target?.value);
    const englishOnly = data?.target?.value?.replace(ENG_ONLY, '');
    const emailOnly = data?.target?.value?.replace(EMAIL_ONLY, '');
    const DOORS_NUM_ONLY = data?.target?.value?.replace(/^\s+/, '').replace(DOOR__NUM_ONLY, '');
    const updatedSubNumber = data?.target?.value.replace(SMALL_LETTERS, (match) => match.toUpperCase()).replace(/^\s+/, '').replace(DOOR_SUB_NUM, '');
    const mobileValidation = data?.target?.value?.replace(MOBILE_ONLY, '');

    if (data) {
      switch (field) {
        case 'countryId':
          setValue('countryCode', data.countryCode);
          if (data.id !== DEFAULT_COUNTRY.id) {
            setValue('documentType', 3);
          } else {
            setValue('documentType', 1);
          }
          setValue('documentNo', null);
          setValue('stateId', null);
          setValue('districtId', null);
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          setValue('officeId', null);
          setValue('postOfficeName', null);
          setValue('pincode', null);
          break;
        case 'stateId':
          setActionTriggered({ loading: true, id: 'counter-applicant-district' });
          fetchDistricts(data.id);
          setValue('districtId', null);
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          setValue('officeId', null);
          setValue('postOfficeName', null);
          setValue('pincode', null);
          break;
        case 'districtId':
          setActionTriggered({ loading: true, id: 'counter-applicant-postoffice' });
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          setValue('officeId', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'localBodyTypeId':
          setValue('localBodyTypeId', data.id);
          setValue('localBodyNameId', null);
          setActionTriggered({ loading: true, id: 'counter-applicant-localbody' });
          fetchLocalBodyByDistrictByTypeApplicant({ districtId: getValues('districtId'), lbTypeId: data.id });
          setValue('wardName', null);
          setValue('postOffice', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'localBodyNameId':
          setValue('officeId', data.officeCode);
          setValue('localBodyNameId', data.lbId);
          setActionTriggered({ loading: true, id: 'counter-applicant-ward' });
          fetchWard(data.officeCode);
          setValue('wardName', null);
          setValue('postOffice', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'documentType':
          setValue('documentType', Number(data));
          break;
        case 'mobileNo':
          setValue('mobileNo', mobileValidation);
          if (mobileValidation?.toString().length === 10) {
            fetchKsmartId();
          }
          if (isWhatsappSame) {
            setValue('whatsapp', mobileValidation);
          }
          break;
        case 'whatsappCheck':
          setValue('isWhatsappSame', data);
          setValue('whatsapp', data ? mobileNo : '');
          break;
        case 'wardName':
          setValue('wardName', data.wardId);
          setActionTriggered({ loading: true, id: 'counter-applicant-postoffice' });
          fetchPostOffice({ offLbCode: data.officeLbCode });
          setValue('postOffice', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'postOffice':
          setValue('postOffice', data.id);
          setValue('pincode', data.pinCode);
          setValue('postOfficeName', data.name);
          break;
        case 'pincode':
          if (data.length >= 6) {
            fetchPostOfficeByPin(data);
          }
          break;
        case 'isWhatsappSame':
          setValue('isWhatsappSame', data);
          setValue('whatsapp', data ? mobileValidation : '');
          break;
        case 'whatsapp':
          setValue('whatsapp', mobileValidation);
          if (mobileValidation !== whatsapp) {
            setValue('isWhatsappSame', false);
          }
          break;
        case 'localFirstName':
          setValue('localFirstName', malayalamOnly);
          break;
        case 'localMiddleName':
          setValue('localMiddleName', malayalamOnly);
          break;
        case 'localLastName':
          setValue('localLastName', malayalamOnly);
          break;
        case 'localHouseName':
          setValue('localHouseName', malayalamOnly);
          break;
        case 'localStreet':
          setValue('localStreet', malayalamOnly);
          break;
        case 'localLocalPlace':
          setValue('localLocalPlace', malayalamOnly);
          break;
        case 'localMainPlace':
          setValue('localMainPlace', malayalamOnly);
          break;
        case 'localInstitutionName':
          setValue('localInstitutionName', malayalamOnly);
          break;
        case 'localOfficeName':
          setValue('localOfficeName', malayalamOnly);
          break;
        case 'localDesignation':
          setValue('localDesignation', malayalamOnly);
          break;
        case 'firstName':
          setValue('firstName', nameValidate);
          break;
        case 'middleName':
          setValue('middleName', englishOnly);
          break;
        case 'lastName':
          setValue('lastName', nameValidate);
          break;
        case 'houseName':
          setValue('houseName', englishOnly);
          break;
        case 'street':
          setValue('street', englishOnly);
          break;
        case 'localPlace':
          setValue('localPlace', englishOnly);
          break;
        case 'mainPlace':
          setValue('mainPlace', englishOnly);
          break;
        case 'emailId':
          setValue('emailId', emailOnly);
          break;
        case 'doorNo':
          setValue('doorNo', DOORS_NUM_ONLY);
          break;
        case 'subNo':
          setValue('subNo', updatedSubNumber);
          break;
        default:
          break;
      }
    } else {
      switch (field) {
        case 'countryId':
          setValue('countryCode', null);
          setValue('documentType', 1);
          setValue('documentNo', null);
          setValue('pincode', null);
          setValue('stateId', null);
          break;
        case 'stateId':
          setValue('districtId', null);
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          setValue('officeId', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'districtId':
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          setValue('officeId', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'localBodyTypeId':
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'localBodyNameId':
          setValue('localBodyNameId', null);
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'wardName':
          setValue('postOffice', null);
          setValue('wardName', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        case 'postOffice':
          setValue('postOffice', null);
          setValue('pincode', null);
          setValue('postOfficeName', null);
          break;
        default:
          break;
      }
    }
  };

  const onSubmitForm = (data) => {
    if (editState && joinStatus) {
      const applicantArray = JSON.parse(JSON.stringify(jointApplicantData));
      applicantArray[editIndex] = formatApplicantDetails(data);
      applicantArray[editIndex].id = editId || null;
      setJointApplicant(applicantArray);
      setEditState(false);
      setEditIndex('');
      resetData();
    } else {
      let jointArray = JSON.parse(JSON.stringify(jointApplicantData));
      if (editState && !joinStatus) {
        const newData = data;
        newData.addressType = data.localBodyType ? data.localBodyType : data.addressType;
        setJointApplicant(newData);
        jointArray = [newData];
      } else if (!editState && joinStatus) {
        const newData = data;
        newData.addressType = data.localBodyType ? data.localBodyType : data.addressType;
        newData.id = null;
        setEditId(null);
        jointArray.push(newData);
        setJointApplicant(jointArray);
        resetData();
      } else {
        const newData = data;
        newData.addressType = data.localBodyType ? data.localBodyType : data.addressType;
        setJointApplicant([newData]);
        jointArray = [newData];
      }

      if ((!joinStatus && !editState) || (!joinStatus && editState)) {
        const applicantDetails = [];

        // eslint-disable-next-line array-callback-return
        jointArray.map((item) => {
          applicantDetails.push(formatApplicantDetails(item, editId));
        });

        if (data.localbodyType !== 3) {
          const saveData = {
            inwardId: params.id,
            applicantDetails
          };
          setActionTriggered({ loading: true, id: 'saveEfileApplicant' });
          saveEFileApplicantDetails(saveData);
        }
      }
    }
  };

  useEffect(() => {
    fetchCountry();
    setActionTriggered({ loading: false, id: 'counter-applicant-country' });
    fetchStates();
    fetchDistricts(DEFAULT_STATE.id);
  }, []);

  // useEffect(() => {
  //   console.log('userInfo', userInfo);
  //   if (_.keys(userInfo) > 0) {
  //     fetchWard(userInfo.id);
  //   }
  // }, [JSON.stringify(userInfo)]);

  useEffect(() => {
    if (isWhatsappSame) {
      setValue('whatsapp', mobileNo);
    }
  }, [isWhatsappSame, mobileNo]);

  useEffect(() => {
    if (mobileNo !== whatsapp) {
      setValue('isWhatsappSame', false);
    }
  }, [whatsapp]);

  useEffect(() => {
    if (formActiveData) {
      if (Object.keys(formActiveData).length > 0) {
        const addressList = formActiveData?.applicantDetailsResponses;
        if (addressList?.length > 0) {
          setJointApplicant(formActiveData?.applicantDetailsResponses);
          if (addressList.length > 1) {
            setJoinStatus(true);
            setValue('jointApplication', true);
          } else {
            setEditState(true);
            const data = addressList[0];
            setEditIndex(0);
            setEditId(data.id);
            setValue('localbodyType', numberNull(Number(data.addressType)));
            setValue('countryId', numberNull(Number(data.countryId)));
            setValue('stateId', numberNull(Number(data.stateId)));
            setValue('districtId', numberNull(Number(data.districtId)));
            setValue('documentType', formateDocumentType(data));
            setValue('documentNo', setDocument(data));
            setValue('firstName', data.firstName);
            setValue('middleName', data.middleName);
            setValue('lastName', data.lastName);
            setValue('localFirstName', data.localFirstName);
            setValue('localMiddleName', data.localMiddleName);
            setValue('localLastName', data.localLastName);
            setValue('houseName', data.houseName);
            setValue('localHouseName', data.localHouseName);
            setValue('wardName', data.wardName);
            setValue('doorNo', data.doorNo ? data.doorNo : null);
            setValue('subNo', data.subNo);
            setValue('postOffice', Number(data.postOffice));
            setValue('pincode', data.pincode);
            setValue('street', data.street);
            setValue('localPlace', data.localPlace);
            setValue('mainPlace', Number(data.countryId) !== DEFAULT_COUNTRY.id ? data.city : data.mainPlace);
            setValue('emailId', data.emailId);
            setValue('mobileNo', data.mobileNo);
            setValue('institutionName', data.institutionName);
            setValue('isWhatsappSame', data.isWhatsappSame);
            setValue('whatsapp', data.whatsappNo);
            setValue('landLine', data.landLine);
            setValue('referenceNo', data.referenceNo);
            setValue('institutionDate', data.institutionDate);
            setValue('officerName', data.officerName);
            setValue('designation', data.designation);
            setValue('postOfficeName', getPostOfficePreview(Number(data.postOffice), data.postOfficeList));
            setValue('localStreet', data.localStreet);
            setValue('localLocalPlace', data.localLocalPlace);
            setValue('localMainPlace', data.localMainPlace);
            if (data?.localBodyDetails?.officeCode) {
              fetchWard(data?.localBodyDetails?.officeCode);
            }
            if (data.localBodyTypeId) {
              fetchLocalBodyByDistrictByTypeApplicant({ districtId: Number(data.districtId), lbTypeId: data.localBodyTypeId });
            }
            if (data?.wardInfo?.officeLbCode) {
              fetchPostOffice({ offLbCode: data?.wardInfo?.officeLbCode });
            }
            setValue('localBodyTypeId', data.localBodyTypeId);
          }
        }
      }
    }
  }, [JSON.stringify(formActiveData)]);

  useEffect(() => {
    if (formActiveData) {
      if (Object.keys(formActiveData).length > 0) {
        const addressList = formActiveData?.applicantDetailsResponses;
        if (addressList?.length === 1) {
          const data = addressList[0];
          setValue('localBodyNameId', data.localBodyNameId);
        }
      }
    }
    if (localBodyTypeByDBylbCode) {
      setActionTriggered({ loading: false, id: 'counter-applicant-localbody' });
    }
  }, [JSON.stringify(localBodyTypeByDBylbCode), JSON.stringify(formActiveData)]);

  useEffect(() => {
    if (jointApplicantData) {
      setJointApplications(jointApplicantData);
    } else {
      setJointApplications([]);
    }
  }, [jointApplicantData]);

  const saveApplicant = () => {
    const applicantDetails = [];
    jointApplications.map((item) => applicantDetails.push(formatApplicantDetails(item, editId)));
    const saveData = {
      inwardId: params.id,
      applicantDetails
    };
    setActionTriggered({ loading: true, id: 'saveEfileApplicant' });
    saveEFileApplicantDetails(saveData);
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmitForm)} id="applicant-form">
        <div id="applicant_details" />
        <FormWrapper>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <Button
              variant="link"
              style={{ textDecoration: 'none' }}
              leftIcon={appliedForStatus ? <CheckedBox /> : <UnCheckedBox />}
              onClick={handleAppliedForOthers}
              isDisabled={joinStatus && jointApplications.length > 0}
            >
              {t('appliedForOthers')}
            </Button>
          </div>

          <div className="lg:col-span-8 md:col-span-6 col-span-12">
            <Button
              variant="link"
              style={{ textDecoration: 'none' }}
              leftIcon={joinStatus ? <CheckedBox /> : <UnCheckedBox />}
              onClick={handleJointCheck}
            >
              {t('jointApplication')}
            </Button>
          </div>

          {Number(countrySelected) === DEFAULT_COUNTRY.id ? (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="mobileNo"
                type="text"
                label={t('concatLabel', { label: t('mobile'), type: t('number') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('mobileNo', data)}
                maxLength={10}
                disabled={!appliedForStatus}
                rightContent={(
                  <>
                    {/* {appliedForStatus
                      && (
                        <Button
                          isLoading={actionTriggered?.id === 'fetchKsmartId' && actionTriggered?.loading}
                          onClick={() => fetchKsmartId()}
                          variant="primary"
                          size="md"
                          className="w-[80px]"
                        >
                          {t('fetch')}
                        </Button>
                      )} */}

                    <div className="w-[2px] mx-2 bg-gray-100 h-[54px]" />

                    <FormController
                      type="check"
                      control={control}
                      errors={errors}
                      name="isWhatsappSame"
                      label={<WhatsappIcon />}
                    />
                  </>
                )}
                required
              />
            </div>
          ) : (
            <div className="lg:col-span-4 md:col-span-6 col-span-12 flex">
              <div className="border rounded-l-lg mr-[-5px] p-[15px]">
                +{getValues('countryCode')}
              </div>
              <FormController
                name="internationalMobileNo"
                type="text"
                label={t('concatLabel', { label: t('mobile'), type: t('number') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('internationalMobileNo', data)}
                required
              />
            </div>
          )}

          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="whatsapp"
                type="text"
                label={t('concatLabel', { label: t('whatsapp'), type: t('number') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('whatsapp', data)}
                maxLength={10}
              />
            </div>
          )}

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="documentNo"
              type="text"
              label={docNumber(documentType)}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('documentNo', data)}
              required={Number(countrySelected) !== DEFAULT_COUNTRY.id}
              maxlength={docLength(documentType)}
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="countryId"
              type="select"
              label={t('country')}
              control={control}
              errors={errors}
              options={_.get(countryDropdown, 'data', [])}
              handleChange={(data) => handleFieldChange('countryId', data)}
              isLoading={actionTriggered?.id === 'counter-applicant-country' && actionTriggered?.loading}
              optionKey="id"
              required
              isClearable
            />
          </div>
          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="stateId"
                  type="select"
                  label={t('state')}
                  control={control}
                  errors={errors}
                  optionKey="id"
                  options={_.get(stateDropdown, 'data', [])}
                  handleChange={(data) => handleFieldChange('stateId', data)}
                  isLoading={actionTriggered?.id === 'counter-applicant-state' && actionTriggered?.loading}
                  required
                  isClearable
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="districtId"
                  type="select"
                  label={t('district')}
                  control={control}
                  errors={errors}
                  optionKey="id"
                  options={_.get(districtDropdown, 'data', [])}
                  handleChange={(data) => handleFieldChange('districtId', data)}
                  isLoading={actionTriggered?.id === 'counter-applicant-district' && actionTriggered?.loading}
                  required
                  isClearable
                />
              </div>

              {Number(stateSelected) === DEFAULT_STATE.id && (
                <>
                  <div className="lg:col-span-4 md:col-span-6 col-span-12">
                    <FormController
                      name="localBodyTypeId"
                      type="select"
                      label={t('localBodyType')}
                      control={control}
                      errors={errors}
                      options={localBodyTypeApplicant}
                      isLoading={actionTriggered?.id === 'counter-applicant-localBodyType' && actionTriggered?.loading}
                      optionKey="id"
                      handleChange={(data) => {
                        handleFieldChange('localBodyTypeId', data);
                      }}
                      isClearable
                      required
                    />
                  </div>

                  <div className="lg:col-span-4 md:col-span-6 col-span-12">
                    <FormController
                      name="localBodyNameId"
                      type="select"
                      label={t('localBodyName')}
                      control={control}
                      errors={errors}
                      options={_.get(localBodyTypeByDBylbCode, 'data', [])}
                      isLoading={actionTriggered?.id === 'counter-applicant-localbody' && actionTriggered?.loading}
                      optionKey="lbId"
                      handleChange={(data) => {
                        handleFieldChange('localBodyNameId', data);
                      }}
                      isClearable
                      required
                    />
                  </div>
                </>
              )}
            </>
          )}

          <div className="col-span-12">
            <div className="grid grid-cols-3 gap-4 items-center">

              {otpStatus && (
                <div>
                  <FormController
                    name="validateAadhar"
                    type="text"
                    label={t('otp')}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('validateAadhar', data)}
                    required
                    rightContent={
                      Number(documentType) === 1 ? (
                        <Button
                          isLoading={actionTriggered?.id === 'validateAadharOtp' && actionTriggered?.loading}
                          onClick={() => verifyAadharOtpFuc()}
                          variant="primary_outline"
                          size="md"
                        >
                          {t('validate')}
                        </Button>
                      ) : null
                    }
                  />
                </div>
              )}
            </div>
          </div>

          <>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="firstName"
                type="text"
                label={t('concatLabel', { label: t('first'), type: t('name') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('firstName', data)}
                required
                disabled={!appliedForStatus}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="middleName"
                type="text"
                label={t('concatLabel', { label: t('middle'), type: t('name') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('middleName', data)}
                disabled={!appliedForStatus}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="lastName"
                type="text"
                label={t('concatLabel', { label: t('last'), type: t('name') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('lastName', data)}
                disabled={!appliedForStatus}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="localFirstName"
                type="text"
                label={t('concatLabel', { label: t('firstName'), type: t('local') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('localFirstName', data)}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="localMiddleName"
                type="text"
                label={t('concatLabel', { label: t('middleName'), type: t('local') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('localMiddleName', data)}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="localLastName"
                type="text"
                label={t('concatLabel', { label: t('lastName'), type: t('local') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('localLastName', data)}
              />
            </div>
            {Number(countrySelected) === DEFAULT_COUNTRY.id && (
              <>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="houseName"
                    type="text"
                    label={t('concatLabel', { label: t('house'), type: t('name') })}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('houseName', data)}
                    required={Number(countrySelected) === DEFAULT_COUNTRY.id}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="localHouseName"
                    type="text"
                    label={t('concatLabel', { label: t('houseName'), type: t('local') })}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('localHouseName', data)}
                  />
                </div>
              </>
            )}
          </>

          {watch('stateId') === DEFAULT_STATE.id

            && (
              <>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="wardName"
                    type="select"
                    label={t('concatLabel', { label: t('ward'), type: t('name') })}
                    control={control}
                    errors={errors}
                    optionKey="wardId"
                    options={wardFormated}
                    handleChange={(data) => handleFieldChange('wardName', data)}
                    isLoading={actionTriggered?.id === 'counter-applicant-ward' && actionTriggered?.loading}
                    required
                    isClearable
                  />
                </div>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="doorNo"
                    type="text"
                    label={t('concatLabel', { label: t('door'), type: t('number') })}
                    control={control}
                    errors={errors}
                    maxLength={5}
                    handleChange={(data) => handleFieldChange('doorNo', data)}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="subNo"
                    type="text"
                    maxLength={10}
                    label={t('concatLabel', { label: t('sub'), type: t('number') })}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('subNo', data)}
                  />
                </div>
              </>
            )}

          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <>
              {watch('stateId') === DEFAULT_STATE.id
                && (
                  <div className="lg:col-span-4 md:col-span-6 col-span-12">
                    <FormController
                      name="postOffice"
                      type="select"
                      label={t('postOffice')}
                      control={control}
                      errors={errors}
                      optionKey="id"
                      options={_.get(postOfficeDropdown, 'data', [])}
                      handleChange={(data) => handleFieldChange('postOffice', data)}
                      isLoading={actionTriggered?.id === 'counter-applicant-postoffice' && actionTriggered?.loading}
                      required
                      isClearable
                    />
                  </div>
                )}
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="pincode"
                  type="text"
                  label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pinCode' : 'postZipCode')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('pincode', data)}
                  required={Number(countrySelected) === DEFAULT_COUNTRY.id && watch('stateId') === DEFAULT_STATE.id}
                />
              </div>
            </>
          )}

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="street"
              type="text"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'street' : 'streetNoName')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('street', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localStreet"
              type="text"
              label={
                Number(countrySelected) === DEFAULT_COUNTRY.id
                  ? t('concatLabel', { label: t('street'), type: t('local') })
                  : t('concatLabel', { label: t('streetNoName'), type: t('local') })
              }
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localStreet', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localPlace"
              type="text"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'localPlace' : 'locality')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localPlace', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localLocalPlace"
              type="text"
              label={
                Number(countrySelected) === DEFAULT_COUNTRY.id
                  ? t('concatLabel', { label: t('localPlace'), type: t('local') })
                  : t('concatLabel', { label: t('locality'), type: t('local') })
              }
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localLocalPlace', data)}
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="mainPlace"
              type="text"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'mainPlace' : 'cityTown')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('mainPlace', data)}
              required
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localMainPlace"
              type="text"
              label={
                Number(countrySelected) === DEFAULT_COUNTRY.id
                  ? t('concatLabel', { label: t('mainPlace'), type: t('local') })
                  : t('concatLabel', { label: t('cityTown'), type: t('local') })
              }
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localMainPlace', data)}
            />
          </div>
          {Number(countrySelected) !== DEFAULT_COUNTRY.id && (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="pincode"
                type="text"
                label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pincode' : 'postZipCode')}
                control={control}
                errors={errors}
                disabled={Number(countrySelected) === DEFAULT_COUNTRY.id}
                handleChange={(data) => handleFieldChange('pincode', data)}
                required={Number(countrySelected) === DEFAULT_COUNTRY.id}
              />
            </div>
          )}

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="emailId"
              type="text"
              label={t('emailId')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('emailId', data)}
            />
          </div>

          <div className="col-span-12">
            {jointApplications.length > 0 && jointApplication && (
              <CommonTable tableData={jointApplications} columns={columns} />
            )}
          </div>

          <div className="col-span-12 text-right">
            {jointApplication ? (
              <Button type="submit" variant="secondary_outline" className="shadow-md mr-4" form="applicant-form">
                {editState ? t('updateApplicant') : t('addApplicant')}
              </Button>
            ) : (
              <Button
                variant="secondary_outline"
                className="shadow-md"
                type="submit"
                isLoading={actionTriggered?.id === 'saveEfileApplicant' && actionTriggered?.loading}
              >
                {t('proceed')}
              </Button>
            )}
            {jointApplications.length > 1 && jointApplication && (
              <Button
                variant="secondary_outline"
                className="shadow-md"
                onClick={saveApplicant}
                isLoading={actionTriggered?.id === 'saveEfileApplicant' && actionTriggered?.loading}
              >
                {t('proceed')}
              </Button>
            )}
          </div>
        </FormWrapper>
      </form>
      <Registration />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  countryDropdown: getCountry,
  stateDropdown: getState,
  districtDropdown: getDistricts,
  wardDropdown: getWardYear,
  postOfficeDropdown: getPostOffice,
  formComponentData: getSidebarData,
  jointApplicantData: getJointApplicant,
  userInfo: getUserInfo,
  localBodyType: getLocalBodyType,
  localBodyTypeByDBylbCode: getLocalBodyTypeByDBylbCodeApplicant,
  userLocalBody: getUserLocalBody,
  generateAadharOtpRes: getGenerateAadharOtp,
  validateAadharOtpRes: getVerifyAadharOtp,
  otpStatus: getOtpStatus,
  smartDetails: getSmartDetails,
  actionTriggered: getActionTriggered,
  userData: getUserData
});

const mapDispatchToProps = (dispatch) => ({
  fetchCountry: () => dispatch(commonActions.fetchCountry()),
  fetchStates: () => dispatch(commonActions.fetchState()),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchWard: (data) => dispatch(commonActions.fetchWardYear(data)),
  fetchPostOffice: (data) => dispatch(commonActions.fetchPostOffice(data)),
  setActiveAccordian: (data) => dispatch(sliceActions.setActiveAccordian(data)),
  setApplicationSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  saveEFileApplicantDetails: (data) => dispatch(actions.saveEFileApplicantDetails(data)),
  setJointApplicant: (data) => dispatch(sliceActions.setJointApplicant(data)),
  deleteApplicant: (data) => dispatch(actions.deleteApplicant(data)),
  fetchLocalBodyByDistrictByTypeApplicant: (data) => dispatch(commonActions.fetchLocalBodyByDistrictByTypeApplicant(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  verifyAadharOtp: (data) => dispatch(commonActions.verifyAadharOtp(data)),
  fetchSmartProfile: (data) => dispatch(commonActions.fetchSmartProfile(data)),
  setRegisterOpen: (data) => dispatch(commonSliceActions.setRegisterOpen(data)),
  fetchPostOfficeByPin: (data) => dispatch(commonActions.fetchPostOfficeByPin(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ApplicantDetails);
