import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import { actions as commonSliceActions } from 'pages/common/slice';
import { Button, FormController, FormWrapper } from 'common/components';
import { t } from 'i18next';
import { getActionTriggered, getOtpStatus } from 'pages/common/selectors';
import { DoneIcon, ErrorIcon } from 'assets/Verification';
import { primary, secondary } from 'utils/color';
import { otpVerification } from '../validate';
import { getEFilePreview, getOtpSendResponse } from '../selectors';
import * as actions from '../actions';

const OtpVerfication = (props) => {
  const [sendButtonDisable, setSendButtonDisable] = useState(true);
  const [countdownTime, setCountdownTime] = useState(60);
  const {
    otpStatus,
    actionTriggered,
    eFilePreview,
    sendOtp,
    verifyOtp,
    setActionTriggered,
    otpSendResponse
  } = props;

  useEffect(() => {
    let countdownInterval;

    if (!sendButtonDisable) {
      countdownInterval = setInterval(() => {
        setCountdownTime((prevTime) => (prevTime > 0 ? prevTime - 1 : 0));
      }, 999);
    }

    return () => {
      setCountdownTime(60);
      clearInterval(countdownInterval);
    };
  }, [!sendButtonDisable]);

  useEffect(() => {
    if (countdownTime === 0) {
      setSendButtonDisable(true);
    }
  }, [countdownTime]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues
  } = useForm({
    mode: 'all',
    defaultValues: {
      phone: null,
      otp: null
    },
    resolver: yupResolver(otpVerification)
  });

  useEffect(() => {
    if (eFilePreview) {
      if (Object.keys(eFilePreview).length > 0) {
        const addressList = eFilePreview?.applicantDetailsResponses?.length > 0 ? eFilePreview?.applicantDetailsResponses : eFilePreview?.institutionDetailsResponses;
        if (addressList?.length > 0) {
          const data = addressList[0];
          setValue('phone', data?.mobileNo);
        }
      }
    }
  }, [JSON.stringify(eFilePreview)]);

  const onSubmitForm = () => {

  };

  const handleVerifyOtp = () => {
    setActionTriggered({ loading: true, id: 'otpverify' });
    const verifyData = {
      inwardId: eFilePreview?.inwardId,
      params: {
        otp: getValues('otp'),
        id: otpSendResponse?.otp?.UUID
      }
    };
    verifyOtp(verifyData);
  };

  const handleSendOtp = () => {
    setSendButtonDisable(false);
    setActionTriggered({ loading: true, id: 'otpsend' });
    sendOtp({ inwardId: eFilePreview?.inwardId, mobileNo: getValues('phone') });
  };

  return (
    <div>
      <form
        id="local-body-form"
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div id="local-body_details" />
        <FormWrapper>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="phone"
              type="number"
              label={t('phoneNumber')}
              control={control}
              errors={errors}
              readOnly
              rightContent={(
                <Button
                  isLoading={actionTriggered?.id === 'otpsend' && actionTriggered?.loading}
                  onClick={() => handleSendOtp()}
                  variant="primary_outline"
                  size="md"
                  isDisabled={eFilePreview?.otpValidationStatus}
                >
                  {t('send')}
                </Button>
              )}
            />
            {!sendButtonDisable && countdownTime > 0 && otpStatus ? (
              <div className="text-[13px] font-semibold text-[#747474] w-full block text-right pt-2">
                Resend OTP in {countdownTime}s
              </div>
            ) : null}
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            {otpStatus && (
              <FormController
                name="otp"
                type="number"
                label={t('otp')}
                control={control}
                errors={errors}
                required
                rightContent={(
                  <Button
                    isLoading={actionTriggered?.id === 'otpverify' && actionTriggered?.loading}
                    onClick={() => handleVerifyOtp()}
                    variant="primary_outline"
                    size="md"
                  >
                    {t('verify')}
                  </Button>
                )}
              />
            )}
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            {/* <DoneIcon color={primary} width="12" height="12" /> */}
            {eFilePreview?.otpValidationStatus ? (
              <Button style={{ display: 'flex', paddingTop: '10px', marginLeft: 'auto' }} variant="unstyled" leftIcon={<DoneIcon color={primary} width="5" height="5" />}>
                {t('verified')}
              </Button>
            ) : (
              <Button style={{ display: 'flex', paddingTop: '10px', marginLeft: 'auto' }} variant="unstyled" leftIcon={<ErrorIcon color={secondary} width="5" height="5" />}>
                {t('notVerified')}
              </Button>
            )}
          </div>
        </FormWrapper>

      </form>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  otpStatus: getOtpStatus,
  actionTriggered: getActionTriggered,
  eFilePreview: getEFilePreview,
  otpSendResponse: getOtpSendResponse
});

const mapDispatchToProps = (dispatch) => ({
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  sendOtp: (data) => dispatch(actions.sendOtp(data)),
  verifyOtp: (data) => dispatch(actions.verifyOtp(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(OtpVerfication);
