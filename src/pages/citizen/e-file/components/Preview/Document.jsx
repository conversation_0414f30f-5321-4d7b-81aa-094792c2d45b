import { <PERSON><PERSON>, CardWithHeader } from '@ksmartikm/ui-components';
import { FormWrapper, t } from 'common/components';
import { useState } from 'react';
import NoNotesIcon from 'assets/NoNotesIcon';
import { dark } from 'utils/color';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getCompletedSteps } from 'pages/common/selectors';
import { STORAGE_KEYS } from 'common/constants';
import {
  checkDocumentType, formatSupportingDocs, formateMandatoryDocs
} from './helper';
import { APPLICATION_STAGE, E_FILE_KEYS_INDEX, documenturl } from '../../constants';

const DocumentPreview = (props) => {
  const {
    userInfo,
    data,
    documentTypeDropdown,
    setSidebarStatus,
    completedSteps,
    applicationStatus
  } = props;
  const [docPreview, setDocPreview] = useState(null);
  const [loading, setLoading] = useState(true);

  function getDocument(url, token, body) {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  }

  const handlePreview = (val, type, typeId) => {
    const sendData = {
      inwardId: data.inwardId,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: val.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };

    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };

  const navigateToDeclaration = () => {
    const nextStep = E_FILE_KEYS_INDEX.Declaration;
    setSidebarStatus({ activeStep: nextStep, completedSteps });
  };

  const document = (
    data?.supportingDocs?.length !== 0 || data?.documentsTypeUpload?.length !== 0 ? (
      <>
        {data?.documentsTypeUpload
          && (
            <div className="col-span-12">
              <CardWithHeader
                heading={t('mandantoryDocuments')}
              />

              {
                data?.documentsTypeUpload?.map((item, index) => {
                  return (
                    <>
                      <p className="pt-3 pb-1" style={{ color: dark }}>{index + 1}. {checkDocumentType(item?.documentType, documentTypeDropdown)}</p>
                      <CardWithHeader
                        key={item}
                        columns={4}
                        data={formateMandatoryDocs(item, handlePreview, docPreview, loading, documenturl, setLoading)}
                      />
                    </>
                  );
                })
              }
            </div>
          )}
        {
          data?.supportingDocs?.map((item) => {
            return (
              <div className="col-span-12" key={item}>
                <CardWithHeader
                  data={formatSupportingDocs(item, handlePreview, docPreview, loading, documenturl, setLoading)}
                  heading={t('supportingDocuments')}
                />
              </div>
            );
          })
        }
        {(applicationStatus === APPLICATION_STAGE.PARTIAL || applicationStatus === APPLICATION_STAGE.RETURN_TO_CITIZEN)
          && (
            <div className="col-span-12 text-right">
              <Button
                variant="secondary_outline"
                className="shadow-md"
                onClick={navigateToDeclaration}
              >
                {t('proceed')}
              </Button>
            </div>
          )}

      </>
    ) : (
      <div className="col-span-12">
        <div className="p-10 text-center">
          <NoNotesIcon width="100px" height="100px" className="mx-auto" />
          <h4>{t('previewNotAvailable')}</h4>
        </div>
      </div>
    )
  );

  return (
    <FormWrapper>
      {data?.supportingDocs || data?.documentsTypeUpload
        ? document : (
          <div className="col-span-12">
            <div className="p-10 text-center">
              <NoNotesIcon width="100px" height="100px" className="mx-auto" />
              <h4>{t('previewNotAvailable')}</h4>
            </div>
          </div>
        )}
    </FormWrapper>
  );
};

const mapStateToProps = createStructuredSelector({
  completedSteps: getCompletedSteps
});

const mapDispatchToProps = (dispatch) => ({
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DocumentPreview);
