import { t } from 'common/components';
import PreviewThumbnail from 'common/components/DocumentPreview/previewThumbnail';
import _ from 'lodash';

export const getPostOfficePreview = (postOffice, postOfficeList) => {
  if (postOffice && postOfficeList) {
    const name = postOfficeList?.find((item) => item.id === Number(postOffice));
    if (name) {
      if (Object.keys(name).length > 0) {
        return name?.name;
      } return '';
    } return '';
  } return '';
};

export const formatPreviewLocalBody = (data) => {
  const dataSave = [{
    title: t('district'),
    value: data?.districtInfo?.name
  }, {
    title: t('localBodyType'),
    value: data?.localBodyInfo?.name
  }, {
    title: t('localBodyName'),
    value: data?.localBodyDetails?.name
  }];
  return dataSave;
};

export const formatPreviewApplicant = (data) => {
  const dataSave = [{
    title: t('country'),
    value: data?.countryName
  },
  {
    title: t('state'),
    value: data?.stateName
  },
  {
    title: t('district'),
    value: data?.districtName
  },
  {
    title: t('aadharNumber'),
    value: data?.aadharNo
  },
  {
    title: t('firstName'),
    value: data?.firstName
  },
  {
    title: t('middleName'),
    value: data?.middleName
  },
  {
    title: t('lastName'),
    value: data?.lastName
  },
  {
    title: t('houseName'),
    value: data?.houseName
  },
  {
    title: t('wardName'),
    value: _.has(data, 'wardInfo') ? `${data?.wardInfo?.wardNo} - ${data?.wardInfo?.name}` : ''
  },
  {
    title: t('doorNo'),
    value: data?.doorNo
  },
  {
    title: t('subNo'),
    value: data?.subNo
  },
  {
    title: t('postOffice'),
    value: getPostOfficePreview(data?.postOffice, data.postOfficeList)
  },
  {
    title: t('pinCode'),
    value: data?.pincode
  },
  {
    title: t('street'),
    value: data?.street
  },
  {
    title: t('localPlace'),
    value: data?.localPlace
  },
  {
    title: t('mainPlace'),
    value: data?.mainPlace
  },
  {
    title: t('localFirstName'),
    value: data?.localFirstName
  },
  {
    title: t('localMiddleName'),
    value: data?.localMiddleName
  },
  {
    title: t('localLastName'),
    value: data?.localLastName
  },
  {
    title: t('localHouseName'),
    value: data?.localHouseName
  },
  {
    title: t('localStreetName'),
    value: data?.localStreet
  },
  {
    title: t('localLocalName'),
    value: data?.localLocalPlace
  },
  {
    title: t('localMainName'),
    value: data?.localMainPlace
  },
  {
    title: t('emailId'),
    value: data?.emailId
  },
  {
    title: t('mobileNumber'),
    value: data?.mobileNo
  },
  {
    title: t('whatsAppNumber'),
    value: data?.whatsappNo
  }
  ];
  return dataSave;
};

export const formatPreviewOrgApplicant = (data) => {
  const dataSave = [{
    title: t('country'),
    value: data?.countryName
  },
  {
    title: t('state'),
    value: data?.stateName
  },
  {
    title: t('district'),
    value: data?.districtName
  },
  {
    title: t('institutionName'),
    value: data?.institutionName
  },
  {
    title: t('localInstitutionName'),
    value: data?.localInstitutionName
  },
  {
    title: t('officerName'),
    value: data?.officerName
  },
  {
    title: t('designation'),
    value: data?.designation
  },
  {
    title: t('localDesignation'),
    value: data?.localDesignation
  },
  {
    title: t('referenceDate'),
    value: data?.referenceDate
  },
  {
    title: t('referenceNo'),
    value: data?.referenceNo
  },
  {
    title: t('localBodyType'),
    value: data?.localBodyInfo?.name
  },
  {
    title: t('localBody'),
    value: data?.localBodyDetails?.name
  },
  {
    title: t('postOffice'),
    value: getPostOfficePreview(data?.postOffice, data.postOfficeList)
  },
  {
    title: t('pinCode'),
    value: data?.pincode
  },
  {
    title: t('street'),
    value: data?.street
  },
  {
    title: t('localPlace'),
    value: data?.localPlace
  },
  {
    title: t('mainPlace'),
    value: data?.mainPlace
  },
  {
    title: t('localStreetName'),
    value: data?.localStreet
  },
  {
    title: t('localLocalName'),
    value: data?.localLocalPlace
  },
  {
    title: t('localMainName'),
    value: data?.localMainPlace
  },
  {
    title: t('emailId'),
    value: data?.emailId
  },
  {
    title: t('mobileNumber'),
    value: data?.mobileNo
  },
  {
    title: t('whatsAppNumber'),
    value: data?.whatsappNo
  },
  {
    title: t('landline'),
    value: data?.landlineNo
  }
  ];
  return dataSave;
};

export const formatGeneralInformationDescription = (data) => {
  const dataSave = [{
    title: t('description'),
    value: data?.description
  }];
  return dataSave;
};

export const formatGeneralInformationDetails = (data) => {
  const dataSave = [{
    title: t('referenceNo'),
    value: data?.referenceNo
  },
  {
    title: t('ward'),
    value: data?.wardInfo?.name
  },
  {
    title: t('doorNo'),
    value: data?.doorNo
  },
  {
    title: t('subNo'),
    value: data?.subNo
  },
  {
    title: t('ownership'),
    value: data?.ownershipInfo?.name
  },
  {
    title: t('functionalGroup'),
    value: data?.functionalGroupInfo
  },
  {
    title: t('functions'),
    value: data?.functionInfo
  },
  {
    title: t('ownership'),
    value: data?.ownershipInfo?.name
  },
  {
    title: t('ownerName'),
    value: data?.ownerName
  },
  {
    title: t('ksebPostNo'),
    value: data?.ksebPostNo
  },
  {
    title: t('roadName'),
    value: data?.roadName
  },
  {
    title: t('landmark'),
    value: data?.landMark
  },
  {
    title: t('taluk'),
    value: data?.talukName
  },
  {
    title: t('village'),
    value: data?.villageName
  },
  {
    title: t('surveyNumber'),
    value: data?.surveyNo
  },
  {
    title: t('receiptNo'),
    value: data?.receiptNo
  },
  {
    title: t('localBodyPropertyType'),
    value: data?.localBodyPropertyTypeInfo
  },
  {
    title: t('eventStartDate'),
    value: data?.dateOfEvent
  },
  {
    title: t('eventEndDate'),
    value: data?.eventEndDate
  },
  {
    title: t('designation'),
    value: data?.designationInfo
  },
  {
    title: t('billType'),
    value: data?.billTypeInfo
  },
  {
    title: t('buildingArea'),
    value: data?.buildingAreaInfo
  },
  {
    title: t('buildingUsage'),
    value: data?.buildingUsageInfo
  },
  {
    title: t('establishmentType'),
    value: data?.establishmentInfo
  },
  {
    title: t('professionalTaxType'),
    value: data?.professionTaxTypeInfo
  },
  {
    title: t('typeOfAudit'),
    value: data?.typeOfAuditInfo
  },
  {
    title: t('amountFromClaim'),
    value: data?.amountFromClaimInfo
  },
  {
    title: t('estimateAmount'),
    value: data?.estimateAmountInfo
  },
  {
    title: t('occupancy'),
    value: data?.occupancyInfo
  },
  {
    title: t('fund'),
    value: data?.fundTypeInfo
  },
  {
    title: t('ksebPostNo'),
    value: data?.ksebPostNo
  },
  {
    title: t('lbBuilding'),
    value: data?.lbBuildingInfo
  },
  {
    title: t('meetingType'),
    value: data?.meetingTypeInfo
  },
  {
    title: t('mission'),
    value: data?.missionInfo
  },
  {
    title: t('officeType'),
    value: data?.officeTypeInfo
  },
  {
    title: t('deductionHead'),
    value: data?.deductionHeadInfo
  },
  {
    title: t('wasteManagementId'),
    value: data?.wasteManagementInfo
  }
  ];
  return dataSave;
};

export const formatGeneralDetails = (data) => {
  const dataSave = [{
    title: t('category'),
    value: data?.categoryInfo
  },
  {
    title: t('financialStatus'),
    value: data?.financialStatusInfo
  },
  {
    title: t('gender'),
    value: data?.genderType
  },
  {
    title: t('dateOfBirth'),
    value: data?.dateOfBirth
  },
  {
    title: t('income'),
    value: data?.income
  },
  {
    title: t('accountType'),
    value: data?.accountTypeInfo
  },
  {
    title: t('treasuryType'),
    value: data?.treasuryTypeInfo
  },
  {
    title: t('treasuryAccountNo'),
    value: data?.treasuryAccountNo
  },
  {
    title: t('headOfAccount'),
    value: data?.headOfAccount
  },
  {
    title: t('bank'),
    value: data?.bankName
  },
  {
    title: t('branch'),
    value: data?.bankBranchName
  },
  {
    title: t('ifsc'),
    value: data?.ifsc
  },
  {
    title: t('accountNumber'),
    value: data?.accountNo
  },
  {
    title: t('educationalQualification'),
    value: data?.educationalQualificationInfo
  }
  ];
  return dataSave;
};

export const formatSubHeadingApplicant = (item) => {
  if (item?.firstName) {
    return `${item.firstName ? item.firstName : ''} ${item.middleName ? item.middleName : ''} ${item.lastName ? item.lastName : ''} `;
  } if (item?.institutionName) {
    return item?.institutionName;
  } return '';
};

export const formatSupportingDocs = (item, handlePreview, docPreview, loading, documenturl, setLoading) => {
  const dataSave = [{
    title: t('documentName'),
    value: item?.documentName
  },
  {
    title: t('issueDate'),
    value: item?.issueDate
  },
  {
    title: t('validUpto'),
    value: item?.validUpto
  },
  {
    title: t('document'),
    value: <PreviewThumbnail
      key={item.id}
      handlePreview={(data) => handlePreview(data, 2, 0)}
      item={item}
      preview={docPreview}
      fileType={item?.documentContentType}
      documenturl={documenturl}
      setLoading={setLoading}
      loading={loading}
      from="table"
    />
  }
  ];
  return dataSave;
};

export const checkDocumentType = (data, dropItems) => {
  const find = dropItems?.filter((item) => item.id === Number(data));
  if (find?.length > 0) {
    return find[0]?.name;
  } return '';
};

export const formateMandatoryDocs = (item, handlePreview, docPreview, loading, documenturl, setLoading) => {
  const dataSave = [
    {
      title: t('documentNumber'),
      value: item?.documentNo
    },
    {
      title: t('issueDate'),
      value: item?.issueDate
    },
    {
      title: t('validUpto'),
      value: item?.validUpto
    },
    {
      title: t('document'),
      value: <PreviewThumbnail
        key={item.id}
        handlePreview={(data) => handlePreview(data, 1, item.documentType)}
        item={item}
        preview={docPreview}
        fileType={item?.documentContentType}
        documenturl={documenturl}
        setLoading={setLoading}
        loading={loading}
        from="table"
      />
    }
  ];
  return dataSave;
};

export const formateMandatoryDocTypes = () => {

};
