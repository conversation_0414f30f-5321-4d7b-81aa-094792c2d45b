import { CardWithHeader } from '@ksmartikm/ui-components';
import { FormWrapper, t } from 'common/components';
import NoNotesIcon from 'assets/NoNotesIcon';
import { formatGeneralDetails, formatGeneralInformationDescription, formatGeneralInformationDetails } from './helper';

const GeneralPreview = (props) => {
  const {
    data
  } = props;
  const list = data?.generalDetailsResponses;
  return (
    <FormWrapper>
      {list ? (
        <>
          <div className="col-span-12">
            <CardWithHeader
              data={formatGeneralInformationDetails(list).filter((item) => item.value !== null && item.value !== '' && item.value !== undefined)}
              heading={t('generalInformation')}
            />

          </div>
          <div className="col-span-12">
            <CardWithHeader
              data={formatGeneralInformationDescription(list)}
              columns={1}
            />
          </div>
          {
          list?.details?.map((item) => {
            return (
              <div className="col-span-12" key={item}>
                <CardWithHeader
                  data={formatGeneralDetails(item)}
                  // subHeading={formatSubHeading(activeApplicationData?.applicantDetailsAddress, activeApplicationData?.generalDetailsResponses?.details)}
                  heading={t('generalDetails')}
                />
              </div>
            );
          })
        }

        </>
      ) : (
        <div className="col-span-12">
          <div className="p-10 text-center">
            <NoNotesIcon width="100px" height="100px" className="mx-auto" />
            <h4>{t('previewNotAvailable')}</h4>
          </div>
        </div>
      )}
    </FormWrapper>
  );
};

export default GeneralPreview;
