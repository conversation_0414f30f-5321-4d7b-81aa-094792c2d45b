import { CardWithHeader } from '@ksmartikm/ui-components';
import { FormWrapper, t } from 'common/components';
import NoNotesIcon from 'assets/NoNotesIcon';
import { formatPreviewApplicant, formatSubHeadingApplicant } from './helper';

const ApplicantPreview = (props) => {
  const {
    data
  } = props;
  const list = data?.applicantDetailsResponses;
  return (
    <FormWrapper>
      {list?.length !== 0 && list ? (
        list?.map((item) => {
          return (
            <div className="col-span-12" key={item.id}>
              <CardWithHeader
                data={formatPreviewApplicant(item)}
                subHeading={formatSubHeadingApplicant(item)}
                heading={t('applicantDetails')}
              />
            </div>
          );
        })
      ) : (
        <div className="col-span-12">
          <div className="p-10 text-center">
            <NoNotesIcon width="100px" height="100px" className="mx-auto" />
            <h4>{t('previewNotAvailable')}</h4>
          </div>
        </div>
      )}
    </FormWrapper>
  );
};

export default ApplicantPreview;
