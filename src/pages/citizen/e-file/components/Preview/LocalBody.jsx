import { CardWithHeader } from '@ksmartikm/ui-components';
import { FormWrapper } from 'common/components';
import NoNotesIcon from 'assets/NoNotesIcon';
import { t } from 'i18next';
import { formatPreviewLocalBody } from './helper';

const LocalBodyPreview = (props) => {
  const {
    data
  } = props;

  const list = formatPreviewLocalBody(data);
  return (
    <FormWrapper>
      {data
        ? (
          <div className="col-span-12">
            <CardWithHeader
              data={list}
            />
          </div>
        )
        : (
          <div className="col-span-12">
            <div className="p-10 text-center">
              <NoNotesIcon width="100px" height="100px" className="mx-auto" />
              <h4>{t('previewNotAvailable')}</h4>
            </div>
          </div>
        )}
    </FormWrapper>
  );
};

export default LocalBodyPreview;
