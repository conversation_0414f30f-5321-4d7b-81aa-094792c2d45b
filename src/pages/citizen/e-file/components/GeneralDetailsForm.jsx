import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, t, <PERSON><PERSON>, FormWrapper
} from 'common/components';
import _ from 'lodash';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import * as commonActions from 'pages/common/actions';
import {
  getBanks,
  getBranches,
  getCategory,
  getEducation,
  getFinancialStatus,
  getGender
} from 'pages/common/selectors';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { reFormattedDate } from 'utils/date';
import { GeneralDetailSchema } from '../validate';
import { getGeneralFieldValidation } from '../selectors';

const GeneralDetailsForm = (props) => {
  const {
    applicantData,
    addGeneralDetails,
    banksOptions,
    fetchBanks,
    branchesOptions,
    fetchBranchByBank,
    genderOptions,
    fetchGender,
    educationOptions,
    fetchEducation,
    generalFieldValidation,
    fetchCategory,
    category,
    fetchFinancialStatus,
    financialStatus
  } = props;
  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
    reset
  } = useForm({
    mode: 'all',
    defaultValues: {
      genderId: null,
      category: null,
      financialStatusId: null,
      accountNo: '',
      bank: null,
      bankNameId: null,
      bankBranchId: null,
      ifsc: null,
      dateOfBirth: null,
      income: null,
      educationalQualificationId: null,
      fieldsValidation: generalFieldValidation || {}
    },
    resolver: yupResolver(GeneralDetailSchema)
  });

  const params = useParams();

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  const handleFieldChange = (field, data) => {
    if (data) {
      switch (field) {
        case 'fieldsValidation':
          setValue('fieldsValidation', data);
          break;
        case 'genderId':
          setValue('genderId', data.id);
          break;
        case 'category':
          setValue('category', Number(data.id));
          break;
        case 'financialStatusId':
          setValue('financialStatusId', Number(data.id));
          break;
        case 'ownership':
          setValue('ownership', Number(data));
          break;
        case 'ward':
          setValue('ward', data.wardId);
          break;
        case 'bankNameId':
          fetchBranchByBank({ bankId: Number(data.id) });
          setValue('bankNameId', data.id);
          break;
        case 'bankBranchId':
          setValue('bankBranchId', data.id);
          setValue('ifsc', data.ifscCode);
          break;
        case 'educationalQualificationId':
          setValue('educationalQualificationId', data.id);
          break;
        default:
          break;
      }
    } else {
      switch (field) {
        case 'bankNameId':
          setValue('bankBranchId', null);
          setValue('ifsc', null);
          break;
        case 'bankBranchId':
          setValue('ifsc', null);
          break;
        default:
          break;
      }
    }
  };

  const resetData = () => {
    reset({
      genderId: null,
      category: null,
      financialStatusId: null,
      ownership: null,
      accountNo: '',
      bank: null,
      bankNameId: null,
      bankBranchId: null,
      ifsc: null,
      dateOfBirth: null,
      income: null,
      ward: null,
      doorNo: null,
      subNo: null,
      educationalQualificationId: null,
      description: null
    });
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  useEffect(() => {
    if (applicantData) {
      if (Object.keys(applicantData).length > 0) {
        setValue('genderId', Number(applicantData.genderId));
        setValue('category', applicantData.categoryId);
        setValue('financialStatusId', applicantData.financialStatusId);
        setValue('ownership', applicantData.ownership);
        setValue('accountNo', applicantData.accountNo);
        setValue('bankNameId', applicantData.bankNameId);
        if (Number(applicantData.bankNameId)) {
          fetchBranchByBank({ bankId: Number(applicantData.bankNameId) });
        }
        setValue('bankBranchId', Number(applicantData.bankBranch));
        setValue('ifsc', applicantData.ifsc);
        setValue('income', applicantData.income);
        setValue('ward', applicantData.ward);
        setValue('doorNo', applicantData.doorNo);
        setValue('subNo', applicantData.subNo);
        setValue('educationalQualificationId', applicantData.educationalQualificationId);
        setValue('description', applicantData.description);

        setValue('dateOfBirth', applicantData?.dateOfBirth ? reFormattedDate(applicantData?.dateOfBirth) : '');
      } else {
        resetData();
      }
    } else {
      resetData();
    }
  }, [JSON.stringify(applicantData)]);

  // useEffect(() => {
  //   if (branchesOptions?.data) {
  //     setValue('bankBranchId', applicantData?.bankBranchId);
  //   }
  // }, [JSON.stringify(branchesOptions), JSON.stringify(applicantData)]);

  const onSubmitForm = (data) => {
    addGeneralDetails(data);
  };

  useEffect(() => {
    fetchBanks();
    fetchGender();
    fetchEducation();
    fetchCategory();
    fetchFinancialStatus();
  }, []);

  return (
    <form
      onSubmit={handleSubmit(onSubmitForm)}
    >

      <FormWrapper>
        <div hidden>
          <FormController
            name="fieldsValidation"
            variant="outlined"
            type="select"
            optionKey="id"
            control={control}
            errors={errors}
            options={[]}
            handleChange={(data) => handleFieldChange('fieldsValidation', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="genderId"
            variant="outlined"
            type="select"
            label={t('gender')}
            optionKey="id"
            control={control}
            errors={errors}
            isClearable
            options={_.get(genderOptions, 'data', [])}
            handleChange={(data) => handleFieldChange('genderId', data)}
            required={generalFieldValidation.genderId === 1}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="category"
            variant="outlined"
            type="select"
            label={t('category')}
            optionKey="id"
            control={control}
            errors={errors}
            options={_.get(category, 'data', [])}
            isClearable
            handleChange={(data) => handleFieldChange('category', data)}
            required={generalFieldValidation.category === 1}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="financialStatusId"
            variant="outlined"
            type="select"
            label={t('financialStatus')}
            optionKey="id"
            control={control}
            errors={errors}
            isClearable
            options={_.get(financialStatus, 'data', [])}
            handleChange={(data) => handleFieldChange('financialStatusId', data)}
            required={generalFieldValidation.financialStatusId === 1}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="dateOfBirth"
            variant="outlined"
            type="date"
            label={t('dateOfBirth')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('dateOfBirth', data)}
            maxDate={new Date()}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="income"
            variant="outlined"
            type="text"
            label={t('income')}
            optionKey="id"
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('income', data)}
            required={generalFieldValidation.income === 1}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="bankNameId"
            variant="outlined"
            type="select"
            label={t('bank')}
            optionKey="id"
            control={control}
            errors={errors}
            isClearable
            options={_.get(banksOptions, 'data', [])}
            handleChange={(data) => handleFieldChange('bankNameId', data)}
            required={generalFieldValidation.bankNameId === 1}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="bankBranchId"
            variant="outlined"
            type="select"
            label={t('branch')}
            optionKey="id"
            control={control}
            errors={errors}
            isClearable
            options={_.get(branchesOptions, 'data', [])}
            handleChange={(data) => handleFieldChange('bankBranchId', data)}
            required={generalFieldValidation.branch === 1}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="ifsc"
            variant="outlined"
            type="text"
            label={t('ifsc')}
            control={control}
            errors={errors}
            readOnly
            handleChange={(data) => handleFieldChange('ifsc', data)}
            required={generalFieldValidation.ifsc === 1}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="accountNo"
            variant="outlined"
            type="text"
            label={t('accountNo')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('accountNo', data)}
            required={generalFieldValidation.accountNo === 1}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-12 col-span-12">
          <FormController
            name="educationalQualificationId"
            variant="outlined"
            type="select"
            label={t('educationalQualification')}
            optionKey="id"
            control={control}
            errors={errors}
            isClearable
            options={_.get(educationOptions, 'data', [])}
            handleChange={(data) => handleFieldChange('educationalQualificationId', data)}
            required={generalFieldValidation.educationalQualificationId === 1}
          />
        </div>

        <div className="col-span-12 text-right">
          <Button
            type="submit"
            variant="secondary_outline"
            className="shadow-md"
          >
            {t('next')}
          </Button>
        </div>

      </FormWrapper>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  banksOptions: getBanks,
  branchesOptions: getBranches,
  educationOptions: getEducation,
  genderOptions: getGender,
  generalFieldValidation: getGeneralFieldValidation,
  category: getCategory,
  financialStatus: getFinancialStatus
});

const mapDispatchToProps = (dispatch) => ({
  fetchBanks: () => dispatch(commonActions.fetchBanks()),
  fetchBranchByBank: (data) => dispatch(commonActions.fetchBranchByBank(data)),
  fetchEducation: () => dispatch(commonActions.fetchEducation()),
  fetchGender: () => dispatch(commonActions.fetchGender()),
  fetchCategory: () => dispatch(commonActions.fetchCategory()),
  fetchFinancialStatus: () => dispatch(commonActions.fetchFinancialStatus())

});

export default connect(mapStateToProps, mapDispatchToProps)(GeneralDetailsForm);
