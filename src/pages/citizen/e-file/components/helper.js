import { DEFAULT_COUNTRY, DEFAULT_DISTRICT, DEFAULT_STATE } from 'common/constants';
import { DOCUMENT_TYPE, LOCAL_BODY_TYPE } from 'pages/counter/new/constants';

const localBodyDefaultValues = {
  districtId: null,
  districtCode: null,
  localBodyTypeId: null,
  localBodyNameId: null,
  officeId: null
};

const applicantDefaultValues = {
  countryId: DEFAULT_COUNTRY.id,
  stateId: DEFAULT_STATE.id,
  districtId: DEFAULT_DISTRICT.id,
  countryCode: '',
  localBodyTypeId: null,
  localBodyNameId: null,
  jointApplication: false,
  documentType: 1,
  documentNo: '',
  firstName: '',
  middleName: null,
  lastName: null,
  localFirstName: null,
  localMiddleName: null,
  localLastName: null,
  houseName: '',
  localHouseName: null,
  wardName: null,
  doorNo: null,
  subNo: null,
  postOffice: null,
  pincode: null,
  street: null,
  localPlace: null,
  mainPlace: '',
  localStreet: null,
  localLocalPlace: null,
  localMainPlace: null,
  emailId: null,
  mobileNo: '',
  institutionName: '',
  localInstitutionName: null,
  localOfficeName: null,
  localDesignation: null,
  isWhatsappSame: false,
  whatsapp: null,
  landLine: '',
  referenceNo: null,
  institutionDate: null,
  officerName: '',
  designation: '',
  internationalMobileNo: null
};

const orgApplicantDefaultValues = {
  countryId: DEFAULT_COUNTRY.id,
  stateId: DEFAULT_STATE.id,
  districtId: DEFAULT_DISTRICT.id,
  countryCode: '',
  localBodyTypeId: null,
  localBodyNameId: null,
  postOffice: null,
  pincode: null,
  street: null,
  localPlace: null,
  mainPlace: '',
  localStreet: null,
  localLocalPlace: null,
  localMainPlace: null,
  emailId: null,
  mobileNo: '',
  institutionName: '',
  localInstitutionName: null,
  localOfficeName: null,
  localDesignation: null,
  isWhatsappSame: false,
  whatsapp: null,
  landLine: '',
  referenceNo: null,
  institutionDate: null,
  officerName: '',
  designation: ''
};

const generalDetailsDefaultValues = {
  gender: null,
  category: null,
  financialStatus: null,
  ownership: null,
  accountNo: '',
  bank: null,
  bankName: '',
  branch: null,
  branchName: null,
  ifsc: null,
  dateOfBirth: null,
  income: null,
  ward: null,
  doorNo: null,
  subNo: null,
  educationalQualification: null,
  description: null,
  fieldsValidation: null
};

const formatLocalBodyType = (data) => {
  let locType;
  switch (data) {
    case LOCAL_BODY_TYPE.JOINT_APPLICANT_ADDRESS_INSIDE_LOCAL_BODY:
      locType = 1;
      break;
    case LOCAL_BODY_TYPE.JOINT_APPLICANT_ADDRESS_OUTSIDE_LOCAL_BODY:
      locType = 2;
      break;
    default:
      locType = 3;
      break;
  }
  return locType;
};

const formatApplicantDetails = (data, editId, from) => {
  let docType;

  switch (Number(data.documentType)) {
    case 1:
      docType = DOCUMENT_TYPE.AADHAR;
      break;
    case 2:
      docType = DOCUMENT_TYPE.UUID;
      break;
    default:
      docType = DOCUMENT_TYPE.PASSPORT;
      break;
  }

  const dataSave = {
    ...data,
    id: data.id || editId,
    [docType]: data.documentNo,
    firstName: data.firstName?.replace(/^\s+|\s+$/gm, ''),
    middleName: data.middleName,
    lastName: data.lastName === '' ? null : data.lastName,
    localFirstName: data.localFirstName?.replace(/\s*$/, ''),
    localMiddleName: data.localMiddleName?.replace(/\s*$/, ''),
    localLastName: data.localLastName?.replace(/\s*$/, ''),
    houseName: Number(data.countryId) === DEFAULT_COUNTRY.id ? data.houseName : null,
    localHouseName: data.localHouseName,
    wardName: data.wardName,
    doorNo: data.doorNo === '' ? null : Number(data.doorNo),
    subNo: data.subNo === '' ? null : data.subNo,
    postOffice: Number(data.countryId) !== DEFAULT_COUNTRY.id ? null : data.postOffice,
    pincode: data.pincode === '' ? null : data.pincode,
    postOfficeName: data.postOfficeName,
    street: data.street,
    localPlace: data.localPlace,
    localStreet: data.localStreet,
    localLocalPlace: data.localLocalPlace,
    localMainPlace: data.localMainPlace,
    mainPlace: Number(data.countryId) !== DEFAULT_COUNTRY.id ? null : data.mainPlace,
    city: Number(data.countryId) === DEFAULT_COUNTRY.id ? null : data.mainPlace,
    emailId: data.emailId,
    mobileNo: Number(data.countryId) === DEFAULT_COUNTRY.id ? data.mobileNo : null,
    whatsappNo: data.whatsapp,
    countryId: Number(data.countryId),
    stateId: Number(data.countryId) === DEFAULT_COUNTRY.id ? Number(data.stateId) : null,
    districtId: Number(data.countryId) === DEFAULT_COUNTRY.id ? Number(data.districtId) : null,
    internationalMobileNo: Number(data.countryId) === DEFAULT_COUNTRY.id ? null : `+${data.countryCode}${data.internationalMobileNo}`,
    year: 0,
    beneficiaryFlag: from === 'beneficiary'
  };

  return dataSave;
};

const formatOrgApplicantDetails = (data, editId, from) => {
  const dataSave = {
    ...data,
    id: editId || null,
    countryId: Number(data.countryId),
    institutionName: data.institutionName?.replace(/^\s+|\s+$/gm, ''),
    officerName: data.officerName.replace(/^\s+|\s+$/gm, ''),
    beneficiaryFlag: from === 'beneficiary',
    year: 0
  };
  return dataSave;
};

const formateDocumentType = (data) => {
  if (data.documentType) {
    return Number(data.documentType);
  }
  if (data.aadharNo) {
    return 1;
  } if (data.udid) {
    return 2;
  }
  return 3;
};

const formateDocumentTypeName = (data) => {
  if (data.aadharNo) {
    return DOCUMENT_TYPE.AADHAR;
  } if (data.udid) {
    return DOCUMENT_TYPE.UUID;
  }
  return DOCUMENT_TYPE.PASSPORT;
};

export {
  applicantDefaultValues,
  generalDetailsDefaultValues,
  formatLocalBodyType,
  formatApplicantDetails,
  formateDocumentType,
  formateDocumentTypeName,
  localBodyDefaultValues,
  orgApplicantDefaultValues,
  formatOrgApplicantDetails
};
