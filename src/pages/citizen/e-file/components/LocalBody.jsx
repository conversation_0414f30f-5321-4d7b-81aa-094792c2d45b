import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON>per, FormController, t, Button
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  getSidebarData,
  getDistricts,
  getLocalBodyType,
  getLocalBodyTypeByDBylbCode,
  getActionTriggered,
  getCompletedSteps
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { numberNull } from 'utils/numberNull';
import { DEFAULT_STATE } from 'common/constants';
import * as actions from '../actions';
import { LocalBodyFormSchema } from '../validate';
import { actions as sliceActions } from '../slice';
import {
  getActiveAccordian
} from '../selectors';
import { E_FILE_KEYS_INDEX } from '../constants';
import { localBodyDefaultValues } from './helper';

const Services = (props) => {
  const {
    fetchDistricts,
    setSidebarStatus,
    createEFile,
    districtDropdown,
    localBodyType,
    fetchLocalBodyType,
    fetchLocalBodyByDistrictByType,
    localBodyTypeByDBylbCode,
    formActiveData,
    setActionTriggered,
    actionTriggered,
    completedSteps
  } = props;

  const params = useParams();
  const [searchParams] = useSearchParams();

  const [localBodyTypeSubmittingTo, setLocalBodyTypeSubmittingTo] = useState([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
    reset
  } = useForm({
    mode: 'all',
    defaultValues: localBodyDefaultValues,
    resolver: yupResolver(LocalBodyFormSchema)
  });

  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'saveEfile' });
    const saveData = {
      districtId: numberNull(Number(data.districtId)),
      localBodyTypeId: numberNull(Number(data.localBodyTypeId)),
      localBodyNameId: numberNull(Number(data.localBodyNameId)),
      serviceCode: (params.serviceCode).toUpperCase(),
      officeId: data.officeId,
      kswiftId: searchParams.get('kswiftId') || null
    };
    createEFile(saveData);
  };

  const resetData = () => {
    reset({ ...localBodyDefaultValues }, { keepValues: false });
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  useEffect(() => {
    if (localBodyType?.data?.length > 0) {
      const lbType = localBodyType?.data;
      const submitingTop = lbType.filter((item) => item.code !== 'LB_TYPE_DISTRICT_PANCHAYATH' && item.code !== 'LB_TYPE_BLOCK_PANCHAYATH' && item.code !== 'LB_TYPE_GRAMA_PANCHAYATH');
      setLocalBodyTypeSubmittingTo(submitingTop);
    }
  }, [localBodyType]);

  useEffect(() => {
    fetchDistricts(DEFAULT_STATE.id);
    fetchLocalBodyType();
  }, []);

  useEffect(() => {
    if (formActiveData) {
      setValue('districtId', formActiveData?.districtId);
      fetchLocalBodyByDistrictByType({ districtId: formActiveData?.districtId, lbTypeId: formActiveData?.localBodyTypeId });
      setValue('localBodyTypeId', formActiveData?.localBodyTypeId);
      setValue('localBodyNameId', formActiveData?.localBodyNameId);
    }
  }, [JSON.stringify(formActiveData), JSON.stringify(localBodyTypeByDBylbCode)]);

  const handleFieldChange = (field, data) => {
    if (data) {
      switch (field) {
        case 'districtId':
          setValue('districtId', data.id);
          setValue('localBodyTypeId', null);
          setValue('officeId', null);
          setValue('localBodyNameId', null);
          break;
        case 'localBodyTypeId':
          setValue('localBodyTypeId', data.id);
          fetchLocalBodyByDistrictByType({ districtId: getValues('districtId'), lbTypeId: data.id });
          setValue('officeId', null);
          setValue('localBodyNameId', null);
          break;
        case 'localBodyNameId':
          setValue('officeId', data.officeCode);
          setValue('localBodyNameId', data.lbId);
          break;
        default:
          break;
      }
    } else {
      switch (field) {
        case 'districtId':
          setValue('districtId', null);
          setValue('localBodyTypeId', null);
          setValue('officeId', null);
          setValue('localBodyNameId', null);
          break;
        case 'localBodyTypeId':
          setValue('localBodyTypeId', null);
          setValue('officeId', null);
          setValue('localBodyNameId', null);
          break;
        case 'localBodyNameId':
          setValue('officeId', null);
          setValue('localBodyNameId', null);
          break;
        default:
          break;
      }
    }
  };

  const navigateToApplicant = () => {
    const nextStep = E_FILE_KEYS_INDEX.Applicant;
    setSidebarStatus({ activeStep: nextStep, completedSteps });
  };

  return (
    <form
      id="local-body-form"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <div id="local-body_details" />
      <FormWrapper>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">

          <FormController
            name="districtId"
            type="select"
            label={t('district')}
            control={control}
            errors={errors}
            optionKey="id"
            options={_.get(districtDropdown, 'data', [])}
            handleChange={(data) => {
              handleFieldChange('districtId', data);
            }}
            required
            isClearable
            isDisabled={params?.id}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">

          <FormController
            name="localBodyTypeId"
            type="select"
            label={t('localBodyType')}
            control={control}
            errors={errors}
            options={localBodyTypeSubmittingTo}
            optionKey="id"
            handleChange={(data) => {
              handleFieldChange('localBodyTypeId', data);
            }}
            required
            isClearable
            isDisabled={params?.id}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">

          <FormController
            name="localBodyNameId"
            type="select"
            label={t('localBodyName')}
            control={control}
            errors={errors}
            options={_.get(localBodyTypeByDBylbCode, 'data', [])}
            optionKey="lbId"
            handleChange={(data) => {
              handleFieldChange('localBodyNameId', data);
            }}
            required
            isClearable
            isDisabled={params?.id}
          />
        </div>

        <div className="col-span-12 text-right">
          {params?.id ? (
            <Button
              variant="secondary_outline"
              className="shadow-md"
              onClick={navigateToApplicant}
            >
              {t('proceed')}
            </Button>
          )
            : (
              <Button
                type="submit"
                variant="secondary_outline"
                className="shadow-md"
                form="local-body-form"
                isLoading={actionTriggered?.id === 'saveEfile' && actionTriggered?.loading}
              >
                {t('proceed')}
              </Button>
            )}
        </div>

      </FormWrapper>

    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  activeAccordian: getActiveAccordian,
  formComponentData: getSidebarData,
  districtDropdown: getDistricts,
  localBodyType: getLocalBodyType,
  localBodyTypeByDBylbCode: getLocalBodyTypeByDBylbCode,
  actionTriggered: getActionTriggered,
  completedSteps: getCompletedSteps
});

const mapDispatchToProps = (dispatch) => ({
  setActiveAccordian: (data) => dispatch(sliceActions.setActiveAccordian(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  createEFile: (data) => dispatch(actions.createEFile(data)),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchLocalBodyType: (data) => dispatch(commonActions.fetchLocalBodyType(data)),
  fetchLocalBodyByDistrictByType: (data) => dispatch(commonActions.fetchLocalBodyByDistrictByType(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Services);
