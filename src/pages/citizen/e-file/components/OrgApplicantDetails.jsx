import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import _ from 'lodash';
import {
  FormWrapper, FormController, t, Button
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as commonActions from 'pages/common/actions';
import {
  getActionTriggered,
  getCountry, getDistricts, getLocalBodyType, getLocalBodyTypeByDBylbCodeApplicant, getPostOffice, getState
} from 'pages/common/selectors';
import { DEFAULT_DISTRICT, DEFAULT_STATE } from 'common/constants';
import WhatsappIcon from 'assets/Whatsapp';
import { ML_ONLY, MO<PERSON><PERSON>_ONLY } from 'common/regex';
import { nameValidation } from 'utils/validateFile';
import { numberNull } from 'utils/numberNull';
import * as actions from '../actions';
import { OrgApplicantDetailsFormSchema } from '../validate';
import { formatOrgApplicantDetails, orgApplicantDefaultValues } from './helper';
import { getJointApplicant, getUserData } from '../selectors';
import { getPostOfficePreview } from './Preview/helper';

const OrgApplicantDetails = (props) => {
  const {
    formActiveData,
    fetchCountry,
    fetchStates,
    stateDropdown,
    fetchDistricts,
    districtDropdown,
    localBodyType,
    actionTriggered,
    setActionTriggered,
    localBodyTypeByDBylbCode,
    fetchLocalBodyByDistrictByTypeApplicant,
    fetchPostOfficeByPin,
    postOfficeDropdown,
    fetchPostOffice,
    saveEFileApplicantDetails
  } = props;

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    getValues,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: orgApplicantDefaultValues,
    resolver: yupResolver(OrgApplicantDetailsFormSchema)
  });

  const params = useParams();
  const [editId, setEditId] = useState(null);

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  const stateSelected = watch('stateId');
  const isWhatsappSame = watch('isWhatsappSame');
  const mobileNo = watch('mobileNo');
  const whatsapp = watch('whatsapp');

  useEffect(() => {
    if (isWhatsappSame) {
      setValue('whatsapp', mobileNo);
    }
  }, [isWhatsappSame, mobileNo]);

  useEffect(() => {
    if (mobileNo !== whatsapp) {
      setValue('isWhatsappSame', false);
    }
  }, [whatsapp]);

  const handleFieldChange = (field, data) => {
    if (data) {
      const mobileValidation = data?.target?.value?.replace(MOBILE_ONLY, '');
      const malayalamOnly = data?.target?.value?.replace(ML_ONLY, '');
      const nameValidate = nameValidation(data?.target?.value);
      switch (field) {
        case 'stateId':
          setActionTriggered({ loading: true, id: 'counter-applicant-district' });
          fetchDistricts(data.id);
          setValue('districtId', null);
          setValue('localBodyNameId', null);
          setValue('localBodyTypeId', null);
          break;
        case 'districtId':
          fetchPostOffice({ districtId: data.id });
          setValue('postOffice', null);
          break;
        case 'localBodyTypeId':
          setValue('localBodyTypeId', data.id);
          setValue('localBodyNameId', null);
          setActionTriggered({ loading: true, id: 'counter-applicant-localbody' });
          fetchLocalBodyByDistrictByTypeApplicant({ districtId: getValues('districtId'), lbTypeId: data.id });
          break;
        case 'localBodyNameId':
          setValue('localBodyNameId', data.lbId);
          setActionTriggered({ loading: true, id: 'counter-applicant-ward' });
          break;
        case 'mobileNo':
          if (mobileValidation) {
            setValue('mobileNo', mobileValidation);
            if (isWhatsappSame) {
              setValue('whatsapp', mobileValidation);
            }
          } else {
            setValue('mobileNo', null);
          }
          break;
        case 'whatsappCheck':
          setValue('isWhatsappSame', data);
          setValue('whatsapp', data ? mobileNo : '');
          break;
        case 'postOffice':
          setValue('pincode', data.pinCode);
          setValue('postOfficeName', data.name);
          break;
        case 'pincode':
          if (data.length >= 6) {
            fetchPostOfficeByPin(data);
          }
          break;
        case 'isWhatsappSame':
          setValue('isWhatsappSame', data);
          setValue('whatsapp', data ? mobileValidation : '');
          break;
        case 'whatsapp':
          setValue('whatsapp', mobileValidation);
          if (mobileValidation !== whatsapp) {
            setValue('isWhatsappSame', false);
          }
          break;
        case 'localStreet':
          setValue('localStreet', malayalamOnly);
          break;
        case 'localLocalPlace':
          setValue('localLocalPlace', malayalamOnly);
          break;
        case 'localMainPlace':
          setValue('localMainPlace', malayalamOnly);
          break;
        case 'localInstitutionName':
          setValue('localInstitutionName', malayalamOnly);
          break;
        case 'localOfficeName':
          setValue('localOfficeName', malayalamOnly);
          break;
        case 'localDesignation':
          setValue('localDesignation', malayalamOnly);
          break;
        case 'street':
          setValue('street', nameValidate);
          break;
        case 'localPlace':
          setValue('localPlace', nameValidate);
          break;
        case 'mainPlace':
          setValue('mainPlace', nameValidate);
          break;
        default:
          break;
      }
    } else {
      switch (field) {
        case 'stateId':
          setValue('districtId', null);
          setValue('localBodyNameId', null);
          setValue('localBodyTypeId', null);
          break;
        case 'districtId':
          break;
        case 'localBodyTypeId':
          setValue('localBodyTypeId', null);
          setValue('localBodyNameId', null);
          break;
        case 'localBodyNameId':
          setValue('localBodyNameId', null);
          break;
        default:
          break;
      }
    }
  };

  useEffect(() => {
    fetchCountry();
    setActionTriggered({ loading: false, id: 'counter-applicant-country' });
    fetchStates();
    fetchDistricts(DEFAULT_STATE.id);
    fetchPostOffice({ districtId: DEFAULT_DISTRICT?.id });
  }, []);

  const onSubmitForm = (data) => {
    const saveData = {
      inwardId: params.id,
      institutionDetails: [formatOrgApplicantDetails(data, editId)]
    };
    setActionTriggered({ loading: true, id: 'saveEfileApplicant' });
    saveEFileApplicantDetails(saveData);
  };

  useEffect(() => {
    if (formActiveData) {
      if (Object.keys(formActiveData).length > 0) {
        const addressList = formActiveData?.institutionDetailsResponses;

        if (addressList?.length > 0) {
          const data = addressList[0];
          setEditId(data?.id);
          setValue('stateId', numberNull(Number(data.stateId)));
          setValue('districtId', numberNull(Number(data.districtId)));
          setValue('postOffice', Number(data.postOffice));
          setValue('pincode', data.pincode);
          setValue('street', data.street);
          setValue('localPlace', data.localPlace);
          setValue('mainPlace', data.mainPlace);
          setValue('emailId', data.emailId);
          setValue('mobileNo', data.mobileNo);
          setValue('institutionName', data.institutionName);
          setValue('isWhatsappSame', data.isWhatsappSame);
          setValue('whatsapp', data.whatsappNo);
          setValue('landLine', data.landLine);
          setValue('referenceNo', data.referenceNo);
          setValue('institutionDate', data.institutionDate);
          setValue('officerName', data.officerName);
          setValue('designation', data.designation);
          setValue('postOfficeName', getPostOfficePreview(Number(data.postOffice), data.postOfficeList));
          setValue('localStreet', data.localStreet);
          setValue('localLocalPlace', data.localLocalPlace);
          setValue('localMainPlace', data.localMainPlace);
          setValue('localBodyTypeId', data.localBodyTypeId);
          setValue('localBodyNameId', data.localBodyNameId);
          if (data.localBodyTypeId) {
            fetchLocalBodyByDistrictByTypeApplicant({ districtId: Number(data.districtId), lbTypeId: data.localBodyTypeId });
          }
          if (data?.wardInfo?.officeLbCode) {
            fetchPostOffice({ offLbCode: data?.wardInfo?.officeLbCode });
          }
          setValue('localBodyTypeId', data.localBodyTypeId);
        }
      }
    }
  }, [JSON.stringify(formActiveData)]);

  return (
    <form onSubmit={handleSubmit(onSubmitForm)} id="applicant-form">
      <div id="applicant_details" />
      <FormWrapper>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="stateId"
            type="select"
            label={t('state')}
            control={control}
            errors={errors}
            optionKey="id"
            options={_.get(stateDropdown, 'data', [])}
            handleChange={(data) => handleFieldChange('stateId', data)}
            isLoading={actionTriggered?.id === 'counter-applicant-state' && actionTriggered?.loading}
            required
            isClearable
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="districtId"
            type="select"
            label={t('district')}
            control={control}
            errors={errors}
            optionKey="id"
            options={_.get(districtDropdown, 'data', [])}
            handleChange={(data) => handleFieldChange('districtId', data)}
            isLoading={actionTriggered?.id === 'counter-applicant-district' && actionTriggered?.loading}
            required
            isClearable
          />
        </div>
        {Number(stateSelected) === DEFAULT_STATE.id && (
        <>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localBodyTypeId"
              type="select"
              label={t('localBodyType')}
              control={control}
              errors={errors}
              options={localBodyType?.data?.filter((item) => item.code !== 'LB_TYPE_DISTRICT_PANCHAYATH' && item.code !== 'LB_TYPE_BLOCK_PANCHAYATH')}
              isLoading={actionTriggered?.id === 'counter-applicant-localBodyType' && actionTriggered?.loading}
              optionKey="id"
              handleChange={(data) => {
                handleFieldChange('localBodyTypeId', data);
              }}
              isClearable
              required
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localBodyNameId"
              type="select"
              label={t('localBodyName')}
              control={control}
              errors={errors}
              options={_.get(localBodyTypeByDBylbCode, 'data', [])}
              isLoading={actionTriggered?.id === 'counter-applicant-localbody' && actionTriggered?.loading}
              optionKey="lbId"
              handleChange={(data) => {
                handleFieldChange('localBodyNameId', data);
              }}
              isClearable
              required
            />
          </div>
        </>
        )}
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="mobileNo"
            type="text"
            label={t('concatLabel', { label: t('mobile'), type: t('number') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('mobileNo', data)}
            maxLength={10}
            rightContent={(
              <FormController
                type="check"
                control={control}
                errors={errors}
                name="isWhatsappSame"
                label={<WhatsappIcon />}
              />
                )}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="whatsapp"
            type="text"
            label={t('concatLabel', { label: t('whatsapp'), type: t('number') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('whatsapp', data)}
            maxLength={10}
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="referenceNo"
            type="text"
            label={t('concatLabel', { label: t('reference'), type: t('number') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('referenceNo', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="institutionDate"
            type="date"
            label={t('date')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('institutionDate', data)}
            maxDate={new Date()}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="institutionName"
            type="text"
            label={t('concatLabel', { label: t('institution'), type: t('name') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('institutionName', data)}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="officerName"
            type="text"
            label={t('concatLabel', { label: t('officer'), type: t('name') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('officerName', data)}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="designation"
            type="text"
            label={t('designation')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('designation', data)}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localInstitutionName"
            type="text"
            label={t('concatLabel', { label: t('institution'), type: t('local') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localInstitutionName', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localOfficeName"
            type="text"
            label={t('concatLabel', { label: t('officer'), type: t('local') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localOfficeName', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localDesignation"
            type="text"
            label={t('concatLabel', { label: t('designation'), type: t('local') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localDesignation', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="postOffice"
            type="select"
            label={t('postOffice')}
            optionKey="id"
            control={control}
            errors={errors}
            options={_.get(postOfficeDropdown, 'data', [])}
            handleChange={(data) => handleFieldChange('postOffice', data)}
            isLoading={actionTriggered?.id === 'counter-applicant-postoffice' && actionTriggered?.loading}
            required
            isClearable
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="pincode"
            type="text"
            label={t('pinCode')}
            control={control}
            errors={errors}
            handleChange={(event) => handleFieldChange('pincode', event.target.value)}
            required
          />
        </div>

        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="street"
            type="text"
            label={t('street')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('street', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localStreet"
            type="text"
            label={t('concatLabel', { label: t('street'), type: t('local') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localStreet', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localPlace"
            type="text"
            label={t('localPlace')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localPlace', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localLocalPlace"
            type="text"
            label={t('concatLabel', { label: t('localPlace'), type: t('local') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localLocalPlace', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="mainPlace"
            type="text"
            label={t('mainPlace')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('mainPlace', data)}
            required
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="localMainPlace"
            type="text"
            label={t('concatLabel', { label: t('mainPlace'), type: t('local') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('localMainPlace', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="emailId"
            type="text"
            label={t('emailId')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('emailId', data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="landLine"
            type="text"
            label={t('concatLabel', { label: t('landLine'), type: t('number') })}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('landLine', data)}
          />
        </div>
        <div className="col-span-12 text-right">
          {params?.id && (
          <Button
            variant="secondary_outline"
            className="shadow-md"
            type="submit"
            isLoading={actionTriggered?.id === 'counter-applicant' && actionTriggered?.loading}
          >
            {t('proceed')}
          </Button>
          )}
        </div>
      </FormWrapper>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  countryDropdown: getCountry,
  stateDropdown: getState,
  districtDropdown: getDistricts,
  userData: getUserData,
  localBodyType: getLocalBodyType,
  actionTriggered: getActionTriggered,
  localBodyTypeByDBylbCode: getLocalBodyTypeByDBylbCodeApplicant,
  jointApplicantData: getJointApplicant,
  postOfficeDropdown: getPostOffice
});

const mapDispatchToProps = (dispatch) => ({
  fetchCountry: () => dispatch(commonActions.fetchCountry()),
  fetchStates: () => dispatch(commonActions.fetchState()),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchPostOffice: (data) => dispatch(commonActions.fetchPostOffice(data)),
  fetchPostOfficeByPin: (data) => dispatch(commonActions.fetchPostOfficeByPin(data)),
  fetchLocalBodyByDistrictByTypeApplicant: (data) => dispatch(commonActions.fetchLocalBodyByDistrictByTypeApplicant(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  saveEFileApplicantDetails: (data) => dispatch(actions.saveEFileApplicantDetails(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(OrgApplicantDetails);
