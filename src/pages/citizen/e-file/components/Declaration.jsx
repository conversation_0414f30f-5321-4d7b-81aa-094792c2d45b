import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  t, But<PERSON>, FormWrapper
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getActionTriggered,
  getSidebarData
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import {
  getActiveAccordian, getEFilePreview
} from '../selectors';

const Declaration = (props) => {
  const {
    saveEfileDeclaration,
    eFilePreview,
    actionTriggered,
    setActionTriggered
  } = props;

  const params = useParams();

  const [knowledgeDeclareStatus, setKnowledgeDeclareStatus] = useState(false);
  const [validate, setValidate] = useState(false);

  const navigateToApplicant = () => {
    if (knowledgeDeclareStatus) {
      setActionTriggered({ loading: true, id: 'e-file-declaration' });
      saveEfileDeclaration({ id: params?.id, declaration: knowledgeDeclareStatus });
      setValidate(false);
    } else {
      setValidate(true);
    }
  };

  const handleDeclare = () => {
    setKnowledgeDeclareStatus(!knowledgeDeclareStatus);
    setValidate(false);
  };

  useEffect(() => {
    if (eFilePreview?.declaration) {
      setKnowledgeDeclareStatus(eFilePreview?.declaration);
    } else {
      setKnowledgeDeclareStatus(false);
    }
  }, [JSON.stringify(JSON.stringify(eFilePreview))]);

  return (
    <FormWrapper>

      <div className="col-span-12">
        <p className="pb-3">{t('iHerebyDeclareThat')}</p>
        <Button variant="link" style={{ textDecoration: 'none' }} leftIcon={knowledgeDeclareStatus ? <CheckedBox /> : <UnCheckedBox />} onClick={() => handleDeclare()}>
          {t('iAgree')}
        </Button>
        {validate
        && (
        <div className="col-span-12 text-red-500 text-left text-xs p-2 absolute w-full">
          {t('pleaseCompleteSelfDeclaration')}
        </div>
        )}
      </div>

      {params?.id && (
        <div className="col-span-12 text-right">
          <Button
            variant="secondary_outline"
            className="shadow-md"
            onClick={navigateToApplicant}
            isLoading={actionTriggered?.id === 'e-file-declaration' && actionTriggered?.loading}
          >
            {t('proceed')}
          </Button>
        </div>
      )}
    </FormWrapper>
  );
};

const mapStateToProps = createStructuredSelector({
  activeAccordian: getActiveAccordian,
  formComponentData: getSidebarData,
  eFilePreview: getEFilePreview,
  actionTriggered: getActionTriggered

});

const mapDispatchToProps = (dispatch) => ({
  setActiveAccordian: (data) => dispatch(sliceActions.setActiveAccordian(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  saveEfileDeclaration: (data) => dispatch(actions.saveEfileDeclaration(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Declaration);
