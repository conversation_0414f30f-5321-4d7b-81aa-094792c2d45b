import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, t, <PERSON><PERSON>, ErrorText, Info
} from 'common/components';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import { convertToLocalDate } from 'utils/date';
import * as commonActions from 'pages/common/actions';
import PreviewThumbnail from 'common/components/DocumentPreview/previewThumbnail';
import { dark, light } from 'utils/color';
import {
  getActionTriggered,
  getCompletedSteps, getServiceValidation, getSidebarData, getUserInfo
} from 'pages/common/selectors';
import { DATE_FORMAT } from 'pages/common/constants';
import { actions as counterSliceActions } from 'pages/counter/new/slice';
import { actions as commonSliceActions } from 'pages/common/slice';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { getRequiredDocsCount } from 'pages/counter/new/selectors';
import DownloadIcon from 'assets/Download';
import { STORAGE_KEYS } from 'common/constants';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import {
  getActiveAccordian,
  getActiveApplicationData, getDocumentDropdownTypes, getDocumentPreview, getDocumentsDetails, getEFilePreview
} from '../selectors';
import { DocumentDetailsFormSchema } from '../validate';
import { E_FILE_KEYS, E_FILE_KEYS_INDEX, documenturl } from '../constants';

const Documents = (props) => {
  const [tempArray, setTempArray] = useState([]);
  const [docPreview, setDocPreview] = useState(null);

  const {
    documentTypeDropdown,
    saveDocuments,
    deleteDocuments,
    getDocumentTypes,
    saveMandatoryDocuments,
    formActiveData,
    setActiveAccordian,
    servicevalidation,
    setRequiredDocsCount,
    fetchEFilePreview,
    eFilePreview,
    setSidebarStatus,
    completedSteps,
    setAlertAction,
    requiredDocsCount,
    setActionTriggered,
    actionTriggered
  } = props;

  const params = useParams();

  const {
    control, formState: { errors }, setValue, getValues, reset, watch
  } = useForm({
    mode: 'all',
    defaultValues: {
      supportDocumentsName: '',
      supportingDocuments: '',
      documents: '',
      documentType: '',
      issueDate: '',
      validUpto: '',
      documentNo: ''
    },
    resolver: yupResolver(DocumentDetailsFormSchema)
  });

  const [existingSupportingDocs, setExistingSupportingDocs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [documentIds, setDocumentIds] = useState([]);
  const [supportingValidateShow, setSupportingValidateShow] = useState('');

  useEffect(() => {
    if (formActiveData) {
      if (formActiveData?.supportingDocs) {
        if (formActiveData?.supportingDocs?.length > 0) {
          setExistingSupportingDocs(formActiveData?.supportingDocs);
          setValue('supportingDocuments', null);
          setValue('supportDocumentsName', '');
        } else {
          setExistingSupportingDocs([]);
        }
      }
    }
  }, [formActiveData]);

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  useEffect(() => {
    if (servicevalidation) {
      setDocumentIds(servicevalidation?.documentId);
    }
  }, [servicevalidation]);

  const formateDocType = (response) => {
    setTempArray([]);
    const formattedResponse = (response || []).map((item) => ({
      id: item?.id,
      name: item?.name,
      nameInLocal: item?.nameInLocal,
      data: []
    }));

    if (formActiveData) {
      const documentsTypeUpload = formActiveData?.documentsTypeUpload;
      if (documentsTypeUpload) {
        if (documentsTypeUpload?.length > 0) {
          for (let i = 0; i < documentsTypeUpload?.length; i += 1) {
            const findFileTypeIndex = formattedResponse.findIndex((item) => item.id === parseInt(documentsTypeUpload[i]?.documentType, 10));
            const exist = formattedResponse[findFileTypeIndex] ? JSON.parse(JSON.stringify(formattedResponse[findFileTypeIndex]?.data)) : [];
            const newData = [documentsTypeUpload[i]];
            const merge = [...exist, ...newData];
            if (findFileTypeIndex > -1) {
              formattedResponse[findFileTypeIndex].data = merge;
            }
          }
        }
      }
    }
    setTempArray(formattedResponse);
  };

  useEffect(() => {
    if (documentIds.length > 0) {
      if (documentIds[0] !== 0) {
        const sortedDocs = documentIds.map((ids) => {
          const sortedIds = documentTypeDropdown.find((item) => item.id === ids);
          return sortedIds;
        });
        setRequiredDocsCount(sortedDocs.length);
        if (sortedDocs) {
          formateDocType(sortedDocs);
        }
      }
    } else {
      setRequiredDocsCount(0);
    }
  }, [documentTypeDropdown, formActiveData, documentIds]);

  useEffect(() => {
    if (params.id) {
      fetchEFilePreview(params.id);
      getDocumentTypes();
    }
  }, [params.id]);

  useEffect(() => {
    getDocumentTypes();
  }, []);

  const resetData = () => {
    setExistingSupportingDocs([]);
    setTempArray([]);
    reset({
      supportDocumentsName: '',
      supportingDocuments: '',
      documents: '',
      documentType: '',
      issueDate: '',
      validUpto: '',
      documentNo: ''
    });
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  const navigateToPreview = () => {
    if (requiredDocsCount === 0 || eFilePreview?.documentsTypeUpload?.length === requiredDocsCount) {
      const nextStep = E_FILE_KEYS_INDEX.Preview;
      const currentStep = E_FILE_KEYS_INDEX.Document;
      setSidebarStatus({ activeStep: nextStep, completedSteps: [...completedSteps, currentStep] });
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseComplete')} ${t('the')} ${t('mandatoryDocument')} ${t('section')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
      scrollToTop('documents_details');
    }
  };

  const handleFieldChange = (field, data, index) => {
    switch (field) {
      case 'supportDocumentsName':
        setSupportingValidateShow(false);
        break;
      case 'docNo':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].documentNo = (data); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      case 'issueDate':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].issueDate = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      case 'validUpto':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].validUpto = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      default:
        break;
    }
  };

  const onFileSelected = (data) => {
    if (!data) {
      setValue('supportingDocuments', null);
      return;
    }

    if (params?.id) {
      if (data) {
        if ((getValues('supportDocumentsName') === '' || getValues('supportDocumentsName') === undefined)) {
          setSupportingValidateShow(true);
          setValue('supportingDocuments', null);
        } else {
          setSupportingValidateShow(false);
          const saveData = {
            inwardId: params?.id,
            documentName: getValues('supportDocumentsName'),
            issueDate: convertToLocalDate(getValues('issueDate'), DATE_FORMAT.DATE_LOCAL),
            validUpto: convertToLocalDate(getValues('validUpto'), DATE_FORMAT.DATE_LOCAL),
            hardCopyReceived: false,
            userInfo: {
              officeId: eFilePreview?.localBodyDetails?.officeCode
            }
          };
          setActionTriggered({ loading: true, id: 'citizen-supporting-doc-upload' });
          saveDocuments({ supportingDocs: data, request: saveData });
        }
      } else {
        setSupportingValidateShow(false);
        setValue('supportingDocuments', null);
      }
    } else {
      setValue('supportingDocuments', null);
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseComplete')} ${t('the')} ${t('localBody')} ${t('section')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
      setActiveAccordian(E_FILE_KEYS.LocalBody);
      setSidebarStatus({
        activeStep: 0
      });
    }
  };

  const onHandleRemove = (data) => {
    const sendData = {
      inwardId: params.id,
      documentTypeId: Number(data.documentType),
      fileId: data.id,
      userInfo: {
        officeId: eFilePreview?.localBodyDetails?.officeCode
      },
      source: 'efile'
    };
    deleteDocuments(sendData);
  };

  function getDocument(url, token, body) {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  }

  const handlePreview = (data, type, typeId) => {
    const sendData = {
      inwardId: params.id,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: data.id,
      userInfo: {
        officeId: eFilePreview?.localBodyDetails?.officeCode
      }
    };

    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };

  const onSelectDocumentTypes = (data, id, index) => {
    if (!data) {
      setValue('supportingDocuments', null);
      return;
    }

    if (data) {
      const saveData = {
        inwardId: params.id,
        documentTypeId: id,
        documentNo: tempArray[index]?.documentNo,
        issueDate: tempArray[index]?.issueDate,
        validUpto: tempArray[index]?.validUpto,
        hardCopyReceived: false,
        userInfo: {
          officeId: eFilePreview?.localBodyDetails?.officeCode
        }
      };
      setActionTriggered({ loading: true, id: `citizen-mandatory-doc-upload-${id}` });
      saveMandatoryDocuments({ documentTypeDocs: data, request: saveData });
    }
  };

  return (

    <FormWrapper py={false}>
      <div id="counter_documents" />

      {tempArray?.length > 0
        && (
          <div className="col-span-12 border border-[#e8ecee] rounded-[12px] p-[20px] grid mb-5">
            <div className="col-span-12">
              <div className="px-5 py-3 rounded-full font-medium" style={{ background: light, color: dark }}>
                {t('numberofMandatoryDocuments')}<span>: {tempArray.length}</span>
              </div>
            </div>

            {tempArray?.map((item, index) => {
              return (
                <div className="col-span-12 mt-5" key={item.id}>
                  <div className="mb-5">
                    <h4 className="font-medium" style={{ color: dark }}>{index + 1}. {item.name}</h4>
                  </div>
                  {item.id !== 117 && (
                    <div className="grid grid-cols-3 gap-5">

                      <FormController
                        name={`fieldDocNo${item.id}`}
                        type="text"
                        label={t('documentNo')}
                        placeholder={t('documentNo')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('docNo', data.target.value, index)}
                      />

                      <FormController
                        name={`fieldIssueDate${item.id}`}
                        variant="outlined"
                        type="date"
                        label={t('issueDate')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('issueDate', data, index)}
                      />

                      <FormController
                        name={`fieldValidUpto${item.id}`}
                        variant="outlined"
                        type="date"
                        label={t('validUpto')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('validUpto', data, index)}
                      />
                    </div>
                  )}

                  <div className="col-span-12 mt-5">
                    <div className="grid grid-cols-12 gap-5">
                      {item.id === 117 && (
                        <div className="col-span-4 border pl-5 pr-2 rounded-lg h-[56px] gap-3 flex items-center" style={{ color: '#09327B' }}>
                          <div className="flex-none">
                            {t('downloadDataSheet')}
                          </div>
                          <div className="flex-none">
                            <Info text={t('dataSheetDownloadInfo')} />
                          </div>
                          <div className="flex-grow" />
                          <a download href={servicevalidation?.datasheet} target="_blank" aria-label="Download" rel="noreferrer"><DownloadIcon className="bg-green-400 hover:bg-green-700 rounded-lg py-[1px]" style={{ color: '#fff' }} width="32px" height="32px" /></a>
                        </div>
                      )}
                      <div className="col-span-4">
                        <FormController
                          name={`fieldDocuments${item.id}`}
                          type="file"
                          label={t('attachments')}
                          placeholder={t('dropOrChooseFilesToUpload')}
                          control={control}
                          errors={errors}
                          handleChange={(data) => onSelectDocumentTypes(data, item?.id, index)}
                          optionKey="image"
                          accept={item.id === 117 ? 'pdf' : 'image,pdf'}
                          required
                          loading={actionTriggered?.id === `citizen-mandatory-doc-upload-${item?.id}` && actionTriggered?.loading}
                        />

                      </div>
                      <div className="col-span-4">
                        {item.data.length > 0
                          && (
                            <PreviewThumbnail
                              key={item.id}
                              handlePreviewRemove={(data) => { onHandleRemove(data); setValue(`fieldDocuments${item.id}`, null); }}
                              handlePreview={(data) => handlePreview(data, 1, data?.documentType)}
                              item={item.data[0]}
                              preview={docPreview}
                              fileType={item?.data[0]?.documentContentType}
                              setLoading={setLoading}
                              loading={loading}
                              documentName={item?.documentNo}

                            />
                          )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

          </div>
        )}

      <div className="col-span-12 border border-[#e8ecee] rounded-[12px] p-[20px] mb-5">
        <div className="col-span-12 mb-5">
          <div className="px-5 py-3 bg-slate-200 rounded-full font-medium text-blue-900" style={{ background: light, color: dark }}>
            {t('supportingDocuments')}
          </div>
        </div>
        <div className="grid grid-cols-3 gap-5 mb-5">
          <div>
            <FormController
              name="supportDocumentsName"
              type="text"
              label={t('documentName')}
              placeholder={t('documentName')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('supportDocumentsName', data)}
              required
            />
            {supportingValidateShow
              && <ErrorText error={t('documentNameisRequired')} />}
          </div>

          <FormController
            name="issueDate"
            variant="outlined"
            type="date"
            label={t('issueDate')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('issueDate', data)}
            maxDate={new Date()}
          />

          <FormController
            name="validUpto"
            variant="outlined"
            type="date"
            label={t('validUpto')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('validUpto', data)}
            toYear={new Date().getFullYear() + 20}
            fromYear={new Date().getFullYear()}
            minDate={getValues('issueDate', '')}
          />
        </div>
        <div className="col-span-12">
          <div className="grid grid-cols-12 gap-5">
            <div className="col-span-4">
              <FormController
                name="supportingDocuments"
                type="file"
                label={t('attachSupportingDocuments')}
                placeholder={t('dropOrChooseFilesToUpload')}
                control={control}
                errors={errors}
                handleChange={(data) => onFileSelected(data)}
                accept="image,pdf"
                loading={actionTriggered?.id === 'citizen-supporting-doc-upload' && actionTriggered?.loading}
                isDisabled={!watch('supportDocumentsName')}
              />
            </div>
            <div className="col-span-8 gap-3 flex flex-wrap">
              {existingSupportingDocs.map((item) => (
                <PreviewThumbnail
                  key={item.id}
                  handlePreviewRemove={(data) => onHandleRemove(data)}
                  handlePreview={(data) => handlePreview(data, 2, 0)}
                  item={item}
                  preview={docPreview}
                  fileType={item?.documentContentType}
                  documenturl={documenturl}
                  setLoading={setLoading}
                  loading={loading}
                  documentName={item.documentName}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {params?.id

        && (
          <div className="col-span-12 my-5 pb-10 text-right">
            <Button
              variant="secondary_outline"
              className="shadow-md"
              onClick={navigateToPreview}
            >
              {t('proceedAndPreview')}
            </Button>
          </div>
        )}
    </FormWrapper>
  );
};

const mapStateToProps = createStructuredSelector({
  activeAccordian: getActiveAccordian,
  formComponentData: getSidebarData,
  documentsDetails: getDocumentsDetails,
  documentTypeDropdown: getDocumentDropdownTypes,
  activeApplicationData: getActiveApplicationData,
  documentPreview: getDocumentPreview,
  userInfo: getUserInfo,
  servicevalidation: getServiceValidation,
  eFilePreview: getEFilePreview,
  completedSteps: getCompletedSteps,
  requiredDocsCount: getRequiredDocsCount,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  setActiveAccordian: (data) => dispatch(sliceActions.setActiveAccordian(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  saveDocuments: (data) => dispatch(actions.saveDocuments(data)),
  getDocumentTypes: (data) => dispatch(actions.getDocumentTypes(data)),
  fetchEFilePreview: (data) => dispatch(actions.fetchEFilePreview(data)),
  saveMandatoryDocuments: (data) => dispatch(actions.saveMandatoryDocuments(data)),
  deleteDocuments: (data) => dispatch(commonActions.deleteDocuments(data)),
  previewApplicant: (data) => dispatch(actions.previewApplicant(data)),
  setRequiredDocsCount: (data) => dispatch(counterSliceActions.setRequiredDocsCount(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(Documents);
