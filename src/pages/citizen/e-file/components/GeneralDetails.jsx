import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  t, Toast
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import AddCircle from 'assets/addCircle';
import MinusCircle from 'assets/MinusCircle';
import { useParams } from 'react-router-dom';
import { convertToLocalDate } from 'utils/date';
import {
  getDoorKey,
  getRouteKey,
  getUserInfo
} from 'pages/common/selectors';
import { numberNull } from 'utils/numberNull';
import { DATE_FORMAT } from 'pages/common/constants';
import NoNotesIcon from 'assets/NoNotesIcon';
import { actions as commonSliceActions } from 'pages/common/slice';
import RoutingKeys from 'pages/common/components/GeneralDetails/RoutingKeys';
import GeneralDetailsForm from 'pages/common/components/GeneralDetails/GeneralDetailsForm';
import { existingGeneralData, generalRoutingKeys, generalSaveData } from 'pages/common/components/GeneralDetails/helper';
import { USER_TYPE } from 'common/constants';
import * as actions from '../actions';
import { getEFilePreview } from '../selectors';
import { APPLICATION_STAGE } from '../constants';

const GeneralDetails = (props) => {
  const {
    updateGeneralDetails,
    eFilePreview,
    setActionTriggered,
    doorkey,
    userInfo
  } = props;
  const params = useParams();
  const { errorTost } = Toast;
  const [activeIndex, setActiveIndex] = useState(0);
  const [existingData, setExistingData] = useState([]);
  const [serviceInfo, setServiceInfo] = useState({});
  const [existingGeneral, setExistingGeneral] = useState({});

  function dataFact(existGeneral) {
    if (existGeneral) {
      const saveData = {
        id: existGeneral.id || null,
        efileApplicantDetailsId: existGeneral.efileApplicantDetailsId || null,
        inwardId: params.id,
        genderId: existGeneral.genderId,
        categoryId: existGeneral.categoryId,
        dateOfBirth: existGeneral.dateOfBirth,
        financialStatusId: existGeneral.financialStatusId,
        income: existGeneral.income,
        ownershipIdId: existGeneral.ownershipIdId,
        wardNo: existGeneral.ward,
        doorNo: existGeneral.doorNo,
        subNo: existGeneral.subNo,
        accountNo: existGeneral.accountNo,
        bank: existGeneral.bank,
        bankNameId: existGeneral.bankNameId,
        bankBranch: existGeneral.bankBranchId,
        ifsc: existGeneral.ifsc,
        educationalQualificationId: existGeneral.educationalQualificationId,
        description: existGeneral.description,
        accountTypeId: existGeneral.accountTypeId,
        treasuryTypeId: existGeneral.treasuryTypeId,
        treasuryAccountNo: existGeneral.treasuryAccountNo,
        headOfAccount: existGeneral.headOfAccount
      };

      return saveData;
    }
    return {};
  }

  const resetData = () => {
    setExistingData(null);
    setActiveIndex(0);
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  const formaGeneralExisting = (data, index) => {
    if (data?.generalDetailsResponses?.details) {
      if (data?.generalDetailsResponses?.details[index]) {
        return dataFact(data?.generalDetailsResponses?.details[index]);
      }
      return {};
    }
    return {};
  };

  useEffect(() => {
    if (!eFilePreview) return;
    const generalRes = eFilePreview?.applicantDetailsResponses?.length > 0 ? eFilePreview?.applicantDetailsResponses : eFilePreview?.institutionDetailsResponses;
    if (!generalRes || generalRes.length === 0) return;
    setExistingData(null);
    const newData = generalRes.map((applicant, index) => ({
      id: applicant.id,
      firstName: applicant.institutionName ? applicant.institutionName : `${applicant.firstName ? applicant.firstName : ''} ${applicant.middleName ? applicant.middleName : ''} ${applicant.lastName ? applicant.lastName : ''} `,
      data: formaGeneralExisting(eFilePreview, index)
    }));
    setExistingData(newData);
    setServiceInfo({ code: eFilePreview?.serviceCode, name: eFilePreview?.serviceName });
  }, [eFilePreview]);

  useEffect(() => {
    if (eFilePreview?.generalDetailsResponses) {
      const generalDetailsResponses = eFilePreview?.generalDetailsResponses;
      setExistingGeneral(existingGeneralData(generalDetailsResponses));
    }
  }, [eFilePreview]);

  const handleOpenCard = (data, index) => {
    if (activeIndex === index) {
      setActiveIndex(null);
    } else {
      setActiveIndex(index);
    }
  };

  const addGeneralDetails = (data) => {
    let applicantType;

    if (userInfo?.userDetails?.userType === USER_TYPE.ORGANIZATION) {
      applicantType = 'efileInstitutionDetailsId';
    } else {
      applicantType = 'efileApplicantDetailsId';
    }

    const saveData = {
      [applicantType]: existingData[activeIndex].id,
      genderId: numberNull(Number(data.genderId)),
      categoryId: data.category,
      dateOfBirth: convertToLocalDate(data.dateOfBirth, DATE_FORMAT.DATE_LOCAL),
      financialStatusId: numberNull(Number(data.financialStatusId)),
      income: data.income,
      accountNo: data.accountNo,
      bankNameId: numberNull(Number(data.bankNameId)),
      bankBranchId: numberNull(Number(data.bankBranchId)),
      ifsc: data.ifsc,
      educationalQualificationId: numberNull(Number(data.educationalQualificationId)),
      accountTypeId: data.accountTypeId,
      treasuryTypeId: data.treasuryTypeId,
      treasuryAccountNo: data.treasuryAccountNo,
      headOfAccount: data.headOfAccount
    };

    const existingArray = JSON.parse(JSON.stringify(existingData || []));
    existingArray[activeIndex].data = saveData;
    setExistingData(existingArray);

    if (activeIndex < existingData.length) {
      setActiveIndex(activeIndex + 1);
    }
  };

  const routingSave = (data) => {
    const inCompletedSteps = existingData.map((item, index) => {
      if (Object.keys(item.data).length === 0) {
        return index;
      } return null;
    });
    const steps = _.filter(inCompletedSteps, (el) => !_.isNull(el));
    if (steps.length > 0) {
      setActiveIndex(steps[0]);
      errorTost({
        title: t('incompleteDetails'),
        description: t('pleaseCompleteMissingDetails')
      });
    } else {
      setActionTriggered({ loading: true, id: 'efileGeneralDetailsCreate' });
      const keyData = generalRoutingKeys(data, doorkey);
      const rounteKeyData = _.omitBy(keyData, _.isNil);
      const saveData = generalSaveData(
        rounteKeyData,
        params?.id,
        existingData,
        data,
        eFilePreview?.localBodyDetails?.officeCode,
        'efile'
      );
      updateGeneralDetails(_.omitBy(saveData, _.isNil));
    }
  };

  return (
    <div className="py-5">
      {existingData?.length === 0 && (
        <div className="p-10 text-center">
          <NoNotesIcon width="100px" height="100px" className="mx-auto" />
          <h4>Please Complete the Applicant Details</h4>
        </div>
      )}
      <div id="general_details" />
      {existingData.map((item, index) => {
        return (
          <>
            <FormWrapper py={3}>
              <div className="col-span-12">
                <div className="p-3 px-6 cursor-pointer bg-slate-200 rounded-full" aria-hidden onClick={() => handleOpenCard(item, index)}>
                  <div className="flex">
                    <div className="flex-none w-8 font-medium text-blue-900">
                      {index + 1}.
                    </div>
                    <div className="grow font-medium text-blue-900">
                      {item.firstName}
                    </div>
                    <div className="flex-none">
                      {activeIndex === index ? <MinusCircle width="24" height="24" /> : <AddCircle width="24" height="24" />}
                    </div>
                  </div>
                </div>
              </div>
            </FormWrapper>
            {activeIndex === index
              && (
                <GeneralDetailsForm
                  applicantData={item.data}
                  addGeneralDetails={addGeneralDetails}
                  serviceInfo={serviceInfo}
                  addressType={userInfo?.userDetails?.userType === USER_TYPE.ORGANIZATION ? 3 : 1}
                />
              )}

          </>
        );
      })}
      {existingData?.length > 0
        && (
          <RoutingKeys
            routingSave={routingSave}
            activeIndex={activeIndex === existingData?.length}
            existingData={existingGeneral}
            serviceInfo={serviceInfo}
            isReturnToCitizen={eFilePreview?.stage === APPLICATION_STAGE.RETURN_TO_CITIZEN}
            officeCode={eFilePreview?.localBodyDetails?.officeCode}
          />
        )}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  routeKey: getRouteKey,
  eFilePreview: getEFilePreview,
  doorkey: getDoorKey,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  updateGeneralDetails: (data) => dispatch(actions.saveGeneralDetails(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(GeneralDetails);
