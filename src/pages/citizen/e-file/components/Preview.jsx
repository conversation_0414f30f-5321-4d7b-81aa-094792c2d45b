import React from 'react';
import { useParams } from 'react-router-dom';
import {
  t, Button, FormWrapper
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getSidebarData
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from '../slice';
import {
  getActiveAccordian
} from '../selectors';
import { E_FILE_KEYS } from '../constants';

const Preview = (props) => {
  const {
    // formActiveData,
    formComponentData,
    setSidebarStatus,
    setActiveAccordian
  } = props;

  const params = useParams();

  const navigateToApplicant = () => {
    setActiveAccordian(E_FILE_KEYS.Preview);
    setSidebarStatus({ activeStep: 5, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
  };

  return (
    <FormWrapper>

      {params?.id && (
        <div className="col-span-12 text-right">
          <Button
            variant="secondary_outline"
            className="shadow-md"
            onClick={navigateToApplicant}
          >
            {t('proceed')}
          </Button>
        </div>
      )}
    </FormWrapper>
  );
};

const mapStateToProps = createStructuredSelector({
  activeAccordian: getActiveAccordian,
  formComponentData: getSidebarData
});

const mapDispatchToProps = (dispatch) => ({
  setActiveAccordian: (data) => dispatch(sliceActions.setActiveAccordian(data)),
  setSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Preview);
