import * as yup from 'yup';

import {
  AADHAAR, NAME, NUMERIC, EN_NUMERIC, EN, EMAIL, MOBILE, ML, EN_SPACE, FILE_NO, INCOME_LIMIT,
  EN_SPECIAL
} from 'common/regex';

import { t } from 'common/components';
import { DEFAULT_COUNTRY, DEFAULT_STATE } from 'common/constants';

export const LocalBodyFormSchema = yup.object({
  districtId: yup.string().required(t('isRequired', { type: t('district') })),
  localBodyTypeId: yup.string().required(t('isRequired', { type: t('localBodyType') })),
  localBodyNameId: yup.string().required(t('isRequired', { type: t('localBodyName') }))
}).required();

export const ApplicatDetailsFormSchema = yup.object().shape({
  countryId: yup.string()
    .required(t('isRequired', { type: t('country') })),
  stateId: yup.number()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('state') }));
      }
      return schema.notRequired();
    }),
  districtId: yup.number()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('district') }));
      }
      return schema.notRequired();
    }),
  localBodyTypeId: yup.number()
    .when(['stateId'], (stateId, schema) => {
      if (Number(stateId[0]) === DEFAULT_STATE.id) {
        return schema.required(t('isRequired', { type: t('localBodyType') }));
      }
      return schema.notRequired();
    }),
  localBodyNameId: yup.number()
    .when(['stateId'], (stateId, schema) => {
      if (Number(stateId[0]) === DEFAULT_STATE.id) {
        return schema.required(t('isRequired', { type: t('localBodyName') }));
      }
      return schema.notRequired();
    }),
  mobileNo: yup.string().when(['countryId'], {
    is: (countryId) => {
      if (Number(countryId) === DEFAULT_COUNTRY.id) {
        return true;
      } return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: `${t('mobile')} ${t('number')}` })).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` }))
  }),
  internationalMobileNo: yup.string()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.notRequired().nullable().transform((val) => val || null);
      }
      if (Number(countryId[0]) !== DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: `${t('mobile')} ${t('number')}` })).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` }));
      }
      return schema.notRequired();
    }),
  whatsapp: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(MOBILE, t('invalidType', { type: `${t('whatsapp')} ${t('number')}` })),
  documentNo: yup.string()
    .when(['countryId'], (data, schema) => {
      if (Number(data[0]) === DEFAULT_COUNTRY.id) {
        return schema.notRequired(t('isRequired', { type: `${t('aadhar')} ${t('number')}` })).nullable().transform((val) => val || null).matches(AADHAAR, t('invalidType', { type: `${t('aadhar')} ${t('number')}` }));
      }
      return schema.required(t('isRequired', { type: `${t('passport')} ${t('number')}` })).length(9).matches(EN_NUMERIC, t('invalidType', { type: `${t('passport')} ${t('number')}` }));
    }),
  documentType: yup.string()
    .required(t('isRequiNumber(data[0], 10)red', { type: t('document') })),
  firstName: yup.string()
    .required(t('isRequired', { type: `${t('first')} ${t('name')}` }))
    .max(150),
  middleName: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(NAME, `${t('middle')} ${t('name')} ${t('inEnglishRequired')}`),
  lastName: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null),
  localFirstName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('first')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localMiddleName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('middle')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localLastName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('last')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  houseName: yup.string()
    .required(t('isRequired', { type: `${t('house')} ${t('name')}` }))
    .max(150)
    .matches(EN_SPACE, `${t('house')} ${t('name')} ${t('inEnglishRequired')}`),
  localHouseName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('house')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  wardName: yup.string()
    .when(['stateId'], (stateId, schema) => {
      if (Number(stateId[0]) === DEFAULT_STATE.id) {
        return schema.required(t('isRequired', { type: t('ward') }));
      }
      return schema.notRequired();
    }),
  doorNo: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null),
  postOffice: yup.string()
    .when(['stateId', 'countryId'], (data, schema) => {
      if (Number(data[0]) === DEFAULT_STATE.id && Number(data[1]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('postOffice') }));
      }
      return schema.notRequired();
    }),
  pincode: yup.string()
    .when(['stateId', 'countryId'], (data, schema) => {
      if (Number(data[0]) === DEFAULT_STATE.id && Number(data[1]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('pincode') })).matches(NUMERIC, t('useNumbersOnly'));
      }
      return schema.notRequired();
    }),
  street: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('street')} ${t('inEnglishRequired')}`),
  localPlace: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('localPlace')} ${t('inEnglishRequired')}`),
  mainPlace: yup.string()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('mainPlace') })).max(150).matches(EN_NUMERIC, t('useAlphaAndNumOnly'));
      }
      return schema.required(t('isRequired', { type: t('mainPlace') })).max(150).matches(EN, `${t('mainPlace')} ${t('inEnglishRequired')}`);
    }),
  localStreet: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('street')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localLocalPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('localPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localMainPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('mainPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localInstitutionName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('institution')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localOfficeName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localDesignation: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  emailId: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EMAIL, t('invalidType', { type: t('emailId') }))

}).required();

export const OrgApplicantDetailsFormSchema = yup.object().shape({
  countryId: yup.string()
    .required(t('isRequired', { type: t('country') })),
  stateId: yup.number().required(t('isRequired', { type: t('state') })),
  districtId: yup.number().required(t('isRequired', { type: t('district') })),
  localBodyTypeId: yup.number().required(t('isRequired', { type: t('localBodyType') })),
  localBodyNameId: yup.number().required(t('isRequired', { type: t('localBodyName') })),
  mobileNo: yup.string().required(t('isRequired', { type: `${t('mobile')} ${t('number')}` })).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` })),
  whatsapp: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(MOBILE, t('invalidType', { type: `${t('whatsapp')} ${t('number')}` })),
  postOffice: yup.string()
    .when(['stateId'], (data, schema) => {
      if (Number(data[0]) === DEFAULT_STATE.id) {
        return schema.required(t('isRequired', { type: t('postOffice') }));
      }
      return schema.notRequired();
    }),
  pincode: yup.string()
    .when(['stateId'], (data, schema) => {
      if (Number(data[0]) === DEFAULT_STATE.id) {
        return schema.required(t('isRequired', { type: t('pincode') })).matches(NUMERIC, t('useNumbersOnly'));
      }
      return schema.notRequired();
    }),
  street: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('street')} ${t('inEnglishRequired')}`),
  localPlace: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('localPlace')} ${t('inEnglishRequired')}`),
  mainPlace: yup.string().required(t('isRequired', { type: t('mainPlace') })).max(150).matches(EN_NUMERIC, t('useAlphaAndNumOnly')),
  localStreet: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('street')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localLocalPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('localPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localMainPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('mainPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localInstitutionName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('institution')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localOfficeName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localDesignation: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  emailId: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EMAIL, t('invalidType', { type: t('emailId') })),
  referenceNo: yup.string()
    .max(50)
    .nullable()
    .transform((val) => val || null),
  institutionDate: yup.string()
    .nullable()
    .transform((val) => val || null),
  institutionName: yup.string().required(t('isRequired', { type: `${t('institution')} ${t('name')}` })).max(150).matches(EN_SPECIAL, `${t('institution')} ${t('name')} ${t('inEnglishRequired')}`),
  officerName: yup.string().required(t('isRequired', { type: `${t('officer')} ${t('name')}` })).matches(EN, `${t('officer')} ${t('name')} ${t('inEnglishRequired')}`),
  designation: yup.string().required(t('isRequired', { type: `${t('designation')}` })).matches(EN, `${t('designation')} ${t('inEnglishRequired')}`),
  landLine: yup.string().max(15)
}).required();

export const DocumentDetailsFormSchema = yup.object().shape({
  supportingDocuments: yup.string()
    .required(t('isRequired', { type: t('supportingDocuments') }))

}).required();

export const AdvanceSearchSchema = yup.object({
  services: yup.string().required(t('isRequired', { type: t('service') })),
  modules: yup.string().required(t('isRequired', { type: t('modules') })),
  subModule: yup.string().required(t('isRequired', { type: t('subModule') }))
}).required();

export const GeneralDetailSchema = yup.object({
  genderId: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.genderId === 1) {
        return schema.required(t('isRequired', { type: t('gender') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  financialStatusId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.financialStatusId === 1) {
        return schema.required(t('isRequired', { type: t('financialStatus') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),

  dateOfBirth: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfBirth === 1) {
        return schema.required(t('isRequired', { type: t('dateOfBirth') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  category: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.category === 1) {
        return schema.required(t('isRequired', { type: t('category') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  accountNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.accountNo === 1) {
        return schema.required(t('isRequired', { type: t('accountNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  bankNameId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.bankNameId === 1) {
        return schema.required(t('isRequired', { type: t('bankName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  bankBranchId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.bankBranchId === 1) {
        return schema.required(t('isRequired', { type: t('bankBranch') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ifsc: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ifsc === 1) {
        return schema.required(t('isRequired', { type: t('ifsc') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  income: yup.string()
    .matches(INCOME_LIMIT, `${t('income')} ${t('greaterThanNine')}`)
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.income === 1) {
        return schema.required(t('isRequired', { type: t('income') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  educationalQualificationId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.educationalQualificationId === 1) {
        return schema.required(t('isRequired', { type: t('educationalQualification') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })
}).required();

export const RoutingKeySchema = yup.object({
  referenceNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.referenceNo === 1) {
        return schema.required(t('isRequired', { type: t('referenceNo') })).matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
      }
      return schema.nullable().transform((val) => val || null).notRequired().matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
    }),
  ward: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ward === 1) {
        return schema.required(t('isRequired', { type: t('ward') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  doorNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.doorNo === 1) {
        return schema
          .max(5, 'Door No must be five digits')
          .required(t('isRequired', { type: t('doorNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  subNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.subNo === 1) {
        return schema.required(t('isRequired', { type: t('subNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownershipId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownershipId === 1) {
        return schema.required(t('isRequired', { type: t('ownershipId') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  localBodyPropertyType: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.localBodyPropertyType === 1) {
        return schema.required(t('isRequired', { type: t('localBodyPropertyType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingUsage: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingUsage === 1) {
        return schema.required(t('isRequired', { type: t('buildingUsage') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingArea: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingArea === 1) {
        return schema.required(t('isRequired', { type: t('buildingArea') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functionalGroup: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functionalGroup === 1) {
        return schema.required(t('isRequired', { type: t('functionalGroup') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functions: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functions === 1) {
        return schema.required(t('isRequired', { type: t('functions') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  description: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.description === 1) {
        return schema.required(t('isRequired', { type: t('description') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownerName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownerName === 1) {
        return schema.required(t('isRequired', { type: t('ownerName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ksebPostNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ksebPostNo === 1) {
        return schema.required(t('isRequired', { type: t('ksebPostNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  roadName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.roadName === 1) {
        return schema.required(t('isRequired', { type: t('roadName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  landMark: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.landmark === 1) {
        return schema.required(t('isRequired', { type: t('landmark') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  talukId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.taluk === 1) {
        return schema.required(t('isRequired', { type: t('taluk') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  village: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.village === 1) {
        return schema.required(t('isRequired', { type: t('village') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  surveyNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.surveyNumber === 1) {
        return schema.required(t('isRequired', { type: t('surveyNumber') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  dateOfEvent: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfEvent === 1) {
        return schema.required(t('isRequired', { type: t('dateOfEvent') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  receiptNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.receiptNo === 1) {
        return schema.required(t('isRequired', { type: t('receiptNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })

}).required();

export const otpVerification = yup.object({
  otp: yup.number().required(t('isRequired', { type: t('otp') }))
}).required();
