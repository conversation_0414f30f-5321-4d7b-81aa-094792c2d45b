import {
  all, takeLatest, call, fork, put, take, select
} from 'redux-saga/effects';
import { Toast, t } from 'common/components';
import { handleAPIRequest } from 'utils/http';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as fileSliceActions } from 'pages/file/details/slice';
import {
  getCompletedSteps
} from 'pages/common/selectors';
import { BASE_PATH, FINANCE_BASE_PATH } from 'common/constants';
import { fetchFileDetails } from 'pages/file/details/saga';
import * as action from './actions';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { E_FILE_KEYS_INDEX } from './constants';

const { successTost, errorTost } = Toast;

export function* fetchEFilePreview({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchEFilePreview, payload);
}

export function* createEFile({ payload = {} }) {
  const { kswiftId } = payload;
  yield fork(handleAPIRequest, api.createEFile, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.CREATE_E_FILE_SUCCESS,
    ACTION_TYPES.CREATE_E_FILE_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'saveEfile' }));
  if (type === ACTION_TYPES.CREATE_E_FILE_SUCCESS) {
    const { serviceCode } = payload;
    const completedSteps = yield select(getCompletedSteps);
    yield put(commonSliceActions.setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Applicant, completedSteps }));
    yield call(successTost, { id: 'saved', title: t('success'), description: 'success' });
    if (kswiftId) {
      yield put(commonSliceActions.navigateTo({ to: `${BASE_PATH}/citizen/e-file/${serviceCode}/${responsePayLoad.data}?kswiftId=${kswiftId}` }));
    } else {
      yield put(commonSliceActions.navigateTo({ to: `${BASE_PATH}/citizen/e-file/${serviceCode}/${responsePayLoad.data}` }));
    }
  }
}

export function* saveEFileApplicantDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveEFileApplicantDetails, payload);
  const { fileNo } = payload;
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS_SUCCESS,
    ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'saveEfileApplicant' }));
  if (type === ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS_SUCCESS) {
    const completedSteps = yield select(getCompletedSteps);
    yield put(commonSliceActions.setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.General, completedSteps }));
    const { inwardId } = payload;
    if (fileNo) {
      yield put(fileSliceActions.setActiveBenAccordian(1));
      yield call(fetchFileDetails, { payload: fileNo });
      yield call(fetchEFilePreview, { payload: inwardId });
    } else {
      yield call(fetchEFilePreview, { payload: inwardId });
    }
    if (payload?.from === 'beneficiary') {
      if (payload?.applicantId) {
        yield put(
          commonSliceActions.navigateTo({
            to: `${BASE_PATH}/file/${fileNo}/beneficiary/application/${payload?.inwardId}/${payload?.applicantId}`
          })
        );
      } else {
        const currentApplicantIds = payload?.existingApplicants || [];
        const updatedApplicantIds = responsePayLoad?.data || [];
        const newid = updatedApplicantIds.filter((item) => !currentApplicantIds.includes(item));
        yield put(
          commonSliceActions.navigateTo({
            to: `${BASE_PATH}/file/${fileNo}/beneficiary/application/${payload?.inwardId}/${newid[0]}`
          })
        );
      }
    }
    yield call(successTost, { id: 'saved', title: t('success'), description: responsePayLoad.message });
  } else {
    const errorMessage = responsePayLoad?.error?.response?.data?.errors;
    const values = Object.values(errorMessage).toString().replace(',', '<br />');

    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: values,
      title: t('error'),
      backwardActionText: t('ok')
    }));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant' }));
  }
}

export function* saveDocuments({ payload = {} }) {
  const { supportingDocs, request } = payload;
  const formData = new FormData();
  formData.append('supportingDocs', supportingDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));

  yield fork(handleAPIRequest, api.saveDocuments, formData);
  const { type } = yield take([
    ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'citizen-supporting-doc-upload' }));
    yield call(fetchEFilePreview, { payload: request.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'citizen-supporting-doc-upload' }));
  }
}

export function* saveMandatoryDocuments({ payload = {} }) {
  const { documentTypeDocs, request } = payload;
  const formData = new FormData();
  formData.append('documentTypeDocs', documentTypeDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));

  yield fork(handleAPIRequest, api.saveMandatoryDocuments, formData);
  const { type } = yield take([
    ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'citizen-mandatory-doc-upload' }));
    yield call(fetchEFilePreview, { payload: request.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'citizen-mandatory-doc-upload' }));
  }
}

export function* getDocumentTypes({ payload = {} }) {
  yield fork(handleAPIRequest, api.getDocumentTypes, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DOCUMENT_TYPES_SUCCESS,
    ACTION_TYPES.FETCH_DOCUMENT_TYPES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_DOCUMENT_TYPES_SUCCESS) {
    yield put(sliceActions.setDocumentTypes(responsePayLoad.data));
  }
}

export function* saveDocumentsTypeData({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveDocumentsTypeData, payload);
  const { type } = yield take([
    ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_SUCCESS,
    ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_FAILURE]);
  if (type === ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
    yield call(fetchEFilePreview, { payload: payload.id });
  } else {
    yield call(errorTost, { id: t('error'), title: t('error'), description: t('fileUploadFailed') });
  }
}

export function* saveGeneralDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveGeneralDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_GENERAL_DETAILS_SUCCESS,
    ACTION_TYPES.SAVE_GENERAL_DETAILS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_GENERAL_DETAILS_SUCCESS) {
    const completedSteps = yield select(getCompletedSteps);
    yield call(fetchEFilePreview, { payload: responsePayLoad?.data?.inwardId });
    yield put(commonSliceActions.setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Document, completedSteps }));
    yield call(successTost, { id: 'saved', title: t('success'), description: t('generalDetailsSavedSucessfully') });

    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'efileGeneralDetailsCreate' }));
  } else {
    yield call(errorTost, { id: t('failed'), title: t('failed'), description: t('generalDetailsSaveFailed') });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'efileGeneralDetailsCreate' }));
  }
}

export function* fetchInwards({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchInwards, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_INWARDS_SUCCESS,
    ACTION_TYPES.FETCH_INWARDS_FAILURE]);
  if (type === ACTION_TYPES.FETCH_INWARDS_SUCCESS) {
    yield put(sliceActions.setApplications(responsePayLoad?.data));
  }
}

export function* createFile({ payload = {} }) {
  const { officeId, ...restPayload } = payload;
  yield fork(handleAPIRequest, api.createFile, restPayload);

  const { type } = yield take([
    ACTION_TYPES.CREATE_FILE_SUCCESS,
    ACTION_TYPES.CREATE_FILE_FAILURE]);
  if (type === ACTION_TYPES.CREATE_FILE_SUCCESS) {
    if (payload?.source !== 4) {
      yield call(fetchInwards, { payload: { data: '?page=0&size=10', status: 'completed', officeId } });
      yield call(successTost, { id: 'created', title: 'Success', description: 'File generated successfully' });
      yield put(sliceActions.setSelectedInwards(null));
      yield put(sliceActions.setSelectedServices(null));
      yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'inwardCreate' }));
    }
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'inwardCreate' }));
  }
}

export function* saveComplete({ payload = {} }) {
  const { inwardId, filePayload } = payload;
  yield fork(handleAPIRequest, api.saveComplete, inwardId);
  const { type } = yield take([
    ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
    ACTION_TYPES.COMPLETE_SAVE_FAILURE]);
  if (type === ACTION_TYPES.COMPLETE_SAVE_SUCCESS) {
    yield put(action.createFile(filePayload));
    const completedSteps = yield select(getCompletedSteps);
    const nextStep = E_FILE_KEYS_INDEX.LocalBody;
    const currentStep = E_FILE_KEYS_INDEX.Verification;
    yield put(commonSliceActions.setSidebarStatus({ activeStep: nextStep, completedSteps: [...completedSteps, currentStep] }));
    yield put(commonSliceActions.setAcknowledgeTriggered(true));
  }
}

export function* deleteApplicant({ payload = {} }) {
  yield fork(handleAPIRequest, api.deleteApplicant, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.DELETE_APPLICANT_SUCCESS,
    ACTION_TYPES.DELETE_APPLICANT_FAILURE]);
  if (type === ACTION_TYPES.DELETE_APPLICANT_SUCCESS) {
    if (responsePayLoad?.status === 'SUCCESS') {
      yield call(fetchEFilePreview, { payload: payload.inwardId });
      yield call(successTost, { id: 'saved', title: t('success'), description: responsePayLoad.message });
    } else {
      yield call(successTost, { id: 'failed', title: t('failed'), description: responsePayLoad.message });
    }
  }
}

export function* saveEfileDeclaration({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveEfileDeclaration, payload);
  const { type } = yield take([
    ACTION_TYPES.EFILE_DECLARATION_SUCCESS,
    ACTION_TYPES.EFILE_DECLARATION_FAILURE]);
  if (type === ACTION_TYPES.EFILE_DECLARATION_SUCCESS) {
    const completedSteps = yield select(getCompletedSteps);
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'e-file-declaration' }));
    yield call(fetchEFilePreview, { payload: payload?.id });
    yield put(commonSliceActions.setSidebarStatus({ activeStep: E_FILE_KEYS_INDEX.Verification, completedSteps }));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'e-file-declaration' }));
    yield call(fetchEFilePreview, { payload: payload?.id });
  }
}

export function* getUser({ payload = {} }) {
  yield fork(handleAPIRequest, api.getUser, payload);
}

export function* epayGenerate({ payload = {} }) {
  yield fork(handleAPIRequest, api.epayGenerate, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.EPAY_GENERATE_SUCCESS,
    ACTION_TYPES.EPAY_GENERATE_FAILURE]);
  if (type === ACTION_TYPES.EPAY_GENERATE_SUCCESS) {
    if (responsePayLoad?.data?.id) {
      window.location.href = `${FINANCE_BASE_PATH}/e-pay/summary/${responsePayLoad?.data?.id}`;
    }
  }
}

export function* sendOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.sendOtp, payload);
  const { mobileNo } = payload;
  const { type } = yield take([
    ACTION_TYPES.SEND_OTP_SUCCESS,
    ACTION_TYPES.SEND_OTP_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'otpsend' }));
  if (type === ACTION_TYPES.SEND_OTP_SUCCESS) {
    yield put(commonSliceActions.setOtpStatus(true));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: `${t('otpSuccessfullySendto')} ${mobileNo}`, title: t('success'), backwardActionText: t('ok')
    }));
  }
  if (type === ACTION_TYPES.SEND_OTP_FAILURE) {
    yield put(commonSliceActions.setOtpStatus(false));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('otpSendingFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* verifyOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.verifyOtp, payload);
  const { inwardId } = payload;
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.VERIFY_OTP_SUCCESS,
    ACTION_TYPES.VERIFY_OTP_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'otpverify' }));
  if (type === ACTION_TYPES.VERIFY_OTP_SUCCESS) {
    yield call(fetchEFilePreview, { payload: inwardId });
    if (responsePayLoad?.data) {
      yield put(commonSliceActions.setOtpStatus(false));
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'success', message: t('otpSuccessfullyVerified'), title: t('success'), backwardActionText: t('ok')
      }));
    } else {
      yield put(commonSliceActions.setOtpStatus(true));
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'error', message: t('otpVerificationFailed'), title: t('failed'), backwardActionText: t('ok')
      }));
    }
  }
  if (type === ACTION_TYPES.VERIFY_OTP_FAILURE) {
    yield put(commonSliceActions.setOtpStatus(true));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('otpVerificationFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* resubmitEfile({ payload = {} }) {
  yield fork(handleAPIRequest, api.resubmitEfile, payload);
  const { type } = yield take([
    ACTION_TYPES.RESUBMIT_EFILE_SUCCESS,
    ACTION_TYPES.RESUBMIT_EFILE_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'resubmitEfile' }));
  if (type === ACTION_TYPES.RESUBMIT_EFILE_SUCCESS) {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('efileSuccessfullyReSubmitted'), title: t('success'), backwardActionText: t('ok')
    }));
    window.location.href = `${window?.location?.origin}/ui/home/<USER>/dashboard`;
  }
  if (type === ACTION_TYPES.RESUBMIT_EFILE_FAILURE) {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('resubmitEfileFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export default function* counterSaga() {
  yield all([
    takeLatest(ACTION_TYPES.CREATE_E_FILE, createEFile),
    takeLatest(ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS, saveEFileApplicantDetails),
    takeLatest(ACTION_TYPES.E_FILE_PREVIEW, fetchEFilePreview),
    takeLatest(ACTION_TYPES.SAVE_DOCUMENTS, saveDocuments),
    takeLatest(ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS, saveMandatoryDocuments),
    takeLatest(ACTION_TYPES.FETCH_DOCUMENT_TYPES, getDocumentTypes),
    takeLatest(ACTION_TYPES.SAVE_GENERAL_DETAILS, saveGeneralDetails),
    takeLatest(ACTION_TYPES.COMPLETE_SAVE, saveComplete),
    takeLatest(ACTION_TYPES.DELETE_APPLICANT, deleteApplicant),
    takeLatest(ACTION_TYPES.EFILE_DECLARATION, saveEfileDeclaration),
    takeLatest(ACTION_TYPES.GET_USER, getUser),
    takeLatest(ACTION_TYPES.EPAY_GENERATE, epayGenerate),
    takeLatest(ACTION_TYPES.SEND_OTP, sendOtp),
    takeLatest(ACTION_TYPES.VERIFY_OTP, verifyOtp),
    takeLatest(ACTION_TYPES.RESUBMIT_EFILE, resubmitEfile),
    takeLatest(ACTION_TYPES.CREATE_FILE, createFile)
  ]);
}
