import { baseApiURL } from 'utils/http';

export const STATE_REDUCER_KEY = 'E-FILE';

export const SIDEBAR_KEYS = {
  APPLICATION: 'APPLICATION',
  REQUIRED_DOCUMENTS: 'REQUIRED_DOCUMENTS',
  GUIDELINES: 'GUIDELINES',
  QUICK_ACCESS: 'QUICK_ACCESS',
  PROCESS: 'PROCESS'
};
export const documenturl = `${baseApiURL}/inward-management-services/preview-documents`;
export const inwardAknowledgementUrl = `${baseApiURL}/inward-management-services/generate-acknowledgement/`;
export const inwardAknowledgementDownloadUrl = `${baseApiURL}/inward-management-services/acknowledgement-print-pdf/`;

export const E_FILE_KEYS_INDEX = {
  LocalBody: 0,
  Applicant: 1,
  General: 2,
  Document: 3,
  Preview: 4,
  Declaration: 5,
  Verification: 6
};

export const E_FILE_KEYS = {
  LocalBody: 'LocalBody',
  Applicant: 'Applicant',
  General: 'General',
  Document: 'Document',
  Preview: 'Preview',
  Declaration: 'Declaration',
  Verification: 'Verification'
};

export const APPLICATION_STAGE = {
  PARTIAL: 'partial',
  APPLIED: 'APPLIED',
  RETURN_TO_CITIZEN: 'RETURN_TO_CITIZEN'
};

export const buildingUsage = [{
  id: 1,
  name: 'Residence'
}, {
  id: 2,
  name: 'Commercial'
}];

export const ownershipOptions = [{
  id: 1,
  name: 'Own'
}, {
  id: 2,
  name: 'Rental'
}];

export const documentRadioOptions = [{
  id: 1,
  name: 'Aadhaar Number'
}, {
  id: 2,
  name: 'UDID Number'
}];

export const countryCode = [{
  id: 1,
  code: 'IND',
  phoneCode: '91'
},
{
  id: 2,
  code: 'USA',
  phoneCode: '1'
}];

export const localBodyId = 169;
export const localBodyCode = 'kl.cochin';
