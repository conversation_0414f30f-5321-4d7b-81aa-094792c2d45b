import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const createEFile = (data) => {
  return {
    url: API_URL.CITIZEN.CREATE_E_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.CREATE_E_FILE_REQUEST,
        ACTION_TYPES.CREATE_E_FILE_SUCCESS,
        ACTION_TYPES.CREATE_E_FILE_FAILURE
      ],
      data
    }
  };
};

// save applicant details
export const saveEFileApplicantDetails = (data) => {
  return {
    url: API_URL.CITIZEN.SAVE_E_FILE_APPLICANT_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS_REQUEST,
        ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS_SUCCESS,
        ACTION_TYPES.SAVE_E_FILE_APPLICANT_DETAILS_FAILURE
      ],
      data
    }
  };
};

export const fetchEFilePreview = (data) => {
  return {
    url: API_URL.CITIZEN.PREVIEW_E_FILE.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.E_FILE_PREVIEW_REQUEST,
        ACTION_TYPES.E_FILE_PREVIEW_SUCCESS,
        ACTION_TYPES.E_FILE_PREVIEW_FAILURE
      ]
    }
  };
};

export const saveGeneralDetails = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_GENERAL_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_GENERAL_DETAILS_REQUEST,
        ACTION_TYPES.SAVE_GENERAL_DETAILS_SUCCESS,
        ACTION_TYPES.SAVE_GENERAL_DETAILS_FAILURE
      ],
      data
    }
  };
};

export const saveDocuments = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};
export const saveMandatoryDocuments = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_MANDATORY_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};

export const getDocumentTypes = (data) => {
  return {
    url: API_URL.COUNTER.DOCUMENT_TYPES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DOCUMENT_TYPES_REQUEST,
        ACTION_TYPES.FETCH_DOCUMENT_TYPES_SUCCESS,
        ACTION_TYPES.FETCH_DOCUMENT_TYPES_FAILURE
      ]
    },
    data
  };
};

export const saveDocumentsTypeData = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_DOCUMENTS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_REQUEST,
        ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_SUCCESS,
        ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_FAILURE
      ],
      data
    }
  };
};
export const saveComplete = (data) => {
  return {
    url: API_URL.CITIZEN.COMPLETE_SAVE.replace(':id', data),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.COMPLETE_SAVE_REQUEST,
        ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
        ACTION_TYPES.COMPLETE_SAVE_FAILURE
      ]
    }
  };
};

export const deleteApplicant = (data) => {
  return {
    url: API_URL.COUNTER.DELETE_APPLICANT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_APPLICANT_REQUEST,
        ACTION_TYPES.DELETE_APPLICANT_SUCCESS,
        ACTION_TYPES.DELETE_APPLICANT_FAILURE
      ],
      data
    }
  };
};

export const saveEfileDeclaration = (data) => {
  const { id, declaration } = data;
  return {
    url: API_URL.CITIZEN.EFILE_DECLARATION.replace(':id', id).replace(':declarationValue', declaration),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.EFILE_DECLARATION_REQUEST,
        ACTION_TYPES.EFILE_DECLARATION_SUCCESS,
        ACTION_TYPES.EFILE_DECLARATION_FAILURE
      ]
    }
  };
};

export const getUser = (data) => {
  return {
    url: API_URL.CITIZEN.GET_USER.replace(':userId', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.GET_USER_REQUEST,
        ACTION_TYPES.GET_USER_SUCCESS,
        ACTION_TYPES.GET_USER_FAILURE
      ]
    }
  };
};

export const epayGenerate = (data) => {
  const { officeId, inwardId } = data;
  return {
    url: API_URL.CITIZEN.EPAY_GENERATE.replace(':officeId', officeId).replace(':inwardId', inwardId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.EPAY_GENERATE_REQUEST,
        ACTION_TYPES.EPAY_GENERATE_SUCCESS,
        ACTION_TYPES.EPAY_GENERATE_FAILURE
      ]
    }
  };
};

export const sendOtp = (params) => {
  return {
    url: API_URL.CITIZEN.SEND_OTP,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SEND_OTP_REQUEST,
        ACTION_TYPES.SEND_OTP_SUCCESS,
        ACTION_TYPES.SEND_OTP_FAILURE
      ],
      params
    }
  };
};

export const verifyOtp = (data) => {
  const { inwardId, params } = data;
  return {
    url: API_URL.CITIZEN.VERIFY_OTP.replace(':inwardId', inwardId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.VERIFY_OTP_REQUEST,
        ACTION_TYPES.VERIFY_OTP_SUCCESS,
        ACTION_TYPES.VERIFY_OTP_FAILURE
      ],
      params
    }
  };
};

export const resubmitEfile = (data) => {
  return {
    url: API_URL.CITIZEN.RESUBMIT_EFILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.RESUBMIT_EFILE_REQUEST,
        ACTION_TYPES.RESUBMIT_EFILE_SUCCESS,
        ACTION_TYPES.RESUBMIT_EFILE_FAILURE
      ],
      data
    }
  };
};

export const createFile = (data) => {
  return {
    url: API_URL.INWARD.CREATE_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.CREATE_FILE_REQUEST,
        ACTION_TYPES.CREATE_FILE_SUCCESS,
        ACTION_TYPES.CREATE_FILE_FAILURE
      ],
      data
    }
  };
};

export const fetchInwards = (data) => {
  return {
    url: API_URL.INWARD.FETCH_INWARD.replace('?query', data.data ? `${data?.data}&status=${data?.status}&sortDirection=desc&fileCreatedFlag=false&officeId=${data?.officeId}` : `?status=${data?.status}&sortDirection=desc&fileCreatedFlag=false&officeId=${data?.officeId}`),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INWARDS_REQUEST,
        ACTION_TYPES.FETCH_INWARDS_SUCCESS,
        ACTION_TYPES.FETCH_INWARDS_FAILURE
      ]
    }
  };
};
