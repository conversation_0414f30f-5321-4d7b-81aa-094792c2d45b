import {
  t,
  Input, InputGroup, InputRightElement, IconButton
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import TableView from 'assets/TableView';
import { DATE_FORMAT, FILTER_TYPE } from 'pages/common/constants';
import SearchIcon from 'assets/SearchIcon';
import {
  convertToLocalDate
} from 'utils/date';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { dark } from 'utils/color';
import BackArrow from 'assets/BackIcon';
import { AUDIT_APPLICATION_PATH, BASE_UI_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { STATUS } from 'common/regex';
// import ReOpenReasonModal from 'pages/file/details/components/summaryDetails/ReOpenReasonModal';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import { getArchivedListData, getArchivedSearchListParams } from '../selectors';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    paddingRight: '9px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const List = ({
  userInfo, fetchArchivedTableList, setArchivedTableListParams, archivedTableList, archivedListParams,
  setTableLoader, tableLoader
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [search, setSearch] = useState('');
  const [date, setDate] = useState();
  const today = new Date().toISOString().split('T')[0];
  const [numberOfElements, setNumberOfElements] = useState(0);
  // const [reOpenModal, setReOpenModal] = useState(false);
  // const [fileNo, setFileNo] = useState(null);

  const viewActions = (data) => {
    sessionStorage.setItem('file-nav-history', 'archived-file');
    sessionStorage.setItem('file-nav-history-payload', JSON.stringify(archivedListParams));
    window.location = `${BASE_UI_PATH}${data?.url}`;
  };

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      status = (
        <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
          {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
        </div>
      );
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNumber = (
        <>
          <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
          <div className="text-[14px] font-[700] text-[#B5B5B5]">{cellData?.fileDate}</div>
        </>
      );
    }
    return <div className="block">{fileNumber}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.applicantName}</div>
      );
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleForwardedBy = (fileData) => {
    let forwardedBy;
    if (fileData?.row) {
      const cellData = fileData?.row;
      forwardedBy = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px]">{cellData?.forwardedBy}</div>;
    }
    return <div className="block">{forwardedBy}</div>;
  };

  const actionsTable = (row) => {
    return (
      <div className="flex gap-3">
        <IconButton variant="ghost" onClick={() => viewActions(row?.row)} icon={<TableView />} />
        {/* <Button variant="secondary_outline" size="xs" onClick={() => { setReOpenModal(true); setFileNo(row?.row?.fileNo); }}>
          {(t('reOpen'))}
        </Button> */}
      </div>
    );
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('nameOfApplicant'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('service'),
      field: 'serviceName',
      alignment: 'left',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('status'),
      field: 'currentStage',
      cell: (field) => handleFileStatus(field)
    },
    {
      header: t('forwardedBy'),
      field: 'forwardedBy',
      alignment: 'left',
      cell: (field) => handleForwardedBy(field)
    },
    {
      header: t('Actions'),
      alignment: 'left',
      cell: (field) => actionsTable(field)
    }

  ];

  useEffect(() => {
    if (sessionStorage.getItem('file-nav-history-payload')) {
      setArchivedTableListParams({
        ...JSON.parse(sessionStorage.getItem('file-nav-history-payload'))
      });
      const values = JSON.parse(sessionStorage.getItem('file-nav-history-payload'));
      setSearch(values?.keyword);
      setDate(convertToLocalDate(values?.createdAt, DATE_FORMAT.LOCAL_DATE_REVERSE));
      sessionStorage.removeItem('file-nav-history-payload');
    }
  }, [sessionStorage.getItem('file-nav-history-payload')]);

  useEffect(() => {
    if (archivedListParams && !sessionStorage.getItem('file-nav-history-payload')) {
      setTableLoader({ loading: true, id: 'archived-file-table' });
      fetchArchivedTableList();
    }
  }, [archivedListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setArchivedTableListParams({
      ...archivedListParams,
      page: data,
      officeId: userInfo.id
    });
  };

  useEffect(() => {
    if (archivedTableList) {
      setTableLoader({ loading: false, id: 'archived-file-table' });
      if (Object.keys(archivedTableList).length > 0) {
        setTableData(archivedTableList.content);
        setTotalItems(Number(`${archivedTableList.totalPages}0`));
        setNumberOfElements(Number(archivedTableList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [archivedTableList]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setArchivedTableListParams({
          ...archivedListParams,
          keyword: data || null,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setArchivedTableListParams({
          ...archivedListParams,
          createdAt: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          page: 0
        });
        setDate(data);
        break;
      default:
        break;
    }
  };

  const backToHome = () => {
    window.location.href = userInfo?.userDetails?.isAuditor ? AUDIT_APPLICATION_PATH : EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('archivedFile')}
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
              max={today}
            />
          </InputGroup>
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'archived-file-table'}
        />
        {/* <ReOpenReasonModal open={reOpenModal} setReOpenModal={setReOpenModal} fileNo={fileNo} /> */}

      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  archivedTableList: getArchivedListData,
  archivedListParams: getArchivedSearchListParams,
  tableLoader: getTableLoader

});

const mapDispatchToProps = (dispatch) => ({
  fetchArchivedTableList: (data) => dispatch(actions.fetchArchivedTableList(data)),
  setArchivedTableListParams: (data) => dispatch(sliceActions.setArchivedTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
