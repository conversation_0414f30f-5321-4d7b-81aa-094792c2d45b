import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  archivedTableListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    moduleCode: null,
    submoduleCode: null,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    fileStatus: FILE_STATUS_FOR_API_PARAMS.CLOSED
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setArchivedTableList: (state, { payload }) => {
      _.set(state, 'archivedTableList', payload);
    },
    setArchivedTableListParams: (state, { payload }) => {
      _.set(state, 'archivedTableListParams', payload);
    }
  }
});

export const { actions, reducer } = slice;
