import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_ARCHIVED_LIST: `${STATE_REDUCER_KEY}/FETCH_ARCHIVED_LIST`,
  FETCH_ARCHIVED_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ARCHIVED_LIST_REQUEST`,
  FETCH_ARCHIVED_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ARCHIVED_LIST_SUCCESS`,
  FETCH_ARCHIVED_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ARCHIVED_LIST_FAILURE`

};
export const fetchArchivedTableList = createAction(ACTION_TYPES.FETCH_ARCHIVED_LIST);
