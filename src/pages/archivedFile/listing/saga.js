import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import { getArchivedSearchListParams } from './selectors';

export function* fetchArchivedTableList() {
  const apiParams = yield select(getArchivedSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchArchivedTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_ARCHIVED_LIST_SUCCESS,
    ACTION_TYPES.FETCH_ARCHIVED_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_ARCHIVED_LIST_SUCCESS) {
    yield put(sliceActions.setArchivedTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'archived-file-table' }));
  }
}

export default function* counterSaga() {
  yield all([

    takeLatest(ACTION_TYPES.FETCH_ARCHIVED_LIST, fetchArchivedTableList)
  ]);
}
