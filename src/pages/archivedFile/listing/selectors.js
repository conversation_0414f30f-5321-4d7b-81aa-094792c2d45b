import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getArchivedDetails = (state) => state[STATE_REDUCER_KEY];

const archivedListData = (state) => state?.archivedTableList;
export const getArchivedListData = flow(getArchivedDetails, archivedListData);

const archivedSearchListParams = (state) => state?.archivedTableListParams;
export const getArchivedSearchListParams = flow(getArchivedDetails, archivedSearchListParams);
