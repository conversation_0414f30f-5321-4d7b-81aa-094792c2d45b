import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  inwardSearchListParams: {
    sortDirection: 'desc',
    page: 0,
    size: 10,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    stageNotIn: ['partial', 'discarded']
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setInwardSearchList: (state, { payload }) => {
      _.set(state, 'inwardSearchList', payload);
    },
    setInwardSearchListParams: (state, { payload }) => {
      _.set(state, 'inwardSearchListParams', payload);
    }
  }
});

export const { actions, reducer } = slice;
