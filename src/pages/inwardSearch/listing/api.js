import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchInwardSearchFiles = (params) => {
  return {
    url: API_URL.INWARD_SEARCH.FETCH_INWARD_SEARCH_LIST.replace('?query', params),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INWARD_SEARCH_FILES_REQUEST,
        ACTION_TYPES.FETCH_INWARD_SEARCH_FILES_SUCCESS,
        ACTION_TYPES.FETCH_INWARD_SEARCH_FILES_FAILURE
      ]
    }
  };
};
