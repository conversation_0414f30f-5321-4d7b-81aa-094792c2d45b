import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getSearch = (state) => state[STATE_REDUCER_KEY];

const inwardSearchList = (state) => state?.inwardSearchList;
export const getInwardSearchList = flow(getSearch, inwardSearchList);

const inwardSearchListParams = (state) => state?.inwardSearchListParams;
export const getInwardSearchListParams = flow(getSearch, inwardSearchListParams);
