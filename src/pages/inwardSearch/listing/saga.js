import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { getInwardSearchListParams } from './selectors';

export function* fetchInwardSearchFiles() {
  const apiParams = yield select(getInwardSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const {
    page, size, sortDirection, officeId, stageNotIn, inwardDate, searchKeyword
  } = updatedParams;

  let stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&stageNotIn=${stageNotIn[0]}&stageNotIn=${stageNotIn[1]}`;

  if (inwardDate) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&stageNotIn=${stageNotIn[0]}&stageNotIn=${stageNotIn[1]}&inwardDate=${inwardDate}`;
  }
  if (searchKeyword) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&stageNotIn=${stageNotIn[0]}&stageNotIn=${stageNotIn[1]}&searchKeyword=${searchKeyword}`;
  }

  if (searchKeyword && inwardDate) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&stageNotIn=${stageNotIn[0]}&stageNotIn=${stageNotIn[1]}&searchKeyword=${searchKeyword}&inwardDate=${inwardDate}`;
  }

  yield fork(handleAPIRequest, api.fetchInwardSearchFiles, stringfiedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_INWARD_SEARCH_FILES_SUCCESS,
    ACTION_TYPES.FETCH_INWARD_SEARCH_FILES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_INWARD_SEARCH_FILES_SUCCESS) {
    yield put(sliceActions.setInwardSearchList(responsePayLoad?.data));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'inward-search-table' }));
  }
}

export default function* applicationsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_INWARD_SEARCH_FILES, fetchInwardSearchFiles)

  ]);
}
