import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_INWARD_SEARCH_FILES: `${STATE_REDUCER_KEY}/FETCH_INWARD_SEARCH_FILES`,
  FETCH_INWARD_SEARCH_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INWARD_SEARCH_FILES_REQUEST`,
  FETCH_INWARD_SEARCH_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INWARD_SEARCH_FILES_SUCCESS`,
  FETCH_INWARD_SEARCH_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INWARD_SEARCH_FILES_FAILURE`

};

export const fetchInwardSearchFiles = createAction(ACTION_TYPES.FETCH_INWARD_SEARCH_FILES);
