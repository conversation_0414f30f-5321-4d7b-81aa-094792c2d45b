import SearchIcon from 'assets/SearchIcon';

import {
  IconButton, Input, InputGroup, InputRightElement, t,
  Tooltip
} from 'common/components';

import { CommonTable } from 'common/components/Table';
import { actions as commonSliceActions } from 'pages/common/slice';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { convertToLocalDate } from 'utils/date';
import { DATE_FORMAT, FILTER_TYPE } from 'pages/common/constants';
import { getTableLoader } from 'pages/common/selectors';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { Text } from '@ksmartikm/ui-components';
import * as actions from '../actions';
import { getInwardSearchList, getInwardSearchListParams } from '../selectors';
import { actions as sliceActions } from '../slice';

const styles = {
  head: {
    fontSize: '18px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    paddingRight: '10px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #CBD5E0',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '120px'
  },
  sort: {
    display: 'flex'
  }
};

const List = ({
  fetchInwardSearchFiles,
  searchFileData,
  setInwardSearchListParams,
  inwardSearchFileParams,
  setTableLoader,
  tableLoader
}) => {
  const activeRows = [{}];
  const [search, setSearch] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [date, setDate] = useState();
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData.row;
      applicantName = (
        <div className="text-[15px] font-[400] text-[#454545] max-w-[200px] break-keep">
          {
            cellData.applicantNames?.filter((item) => item && item !== 'null')?.join(', ')
            || cellData.applicantInstitutionName?.filter((item) => item && item !== 'null')?.join(', ') || cellData?.createdUser
          }
        </div>
      );
    }

    return <div className="block">{applicantName}</div>;
  };

  const getCurrentUser = (val) => {
    let newVal;
    if (val?.row) {
      const cellData = val?.row;
      if (cellData.mappedUsers?.length > 0) {
        newVal = cellData.mappedUsers?.includes('Employee not mapped') || cellData.mappedUsers?.includes('Employee not mapped.')
          ? (
            <div className="rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
              {t('employeeNotMapped')}
            </div>
          )
          : (
            <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep leading-[25px]">{cellData.mappedUsers?.filter((item) => item && item !== 'null(null)')?.join(', ')}</div>
          );
      }
    }
    return newVal;
  };

  const handleCurrentUser = (val) => {
    const currentUser = getCurrentUser(val);
    return <div className="inline-block">{currentUser}</div>;
  };

  const handleInwardNumber = (val) => {
    let inwardNumber;
    if (val?.row) {
      const cellData = val?.row;
      inwardNumber = (
        <div className="text-[15px] font-[400] text-[#454545] max-w-[200px] break-keep">
          {cellData?.inwardNo || cellData?.applicationNumber}
        </div>
      );
    }
    return <div className="inline-block">{inwardNumber}</div>;
  };

  const handleInwardDate = (val) => {
    let inwardDate;
    if (val?.row) {
      const cellData = val?.row;
      inwardDate = (
        <div className="text-[15px] font-[400] text-[#454545] max-w-[200px] break-keep">
          {cellData?.inwardDate}
        </div>
      );
    }
    return <div className="inline-block">{inwardDate}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleTitleName = (fileData) => {
    let titleName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      titleName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">
          <Tooltip
            label={cellData.title && cellData.title.length > 25 ? cellData.title : ''}
            placement="top"
          >
            <Text lineClamp={1} isTruncated>
              {cellData.title}
            </Text>
          </Tooltip>
        </div>
      );
    }
    return <div className="block">{titleName}</div>;
  };

  const handleReferenceNos = (reqData) => {
    let referenceNos;
    if (reqData?.row) {
      const cellData = reqData?.row;
      referenceNos = (
        <div className="text-[15px] font-[400] text-[#454545] max-w-[200px] break-keep">
          {
            cellData.referenceNos?.filter((item) => item && item !== 'null')?.join(', ')
          }
        </div>
      );
    }
    return <div className="block">{referenceNos}</div>;
  };

  const handleFileNo = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const columns = [

    {
      header: t('inwardNumber'),
      alignment: 'left',
      field: 'inwardNo',
      cell: (field) => handleInwardNumber(field)
    },
    {
      header: t('inwardDate'),
      alignment: 'left',
      field: 'inwardDate',
      cell: (field) => handleInwardDate(field)
    },
    {
      header: t('fileNo'),
      alignment: 'left',
      field: 'fileNo',
      cell: (field) => handleFileNo(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantNames',
      cell: (field) => handleApplicantName(field)
    },

    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('title'),
      alignment: 'left',
      field: 'title',
      cell: (field) => handleTitleName(field)
    },
    {
      header: t('referenceNos'),
      alignment: 'left',
      field: 'referenceNos',
      cell: (field) => handleReferenceNos(field)
    },
    {
      header: t('custodian'),
      alignment: 'left',
      field: 'currentUser',
      cell: (field) => handleCurrentUser(field)
    }
    // {
    //   header: t('stage'),
    //   alignment: 'left',
    //   field: 'stage',
    //   cell: (field) => handleFileStatus(field)
    // }
  ];
  const onPageClick = (data) => {
    setPage(data);
    setInwardSearchListParams({
      ...inwardSearchFileParams,
      page: data
    });
  };

  const triggerSearch = (field, data) => {
    setPage(0);

    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setInwardSearchListParams({
          ...inwardSearchFileParams,
          searchKeyword: data || null,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setInwardSearchListParams({
          ...inwardSearchFileParams,
          inwardDate: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          page: 0
        });
        setDate(data);

        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (inwardSearchFileParams) {
      setTableLoader({ loading: true, id: 'inward-search-table' });
      fetchInwardSearchFiles();
    }
  }, [inwardSearchFileParams]);

  useEffect(() => {
    if (searchFileData) {
      setTableLoader({ loading: false, id: 'inward-search-table' });
      if (Object.keys(searchFileData).length > 0) {
        setTableData(searchFileData.content);
        setTotalItems(Number(`${searchFileData.totalPages}0`));
        setNumberOfElements(Number(searchFileData.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [searchFileData]);

  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center">
        <div className="flex-none">
          <IconButton
            onClick={backToHome}
            variant="unstyled"
            icon={<BackArrow color={dark} width="12" height="12" />}
          />
        </div>
        <div className="grow-[1]">
          <h4 style={styles.head}>
            <strong>{t('inwardSearch')}</strong>
          </h4>
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              name="keyword"
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
            />
          </InputGroup>
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'inward-search-table'}
        />
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  searchFileData: getInwardSearchList,
  inwardSearchFileParams: getInwardSearchListParams,
  tableLoader: getTableLoader
});
const mapDispatchToProps = (dispatch) => ({
  fetchInwardSearchFiles: (data) => dispatch(actions.fetchInwardSearchFiles(data)),
  setInwardSearchListParams: (data) => dispatch(sliceActions.setInwardSearchListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
