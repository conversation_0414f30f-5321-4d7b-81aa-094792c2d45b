import {
  all, takeLatest, call, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getSidebarData } from 'pages/common/selectors';
import { Toast, t } from 'common/components';
import { MODULE_PATH } from 'common/constants';
import { fetchApplication } from 'pages/counter/new/saga';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { ARISING_APPLICATION_KEYS } from './constants';

const { successTost, errorTost } = Toast;

export function* saveApplication({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveApplicationServiceApi, payload);
  const formComponentData = yield select(getSidebarData);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.SAVE_APPLICATION_SERVICE_FAILURE]);
  if (type === ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS) {
    yield put(commonSliceActions.setActiveAccordian(ARISING_APPLICATION_KEYS.Service));
    yield put(commonSliceActions.setSidebarStatus({ activeStep: 1, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    yield call(successTost, { id: 'saved', title: t('success'), description: 'success' });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'arisingServiceCreate' }));
    yield put(commonSliceActions.navigateTo({ to: `${MODULE_PATH}/arising/${responsePayLoad?.data}` }));
  }
}

export function* updateApplication({ payload = {} }) {
  yield fork(handleAPIRequest, api.updateApplicationServiceApi, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.UPDATE_APPLICATION_SERVICE_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_APsPLICATION_SERVICE_SUCCESS) {
    yield put(sliceActions.setActiveArisingFormData(responsePayLoad));
  }
}

export function* saveDocuments({ payload = {} }) {
  const { supportingDocs, request, handleClear = () => { } } = payload;
  const formData = new FormData();
  formData.append('supportingDocs', supportingDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));

  yield fork(handleAPIRequest, api.saveDocuments, formData);
  const { type } = yield take([
    ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS) {
    handleClear();
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'arising-supporting-doc-upload' }));
    yield call(fetchApplication, { payload: request.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'arising-supporting-doc-upload' }));
  }
}

export function* saveMandatoryDocuments({ payload = {} }) {
  const { documentTypeDocs, request } = payload;
  const formData = new FormData();
  formData.append('documentTypeDocs', documentTypeDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));

  yield fork(handleAPIRequest, api.saveMandatoryDocuments, formData);
  const { type } = yield take([
    ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'arising-mandatory-doc-upload' }));
    yield call(fetchApplication, { payload: request.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'arising-mandatory-doc-upload' }));
  }
}

export function* saveComplete({ payload = {} }) {
  const formComponentData = yield select(getSidebarData);
  yield fork(handleAPIRequest, api.saveComplete, payload);
  const { type } = yield take([
    ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
    ACTION_TYPES.COMPLETE_SAVE_FAILURE]);
  if (type === ACTION_TYPES.COMPLETE_SAVE_SUCCESS) {
    yield call(successTost, { id: 'saved', title: 'Success', description: 'success' });
    yield put(sliceActions.setActiveArisingFormData({}));
    yield put(sliceActions.setActiveInwardId(''));
    yield put(commonSliceActions.setServicesSearchList([]));
    yield put(commonSliceActions.clearDropdown());
    yield put(commonSliceActions.setActiveAccordian(ARISING_APPLICATION_KEYS.Service));
    yield put(commonSliceActions.setSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'arisingCreate' }));
    window.location.href = `${MODULE_PATH}/arising`;
  }
}

export function* updateDocuments({ payload: { requestData = {}, handleClear = () => { } } }) {
  yield fork(handleAPIRequest, api.updateDocuments, requestData);
  const { type } = yield take([
    ACTION_TYPES.UPDATE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.UPDATE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_DOCUMENTS_SUCCESS) {
    handleClear();
    yield call(fetchApplication, { payload: requestData?.inwardId });
    yield call(successTost, { id: 'saved', title: t('success'), description: t('documentsUpdatedSuccessfully') });
    yield put(commonSliceActions.setActionTriggered({ loading: false }));
  } else {
    yield call(errorTost, { id: 'failed', title: t('failed'), description: t('documentsUpdateFailed') });
    yield put(commonSliceActions.setActionTriggered({ loading: false }));
  }
}

export default function* arisingSaga() {
  yield all([
    takeLatest(ACTION_TYPES.SAVE_APPLICATION_SERVICE, saveApplication),
    takeLatest(ACTION_TYPES.UPDATE_APPLICATION_SERVICE, updateApplication),
    takeLatest(ACTION_TYPES.SAVE_DOCUMENTS, saveDocuments),
    takeLatest(ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS, saveMandatoryDocuments),
    takeLatest(ACTION_TYPES.COMPLETE_SAVE, saveComplete),
    takeLatest(ACTION_TYPES.UPDATE_DOCUMENTS, updateDocuments)
  ]);
}
