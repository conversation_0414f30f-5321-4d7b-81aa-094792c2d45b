import React, { useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, t, Button
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  getFileTypeDropdown,
  getSubModule,
  getUserInfo,
  getActionTriggered,
  getModuleById,
  getServiceDfmsDropdown
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { FILTER_TYPE } from 'pages/common/constants';
import * as actionsCounter from 'pages/counter/new/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getServiceByCodeDetails } from 'pages/counter/new/selectors';
import * as actions from '../actions';
import { ServiceFormSchema } from '../validate';

const Services = (props) => {
  const {
    fetchDfmsServices,
    fetchSubModuleById,
    fetchFileTypeOptions,
    fileTypeDropdown,
    saveApplicationService,
    subModuleByIdData,
    moduleById,
    formData,
    fetchServiceByServiceCode,
    serviceDfmsDropdown,
    userInfo,
    actionTriggered,
    setActionTriggered,
    serviceByCodeDetails,
    fetchModuleById
  } = props;

  const params = useParams();

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue, reset
  } = useForm({
    mode: 'all',
    defaultValues: {
      services: '',
      modules: '',
      subModule: '',
      fileType: 1,
      title: '',
      discription: '',
      serviceCode: ''
    },
    resolver: yupResolver(ServiceFormSchema)
  });

  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'arisingServiceCreate' });
    const saveData = {

      services: {
        serviceCode: data?.services,
        fileType: Number(data?.fileType),
        title: data?.title,
        description: data?.description,
        module: data?.modules,
        subModule: data?.subModule,
        filingMode: 'arising'
      },
      userInfo: {
        officeId: userInfo?.id
      }

    };
    saveApplicationService(saveData);
  };

  useEffect(() => {
    fetchDfmsServices('serviceTypes=1,2&inputTemplateType=1&eFiling=0');
    fetchFileTypeOptions();
  }, []);

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.SERVICES:
        if (data) {
          fetchSubModuleById(data.subModuleId);
          fetchModuleById(data.moduleId);
          setValue('services', data.serviceCode);
          setValue('serviceCode', data.serviceCode);
        } else {
          setValue('subModule', '');
          setValue('modules', '');
          setValue('serviceCode', null);
        }
        break;
      case 'serviceCode':
        setValue('serviceCode', data.target.value.toUpperCase());
        if (data.target.value.length > 3) {
          fetchServiceByServiceCode(data.target.value);
          setValue('subModule', '');
          setValue('modules', '');
          setValue('services', '');
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (subModuleByIdData) {
      if (Object.keys(subModuleByIdData).length > 0) {
        setValue('subModule', subModuleByIdData?.length > 0 ? subModuleByIdData[0]?.name : subModuleByIdData?.name);
      }
    } else {
      setValue('subModule', '');
    }
  }, [subModuleByIdData]);

  useEffect(() => {
    if (moduleById) {
      if (Object.keys(moduleById).length > 0) {
        setValue('modules', moduleById?.name);
      }
    } else {
      setValue('modules', '');
    }
  }, [moduleById]);

  useEffect(() => {
    if (!_.isEmpty(formData)) {
      if (formData?.inwardId) {
        setValue('services', formData?.serviceCode);
        fetchServiceByServiceCode(formData?.serviceCode);
        setValue('title', formData?.title);
        setValue('description', formData?.description);
        setValue('fileType', formData?.fileType);
      }
    } else {
      reset({
        services: '',
        modules: '',
        subModule: '',
        fileType: 1,
        title: '',
        description: ''
      });
    }
  }, [JSON.stringify(formData)]);
  useEffect(() => {
    if (serviceDfmsDropdown?.length > 0) {
      fetchSubModuleById(serviceDfmsDropdown[0]?.subModuleId);
    }
  }, [serviceDfmsDropdown]);

  useEffect(() => {
    if (serviceByCodeDetails?.length > 0) {
      fetchSubModuleById(serviceByCodeDetails[0]?.subModuleId);
      fetchModuleById(serviceByCodeDetails[0]?.moduleId);
      setValue('services', serviceByCodeDetails[0]?.code);
      setValue('serviceCode', serviceByCodeDetails[0]?.code);
    }
  }, [serviceByCodeDetails]);

  return (
    <form
      id="service-form"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <FormWrapper>

        <div className="lg:col-span-8 md:col-span-8 col-span-12">
          <FormController
            name="services"
            type="select"
            label={t('services')}
            placeholder={t('searchHere')}
            control={control}
            errors={errors}
            options={_.get(serviceDfmsDropdown, 'data', [])}
            handleChange={(data) => {
              handleFieldChange(FILTER_TYPE.SERVICES, data);
            }}
            required
            optionKey="serviceCode"
            isDisabled={params?.id}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="serviceCode"
            type="text"
            label={t('serviceCode')}
            control={control}
            errors={errors}
            handleChange={(data) => handleFieldChange('serviceCode', data)}
            required
            readOnly
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="modules"
            type="text"
            label={t('module')}
            placeholder={t('module')}
            control={control}
            errors={errors}
            disabled
            handleChange={(data) => handleFieldChange(FILTER_TYPE.MODULES, data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="subModule"
            type="text"
            label={t('subModule')}
            placeholder={t('subModule')}
            control={control}
            errors={errors}
            disabled
            handleChange={(data) => handleFieldChange(FILTER_TYPE.SUB_MODULES, data)}
          />
        </div>
        <div className="lg:col-span-4 md:col-span-6 col-span-12">
          <FormController
            name="fileType"
            type="select"
            label={t('priority')}
            placeholder={t('priority')}
            control={control}
            errors={errors}
            optionKey="id"
            options={_.get(fileTypeDropdown, 'data', [])}
            defaultValue="1"
            required
          />
        </div>
        <div className="lg:col-span-12 md:col-span-8 col-span-12">
          <FormController
            name="title"
            type="text"
            label={t('title')}
            placeholder={t('title')}
            control={control}
            errors={errors}
          />
        </div>
        {/* <div className="lg:col-span-12 md:col-span-8 col-span-12">
          <FormController
            name="description"
            type="textarea"
            label={t('description')}
            style={{ height: '100px', lineHeight: '30px' }}
            placeholder={t('description')}
            control={control}
            errors={errors}
          />
        </div> */}
        <div className="col-span-12 text-right">
          <Button
            type="submit"
            variant="secondary_outline"
            className="shadow-md"
            form="service-form"
            isLoading={actionTriggered?.id === 'arisingServiceCreate' && actionTriggered?.loading}
          >
            {t('proceed')}
          </Button>
        </div>
      </FormWrapper>

    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  serviceDfmsDropdown: getServiceDfmsDropdown,
  fileTypeDropdown: getFileTypeDropdown,
  subModuleByIdData: getSubModule,
  moduleById: getModuleById,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  serviceByCodeDetails: getServiceByCodeDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchDfmsServices: (data) => dispatch(commonActions.fetchDfmsServices(data)),
  fetchSubModuleById: (data) => dispatch(commonActions.fetchSubModuleById(data)),
  fetchModuleById: (data) => dispatch(commonActions.fetchModuleById(data)),
  fetchFileTypeOptions: () => dispatch(commonActions.fetchFileTypeDetails()),
  saveApplicationService: (data) => dispatch(actions.saveApplicationService(data)),
  fetchServiceByServiceCode: (data) => dispatch(actionsCounter.fetchServiceByServiceCode(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Services);
