import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, t, To<PERSON>, <PERSON>rrorTex<PERSON>,
  But<PERSON>
} from 'common/components';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { convertToLocalDate, reFormattedDate } from 'utils/date';
import { dark, light } from 'utils/color';
import PreviewThumbnail from 'common/components/DocumentPreview/previewThumbnail';
import { DATE_FORMAT } from 'pages/common/constants';
import {
  getActionTriggered, getAllDocumentsType, getServiceValidation, getSidebarData, getUserInfo
} from 'pages/common/selectors';
import { documenturl } from 'pages/counter/new/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as counterSliceActions } from 'pages/counter/new/slice';
import * as commonActions from 'pages/common/actions';
import * as counterActions from 'pages/counter/new/actions';
import { STORAGE_KEYS } from 'common/constants';
import { yupResolver } from '@hookform/resolvers/yup';
import * as actions from '../actions';
import { ARISING_APPLICATION_KEYS } from '../constants';
import { DocumentDetailsFormSchema } from '../validate';

const Documents = (props) => {
  const [existingSupportingDocs, setExistingSupportingDocs] = useState([]);
  const params = useParams();
  const [tempArray, setTempArray] = useState([]);
  const [docPreview, setDocPreview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [documentsIds, setDocumentsIds] = useState([]);
  const [supportingValidateShow, setSupportingValidateShow] = useState('');
  const [selectedSupportingDocs, setSelectedSupportingDocs] = useState(null);
  const { errorTost } = Toast;

  const {
    documentTypeDropdown,
    formData,
    fecthAllDocumentTypes,
    fetchApplication,
    saveDocuments,
    deleteDocuments,
    userInfo,
    setActiveAccordian,
    setArisingApplicationSidebarStatus,
    formComponentData,
    servicevalidation,
    setRequiredDocsCount,
    setActionTriggered,
    actionTriggered,
    updateDocuments
  } = props;

  const {
    control, formState: { errors }, setValue, getValues, watch, reset, handleSubmit
  } = useForm({
    mode: 'all',
    defaultValues: {
      supportdocs: '',
      documents: '',
      documenttype: ''
    },
    resolver: yupResolver(DocumentDetailsFormSchema)
  });

  useEffect(() => {
    if (formData) {
      if (formData?.supportingDocs) {
        if (formData?.supportingDocs?.length > 0) {
          setExistingSupportingDocs(formData?.supportingDocs);
          setValue('supportingDocuments', 'set');
        } else {
          setExistingSupportingDocs([]);
          setValue('supportingDocuments', []);
        }
      }
    }
  }, [formData]);

  useEffect(() => {
    if (servicevalidation) {
      setDocumentsIds(servicevalidation?.documentId);
    }
  }, [servicevalidation]);

  const formateDocType = (response) => {
    setTempArray([]);
    const formattedResponse = response?.length > 0 && response?.map((item) => ({
      id: item?.id,
      name: item?.name,
      nameInLocal: item?.nameInLocal,
      data: []
    }));
    if (formData) {
      const documentsTypeUpload = formData?.documentsTypeUpload;
      if (documentsTypeUpload) {
        if (documentsTypeUpload?.length > 0) {
          for (let i = 0; i < documentsTypeUpload?.length; i += 1) {
            const findFileTypeIndex = formattedResponse.findIndex((item) => item.id === parseInt(documentsTypeUpload[i]?.documentType, 10));
            const exist = formattedResponse[findFileTypeIndex] ? JSON.parse(JSON.stringify(formattedResponse[findFileTypeIndex]?.data)) : [];
            const newData = [documentsTypeUpload[i]];
            const merge = [...exist, ...newData];
            formattedResponse[findFileTypeIndex].data = merge;
          }
        }
      }
    }

    setTempArray(formattedResponse);
  };

  useEffect(() => {
    if (documentsIds.length > 0) {
      if (documentsIds[0] !== 0) {
        const sortedDocs = documentsIds?.map((ids) => {
          const sortedIds = documentTypeDropdown.find((item) => item.id === ids);
          return sortedIds;
        });
        setRequiredDocsCount(sortedDocs.length);
        if (sortedDocs) {
          formateDocType(sortedDocs);
        }
      }
    } else {
      setRequiredDocsCount(0);
    }
  }, [documentTypeDropdown, formData, documentsIds]);

  useEffect(() => {
    if (params.id) {
      fetchApplication(params.id);
      fecthAllDocumentTypes();
    }
  }, [params.id]);

  useEffect(() => {
    fecthAllDocumentTypes();
  }, []);

  const handleFieldChange = (field, data, index) => {
    switch (field) {
      case 'supportDocumentsName':
        setSupportingValidateShow(false);
        break;
      case 'supportDocumentType':
        setValue('supportDocumentType', data?.id);
        if (!data?.name.includes('Other')) {
          setValue('supportDocumentsName', data?.name); // supportDocumentType is not other so supportDocumentsName and supportDocumentType are same
          setSupportingValidateShow(false);
        } else {
          setValue('supportDocumentsName', ''); // supportDocumentType is other so supportDocumentsName and supportDocumentType are not same
          setSupportingValidateShow(false);
        }
        break;
      case 'docNo':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].documentNo = (data); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      case 'issueDate':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].issueDate = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      case 'validUpto':
        setTempArray((item) => {
          const newArray = [...tempArray]; // Create a copy of the original array
          if (item[index]) {
            newArray[index].validUpto = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL); // Push 'data' into the 'documentNo' array at the specified index
          }
          return newArray; // Return the updated array
        });
        break;
      default:
        break;
    }
  };

  const handleClear = () => {
    setValue('supportDocumentsName', null);
    setValue('supportDocumentType', null);
    setValue('issueDate', null);
    setValue('validUpto', null);
    setValue('supportingDocuments', null);
  };

  const onFileSelected = (data) => {
    if (params?.id) {
      if (data) {
        if (getValues('supportDocumentsName') === '' || getValues('supportDocumentsName') === undefined) {
          setSupportingValidateShow(true);
        } else {
          setSupportingValidateShow(false);
          const saveData = {
            inwardId: params.id,
            documentNo: params.id,
            documentName: getValues('supportDocumentsName'),
            issueDate: convertToLocalDate(getValues('issueDate'), DATE_FORMAT.DATE_LOCAL),
            validUpto: convertToLocalDate(getValues('validUpto'), DATE_FORMAT.DATE_LOCAL),
            documentTypeId: getValues('supportDocumentType'),
            hardCopyReceived: getValues('haveHardCopy'),
            userInfo: {
              officeId: userInfo?.id
            }
          };
          setActionTriggered({ loading: true, id: 'counter-supporting-doc-upload' });
          saveDocuments({ supportingDocs: data, request: saveData, handleClear });
          setValue('supportDocumentsName', '');
          setValue('documentType', '');
          setValue('haveHardCopy', false);
          setValue('activeIdEdit', null);
        }
      } else {
        setSupportingValidateShow(false);
        setValue('supportingDocuments', null);
      }
    } else {
      errorTost({
        title: t('missingSections'),
        description: `${t('pleaseComplete')} ${t('the')} ${t('servicee')} ${t('section')}`
      });
      setValue('supportingDocuments', null);
      setActiveAccordian(ARISING_APPLICATION_KEYS.Service);
      setArisingApplicationSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
    }
  };

  const onHandleRemove = (data) => {
    const sendData = {
      inwardId: params?.id,
      documentTypeId: Number(data?.documentType),
      fileId: data?.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    deleteDocuments(sendData);
  };

  function getDocument(url, token, body) {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  }

  const handlePreview = (data, type, typeId) => {
    const sendData = {
      inwardId: params.id,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: data.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };

    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };

  const onHandleEdit = (data, type) => {
    if (type === 2) {
      setValue('activeIdEdit', data?.id);
      setValue('supportDocumentsName', data.documentName);
      setValue('supportDocumentType', data.documentTypeId);
      setValue('issueDate', reFormattedDate(data.issueDate));
      setValue('validUpto', reFormattedDate(data.validUpto));
      setValue('haveHardCopy', data.hardCopyReceived);
    }
  };

  const handleCancelUpdate = () => {
    reset({
      supportDocumentsName: '',
      supportingDocuments: '',
      documents: '',
      documentType: '',
      issueDate: '',
      validUpto: '',
      documentNo: '',
      haveHardCopy: false,
      activeIdEdit: null
    });
  };

  const handleSupportDoc = (data) => {
    setSelectedSupportingDocs(data);
  };

  const onSubmitForm = () => {
    if (watch('activeIdEdit')) {
      const sendData = {
        inwardId: params?.id,
        documentTypeId: getValues('supportDocumentType'),
        fileId: watch('activeIdEdit'),
        isSupportingDoc: true,
        issueDate: convertToLocalDate(getValues('issueDate'), DATE_FORMAT.DATE_LOCAL),
        validUpto: convertToLocalDate(getValues('validUpto'), DATE_FORMAT.DATE_LOCAL),
        hardCopyReceived: getValues('haveHardCopy'),
        userInfo: {
          officeId: userInfo?.id
        },
        documentName: getValues('supportDocumentsName')
      };
      setActionTriggered({ loading: true, id: 'counter-supporting-doc-upload' });
      updateDocuments({ requestData: sendData, handleClear });
    } else {
      onFileSelected(selectedSupportingDocs);
    }
  };

  return (
    <>
      <div id="arising_documents" />
      <FormWrapper>

        <div className="col-span-12 border border-[#e8ecee] rounded-[12px] p-[20px]">
          <form
            onSubmit={handleSubmit(onSubmitForm)}
          >
            <div className="col-span-12 mb-5">
              <div className="px-5 py-3 bg-slate-200 rounded-full font-medium text-blue-900" style={{ background: light, color: dark }}>
                {t('supportingDocuments')}
              </div>
            </div>
            <div className="grid grid-cols-3 gap-5 mb-5">
              <div>
                <FormController
                  name="supportDocumentType"
                  variant="outlined"
                  type="select"
                  label={t('documentType')}
                  control={control}
                  optionKey="id"
                  errors={errors}
                  options={documentTypeDropdown || []}
                  handleChange={(data) => handleFieldChange('supportDocumentType', data)}
                  required={watch('haveHardCopy')}
                />
              </div>
              <div>
                <FormController
                  name="supportDocumentsName"
                  type="text"
                  label={t('documentName')}
                  placeholder={t('documentName')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('supportDocumentsName', data)}
                />
                {supportingValidateShow
                  && <ErrorText error={t('documentNameisRequired')} />}
              </div>
              <FormController
                name="issueDate"
                variant="outlined"
                type="date"
                label={t('issueDate')}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('issueDate', data)}
                maxDate={new Date()}
              />

              <FormController
                name="validUpto"
                variant="outlined"
                type="date"
                label={t('validUpto')}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('validUpto', data)}
                toYear={new Date().getFullYear() + 20}
                fromYear={new Date().getFullYear()}
                minDate={getValues('issueDate', '')}
              />

              {!watch('activeIdEdit') && (
                <FormController
                  name="supportingDocuments"
                  type="file"
                  accept="image,pdf"
                  label={t('attachSupportingDocuments')}
                  placeholder={t('dropOrChooseFilesToUpload')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleSupportDoc(data)}
                  loading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
                  isDisabled={watch('activeIdEdit')}
                />
              )}

              <div className="flex gap-3 justify-end">
                <Button
                  variant="primary_outline"
                  type="submit"
                  isLoading={actionTriggered?.id === 'counter-supporting-doc-upload' && actionTriggered?.loading}
                >
                  {watch('activeIdEdit') ? t('update') : t('upload')}
                </Button>
                {watch('activeIdEdit') && (
                  <Button
                    variant="secondary_outline"
                    onClick={() => handleCancelUpdate()}
                  >
                    {t('cancel')}
                  </Button>
                )}
              </div>
            </div>
            <div className="col-span-12 flex gap-3 flex-wrap mt-5">
              {existingSupportingDocs.map((item) => (
                <PreviewThumbnail
                  key={item.id}
                  handlePreviewRemove={(data) => onHandleRemove(data)}
                  handlePreview={(data) => handlePreview(data, 2, 0)}
                  item={item}
                  preview={docPreview}
                  fileType={item?.documentContentType}
                  documenturl={documenturl}
                  setLoading={setLoading}
                  loading={loading}
                  documentName={item.documentName}
                  haveEdit
                  handleEdit={(data) => onHandleEdit(data, 2)}
                />
              ))}
            </div>
          </form>

        </div>

      </FormWrapper>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  documentTypeDropdown: getAllDocumentsType,
  userInfo: getUserInfo,
  formComponentData: getSidebarData,
  servicevalidation: getServiceValidation,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  fetchApplication: (data) => dispatch(counterActions.fetchApplicationService(data)),
  fecthAllDocumentTypes: (data) => dispatch(commonActions.fecthAllDocumentTypes(data)),
  saveDocuments: (data) => dispatch(actions.saveDocuments(data)),
  deleteDocuments: (data) => dispatch(commonActions.deleteDocuments(data)),
  saveMandatoryDocuments: (data) => dispatch(actions.saveMandatoryDocuments(data)),
  previewApplicant: (data) => dispatch(actions.previewApplicant(data)),
  setArisingApplicationSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActiveAccordian: (data) => dispatch(counterSliceActions.setActiveAccordian(data)),
  setRequiredDocsCount: (data) => dispatch(counterSliceActions.setRequiredDocsCount(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  updateDocuments: (data) => dispatch(actions.updateDocuments(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(Documents);
