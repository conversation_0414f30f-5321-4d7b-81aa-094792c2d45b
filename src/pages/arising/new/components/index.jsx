import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  AccordionComponent, t, Button, VerticalStepper, Toast,
  Select
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  getActionTriggered,
  getCompletedSteps,
  getInwardNextUserModal,
  getInwardUsers,
  getSidebarData
} from 'pages/common/selectors.js';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActiveApplicationData, getRequiredDocsCount } from 'pages/counter/new/selectors';
import * as commonActions from 'pages/common/actions';
import * as counterActions from 'pages/counter/new/actions';
import InwardUserConfirmation from 'common/components/WorkflowConfirmation';
import { convertToLocalDateTime } from 'utils/date';
import { removeDuplicates } from 'utils/validateFile';
import Documents from './Documents';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import { SIDEBAR_KEYS, ARISING_APPLICATION_KEYS_INDEX, ARISING_APPLICATION_KEYS } from '../constants';
import GeneralDetails from './GeneralDetails';
import Services from './Service';

const ArisingNew = (props) => {
  const {
    setActiveAccordian,
    formComponentData,
    setArisingApplicationComponentDetails,
    setArisingApplicationSidebarStatus,
    activeApplicationData,
    setActiveArisingFormData,
    fetchApplication,
    setFormTitle,
    saveComplete,
    setActionTriggered,
    actionTriggered,
    fetchServiceValidation,
    requiredDocsCount,
    completedSteps,
    fetchInwardUsers,
    inwardUsers,
    inwardNextUserModal,
    setInwardNextUserModal,
    setAlertAction
  } = props;

  const params = useParams();
  const { errorTost } = Toast;
  let sidebarData = [];
  const [nextRole, setNextRole] = useState([]);
  const [nextUser, setNextUser] = useState({});

  useEffect(() => {
    if (inwardUsers?.length > 0) {
      setNextRole(
        inwardUsers?.map((ps) => ({
          id: ps?.postId,
          name: `${ps?.employeeName ? ps?.employeeName : ''} - ${ps?.penNo ? ps?.penNo : ''} - ${ps?.designation ? ps?.designation : ''} - ${ps?.seat ? ps?.seat : ''}`,
          employeeName: ps?.employeeName,
          fileId: ps?.fileId,
          fileNo: ps?.fileNo,
          penNo: ps?.penNo,
          role: ps?.role,
          designation: ps?.designation,
          seat: ps?.seat
        }))
      );
    }
  }, [JSON.stringify(inwardUsers)]);

  useEffect(() => {
    if (nextRole?.length === 1) {
      setNextUser({
        postId: nextRole[0]?.id, inwardNo: activeApplicationData?.inwardNo, forwardingTo: nextRole[0]?.employeeName, designation: nextRole[0]?.designation, role: nextRole[0]?.role || null, penNo: nextRole[0]?.penNo, seat: nextRole[0]?.seat, dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextUser({});
    }
  }, [nextRole]);

  useEffect(() => {
    setFormTitle({ title: t('arising'), variant: 'normal' });
  }, []);

  useEffect(() => {
    if (params.id) {
      fetchApplication(params.id);
    } else {
      setActiveArisingFormData({});
    }
  }, [params]);

  const onClickSidebarItem = (currentStep) => {
    const formFieldKeys = Object.keys(ARISING_APPLICATION_KEYS);
    const currentFormFieldKey = formFieldKeys[Number(currentStep - 1)];
    setArisingApplicationSidebarStatus({ activeStep: currentStep, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
    switch (ARISING_APPLICATION_KEYS[currentFormFieldKey]) {
      case ARISING_APPLICATION_KEYS.Service:
        setActiveAccordian(ARISING_APPLICATION_KEYS.Service);
        break;
      case ARISING_APPLICATION_KEYS.General:
        setActiveAccordian(ARISING_APPLICATION_KEYS.General);
        break;
      case ARISING_APPLICATION_KEYS.Document:
        setActiveAccordian(ARISING_APPLICATION_KEYS.Document);
        break;
      default:
        return false;
    }
    return true;
  };

  const accordionData = [
    {
      title: t('services'),
      content: <Services formData={activeApplicationData} />,
      onPress: () => onClickSidebarItem(ARISING_APPLICATION_KEYS_INDEX.Service),
      isCompleted: completedSteps.includes(ARISING_APPLICATION_KEYS_INDEX.Service)
    },
    {
      title: t('concatLabel', { label: t('general'), type: t('details') }),
      content: <GeneralDetails formActiveData={activeApplicationData} />,
      onPress: () => onClickSidebarItem(ARISING_APPLICATION_KEYS_INDEX.General),
      isCompleted: completedSteps.includes(ARISING_APPLICATION_KEYS_INDEX.Applicant)
    },
    {
      title: t('documents'),
      content: <Documents formData={activeApplicationData} />,
      onPress: () => onClickSidebarItem(ARISING_APPLICATION_KEYS_INDEX.Document),
      isCompleted: completedSteps.includes(ARISING_APPLICATION_KEYS_INDEX.Document)
    }
  ];

  const SIDEBAR_STEPPER_STEPS = {
    APPLICATION:
      [
        { title: t('services'), onClick: () => onClickSidebarItem(ARISING_APPLICATION_KEYS_INDEX.Service) },
        { title: t('concatLabel', { label: t('general'), type: t('details') }), onClick: () => onClickSidebarItem(ARISING_APPLICATION_KEYS_INDEX.General) },
        { title: t('documents'), onClick: () => onClickSidebarItem(ARISING_APPLICATION_KEYS_INDEX.Document) }
      ]
  };

  const getSidebarAccordionData = () => {
    return ([{
      title: t('application'),
      key: SIDEBAR_KEYS.APPLICATION,
      content: <VerticalStepper
        steps={SIDEBAR_STEPPER_STEPS.APPLICATION}
        activeStep={Number(formComponentData.activeStep)}
        completedSteps={formComponentData?.completedSteps}
      />
    },
    {
      title: t('concatLabel', { label: t('required'), type: t('documents') }),
      key: SIDEBAR_KEYS.REQUIRED_DOCUMENTS,
      content: ''
    },
    { title: t('guidelines'), key: SIDEBAR_KEYS.GUIDELINES, content: '' },
    { title: t('quickAccess'), key: SIDEBAR_KEYS.QUICK_ACCESS, content: '' },
    { title: t('process'), key: SIDEBAR_KEYS.PROCESS, content: '' }
    ]);
  };

  useEffect(() => {
    sidebarData = getSidebarAccordionData();
    setArisingApplicationComponentDetails({ data: sidebarData, steps: SIDEBAR_STEPPER_STEPS.APPLICATION });
  }, [formComponentData?.activeStep]);

  useEffect(() => {
    if (activeApplicationData?.serviceCode) {
      fetchServiceValidation(activeApplicationData?.serviceCode);
    }
    if (activeApplicationData?.generalDetailsResponses) {
      fetchInwardUsers(params?.id);
    }
  }, [activeApplicationData]);

  const save = () => {
    const steps = [];
    if (activeApplicationData) {
      if (activeApplicationData?.inwardId) {
        const find = steps.findIndex((item) => item === 'service');
        if (find === -1) {
          steps.push('service');
        }
      }
      if (activeApplicationData?.generalDetailsResponses) {
        const find = steps.findIndex((item) => item === 'general');
        if (find === -1) {
          steps.push('general');
        }
      }
      if (requiredDocsCount === 0 || activeApplicationData?.documentsTypeUpload?.length === requiredDocsCount) {
        const find = steps.findIndex((item) => item === 'documents');
        if (find === -1) {
          steps.push('documents');
        }
      }
    }

    if (steps.length > 0) {
      const findService = steps.findIndex((item) => item === 'service');
      const findGeneral = steps.findIndex((item) => item === 'general');
      const findDocs = steps.findIndex((item) => item === 'documents');
      if (findService === -1) {
        setActiveAccordian(ARISING_APPLICATION_KEYS.Service);
        setArisingApplicationSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] });
        errorTost({
          title: t('missingSections'),
          description: `${t('pleaseComplete')} ${t('the')} ${t('servicee')} ${t('section')}`
        });
      } else if (findGeneral === -1) {
        setActiveAccordian(ARISING_APPLICATION_KEYS.General);
        setArisingApplicationSidebarStatus({
          activeStep: 1,
          completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep]
        });
        errorTost({
          title: t('missingSections'),
          description: `${t('pleaseComplete')} ${t('the')} ${t('general')} ${t('section')}`
        });
      } else if (findDocs === -1) {
        setActiveAccordian(ARISING_APPLICATION_KEYS.Document);
        setArisingApplicationSidebarStatus({
          activeStep: 2,
          completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep]
        });
        errorTost({
          title: t('missingSections'),
          description: `${t('pleaseComplete')} ${t('the')} ${t('document')} ${t('section')}`
        });
      } else {
        if (Object.keys(nextUser).length === 0) {
          setAlertAction({
            open: true,
            variant: 'warning',
            message: 'Please select Forward to',
            title: 'Warning',
            backwardActionText: t('ok'),
            closeOnOverlayClick: false,
            closeOnEsc: false
          });
        } else {
          setInwardNextUserModal(true);
        }
        setActionTriggered({ loading: false, id: 'counterCreate' });
      }
    } else {
      errorTost({
        title: t('missingSections'),
        description: `${t('pleaseComplete')} ${t('the')} ${t('above')} ${t('section')}`
      });
      setActiveAccordian(ARISING_APPLICATION_KEYS.Service);
      setArisingApplicationSidebarStatus({
        activeStep: 0,
        completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep]
      });
    }
  };

  const handleClose = () => {
    setInwardNextUserModal(false);
  };

  const handleSave = () => {
    const inwardIds = [];
    inwardIds.push(params.id);
    const createFileData = {
      inwardIds,
      source: 3,
      postId: nextUser?.postId
    };
    setActionTriggered({ loading: true, id: 'arisingCreate' });
    saveComplete(createFileData);
  };

  const handleChange = (data) => {
    if (data) {
      setNextUser({
        postId: data?.id, inwardNo: activeApplicationData?.inwardNo, forwardingTo: data?.employeeName, designation: data?.designation, role: data?.role || null, penNo: data?.penNo, seat: data?.seat, dateAndTime: convertToLocalDateTime(new Date().toISOString())
      });
    } else {
      setNextUser({});
      setInwardNextUserModal(false);
    }
  };

  return (
    <div className="pb-[50px]">

      <AccordionComponent data={accordionData} allowMultiple={false} currentIndexes={[formComponentData.activeStep]} />
      <div className="grid grid-cols-12 gap-x-5 my-5 bg-white rounded-lg py-5 px-10">
        <div className="lg:col-span-8 md:col-span-8 col-span-12" />
        <div className="lg:col-span-4 md:col-span-4 col-span-12 flex gap-3 items-center justify-end">
          {nextRole?.filter((item) => item?.id !== null).length > 0 && (
            <Select
              name="user"
              type="select"
              value={nextUser?.postId}
              label={t('forwardTo')}
              placeholder={t('select')}
              optionKey="id"
              onChange={(data) => handleChange(data)}
              options={removeDuplicates(nextRole, 'penNo') || []}
              isClearable
            />
          )}
          <Button type="submit" form="hook-form" onClick={save} variant="secondary">
            {t('submit')}
          </Button>

        </div>
      </div>
      <InwardUserConfirmation open={inwardNextUserModal} close={handleClose} handleSelect={handleSave} nextUser={nextUser} isLoading={actionTriggered?.id === 'arisingCreate' && actionTriggered?.loading} />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  formComponentData: getSidebarData,
  activeApplicationData: getActiveApplicationData,
  actionTriggered: getActionTriggered,
  requiredDocsCount: getRequiredDocsCount,
  completedSteps: getCompletedSteps,
  inwardUsers: getInwardUsers,
  inwardNextUserModal: getInwardNextUserModal
});

const mapDispatchToProps = (dispatch) => ({
  setActiveAccordian: (data) => dispatch(commonSliceActions.setActiveAccordian(data)),
  setArisingApplicationComponentDetails: (data) => dispatch(commonSliceActions.setFormComponentData(data)),
  setArisingApplicationSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setActiveArisingFormData: (data) => dispatch(sliceActions.setActiveArisingFormData(data)),
  fetchApplication: (data) => dispatch(counterActions.fetchApplicationService(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  saveComplete: (data) => dispatch(actions.saveComplete(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchServiceValidation: (data) => dispatch(commonActions.fetchServiceValidation(data)),
  fetchInwardUsers: (data) => dispatch(commonActions.fetchInwardUsers(data)),
  setInwardNextUserModal: (data) => dispatch(commonSliceActions.setInwardNextUserModal(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ArisingNew);
