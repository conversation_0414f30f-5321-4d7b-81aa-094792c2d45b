import React, { useEffect, useState } from 'react';
import { getActionTriggered, getDoorKey, getUserInfo } from 'pages/common/selectors';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as counterActions from 'pages/counter/new/actions';
import RoutingKeys from 'pages/common/components/GeneralDetails/RoutingKeys';
import { existingGeneralData, generalRoutingKeys, generalSaveData } from 'pages/common/components/GeneralDetails/helper';
import _ from 'lodash';
import { IKM_FILE_ARISING } from 'pages/file/details/constants';

const GeneralDetails = (props) => {
  const {
    formActiveData,
    userInfo,
    setActionTriggered,
    updateGeneralDetails,
    doorkey
  } = props;
  const params = useParams();

  const [serviceInfo, setServiceInfo] = useState({});
  const [existingGeneral, setExistingGeneral] = useState({});

  useEffect(() => {
    if (formActiveData) {
      setServiceInfo({ code: formActiveData?.serviceCode, name: formActiveData?.serviceName });
    }
  }, [formActiveData]);

  useEffect(() => {
    if (formActiveData?.generalDetailsResponses) {
      const generalDetailsResponses = formActiveData?.generalDetailsResponses;
      setExistingGeneral(existingGeneralData(generalDetailsResponses));
    }
  }, [formActiveData]);

  const routingSave = (data) => {
    setActionTriggered({ loading: true, id: 'GeneralDetailsCreate' });
    const keyData = generalRoutingKeys(data, doorkey);
    const rounteKeyData = _.omitBy(keyData, _.isNil);
    const saveData = generalSaveData(
      rounteKeyData,
      params?.id,
      [],
      data,
      userInfo?.id,
      'arising'
    );

    const update = JSON.parse(JSON.stringify(saveData));
    update.generalDetailsId = formActiveData?.generalDetailsResponses?.details?.length > 0 ? formActiveData?.generalDetailsResponses?.details[0]?.id : null;
    updateGeneralDetails(_.omitBy(update, _.isNil));
  };

  return (
    <div className="pt-5">

      <RoutingKeys
        from={IKM_FILE_ARISING}
        routingSave={routingSave}
        activeIndex
        existingData={existingGeneral}
        serviceInfo={serviceInfo}
        officeCode={userInfo?.id}
      />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  doorkey: getDoorKey,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  updateGeneralDetails: (data) => dispatch(counterActions.saveGeneralDetails(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(GeneralDetails);
