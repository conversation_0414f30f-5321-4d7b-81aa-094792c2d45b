import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const saveApplicationServiceApi = (data) => {
  return {
    url: API_URL.ARISING.SAVE_APPLICATION,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.SAVE_APPLICATION_SERVICE_FAILURE
      ],
      data
    }
  };
};

export const updateApplicationServiceApi = (data) => {
  return {
    url: API_URL.ARISING.UPDATE_APPLICATION.replace(':id', data.id),
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.UPDATE_APPLICATION_SERVICE_FAILURE
      ],
      data
    }
  };
};

export const saveDocuments = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};

export const saveMandatoryDocuments = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_MANDATORY_DOCUMENTS,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};

export const saveComplete = (data) => {
  return {
    url: API_URL.LEGACYFILES.CREATE_LEGACY_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.COMPLETE_SAVE_REQUEST,
        ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
        ACTION_TYPES.COMPLETE_SAVE_FAILURE
      ],
      data
    }
  };
};

export const updateDocuments = (data) => {
  return {
    url: API_URL.COUNTER.UPDATE_DOCUMENTS,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_DOCUMENTS_REQUEST,
        ACTION_TYPES.UPDATE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.UPDATE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};
