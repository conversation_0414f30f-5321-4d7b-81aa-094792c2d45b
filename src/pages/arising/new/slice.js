import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  activeAccordian: 'Service',
  subModule: {},
  module: {},
  subModulesSearch: {},
  servicesSearchList: {},
  advanceSearchResult: {},
  activeArisingFormData: {}
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setSubModule: (state, { payload }) => {
      _.set(state, 'subModule', payload);
    },
    setModule: (state, { payload }) => {
      _.set(state, 'module', payload);
    },
    setSubModulesSearchList: (state, { payload }) => {
      _.set(state, 'subModulesSearchList', payload);
    },
    setServicesSearchList: (state, { payload }) => {
      _.set(state, 'servicesSearchList', payload);
    },
    setActiveArisingFormData: (state, { payload }) => {
      _.set(state, 'activeArisingFormData', payload);
    },
    setActiveInwardId: (state, { payload }) => {
      _.set(state, 'activeInwardId', payload);
    }
  }

});

export const { actions, reducer } = slice;
