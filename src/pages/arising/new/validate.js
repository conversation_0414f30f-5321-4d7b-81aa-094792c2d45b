import * as yup from 'yup';

import {
  AADHAAR, UDID, NAME, NUMERIC, EN_NUMERIC, EN, EMAIL, MOBILE, FILE_NO
} from 'common/regex';

import { t } from 'common/components';

export const ServiceFormSchema = yup.object({
  services: yup.string().required(t('isRequired', { type: t('services') })),
  modules: yup.string().required(t('isRequired', { type: t('modules') })),
  subModule: yup.string().required(t('isRequired', { type: t('subModule') })),
  fileType: yup.string().required(t('isRequired', { type: t('fileType') })),
  referenceNo: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` })),
  title: yup.string()
    .max(150, 'Only 150 characters can be entered in the Title')
}).required();

export const ApplicatDetailsFormSchema = yup.object().shape({
  localbodyType: yup.string()
    .required(t('Local Body Type is Required')),
  country: yup.string()
    .required(t('Country is Required')),
  documentType: yup.string()
    .required(t('Document Type is Required')),
  documentNo: yup.string()
    .when('documentType', (documentType, schema) => {
      if (documentType[0] === 'Aadhaar Number') {
        return schema.required('Aadhaar is Required').matches(AADHAAR, t('invalidType', { type: t('aadhaarNo') }));
      }
      if (documentType[0] === 'UDID') {
        return schema.required('UDID is Required').matches(UDID, t('invalidType', { type: t('UDID') }));
      }
      if (documentType[0] === 'Passport Number') {
        return schema.required('Passport is Required');
      }
      return schema.notRequired();
    }),
  firstName: yup.string()
    .max(150)
    .required('First Name is Required')
    .matches(NAME, t('Use Alphabets only')),
  middleName: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(NAME, t('Use Alphabets only')),
  lastName: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(NAME, t('Use Alphabets only')),
  houseName: yup.string()
    .max(150)
    .required(t('House Name is Required'))
    .matches(NAME, t('Use Alphabets only')),
  wardName: yup.string()
    .when('localbodyType', (localbodyType, schema) => {
      if (localbodyType[0] === 'Inside Localbody') {
        return schema.required('Ward Name is Required');
      }
      if (localbodyType[0] === 'Outside Localbody') {
        return schema.notRequired().nullable().transform((val) => val || null);
      }
      return schema.notRequired();
    }),
  doorNo: yup.string()
    .length(4)
    .required(t('Door No is Required'))
    .matches(NUMERIC, t('Use Number Only')),
  postOffice: yup.string()
    .required(t('Post Office is Required')),
  street: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN_NUMERIC, t('Use Text and Numbers only')),
  localPlace: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN_NUMERIC, t('Use Text and Numbers only')),
  mainPlace: yup.string()
    .when('country', (country, schema) => {
      if (country[0] !== 'IND') {
        return schema.required('City/Town is Required').max(150).matches(EN, t('Use Alphabets only'));
      }
      return schema.required('Main Place is Required').max(150).matches(EN, t('Use Alphabets only'));
    }),
  email: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EMAIL, t('Invalid Email')),
  mobile: yup.string()
    .required('Mobile is Required')
    .matches(MOBILE, t('Invalid Mobile Number')),
  whatsapp: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(MOBILE, t('Invalid Whatsapp Number'))
}).required();

export const AdvanceSearchSchema = yup.object({
  services: yup.string().required('Service is Required')
}).required();

export const GeneralDetailsSchema = yup.object().shape({
  referenceNo: yup.string().when(['serviceValidation'], (serviceValidation, schema) => {
    if (serviceValidation && serviceValidation[0]?.referenceFileNo === 1) {
      return schema.required('Reference number is required');
    }
    return schema.nullable().transform((val) => (val === '' ? null : val)).notRequired();
  })
}).required();

export const RoutingKeySchema = yup.object({
  referenceNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.referenceNo === 1) {
        return schema.required(t('isRequired', { type: t('referenceNo') })).matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
      }
      return schema.nullable().transform((val) => val || null).notRequired().matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
    }),
  ward: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ward === 1) {
        return schema.required(t('isRequired', { type: t('ward') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  doorNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.doorNo === 1) {
        return schema
          .max(5, 'Door No must be five digits')
          .required(t('isRequired', { type: t('doorNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  subNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.subNo === 1) {
        return schema.required(t('isRequired', { type: t('subNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownershipId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownershipId === 1) {
        return schema.required(t('isRequired', { type: t('ownershipId') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  localBodyPropertyType: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.localBodyPropertyType === 1) {
        return schema.required(t('isRequired', { type: t('localBodyPropertyType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingUsage: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingUsage === 1) {
        return schema.required(t('isRequired', { type: t('buildingUsage') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingArea: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingArea === 1) {
        return schema.required(t('isRequired', { type: t('buildingArea') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functionalGroup: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functionalGroup === 1) {
        return schema.required(t('isRequired', { type: t('functionalGroup') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functions: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functions === 1) {
        return schema.required(t('isRequired', { type: t('functions') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  description: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.description === 1) {
        return schema.required(t('isRequired', { type: t('description') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownerName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownerName === 1) {
        return schema.required(t('isRequired', { type: t('ownerName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ksebPostNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ksebPostNo === 1) {
        return schema.required(t('isRequired', { type: t('ksebPostNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  roadName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.roadName === 1) {
        return schema.required(t('isRequired', { type: t('roadName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  landMark: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.landmark === 1) {
        return schema.required(t('isRequired', { type: t('landmark') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  talukId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.taluk === 1) {
        return schema.required(t('isRequired', { type: t('taluk') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  village: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.village === 1) {
        return schema.required(t('isRequired', { type: t('village') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  surveyNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.surveyNumber === 1) {
        return schema.required(t('isRequired', { type: t('surveyNumber') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  dateOfEvent: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfEvent === 1) {
        return schema.required(t('isRequired', { type: t('dateOfEvent') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  receiptNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.receiptNo === 1) {
        return schema.required(t('isRequired', { type: t('receiptNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })

}).required();

export const DocumentDetailsFormSchema = yup.object().shape({
  supportDocumentsName: yup.string().required(t('isRequired', { type: t('documentName') })),
  supportDocumentType: yup.string().required(t('isRequired', { type: t('supportDocumentType') }))

}).required();
