import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getArisingNew = (state) => state[STATE_REDUCER_KEY];

const subModuleById = (state) => state?.subModule;
export const getSubModuleById = flow(getArisingNew, subModuleById);

const moduleById = (state) => state?.module;
export const getmoduleById = flow(getArisingNew, moduleById);

const subModulesSearchList = (state) => state?.subModulesSearchList;
export const getSubModulesSearchList = flow(getArisingNew, subModulesSearchList);

const servicesSearchList = (state) => state?.servicesSearchList;
export const getServicesSearchList = flow(getArisingNew, servicesSearchList);

const advanceSearchResult = (state) => state?.advanceSearchResult;
export const getAdvanceSearchResult = flow(getArisingNew, advanceSearchResult);

const activeApplicationData = (state) => state?.activeArisingFormData;
export const getActiveApplicationData = flow(getArisingNew, activeApplicationData);

const setActiveInwardId = (state) => state?.activeInwardId;
export const getActiveInwardId = flow(getArisingNew, setActiveInwardId);
