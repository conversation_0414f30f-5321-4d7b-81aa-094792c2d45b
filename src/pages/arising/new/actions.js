import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  SAVE_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE`,
  SAVE_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_REQUEST`,
  SAVE_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_SUCCESS`,
  SAVE_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_APPLICATION_SERVICE_FAILURE`,

  UPDATE_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE`,
  UPDATE_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE_REQUEST`,
  UPDATE_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE_SUCCESS`,
  UPDATE_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_APPLICATION_SERVICE_FAILURE`,

  SAVE_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS`,
  SAVE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_REQUEST`,
  SAVE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_SUCCESS`,
  SAVE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_DOCUMENTS_FAILURE`,

  SAVE_MANDATORY_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS`,
  SAVE_MANDATORY_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_REQUEST`,
  SAVE_MANDATORY_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_SUCCESS`,
  SAVE_MANDATORY_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_MANDATORY_DOCUMENTS_FAILURE`,

  COMPLETE_SAVE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE`,
  COMPLETE_SAVE_REQUEST: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_REQUEST`,
  COMPLETE_SAVE_SUCCESS: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_SUCCESS`,
  COMPLETE_SAVE_FAILURE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_FAILURE`,

  UPDATE_DOCUMENTS: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS`,
  UPDATE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS_REQUEST`,
  UPDATE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS_SUCCESS`,
  UPDATE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_DOCUMENTS_FAILURE`

};

// save application arising form
export const saveApplicationService = createAction(ACTION_TYPES.SAVE_APPLICATION_SERVICE);
export const updateApplicationService = createAction(ACTION_TYPES.UPDATE_APPLICATION_SERVICE);
export const saveDocuments = createAction(ACTION_TYPES.SAVE_DOCUMENTS);
export const saveMandatoryDocuments = createAction(ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS);
export const saveComplete = createAction(ACTION_TYPES.COMPLETE_SAVE);
export const updateDocuments = createAction(ACTION_TYPES.UPDATE_DOCUMENTS);
