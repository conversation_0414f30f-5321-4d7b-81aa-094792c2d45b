import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getProcessedDetails = (state) => state[STATE_REDUCER_KEY];

const processedFileListData = (state) => state?.processedFileTableList;
export const getProcessedFileListData = flow(getProcessedDetails, processedFileListData);

const servicesSearchListParams = (state) => state?.serviceTableListParams;
export const getServicesSearchListParams = flow(getProcessedDetails, servicesSearchListParams);
