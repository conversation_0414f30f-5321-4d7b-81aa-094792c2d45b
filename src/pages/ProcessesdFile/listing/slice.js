import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  serviceTableListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    moduleCode: null,
    submoduleCode: null,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    fileStatus: FILE_STATUS_FOR_API_PARAMS.RUNNING
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setProcessedFileTableList: (state, { payload }) => {
      _.set(state, 'processedFileTableList', payload);
    },
    setServiceTableListParams: (state, { payload }) => {
      _.set(state, 'serviceTableListParams', payload);
    }
  }
});

export const { actions, reducer } = slice;
