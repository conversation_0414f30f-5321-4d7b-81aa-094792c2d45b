import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import { getServicesSearchListParams } from './selectors';

export function* fetchProcessedFileTableList() {
  const apiParams = yield select(getServicesSearchListParams);
  const updatedParams = { ...apiParams, fileStatus: FILE_STATUS_FOR_API_PARAMS.APPROVED };
  const formatedParams = _.omitBy(updatedParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchProcessedFileTableList, formatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PROCESSED_FILE_LIST_SUCCESS,
    ACTION_TYPES.FETCH_PROCESSED_FILE_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_PROCESSED_FILE_LIST_SUCCESS) {
    yield put(sliceActions.setProcessedFileTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'processed-file-table' }));
  }
}

export default function* counterSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_PROCESSED_FILE_LIST, fetchProcessedFileTableList)
  ]);
}
