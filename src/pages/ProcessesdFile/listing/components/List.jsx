import {
  t, IconButton
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import BackArrow from 'assets/BackIcon.jsx';
import { dark } from 'utils/color.js';
import { handleSlNo } from 'utils/serialNumber';
import { EMPLOYEE_SERVICE_PATH } from 'common/constants';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import { getProcessedFileListData, getServicesSearchListParams } from '../selectors';

const List = ({
  fetchProcessedTableList, setServiceTableListParams, processedFileTableList, serviceListParams, userInfo,
  setTableLoader, tableLoader
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const columns = [
    {
      header: t('slNo'),
      field: 'slNo',
      alignment: 'left',
      cell: (field) => handleSlNo(field, page)
    },
    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left'
    },
    {
      header: t('nameOfApplicant'),
      field: 'applicantName',
      alignment: 'left'
    },
    {
      header: t('servicee'),
      field: 'serviceName',
      alignment: 'left'
    },
    {
      header: t('date'),
      field: 'fileDate',
      alignment: 'left'
    }

  ];

  useEffect(() => {
    if (serviceListParams) {
      setTableLoader({ loading: true, id: 'processed-file-table' });
      fetchProcessedTableList();
    }
  }, [serviceListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setServiceTableListParams({
      ...serviceListParams,
      page: data,
      officeId: userInfo.id
    });
  };

  useEffect(() => {
    if (processedFileTableList) {
      setTableLoader({ loading: false, id: 'processed-file-table' });
      if (Object.keys(processedFileTableList).length > 0) {
        setTableData(processedFileTableList.content);
        setTotalItems(Number(`${processedFileTableList.totalPages}0`));
        setNumberOfElements(Number(processedFileTableList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [processedFileTableList]);

  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('processedFile')}
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'processed-file-table'}
          numberOfElements={numberOfElements}
        />

      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  processedFileTableList: getProcessedFileListData,
  serviceListParams: getServicesSearchListParams,
  userInfo: getUserInfo,
  tableLoader: getTableLoader

});

const mapDispatchToProps = (dispatch) => ({
  fetchProcessedTableList: (data) => dispatch(actions.fetchProcessedTableList(data)),
  setServiceTableListParams: (data) => dispatch(sliceActions.setServiceTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(List);
