import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_PROCESSED_FILE_LIST: `${STATE_REDUCER_KEY}/FETCH_PROCESSED_FILE_LIST`,
  FETCH_PROCESSED_FILE_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PROCESSED_FILE_LIST_REQUEST`,
  FETCH_PROCESSED_FILE_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PROCESSED_FILE_LIST_SUCCESS`,
  FETCH_PROCESSED_FILE_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PROCESSED_FILE_LIST_FAILURE`

};

export const fetchProcessedTableList = createAction(ACTION_TYPES.FETCH_PROCESSED_FILE_LIST);
