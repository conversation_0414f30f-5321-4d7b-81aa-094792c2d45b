import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchProcessedFileTableList = (params) => {
  return {
    url: API_URL.DASHBOARD.FETCH_SERVICE_LIST,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PROCESSED_FILE_LIST_REQUEST,
        ACTION_TYPES.FETCH_PROCESSED_FILE_LIST_SUCCESS,
        ACTION_TYPES.FETCH_PROCESSED_FILE_LIST_FAILURE
      ],
      params
    }
  };
};
