import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchStampInventoryTableList = (params, data) => {
  return {
    url: API_URL.DISPATCH.STAMP_INVENTORY,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST_REQUEST,
        ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST_SUCCESS,
        ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST_FAILURE
      ],
      params,
      data
    }
  };
};

export const fetchCashDenomination = () => {
  return {
    url: API_URL.DISPATCH.FETCH_CASH_DENOMINATION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES_REQUEST,
        ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES_SUCCESS,
        ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES_FAILURE
      ]
    }
  };
};

export const saveStampOpeningDetails = (data) => {
  return {
    url: API_URL.DISPATCH.SAVE_STAMP_OPENING_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS_REQUEST,
        ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS_SUCCESS,
        ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS_FAILURE
      ],
      data
    }
  };
};

export const fetchStampDetails = (params, data) => {
  return {
    url: API_URL.DISPATCH.STAMP_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_STAMP_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_STAMP_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_STAMP_DETAILS_FAILURE
      ],
      params,
      data
    }
  };
};

export const saveStampDetails = (data) => {
  return {
    url: API_URL.DISPATCH.SAVE_STAMP_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_STAMP_DETAILS_REQUEST,
        ACTION_TYPES.SAVE_STAMP_DETAILS_SUCCESS,
        ACTION_TYPES.SAVE_STAMP_DETAILS_FAILURE
      ],
      data
    }
  };
};

export const fetchDraftSendLetterList = (params) => {
  return {
    url: API_URL.DISPATCH.DRAFT_SENT_LETTER_LIST,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST_REQUEST,
        ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST_SUCCESS,
        ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST_FAILURE
      ],
      params
    }
  };
};

export const fetchDraftSendLetterPreviewList = (params) => {
  return {
    url: API_URL.DISPATCH.DRAFT_SENT_LETTER_LIST,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_REQUEST,
        ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_SUCCESS,
        ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_FAILURE
      ],
      params
    }
  };
};
