import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_STAMP_INVENTORY_LIST: `${STATE_REDUCER_KEY}/FETCH_STAMP_INVENTORY_LIST`,
  FETCH_STAMP_INVENTORY_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_STAMP_INVENTORY_LIST_REQUEST`,
  FETCH_STAMP_INVENTORY_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_STAMP_INVENTORY_LIST_SUCCESS`,
  FETCH_STAMP_INVENTORY_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_STAMP_INVENTORY_LIST_FAILURE`,

  FETCH_CASH_DENOMINATION_TYPES: `${STATE_REDUCER_KEY}/FETCH_CASH_DENOMINATION_TYPES`,
  FETCH_CASH_DENOMINATION_TYPES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_CASH_DENOMINATION_TYPES_REQUEST`,
  FETCH_CASH_DENOMINATION_TYPES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_CASH_DENOMINATION_TYPES_SUCCESS`,
  FETCH_CASH_DENOMINATION_TYPES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_CASH_DENOMINATION_TYPES_FAILURE`,

  SAVE_STAMP_OPENING_DETAILS: `${STATE_REDUCER_KEY}/SAVE_STAMP_OPENING_DETAILS`,
  SAVE_STAMP_OPENING_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_STAMP_OPENING_DETAILS_REQUEST`,
  SAVE_STAMP_OPENING_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_STAMP_OPENING_DETAILS_SUCCESS`,
  SAVE_STAMP_OPENING_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_STAMP_OPENING_DETAILS_FAILURE`,

  FETCH_STAMP_DETAILS: `${STATE_REDUCER_KEY}/FETCH_STAMP_DETAILS`,
  FETCH_STAMP_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_STAMP_DETAILS_REQUEST`,
  FETCH_STAMP_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_STAMP_DETAILS_SUCCESS`,
  FETCH_STAMP_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_STAMP_DETAILS_FAILURE`,

  SAVE_STAMP_DETAILS: `${STATE_REDUCER_KEY}/SAVE_STAMP_DETAILS`,
  SAVE_STAMP_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_STAMP_DETAILS_REQUEST`,
  SAVE_STAMP_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_STAMP_DETAILS_SUCCESS`,
  SAVE_STAMP_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_STAMP_DETAILS_FAILURE`,

  FETCH_DRAFT_SEND_LETTER_LIST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_LIST`,
  FETCH_DRAFT_SEND_LETTER_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_LIST_REQUEST`,
  FETCH_DRAFT_SEND_LETTER_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_LIST_SUCCESS`,
  FETCH_DRAFT_SEND_LETTER_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_LIST_FAILURE`,

  FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST`,
  FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_REQUEST`,
  FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_SUCCESS`,
  FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_FAILURE`
};
export const fetchInventoryViewTableList = createAction(ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST);
export const getCashDenominationTypes = createAction(ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES);
export const saveStampOpeningDetails = createAction(ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS);
export const fetchStampDetails = createAction(ACTION_TYPES.FETCH_STAMP_DETAILS);
export const saveStampDetails = createAction(ACTION_TYPES.SAVE_STAMP_DETAILS);
export const fetchDraftSendLetterList = createAction(ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST);

export const fetchDraftSendLetterPreviewList = createAction(ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST);
