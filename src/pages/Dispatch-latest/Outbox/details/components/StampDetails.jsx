import { <PERSON><PERSON>, Spinner } from '@ksmartikm/ui-components';
import { t } from 'common/components';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getStampDenomination, getStampDetails } from 'pages/Dispatch-latest/selector';
import { useForm } from 'react-hook-form';
import { NUMBER_ONLY } from 'common/regex';
import { CASH_DECLARATION_CHARACTERRS } from 'pages/common/constants';
import './Style.css';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getTableLoader } from 'pages/common/selectors';
import * as actions from '../../../actions';

const StampDetails = ({
  handleClose = () => { }, fetchStampDetails,
  stampDetails = [], stampDenominationDetails,
  saveStampDetails, setTableLoader, tableLoader
}) => {
  const [stampValue, setStampValue] = useState([]);
  const [sumOfStampValue, setSumOfStampValue] = useState(0);

  const { handleSubmit } = useForm({
    mode: 'all'
  });

  useEffect(() => {
    setTableLoader({ loading: true, id: 'stamp-table' });
    fetchStampDetails();
  }, []);

  useEffect(() => {
    if (stampDetails?.length > 0) {
      setStampValue(stampDetails);
    }
  }, [stampDetails]);

  const onSubmitForm = () => {
    const filteredArrayOfStampDetails = [];

    _.filter(stampValue, (o) => {
      filteredArrayOfStampDetails.push({
        officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
        stampTypeId: o?.stampTypeId || 0,
        stampCount: Number(o?.value) + Number(o?.count) || 0,
        totalAmount: o?.outputValue || 0,
        finYear: new Date().getFullYear()
      });
      return filteredArrayOfStampDetails;
    });
    saveStampDetails(filteredArrayOfStampDetails);
    handleClose();
  };

  const getSumOfRupees = (updatedArray) => {
    const total = updatedArray.reduce((n, { outputValue }) => n + outputValue, 0);
    setSumOfStampValue(total);
  };

  const handleRupeesChange = (index, val, inputValue, count) => {
    if (val?.length < 8) {
      let newArray = [];
      newArray = JSON.parse(JSON.stringify(stampValue));
      newArray[index].outputValue = (Number(val) + Number(count)) * Number(inputValue);
      newArray[index].value = Number(val);
      setStampValue(newArray);
      getSumOfRupees(newArray);
    } else {
      setStampValue(stampValue);
      getSumOfRupees(stampValue);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmitForm)} className="form">
      <div className="p-5 max-h-[500px] overflow-y-auto">
        <table className="striped-table">
          <thead>
            <tr>
              <th>{t('stampType')}</th>
              <th>{t('stampInHand')}</th>
              <th>{t('stampCount')}</th>
              <th>{t('totalAmount')}</th>
            </tr>
          </thead>
          <tbody>
            {
              tableLoader?.loading && tableLoader?.id === 'stamp-table'
                ? (
                  <tr>
                    <td colSpan={5}>
                      <div style={{ marginTop: '100px', marginLeft: '300px', paddingBottom: '100px' }}>
                        <Spinner />
                      </div>
                    </td>
                  </tr>
                )

                : stampValue?.length > 0
                && stampValue?.map((val, i) => (

                  <tr key={val?.id}>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{_.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenominationValue}</td>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{val?.count}</td>
                    <td aria-label="input" className="text-center">
                      <input
                        id={val?.id}
                        type="number"
                        min="0"
                        onKeyUp={(event) => {
                          if (!NUMBER_ONLY.test(event.key)) {
                            event.preventDefault();
                          }
                        }}
                        onKeyDown={(e) => CASH_DECLARATION_CHARACTERRS?.includes(e.key)
                          && e.preventDefault()}
                        onWheel={(e) => e.target.blur()}
                        placeholder={val?.defaultValue}
                        name={_.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenomination}
                        onChange={(e) => handleRupeesChange(
                          i,
                          e.target.value,
                          _.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenominationValue,
                          val?.count
                        )}
                        value={val?.value}
                      />
                    </td>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{val?.outputValue} Rs.</td>
                  </tr>
                ))
}

          </tbody>
        </table>
      </div>
      <div className="text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3 pr-6">{t('grandTotal')} <span className="font-bold">{sumOfStampValue || 0} Rs</span></div>
      <div>
        <div className="flex justify-end p-[18px]">
          <Button variant="primary_outline" size="sm" mr={3} onClick={handleClose}>
            {t('cancel')}
          </Button>
          <Button variant="primary" size="sm" type="submit">
            {t('save')}
          </Button>
        </div>
      </div>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  stampDetails: getStampDetails,
  stampDenominationDetails: getStampDenomination,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchStampDetails: (data) => dispatch(actions.fetchStampDetails(data)),
  saveStampDetails: (data) => dispatch(actions.saveStampDetails(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(StampDetails);
