import {
  Modal, ModalBody, ModalContent, ModalHeader, ModalOverlay
} from '@ksmartikm/ui-components';
import React from 'react';
import StampInventoryView from './StampInventoryView';
import OpeningBalanceEntry from './OpeningBalanceEntry';
import ViewOpeningBalanceTable from './ViewOpeningBalanceTable';
import StampDetails from './StampDetails';

const InventoryModal = ({
  open, type, handleClose = () => { },
  handleOpeningBalance = () => { },
  handleOpenOpeningBalanceViewTable = () => { },
  handleOpenClaimEntryTextbox = () => { },
  handleCloseClaimEntryTextbox = () => { },
  handleOpenStampDetails = () => { },
  showClaimEntryTextbox
}) => {
  return (
    <Modal isOpen={open} size="3xl" closeOnOverlayClick={false} onClose={handleClose} closeOnEsc={false} className="custom-form-modal">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <span size="xl">
            <h4 className="rounded-[98px] bg-[#E7EFF5] p-[14px] text-center text-[#09327B]">{type}</h4>
          </span>
        </ModalHeader>
        <ModalBody>

          {
            type === 'Stamp Inventory'
            && (
            <StampInventoryView
              handleClose={handleClose}
              handleOpeningBalance={handleOpeningBalance}
              handleOpenOpeningBalanceViewTable={handleOpenOpeningBalanceViewTable}
              handleOpenClaimEntryTextbox={handleOpenClaimEntryTextbox}
              handleCloseClaimEntryTextbox={handleCloseClaimEntryTextbox}
              showClaimEntryTextbox={showClaimEntryTextbox}
              handleOpenStampDetails={handleOpenStampDetails}
            />
            )
          }

          {
            type === 'Stamp Opening Balance'
            && <OpeningBalanceEntry handleClose={handleClose} />
          }

          {
            type === 'View Opening Balance'
            && <ViewOpeningBalanceTable handleClose={handleClose} />
          }

          {
            type === 'Stamp Details'
            && <StampDetails handleClose={handleClose} />
          }

        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default InventoryModal;
