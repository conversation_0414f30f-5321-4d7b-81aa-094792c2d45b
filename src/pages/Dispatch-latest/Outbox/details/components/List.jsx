import {
  Button, IconButton, Input, InputGroup, InputRightElement
} from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import SearchIcon from 'assets/SearchIcon';
import StampInventory from 'assets/StampInventory';
import { t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { BASE_PATH } from 'common/constants';
import { FILTER_TYPE } from 'pages/common/constants';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { dark } from 'utils/color';
import InventoryModal from './InventoryModal';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    paddingRight: '9px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const List = () => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [search, setSearch] = useState('');
  const [date, setDate] = useState();
  const today = new Date().toISOString().split('T')[0];
  const [numberOfElements, setNumberOfElements] = useState(0);
  const navigate = useNavigate();

  const [stampInventoryViewOpen, setStampInventoryViewOpen] = useState(false);
  const [openingBalanceViewOpen, setOpeningBalanceViewOpen] = useState(false);
  const [viewOpeningBalanceTable, setViewOpeningBalanceTable] = useState(false);
  const [showClaimEntryTextbox, setShowClaimEntryTextbox] = useState(false);
  const [openStampDetails, setOpenStampDetails] = useState(false);

  const backToHome = () => {
    navigate(`${BASE_PATH}/services/dispatch`);
  };

  const columns = [

    {
      header: t('letter'),
      field: 'letter',
      alignment: 'left'
    },
    {
      header: t('sentDate'),
      field: 'sentDate',
      alignment: 'left'
    },
    {
      header: t('recipient'),
      field: 'recipient',
      alignment: 'left'
    },
    {
      header: t('sender'),
      field: 'sender',
      alignment: 'left'
    },
    {
      header: t('status'),
      field: 'status',
      alignment: 'left'
    }

  ];

  const onPageClick = (data) => {
    setPage(data);
  };

  useEffect(() => {
    setTableData([]);
    setTotalItems((0));
    setNumberOfElements(0);
  }, []);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:

        break;
      case FILTER_TYPE.DATE:
        setDate(data);
        break;
      default:
        break;
    }
  };

  const handleStampInventory = () => {
    setStampInventoryViewOpen(true);
  };

  const handleClose = () => {
    setStampInventoryViewOpen(false);
  };

  const handleOpeningBalance = () => {
    setOpeningBalanceViewOpen(true);
    setStampInventoryViewOpen(false);
  };

  const handleCloseOpeningBalance = () => {
    setOpeningBalanceViewOpen(false);
    setStampInventoryViewOpen(true);
  };

  const handleOpenOpeningBalanceViewTable = () => {
    setViewOpeningBalanceTable(true);
    setStampInventoryViewOpen(false);
  };

  const handleCloseOpeningBalanceViewTable = () => {
    setViewOpeningBalanceTable(false);
    setStampInventoryViewOpen(true);
  };

  const handleOpenClaimEntryTextbox = () => {
    setShowClaimEntryTextbox(true);
  };

  const handleCloseClaimEntryTextbox = () => {
    setShowClaimEntryTextbox(false);
  };

  const handleOpenStampDetails = () => {
    setOpenStampDetails(true);
    setStampInventoryViewOpen(false);
  };

  const handleCloseStampDetails = () => {
    setOpenStampDetails(false);
    setStampInventoryViewOpen(true);
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('dispatchOutbox')}
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
              max={today}
            />
          </InputGroup>
        </div>
        <div className="flex-none">
          <Button variant="secondary_outline" onClick={handleStampInventory} leftIcon={<StampInventory width="21px" height="21px" />}>{t('stampInventory')}</Button>
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
        // tableLoader={tableLoader?.loading && tableLoader?.id === 'archived-file-table'}
        />

      </div>
      {
        stampInventoryViewOpen && (
          <InventoryModal
            open={stampInventoryViewOpen}
            type="Stamp Inventory"
            handleClose={handleClose}
            handleOpeningBalance={handleOpeningBalance}
            handleOpenOpeningBalanceViewTable={handleOpenOpeningBalanceViewTable}
            handleOpenClaimEntryTextbox={handleOpenClaimEntryTextbox}
            handleCloseClaimEntryTextbox={handleCloseClaimEntryTextbox}
            showClaimEntryTextbox={showClaimEntryTextbox}
            handleOpenStampDetails={handleOpenStampDetails}
          />
        )
      }
      {
        openingBalanceViewOpen && (
          <InventoryModal
            open={openingBalanceViewOpen}
            type="Stamp Opening Balance"
            handleClose={handleCloseOpeningBalance}
          />
        )
      }
      {
        viewOpeningBalanceTable && (
          <InventoryModal
            open={viewOpeningBalanceTable}
            type="View Opening Balance"
            handleClose={handleCloseOpeningBalanceViewTable}
          />
        )
      }
      {
        openStampDetails && (
          <InventoryModal
            open={openStampDetails}
            type="Stamp Details"
            handleClose={handleCloseStampDetails}
          />
        )
      }
    </>
  );
};

export default List;
