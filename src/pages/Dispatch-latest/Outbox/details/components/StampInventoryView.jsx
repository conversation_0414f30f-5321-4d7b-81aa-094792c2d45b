import { Button, t } from 'common/components';
import {
  getStampDenomination, getStampDetails, getStampInventoryViewListParams, getStampInventoyrTableList
} from 'pages/Dispatch-latest/selector';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { CommonTable } from 'common/components/Table';
import { getTableLoader } from 'pages/common/selectors';
import _ from 'lodash';
import { actions as sliceActions } from '../../../slice';
import * as actions from '../../../actions';

const StampInventoryView = ({
  setTableLoader,
  tableLoader, handleClose = () => { },
  handleOpeningBalance = () => { }, stampDenominationDetails,
  getCashDenominationTypes, handleOpenOpeningBalanceViewTable = () => { },
  handleOpenClaimEntryTextbox = () => { },
  handleCloseClaimEntryTextbox = () => { },
  showClaimEntryTextbox,
  handleOpenStampDetails = () => { },
  fetchStampDetails, stampDetails
}) => {
  const [tableData, setTableData] = useState([]);
  const [amountData, setAmountData] = useState(0);

  useEffect(() => {
    setTableLoader({ loading: true, id: 'inventory-view-table' });
    fetchStampDetails();
    getCashDenominationTypes();
  }, []);

  useEffect(() => {
    if (stampDetails) {
      setTableLoader({ loading: false, id: 'inventory-view-table' });
      if (Object.keys(stampDetails)?.length > 0) {
        setTableData(stampDetails);
      } else {
        setTableData([]);
      }
    }
  }, [stampDetails]);

  const handleStampTypeId = (fileData) => {
    let stampTypeId;
    if (fileData?.row) {
      const cellData = fileData?.row;
      stampTypeId = (
        <div>{_.filter(stampDenominationDetails, (el) => el?.id === cellData?.stampTypeId)[0]?.cashDenominationValue}</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{stampTypeId}</div>;
  };

  const handleCount = (fileData) => {
    let count;
    if (fileData?.row) {
      const cellData = fileData?.row;
      count = (
        <div>{cellData?.count} Nos.</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{count}</div>;
  };

  const handleAmount = (fileData) => {
    let amountValue;
    if (fileData?.row) {
      const cellData = fileData?.row;
      amountValue = (
        <div>{cellData?.amount} Rs.</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{amountValue}</div>;
  };

  const columns = [

    {
      header: t('stampValue'),
      field: 'stampTypeId',
      alignment: 'left',
      cell: (field) => handleStampTypeId(field)
    },
    {
      header: t('stampCount'),
      field: 'count',
      alignment: 'left',
      cell: (field) => handleCount(field)
    },

    {
      header: t('totalAmount'),
      field: 'amount',
      alignment: 'left',
      cell: (field) => handleAmount(field)
    }

  ];

  const getAdvancedAmount = () => {
    return amountData
      ? (
        <div className="flex">
          <div className="text-[#5C6E93] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">Advanced Amount<span aria-hidden="true" className="text-[#00B2EB] underline cursor-pointer" onClick={handleOpenStampDetails}>{amountData} ₹</span></div>
          <div className="flex-grow text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">{t('grandTotal')} <span className="font-bold">{stampDetails?.reduce((n, { amount }) => n + amount, 0)} Rs</span></div>

        </div>
      )
      : <div className="flex-none text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">{t('grandTotal')} <span className="font-bold">{stampDetails?.reduce((n, { amount }) => n + amount, 0)} Rs</span></div>;
  };

  return (
    <>
      <div>
        <CommonTable
          // variant="dashboard"
          tableData={tableData}
          columns={columns}
          tableLoader={tableLoader?.loading && tableLoader?.id === 'inventory-view-table'}
        />
      </div>
      {
        showClaimEntryTextbox
          ? (
            <div className="flex">
              <div>
                <input
                  type="number"
                  name="amount"
                  placeholder={t('amount')}
                  className="w-full h-full bg-white px-4 border border-[#E8ECEE] text-[#2D3748] rounded-lg text-base leading-4 transition-all ease-in-out hover:border-[#09327B] hover:outline-none focus-within:outline-none focus:border-[#09327B] placeholder:text-base placeholder:leading-4 placeholder:opacity-30 mt-2"
                  onChange={(e) => setAmountData(e.target.value)}
                />

              </div>
              <div className="pt-3 pl-4">
                <Button variant="primary" size="sm" onClick={handleCloseClaimEntryTextbox}>
                  {t('create')}
                </Button>
              </div>
              <div className="flex-grow text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">{t('grandTotal')} <span className="font-bold">{stampDetails?.reduce((n, { amount }) => n + amount, 0)} Rs</span></div>

            </div>
          )
          : getAdvancedAmount()
      }

      <div>
        <table className="w-full mt-[14px]">
          <tbody>
            <tr className="bg-white rounded-[8px]">
              <td className="pt-[20px] pr-[30px] pb-[20px] text-left w-[30%]">
                <span className="text-[#153171] text-[14px] font-[400]">{t('openingBalance')}</span>
              </td>
              <td className="pl-[30px] w-[36%]">
                <span className="text-[#3C4449] text-[16px] font-[500]">0</span>
              </td>
              <td className="pt-[20px] pr-[30px] pb-[20px] text-left w-[30%]">
                <span className="text-[#153171] text-[14px] font-[400]">{t('expenditure')}</span>
              </td>
              <td className="pl-[30px] w-[36%]">
                <span className="text-[#3C4449] text-[16px] font-[500]">0</span>
              </td>
            </tr>
            <tr className="bg-white rounded-[8px]">
              <td className="pt-[20px] pr-[30px] pb-[20px] text-left w-[30%]">
                <span className="text-[#153171] text-[14px] font-[400]">{t('currentYear')}</span>
              </td>
              <td className="pl-[30px] w-[36%]">
                <span className="text-[#3C4449] text-[16px] font-[500]">2024-25</span>
              </td>
              <td className="pt-[20px] pr-[30px] pb-[20px] text-left w-[30%]">
                <span className="text-[#153171] text-[14px] font-[400]">{t('balance')}</span>
              </td>
              <td className="pl-[30px] w-[36%]">
                <span className="text-[#3C4449] text-[16px] font-[500]">0</span>
              </td>
            </tr>
            <tr className="bg-white rounded-[8px]">
              <td className="pt-[20px] pr-[30px] pb-[20px] text-left w-[30%]">
                <span className="text-[#153171] text-[14px] font-[400]">{t('total')}</span>
              </td>
              <td className="pl-[30px] w-[36%]">
                <span className="text-[#3C4449] text-[16px] font-[500]">0</span>
              </td>
              <td className="pt-[20px] pr-[30px] pb-[20px] text-left w-[30%]">
                <span className="text-[#153171] text-[14px] font-[400]">{t('unsentLetterCount')}</span>
              </td>
              <td className="pl-[30px] w-[36%]">
                <span className="text-[#3C4449] text-[16px] font-[500]">0</span>
              </td>
            </tr>

          </tbody>
        </table>
      </div>
      <div className="flex justify-end gap-3 pt-3 pb-3">
        <Button variant="secondary_outline" size="sm" type="submit" form="stamp-inventory-view-form" onClick={handleClose}>
          {t('cancel')}
        </Button>
        {
          tableData?.length > 0
            ? (
              <Button
                variant="primary"
                size="sm"
                onClick={handleOpenOpeningBalanceViewTable}
              >
                {t('viewOpeningBalance')}
              </Button>
            )
            : (
              <Button
                variant="primary"
                size="sm"
                onClick={handleOpeningBalance}
              >
                {t('openingBalance')}
              </Button>
            )
        }

        <Button variant="primary" size="sm" onClick={handleOpenClaimEntryTextbox}>
          {t('createClaim')}
        </Button>

      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  inventoryViewParams: getStampInventoryViewListParams,
  tableLoader: getTableLoader,
  stampInventoryList: getStampInventoyrTableList,
  stampDetails: getStampDetails,
  stampDenominationDetails: getStampDenomination
});

const mapDispatchToProps = (dispatch) => ({
  fetchInventoryViewTableList: (data) => dispatch(actions.fetchInventoryViewTableList(data)),
  setStampInventoryViewListParams: (data) => dispatch(sliceActions.setStampInventoryViewListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  fetchStampDetails: (data) => dispatch(actions.fetchStampDetails(data)),
  getCashDenominationTypes: (data) => dispatch(actions.getCashDenominationTypes(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(StampInventoryView);
