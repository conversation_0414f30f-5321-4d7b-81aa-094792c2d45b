import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { t } from 'common/components';
import { BASE_PATH } from 'common/constants';
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { dark } from 'utils/color';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getTableLoader, getUserInfo } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getDraftSendLetterList, getDraftSendLetterListParams } from 'pages/Dispatch-latest/selector';
import { actions as sliceActions } from '../../../slice';
import * as actions from '../../../actions';
import DispatchSendLetterFilters from './DispatchSendLetterFilters';
import DispatchSendLetterDetails from './DispatchSendLetterDetails';

const List = ({
  draftSendLetterListParams, setDraftSendLetterListParams, fetchDraftSendLetterList,
  setTableLoader, tableLoader, draftSendLetterList
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [search, setSearch] = useState('');

  const navigate = useNavigate();

  const backToHome = () => {
    navigate(`${BASE_PATH}/services/dispatch`);
  };

  useEffect(() => {
    if (draftSendLetterListParams) {
      setTableLoader({ loading: true, id: 'draft-send-letter-list' });
      fetchDraftSendLetterList();
    }
  }, [draftSendLetterListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setDraftSendLetterListParams({
      ...draftSendLetterListParams,
      page: data
    });
  };

  useEffect(() => {
    if (draftSendLetterList) {
      setTableLoader({ loading: false, id: 'draft-send-letter-list' });
      if (Object.keys(draftSendLetterList).length > 0) {
        setTableData(draftSendLetterList.content);
        setTotalItems(Number(`${draftSendLetterList.totalPages}0`));
        setNumberOfElements(Number(draftSendLetterList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [draftSendLetterList]);

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[18px] normal font-bold leading-[28px] capitalize grow-[1] pt-1">
          {t('dispatchSendLetter')}
        </div>

      </div>
      <div>
        <div className="bg-white pl-10 py-5 rounded-lg">
          <DispatchSendLetterFilters
            setSearch={setSearch}
            search={search}
            setDraftSendLetterListParams={setDraftSendLetterListParams}
            draftSendLetterListParams={draftSendLetterListParams}
          />
        </div>
        <DispatchSendLetterDetails
          tableData={tableData}
          totalItems={totalItems}
          activeRows={activeRows}
          page={page}
          numberOfElements={numberOfElements}
          onPageClick={onPageClick}
          tableLoader={tableLoader}
        />
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  draftSendLetterListParams: getDraftSendLetterListParams,
  tableLoader: getTableLoader,
  draftSendLetterList: getDraftSendLetterList,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchDraftSendLetterList: (data) => dispatch(actions.fetchDraftSendLetterList(data)),
  setDraftSendLetterListParams: (data) => dispatch(sliceActions.setDraftSendLetterListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
