import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, FormWrapper, IconButton, Input, InputGroup, InputRightElement, t
} from 'common/components';
import { getCounterOperator, getSeats, getUserInfo } from 'pages/common/selectors';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import * as commonActions from 'pages/common/actions';
import { FILTER_TYPE } from 'pages/common/constants';
import SearchIcon from 'assets/SearchIcon';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    paddingRight: '9px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const DispatchSendLetterFilters = ({
  fetchCounterOperator, counterOperatorDropdown,
  seatsDropdown, fetchSeats, userInfo, setSearch, search, setDraftSendLetterListParams,
  draftSendLetterListParams
}) => {
  const [post, setPost] = useState([]);

  useEffect(() => {
    if (seatsDropdown?.length > 0) {
      const postArray = seatsDropdown?.filter((item) => item?.name !== null && item?.name !== '' && item?.name !== ' ');
      setPost(postArray);
    } else {
      setPost([]);
    }
  }, [seatsDropdown]);

  useEffect(() => {
    fetchCounterOperator();
  }, []);
  const {
    control,
    setValue
  } = useForm({
    mode: 'all'
  });

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.DEPARTMENT:
        if (data) {
          fetchSeats({ functionalGroupId: data?.functionalGroupId, officeId: userInfo?.id });
          setDraftSendLetterListParams({
            ...draftSendLetterListParams,
            functionalGroupId: data
          });
        } else {
          setValue('seat', '');
          setPost([]);
        }
        break;
      case FILTER_TYPE.SEAT:
        if (data) {
          setDraftSendLetterListParams({
            ...draftSendLetterListParams,
            seatName: data
          });
        }
        break;
      case FILTER_TYPE.SEARCH_KEY_WORD:
        if (data) {
          setDraftSendLetterListParams({
            ...draftSendLetterListParams,
            keyword: data
          });
        }
        break;

      default:
        break;
    }
  };

  return (
    <div>
      <div className="px-5 pt-2 pb-5">
        <FormWrapper px={false} py={false}>
          <div className="sm:col-span-4 col-span-12">
            <FormController
              name="department"
              type="select"
              label={t('department')}
              placeholder={t('select')}
              control={control}
              optionKey="name"
              isClearable
              handleChange={(data) => handleFieldChange(FILTER_TYPE.DEPARTMENT, data)}
              options={counterOperatorDropdown || []}
            />
          </div>
          <div className="sm:col-span-4 col-span-12">
            <FormController
              name="seat"
              type="select"
              label={t('seat')}
              placeholder={t('searchHere')}
              control={control}
              options={post}
              optionKey="postId"
              isClearable
              handleChange={(data) => {
                handleFieldChange(FILTER_TYPE.SEAT, data);
              }}
            />
          </div>
          <div className="sm:col-span-4 col-span-12">
            <InputGroup style={styles.search}>
              <Input
                placeholder={t('searchHere')}
                style={styles.search.input}
                value={search}
                onChange={(event) => {
                  setSearch(event.target.value);
                }}
              />
              <InputRightElement>
                <IconButton onClick={() => handleFieldChange(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
              </InputRightElement>
            </InputGroup>
          </div>

        </FormWrapper>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  counterOperatorDropdown: getCounterOperator,
  seatsDropdown: getSeats,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchCounterOperator: (data) => dispatch(commonActions.fetchCounterOperator(data)),
  fetchSeats: (data) => dispatch(commonActions.fetchSeats(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DispatchSendLetterFilters);
