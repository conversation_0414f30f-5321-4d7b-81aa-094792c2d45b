import TableView from 'assets/TableView';
import { t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const DispatchSendLetterDetails = ({
  tableData, tableLoader, totalItems,
  activeRows, page, numberOfElements,
  onPageClick
}) => {
  const navigate = useNavigate();

  const handleFileNumber = (fileData) => {
    let fileNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNumber = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
      );
    }
    return <div className="block">{fileNumber}</div>;
  };

  const handleDraftType = (fileData) => {
    let draftType;
    if (fileData?.row) {
      const cellData = fileData?.row;
      draftType = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.draftType}</div>
      );
    }
    return <div className="block">{draftType}</div>;
  };

  const handleDraftNo = (fileData) => {
    let draftNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      draftNo = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.draftNo}</div>
      );
    }
    return <div className="block">{draftNo}</div>;
  };

  const handleOfficeType = (fileData) => {
    let officeType;
    if (fileData?.row) {
      const cellData = fileData?.row;
      officeType = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.officeType}</div>
      );
    }
    return <div className="block">{officeType}</div>;
  };

  const handleDispatchSection = (fileData) => {
    let dispatchSection;
    if (fileData?.row) {
      const cellData = fileData?.row;
      dispatchSection = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.seatName}</div>
      );
    }
    return <div className="block">{dispatchSection}</div>;
  };

  const handleDraftDate = (fileData) => {
    let date;
    if (fileData?.row) {
      const cellData = fileData?.row;
      date = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.date}</div>
      );
    }
    return <div className="block">{date}</div>;
  };

  const handleNoOfCommunications = (fileData) => {
    let draftsCount;
    if (fileData?.row) {
      const cellData = fileData?.row;
      draftsCount = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.draftsCount}</div>
      );
    }
    return <div className="block">{draftsCount}</div>;
  };

  const viewActions = (val) => {
    navigate(`${val?.draftId}/preview`);
  };
  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('draftType'),
      field: 'draftType',
      alignment: 'left',
      cell: (field) => handleDraftType(field)
    },
    {
      header: t('draftNo'),
      field: 'draftNo',
      alignment: 'left',
      cell: (field) => handleDraftNo(field)
    },
    {
      header: t('officeType'),
      field: 'officeType',
      cell: (field) => handleOfficeType(field)
    },
    {
      header: t('dispatchSection'),
      field: 'seatName',
      alignment: 'left',
      cell: (field) => handleDispatchSection(field)
    },
    {
      header: t('draftDate'),
      field: 'draftDate',
      alignment: 'left',
      cell: (field) => handleDraftDate(field)
    },
    {
      header: t('noOfCommunications'),
      field: 'draftsCount',
      alignment: 'left',
      cell: (field) => handleNoOfCommunications(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            viewActions(row);
          }
        }
      ]
    }

  ];

  return (
    <div className="col-span-12 pb-20 mt-3">
      <CommonTable
        variant="dashboard"
        tableData={tableData}
        columns={columns}
        activeRows={activeRows}
        onPageClick={onPageClick}
        itemsPerPage={10}
        totalItems={totalItems}
        currentPage={page}
        paginationEnabled
        tableLoader={tableLoader?.loading && tableLoader?.id === 'disposed-file-table'}
        numberOfElements={numberOfElements}
      />

    </div>
  );
};

export default DispatchSendLetterDetails;
