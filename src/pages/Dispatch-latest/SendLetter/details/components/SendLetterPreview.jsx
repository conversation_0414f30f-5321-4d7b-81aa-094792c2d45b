import { CustomTable, IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { t } from 'common/components';
import { BASE_PATH } from 'common/constants';
import { generatePdf } from 'hooks/generatePdf';
import { getDraftSendLetterPreviewList, getDraftSendLetterPreviewListParams } from 'pages/Dispatch-latest/selector';
import { getTableLoader, getUserInfo } from 'pages/common/selectors';
import DraftPreview from 'pages/file/details/components/draft/components/DraftPreview';
import { DRAFT_PDF_URL } from 'pages/file/details/constants';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { dark } from 'utils/color';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from '../../../slice';
import * as actions from '../../../actions';

const SendLetterPreview = ({
  draftSendLetterPreviewListParams, draftSendLetterPreviewList,
  fetchDraftSendLetterPreviewList
}) => {
  const params = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState({ loading: false });
  const [draftPreview, setDraftPreview] = useState({});

  const [tableData, setTableData] = useState([]);

  const handlePreview = async () => {
    setLoading({ loading: true, id: params?.id });
    const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${params?.id}`;
    const generate = generatePdf({ url: urlDraftPdf });
    const { data, status } = await generate.then((result) => result);
    if (status === 'success') {
      setLoading({ loading: false });
      setLoading(false);
      setDraftPreview(data);
    } else {
      setLoading({ loading: false });
    }
  };

  const back = () => {
    navigate(`${BASE_PATH}/services/dispatch/send-letter`);
  };

  useEffect(() => {
    if (params?.id) {
      handlePreview();
    }
  }, [params?.id]);

  useEffect(() => {
    if (draftSendLetterPreviewListParams && params?.id) {
      fetchDraftSendLetterPreviewList({ draftId: params?.id });
    }
  }, [draftSendLetterPreviewListParams, params?.id]);

  useEffect(() => {
    if (draftSendLetterPreviewList) {
      if (Object.keys(draftSendLetterPreviewList).length > 0) {
        setTableData(draftSendLetterPreviewList?.flat());
      } else {
        setTableData([]);
      }
    }
  }, [draftSendLetterPreviewList]);

  const handleDraftNo = (fileData) => {
    let draftNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      draftNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.draftNo}</div>;
    }
    return <div className="block">{draftNo}</div>;
  };

  const handleName = (fileData) => {
    let name;
    if (fileData?.row) {
      const cellData = fileData?.row;
      name = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.name}</div>;
    }
    return <div className="block">{name}</div>;
  };

  const handleAddress = (fileData) => {
    let address;
    if (fileData?.row) {
      const cellData = fileData?.row;
      address = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData?.address}</div>;
    }
    return <div className="block">{address}</div>;
  };

  const handleStatus = (fileData) => {
    let dispatchStatus;
    if (fileData?.row) {
      const cellData = fileData?.row;
      dispatchStatus = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData?.dispatchStatus}</div>;
    }
    return <div className="block">{dispatchStatus}</div>;
  };

  const columns = [
    {
      header: t('draftNo'),
      field: 'draftNo',
      alignment: 'left',
      cell: (field) => handleDraftNo(field)
    },
    {
      header: t('name'),
      field: 'name',
      alignment: 'left',
      cell: (field) => handleName(field)
    },
    {
      header: t('address'),
      field: 'address',
      alignment: 'left',
      cell: (field) => handleAddress(field)
    },

    {
      header: t('status'),
      field: 'dispatchStatus',
      cell: (field) => handleStatus(field)
    }
    // {
    //   header: t('view'),
    //   alignment: 'left',
    //   type: 'actions',
    //   actions: [
    //     {
    //       icon: <TableView />,
    //       onClick: (row) => {
    //         viewActions(row);
    //       }
    //     }
    //   ]
    // }

  ];

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={back} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[18px] normal font-bold leading-[28px] capitalize grow-[1] pt-1">
          {t('dispatchSendLetterPreview')}
        </div>

      </div>
      <div className="grid grid-cols-2 gap-4">

        <div className="bg-white pl-10 py-5 rounded-lg mt-3" style={{ height: 'calc(100vh - 236px)', overflowY: 'auto' }}>
          <DraftPreview draftPreview={params?.id ? draftPreview : null} loading={loading?.loading && loading?.id === params?.id} />
        </div>

        <div className="rounded-lg">
          <CustomTable
            tableData={tableData}
            columns={columns}
            paginationEnabled
            paginationPosition="end"
            itemsPerPage={10}
          />
        </div>
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  draftSendLetterPreviewListParams: getDraftSendLetterPreviewListParams,
  tableLoader: getTableLoader,
  draftSendLetterPreviewList: getDraftSendLetterPreviewList,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchDraftSendLetterPreviewList: (data) => dispatch(actions.fetchDraftSendLetterPreviewList(data)),
  setDraftSendLetterPreviewListParams: (data) => dispatch(sliceActions.setDraftSendLetterPreviewListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(SendLetterPreview);
