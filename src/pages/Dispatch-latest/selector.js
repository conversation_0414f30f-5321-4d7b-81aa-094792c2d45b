import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getDispatchDetails = (state) => state[STATE_REDUCER_KEY];

const stampInventoryViewListParams = (state) => state?.stampInventoryViewListParams;
export const getStampInventoryViewListParams = flow(getDispatchDetails, stampInventoryViewListParams);

const stampInventoyrTableList = (state) => state?.stampInventoyrTableList;
export const getStampInventoyrTableList = flow(getDispatchDetails, stampInventoyrTableList);

const stampDenomination = (state) => state?.stampDenomination;
export const getStampDenomination = flow(getDispatchDetails, stampDenomination);

const stampDetails = (state) => state?.stampDetails;
export const getStampDetails = flow(getDispatchDetails, stampDetails);

const draftSendLetterListParams = (state) => state?.draftSendLetterListParams;
export const getDraftSendLetterListParams = flow(getDispatchDetails, draftSendLetterListParams);

const draftSendLetterList = (state) => state?.draftSendLetterList;
export const getDraftSendLetterList = flow(getDispatchDetails, draftSendLetterList);

const draftSendLetterPreviewListParams = (state) => state?.draftSendLetterPreviewListParams;
export const getDraftSendLetterPreviewListParams = flow(getDispatchDetails, draftSendLetterPreviewListParams);

const draftSendLetterPreviewList = (state) => state?.draftSendLetterPreviewList;
export const getDraftSendLetterPreviewList = flow(getDispatchDetails, draftSendLetterPreviewList);
