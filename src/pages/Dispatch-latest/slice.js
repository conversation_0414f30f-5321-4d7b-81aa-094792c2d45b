import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {

  stampInventoryViewListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc'
  },
  draftSendLetterListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    keyword: null,
    seatName: null,
    functionalGroupId: null,
    // penNo: getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true)?.pen,
    date: null,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID)
  },

  draftSendLetterPreviewListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID)
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setStampInventoryViewListParams: (state, { payload }) => {
      _.set(state, 'stampInventoryViewListParams', payload);
    },
    setStampInventoryTableList: (state, { payload }) => {
      _.set(state, 'stampInventoyrTableList', payload);
    },
    setStampDenomination: (state, { payload }) => {
      _.set(state, 'stampDenomination', payload);
    },
    setStampDetails: (state, { payload }) => {
      _.set(state, 'stampDetails', payload);
    },
    setDraftSendLetterListParams: (state, { payload }) => {
      _.set(state, 'draftSendLetterListParams', payload);
    },
    setDraftSendLetterList: (state, { payload }) => {
      _.set(state, 'draftSendLetterList', payload);
    },

    setDraftSendLetterPreviewListParams: (state, { payload }) => {
      _.set(state, 'draftSendLetterPreviewListParams', payload);
    },

    setDraftSendLetterPreviewList: (state, { payload }) => {
      _.set(state, 'draftSendLetterPreviewList', payload);
    }
  }
});

export const { actions, reducer } = slice;
