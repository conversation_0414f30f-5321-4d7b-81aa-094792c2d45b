import { lazy } from 'react';

const DispatchCards = lazy(() => import('./DispatchCards'));
const DispatchOutbox = lazy(() => import('./Outbox/details/components/List'));
const SendLetter = lazy(() => import('./SendLetter/details/components/List'));
const SendLetterPreview = lazy(() => import('./SendLetter/details/components/SendLetterPreview'));

const routes = [{
  path: 'dispatch',
  element: <DispatchCards />
},
{
  path: 'dispatch/dispatch-outbox',
  element: <DispatchOutbox />
},
{
  path: 'dispatch/send-letter/:id/preview',
  element: <SendLetterPreview />
},
{
  path: 'dispatch/send-letter',
  element: <SendLetter />
}

];

export { routes };
