import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  all, takeLatest, fork, put, take, select, call
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { STORAGE_KEYS } from 'common/constants';
import { getDataFromStorage } from 'utils/encryption';
import { Toast, t } from 'common/components';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { getDraftSendLetterListParams, getDraftSendLetterPreviewListParams, getStampInventoryViewListParams } from './selector';
import { ACTION_TYPES } from './actions';
import { mergeDraftDetails } from './helper';

const { successTost, errorTost } = Toast;

export function* fetchStampInventoryTableList() {
  const apiParams = yield select(getStampInventoryViewListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const userDetails = getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true) || [];
  const officeId = JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID));

  const body = { penNo: userDetails?.pen, officeId, finYear: new Date().getFullYear() };

  yield fork(handleAPIRequest, api.fetchStampInventoryTableList, updatedParams, body);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST_SUCCESS,
    ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST_SUCCESS) {
    const copy = _.get(responsePayLoad, 'data', {});
    const array = JSON.parse(JSON.stringify(_.get(responsePayLoad, 'data', {})?.content)) || [];
    const reverseArray = array?.reverse();
    copy.content = reverseArray;

    yield put(sliceActions.setStampInventoryTableList(copy));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'inventory-view-table' }));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'inventory-view-table' }));
  }
}

export function* fetchCashDenomination({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchCashDenomination, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES_SUCCESS,
    ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES_SUCCESS) {
    const notesArray = [];
    _.get(responsePayLoad, 'data', {}).map((val) => {
      if (val?.isNote === 1) {
        notesArray.push({
          ...val, defaultValue: 0, outputValue: 0, value: ''
        });
      }
      return null;
    });
    yield put(sliceActions.setStampDenomination(notesArray));
  }
}

export function* saveStampOpeningDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveStampOpeningDetails, payload);
  const { type } = yield take([
    ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS_SUCCESS,
    ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS_SUCCESS) {
    yield call(successTost, { id: 'saved', title: t('success'), description: 'success' });
    yield put(commonSliceActions.setTableLoader({ loading: true, id: 'inventory-view-table' }));
    yield call(fetchStampInventoryTableList);
  } else {
    yield call(errorTost, { id: t('error'), title: t('error'), description: 'failed' });
  }
}

export function* fetchStampDetails() {
  const apiParams = yield select(getStampInventoryViewListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const userDetails = getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true) || [];
  const officeId = JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID));

  const body = { penNo: userDetails?.pen, officeId, finYear: new Date().getFullYear() };

  yield fork(handleAPIRequest, api.fetchStampDetails, updatedParams, body);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_STAMP_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_STAMP_DETAILS_FAILURE]);
  if (type === ACTION_TYPES.FETCH_STAMP_DETAILS_SUCCESS) {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'stamp-table' }));
    const copy = _.get(responsePayLoad, 'data', {});
    const array = JSON.parse(JSON.stringify(_.get(responsePayLoad, 'data', {})?.content)) || [];
    const reverseArray = array?.reverse();
    copy.content = reverseArray;
    const finalResponse = copy?.content?.length > 0 && _.map(copy?.content?.[0]?.stampAccountPenSearchResponses[0]?.stampAccountDetailsSearchResponses, (item) => {
      let response = {};
      response = {
        grandTotalAmount: copy?.content?.[0]?.grandTotalAmount,
        grandTotalOpeningAmount: copy?.content?.[0]?.grandTotalOpeningAmount,
        penNo: copy?.content?.[0]?.stampAccountPenSearchResponses[0]?.penNo,
        amount: item?.amount,
        count: item?.count,
        designation: item?.designation,
        employeeName: item?.employeeName,
        stampOpeningAmount: item?.stampOpeningAmount,
        stampOpeningCount: item?.stampOpeningCount,
        stampTypeId: item?.stampTypeId,
        totalAmountForPenNumber: copy?.content?.[0]?.stampAccountPenSearchResponses[0]?.totalAmountForPenNumber,
        totalOpeningAmountForPenNumber: copy?.content?.[0]?.stampAccountPenSearchResponses[0]?.totalOpeningAmountForPenNumber,
        outputValue: 0,
        value: '',
        defaultValue: 0
      };
      return response;
    });

    if (finalResponse?.length > 0) {
      yield put(sliceActions.setStampDetails(finalResponse?.flat()));
    } else {
      yield put(sliceActions.setStampDetails([]));
    }
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'stamp-table' }));
  }
}

export function* saveStampDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveStampDetails, payload);
  const { type } = yield take([
    ACTION_TYPES.SAVE_STAMP_DETAILS_SUCCESS,
    ACTION_TYPES.SAVE_STAMP_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_STAMP_DETAILS_SUCCESS) {
    yield call(successTost, { id: 'saved', title: t('success'), description: 'success' });
    yield call(fetchStampDetails);
  } else {
    yield call(errorTost, { id: t('error'), title: t('error'), description: 'failed' });
  }
}

export function* fetchDraftSendLetterList() {
  const apiParams = yield select(getDraftSendLetterListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchDraftSendLetterList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST_SUCCESS,
    ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST_SUCCESS) {
    yield put(sliceActions.setDraftSendLetterList(_.get(responsePayLoad, 'data', {})));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'draft-send-letter-list' }));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'draft-send-letter-list' }));
  }
}

export function* fetchDraftSendLetterPreviewList({ payload = {} }) {
  const apiParams = yield select(getDraftSendLetterPreviewListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const officeId = JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID));
  const finalParams = { ...updatedParams, draftId: payload?.draftId, officeId };

  yield fork(handleAPIRequest, api.fetchDraftSendLetterPreviewList, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_SUCCESS,
    ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST_SUCCESS) {
    yield put(sliceActions.setDraftSendLetterPreviewList(mergeDraftDetails(_.get(responsePayLoad, 'data', {}))));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'draft-send-letter-preview-list' }));
  } else {
    yield put(sliceActions.setDraftSendLetterPreviewList([]));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'draft-send-letter-preview-list' }));
  }
}

export default function* inboxSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_STAMP_INVENTORY_LIST, fetchStampInventoryTableList),
    takeLatest(ACTION_TYPES.FETCH_CASH_DENOMINATION_TYPES, fetchCashDenomination),
    takeLatest(ACTION_TYPES.SAVE_STAMP_OPENING_DETAILS, saveStampOpeningDetails),
    takeLatest(ACTION_TYPES.FETCH_STAMP_DETAILS, fetchStampDetails),
    takeLatest(ACTION_TYPES.SAVE_STAMP_DETAILS, saveStampDetails),
    takeLatest(ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_LIST, fetchDraftSendLetterList),

    takeLatest(ACTION_TYPES.FETCH_DRAFT_SEND_LETTER_PREVIEW_LIST, fetchDraftSendLetterPreviewList)
  ]);
}
