import _ from 'lodash';

export const mergeDraftDetails = (data) => {
  if (!data?.content?.length) {
    return [];
  }

  return _.map(data.content, (item) => {
    const response = [];

    if (item?.addressDetails?.length) {
      _.map(item.addressDetails, (val) => {
        response.push({
          date: item?.date,
          dispatchDate: item?.dispatchDate,
          dispatchStatus: item?.dispatchStatus,
          draftId: item?.draftId,
          draftNo: item?.draftNo,
          draftStatus: item?.draftStatus,
          draftText: item?.draftText,
          draftType: item?.draftType,
          draftsCount: item?.draftsCount,
          fileNo: item?.fileNo,
          modeOfDispatchId: item?.modeOfDispatchId,
          seatName: item?.seatName,
          subject: item?.subject,

          address: val?.address,
          addressNo: val?.addressNo,
          district: val?.district,
          id: val?.id,
          name: val?.name,
          officeName: val?.officeName,
          officerName: val?.officer<PERSON><PERSON>,
          wardId: val?.wardId
        });
      });
    }

    return response;
  });
};
