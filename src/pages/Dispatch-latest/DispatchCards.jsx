import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import TableView from 'assets/TableView';
import { t } from 'common/components';
import React from 'react';
import { dark } from 'utils/color';

import { Icons } from 'assets/icons';
import { Images } from 'assets/images';
import { EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { DISPATCH } from 'pages/common/constants';

const backToHome = () => {
  window.location.href = EMPLOYEE_SERVICE_PATH;
};

const images = {
  'Send Letter': Images.Reports['Send Letter'],
  Outbox: Images.Reports.Outbox
};

const icons = {
  'Send Letter': Icons.Reports['Send Letter'],
  Outbox: Icons.Reports.Outbox
};

const CardRender = ({ cardDetail }) => {
  return (

    <div
      className="bg-white w-full h-full flex flex-col p-3 rounded-lg"
      key={cardDetail?.id}
      role="button"
      tabIndex={0}
      onClick={() => {
        if (cardDetail?.route !== '') {
          window.location = `${window.location.origin}/${cardDetail?.route}`;
        }
      }}
      aria-hidden="true"
    >
      <div className="relative w-full transition-all ease-linear duration-500 rounded-lg">
        <img className="w-full h-full rounded-lg" src={images[cardDetail?.name]} alt="dummy" />
      </div>
      <div className="w-[60px] h-[60px] border-[1px] rounded-full mx-auto mt-[-30px] bg-white relative pt-[15px] pl-[19px]">
        <TableView />
      </div>
      <div
        key={cardDetail?.name}
        className="w-full relative  duration-200 flex justify-between items-center px-[13px] py-[15px] cursor-pointer bg-[#F2F6FF] rounded-[4px] mt-[10px]"
        aria-hidden="true"
      >
        <div className="flex justify-center items-center gap-2 w-full">
          {icons[cardDetail?.name]}
          <span className="text-[#09327B] text-base font-bold leading-4">{cardDetail?.name}</span>
        </div>
      </div>
    </div>

  );
};

const ReportsCards = () => {
  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('dispatch')}
        </div>
      </div>
      <div className="grid grid-cols-12 gap-6 auto-rows-[1fr]">
        <div className="col-span-12 pb-20">
          <div className="grid grid-cols-5 gap-4">

            {
              DISPATCH
                ?.map((cardDetail) => {
                  return (
                    <CardRender key={cardDetail?.id} cardDetail={cardDetail} />
                  );
                })
            }

          </div>
        </div>
      </div>
    </>
  );
};

export default ReportsCards;
