import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {
  FETCH_E_SIGN_REQUESTS: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_REQUESTS`,
  FETCH_E_SIGN_REQUESTS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_REQUESTS_REQUEST`,
  FETCH_E_SIGN_REQUESTS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_REQUESTS_SUCCESS`,
  FETCH_E_SIGN_REQUESTS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_REQUESTS_FAILURE`
};

export const fetchESignRequestsAction = createAction(ACTION_TYPES.<PERSON>ET<PERSON>_E_SIGN_REQUESTS);
