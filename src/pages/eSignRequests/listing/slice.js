import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  eSignRequests: []
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setESignRequests: (state, { payload }) => {
      _.set(state, 'eSignRequests', payload);
    }
  }
});

export const { actions, reducer } = slice;
