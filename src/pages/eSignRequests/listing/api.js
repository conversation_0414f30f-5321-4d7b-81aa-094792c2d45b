import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchESignRequestsApi = ({ officeId, ...restParams }) => {
  return {
    url: API_URL.E_SIGN.FETCH_E_SIGN_REQUESTS?.replace(':officeId', officeId),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_E_SIGN_REQUESTS_REQUEST,
        ACTION_TYPES.FETCH_E_SIGN_REQUESTS_SUCCESS,
        ACTION_TYPES.FETCH_E_SIGN_REQUESTS_FAILURE
      ],
      params: { ...restParams }
    }
  };
};
