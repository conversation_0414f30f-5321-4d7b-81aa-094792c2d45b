import {
  all, takeLatest, fork, put, take
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSlice } from 'pages/common/slice';
import { t } from 'i18next';
import * as api from './api';
import { ACTION_TYPES } from './actions';
import { actions as sliceActions } from './slice';

export function* fetchESignRequests({ payload }) {
  yield fork(handleAPIRequest, api.fetchESignRequestsApi, payload);

  const { payload: response = {}, type } = yield take([
    ACTION_TYPES.FETCH_E_SIGN_REQUESTS_SUCCESS,
    ACTION_TYPES.FETCH_E_SIGN_REQUESTS_FAILURE
  ]);

  if (type === ACTION_TYPES.FETCH_E_SIGN_REQUESTS_SUCCESS) {
    yield put(
      sliceActions.setESignRequests(_.get(response, 'data', {}))
    );
  } else {
    yield put(
      commonSlice.setAlertAction({
        open: true,
        variant: 'error',
        message: t('requestFetchingFailed'),
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
  }

  yield put(commonSlice.setTableLoader({ loading: false, id: 'e-sign-request-table' }));
}

export default function* eSignSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_E_SIGN_REQUESTS, fetchESignRequests)
  ]);
}
