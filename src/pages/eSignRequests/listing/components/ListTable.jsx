import BackArrow from 'assets/BackIcon';
import { Flex, IconButton } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { t } from 'i18next';
import { getTableLoader, getUserInfo } from 'pages/common/selectors';
import { connect } from 'react-redux';
import { dark } from 'utils/color';
import { actions as commonSliceActions } from 'pages/common/slice';
import { createStructuredSelector } from 'reselect';
import { useEffect, useState } from 'react';
import { E_SIGN_STATUS } from 'pages/common/constants';
import TableView from 'assets/TableView';
import { maskPanNumber, routeRedirect } from 'utils/common';
import { getESignRequests } from '../selectors';
import { fetchESignRequestsAction } from '../actions';

const ListTable = ({
  userInfo = {},
  tableLoader,
  fetchESignRequests,
  eSignRequests,
  setTableLoader
}) => {
  const [page, setPage] = useState(0);

  const handleBack = () => {
    window.history.back();
  };

  const columns = [
    {
      header: t('siNo'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => (field?.rowIndex || 0) + 1
    },
    {
      header: t('name'),
      field: 'nameOnPan',
      alignment: 'left'
    },
    {
      header: t('panNumber'),
      field: 'pan',
      alignment: 'left',
      cell: ({ field }) => maskPanNumber(field)
    },
    {
      header: t('mobileNumber'),
      field: 'mobileNumber',
      alignment: 'left'
    },
    {
      header: t('status'),
      field: 'stage',
      alignment: 'center'
    },
    {
      header: t('action'),
      alignment: 'center',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (item) => {
            routeRedirect(
              `ui/file-management/profile/es/enroll/${item?.userId}/approve`
            );
          }
        }
      ]
    }
  ];

  useEffect(() => {
    if (userInfo?.officeId) {
      setTableLoader({ loading: true, id: 'e-sign-request-table' });
      fetchESignRequests({
        officeId: userInfo?.officeId,
        stage: E_SIGN_STATUS.SUBMIT,
        page,
        size: 10
      });
    }
  }, [userInfo, page]);

  return (
    <div>
      <Flex alignItems="center" gap={2} mb={2}>
        <IconButton
          onClick={handleBack}
          variant="unstyled"
          icon={<BackArrow color={dark} width="10" height="10" />}
        />
        <h6 className="font-bold text-[#09327B]">{t('eSignRequests')}</h6>
      </Flex>
      <div className="max-h-[70vh] overflow-auto">
        <CommonTable
          paginationEnabled
          currentPage={page}
          columns={columns}
          itemsPerPage={10}
          onPageClick={(currentPage) => setPage(currentPage)}
          tableLoader={
            tableLoader?.loading && tableLoader?.id === 'e-sign-request-table'
          }
          tableData={eSignRequests?.content}
          totalItems={eSignRequests?.totalElements}
          numberOfElements={eSignRequests?.totalElements}
        />
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  eSignRequests: getESignRequests,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchESignRequests: (data) => dispatch(fetchESignRequestsAction(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ListTable);
