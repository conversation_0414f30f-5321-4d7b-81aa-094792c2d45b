import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_SERVICES_BY_NAME: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_NAME`,
  FETCH_SERVICES_BY_NAME_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_NAME_REQUEST`,
  FETCH_SERVICES_BY_NAME_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_NAME_SUCCESS`,
  FETCH_SERVICES_BY_NAME_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_NAME_FAILURE`,

  UPDATE_FILE: `${STATE_REDUCER_KEY}/UPDATE_FILE`,
  UPDATE_FILE_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_FILE_REQUEST`,
  UPDATE_FILE_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_FILE_SUCCESS`,
  UPDATE_FILE_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_FILE_FAILURE`,

  FETCH_PULL_FILES: `${STATE_REDUCER_KEY}/FETCH_PULL_FILES`,
  FETCH_PULL_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PULL_FILES_REQUEST`,
  FETCH_PULL_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PULL_FILES_SUCCESS`,
  FETCH_PULL_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PULL_FILES_FAILURE`,

  UPDATE_PULL_FILE: `${STATE_REDUCER_KEY}/UPDATE_PULL_FILE`,
  UPDATE_PULL_FILE_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_PULL_FILE_REQUEST`,
  UPDATE_PULL_FILE_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_PULL_FILE_SUCCESS`,
  UPDATE_PULL_FILE_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_PULL_FILE_FAILURE`

};

export const fetchServicesDetails = createAction(ACTION_TYPES.FETCH_SERVICES_BY_NAME);
export const updateFile = createAction(ACTION_TYPES.UPDATE_FILE);
export const fetchPullFiles = createAction(ACTION_TYPES.FETCH_PULL_FILES);
export const updatePullFile = createAction(ACTION_TYPES.UPDATE_PULL_FILE);
