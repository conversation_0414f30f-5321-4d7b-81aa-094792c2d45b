import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getPullFileNew = (state) => state[STATE_REDUCER_KEY];

const servicesSearchList = (state) => state?.servicesSearchList;
export const getServicesSearchList = flow(getPullFileNew, servicesSearchList);

const pullFileParams = (state) => state?.pullFileParams;
export const getPullFileParams = flow(getPullFileNew, pullFileParams);

const pullFileList = (state) => state?.pullFileList;
export const getPullFileList = flow(getPullFileNew, pullFileList);

const pullStatus = (state) => state?.pullStatus;
export const getPullStatus = flow(getPullFileNew, pullStatus);
