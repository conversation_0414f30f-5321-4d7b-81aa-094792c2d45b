import {
  all, takeLatest, fork, put, take, call, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { Toast } from 'common/components';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { getPullFileParams } from './selectors';

const { successTost } = Toast;

export function* fetchServicesDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchServicesDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SERVICES_BY_NAME_SUCCESS,
    ACTION_TYPES.FETCH_SERVICES_BY_NAME_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SERVICES_BY_NAME_SUCCESS) {
    yield put(sliceActions.setServicesSearchList(_.get(responsePayLoad, 'data', {})));
  }
}

export function* updateFile({ payload = {} }) {
  yield fork(handleAPIRequest, api.updateFile, payload);
  const { type } = yield take([
    ACTION_TYPES.UPDATE_FILE_SUCCESS,
    ACTION_TYPES.UPDATE_FILE_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_FILE_SUCCESS) {
    yield call(successTost, { id: 'created', title: 'Success', description: 'File Pulled successfully' });
  }
}

export function* fetchPullFiles() {
  const apiParams = yield select(getPullFileParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const finalParams = _.omit(updatedParams, ['search']);
  yield fork(handleAPIRequest, api.fetchPullFiles, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PULL_FILES_SUCCESS,
    ACTION_TYPES.FETCH_PULL_FILES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_PULL_FILES_SUCCESS) {
    yield put(sliceActions.setPullFiles(responsePayLoad.data));
  }
}

export function* updatePullFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.updatePullFiles, payload);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.UPDATE_PULL_FILE_SUCCESS,
    ACTION_TYPES.UPDATE_PULL_FILE_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_PULL_FILE_SUCCESS) {
    yield put(sliceActions.setPullStatus({ flag: true, data: responsePayLoad?.data }));
  }
}

export default function* counterSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_SERVICES_BY_NAME, fetchServicesDetails),
    takeLatest(ACTION_TYPES.UPDATE_FILE, updateFile),
    takeLatest(ACTION_TYPES.FETCH_PULL_FILES, fetchPullFiles),
    takeLatest(ACTION_TYPES.UPDATE_PULL_FILE, updatePullFiles)

  ]);
}
