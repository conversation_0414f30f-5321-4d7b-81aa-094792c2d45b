import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchServicesDetails = (data) => {
  return {
    url: API_URL.FILESEARCH.SERVICES_BY_NAME.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICES_BY_NAME_REQUEST,
        ACTION_TYPES.FETCH_SERVICES_BY_NAME_SUCCESS,
        ACTION_TYPES.FETCH_SERVICES_BY_NAME_FAILURE
      ]
    },
    data
  };
};

export const updateFile = (data) => {
  return {
    url: API_URL.INWARD.PULL_FILE,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_FILE_REQUEST,
        ACTION_TYPES.UPDATE_FILE_SUCCESS,
        ACTION_TYPES.UPDATE_FILE_FAILURE
      ],
      data
    }
  };
};

export const fetchPullFiles = (params) => {
  return {
    url: API_URL.PULL_FILE.FETCH_PULL_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PULL_FILES_REQUEST,
        ACTION_TYPES.FETCH_PULL_FILES_SUCCESS,
        ACTION_TYPES.FETCH_PULL_FILES_FAILURE
      ],
      params
    }
  };
};

export const updatePullFiles = (data) => {
  return {
    url: API_URL.PULL_FILE.UPDATE_PULL_FILE,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_PULL_FILE_REQUEST,
        ACTION_TYPES.UPDATE_PULL_FILE_SUCCESS,
        ACTION_TYPES.UPDATE_PULL_FILE_FAILURE
      ],
      data
    }
  };
};
