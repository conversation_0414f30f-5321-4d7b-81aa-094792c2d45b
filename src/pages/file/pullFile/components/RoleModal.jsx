import React, { useState } from 'react';
import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalOverlay, Button, ModalContent, t, FormController
} from 'common/components';
import { useForm } from 'react-hook-form';

const RoleModal = ({
  selectedFile, pullReason, updatePullFile, openRoleModal, setOpenRoleModal, loginedUserRoles
}) => {
  const [role, setRole] = useState(null);
  const {
    setValue, control
  } = useForm({
    mode: 'all',
    defaultValues: {
      role: ''
    }
  });
  const handleClose = () => {
    setOpenRoleModal(false);
  };

  const updatePullFileDetails = () => {
    const data = {
      fileNos: selectedFile,
      pullReason,
      role
    };
    updatePullFile(data);
    handleClose();
  };

  const handleFieldChange = (field, data) => {
    if (field === 'roles') {
      setValue('role', data?.name);
      setRole(data?.name);
    }
  };

  return (
    <Modal isOpen={openRoleModal} size="3xl" onClose={handleClose} className="custom-form-modal" closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <h4 size="md">
            {t('role')}
          </h4>
        </ModalHeader>
        <ModalBody>
          <div className="grid gap-3">
            <div className="col-span-12">
              <FormController
                name="role"
                type="select"
                label={t('role')}
                placeholder={t('role')}
                options={loginedUserRoles || []}
                handleChange={(data) => handleFieldChange('roles', data)}
                control={control}
                optionKey="name"
              />
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="col-span-12 flex justify-end items-center space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              mr={3}
              onClick={() => {
                handleClose();
              }}
            >
              {t('cancel')}
            </Button>
            <Button variant="secondary" onClick={updatePullFileDetails} size="sm" isDisabled={role === ''}>
              {t('submit')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default RoleModal;
