import React from 'react';
import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalOverlay, Button, ModalContent, t
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { actions as sliceActions } from '../slice';

const PullFileStatus = ({ pullStatus, setPullStatus }) => {
  const { flag = false, data = [] } = pullStatus;
  const handleClose = () => {
    setPullStatus({ flag: false, data: [] });
  };

  return (
    <Modal isOpen={flag} size="3xl" onClose={handleClose} className="custom-form-modal" closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <h4 size="md">
            {t('pullFileStatus')}
          </h4>
        </ModalHeader>
        <ModalBody>
          <table className="striped-table">
            <thead>
              <tr className="text-left">
                <th>{t('fileNo')}</th>
                <th>{t('pullReason')}</th>
                <th>{t('pullStatus')}</th>
              </tr>
            </thead>
            <tbody>
              {data?.map((row) => {
                return (
                  <tr key={row}>
                    <td>
                      {row?.fileNo}
                    </td>
                    <td>
                      {row?.pullReason}
                    </td>
                    <td>
                      {row?.pullStatus ? <span className="bg-[#C6E5D3] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#53C684] text-center">{t('success')}</span> : <span className="bg-[#FED0D0] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#E33A7A] text-center">{t('failed')}</span>}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </ModalBody>
        <ModalFooter>
          <div className="col-span-12 flex justify-end items-center space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              mr={3}
              onClick={() => {
                handleClose();
              }}
            >
              {t('close')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
});
const mapDispatchToProps = (dispatch) => ({
  setPullStatus: (data) => dispatch(sliceActions.setPullStatus(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PullFileStatus);
