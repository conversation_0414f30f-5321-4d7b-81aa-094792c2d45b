import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, <PERSON><PERSON>, t, Card, IconButton
} from 'common/components';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import * as commonActions from 'pages/common/actions.js';
import {
  getDepartments, getFiles, getServicesDropdown, getUserInfo
} from 'pages/common/selectors.js';
import { FILTER_TYPE } from 'pages/common/constants.js';
import { CommonTable } from 'common/components/Table/index.js';
import BackArrow from 'assets/BackIcon.jsx';
import { dark } from 'utils/color.js';
import { EMPLOYEE_ROLES, EMPLOYEE_SERVICE_PATH } from 'common/constants.js';
import { searchFileSchema } from '../validate.js';
import * as actions from '../actions.js';
import './style.css';
import { getPullFileList, getPullFileParams, getPullStatus } from '../selectors.js';
import { actions as sliceActions } from '../slice';
import RoleModal from './RoleModal.jsx';
import PullFileStatus from './PullFileStatus.jsx';

const columns = [
  {
    type: 'multi-select'
  },
  {
    header: t('fileNumber'),
    field: 'fileNo',
    alignment: 'left'
  },
  {
    header: t('applicantName'),
    field: 'applicantName',
    alignment: 'left'
  },
  {
    header: t('serviceName'),
    field: 'serviceName',
    alignment: 'left'
  }
];

const styles = {
  head: {
    fontSize: '18px',
    color: '#09327B'
  }
};

const PullFile = (props) => {
  const {
    fetchPullFiles,
    pullFilesList, fetchDepartments,
    updatePullFile,
    userInfo,
    pullFileParams,
    setPullFileParams,
    fetchServiceDetails,
    serviceDropdownList,
    pullStatus
  } = (props);
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm({
    mode: 'all',
    defaultValues: {
      fileNumber: null,
      applicantName: null,
      service: null,
      pullReason: null
    },
    resolver: yupResolver(searchFileSchema)
  });
  const pullReason = watch('pullReason');

  const [tableData, setTableData] = useState([]);
  const [selectedFile, setSelectedFile] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const activeRows = [{}];
  const [loginedUserRoles, setLoginedUserRoles] = useState([]);
  const [openRoleModal, setOpenRoleModal] = useState(false);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const onPageClick = (data) => {
    setPage(data);
    setPullFileParams({ ...pullFileParams, page: data, search: true });
    fetchPullFiles();
  };

  const serviceDropdownArrayWithCurrentUser = userInfo?.userRoles?.map((userServiceCode) => {
    let response = {};
    serviceDropdownList?.data?.map((allServiceCode) => {
      if (userServiceCode?.code === allServiceCode?.code && ((!userServiceCode?.roles?.includes(EMPLOYEE_ROLES.OPERATOR) && userServiceCode?.roles?.length === 1) || (userServiceCode?.roles?.length > 1))) {
        response = {
          id: allServiceCode?.id,
          officeId: userServiceCode?.id,
          code: allServiceCode?.code,
          name: allServiceCode?.name,
          nameInLocal: allServiceCode?.nameInLocal,
          active: allServiceCode?.active,
          moduleId: allServiceCode?.moduleId,
          subModuleId: allServiceCode?.subModuleId
        };
      }
      return null;
    });
    return response;
  });

  const formatedServiceDropdownArrayWithCurrentUser = serviceDropdownArrayWithCurrentUser?.length > 0 && serviceDropdownArrayWithCurrentUser?.filter((value) => Object.keys(value).length !== 0);

  useEffect(() => {
    fetchDepartments();
    fetchServiceDetails();
    setValue('pullReason', null);
    setTableData([]);
    setSelectedFile(null);
  }, []);

  useEffect(() => {
    if (pullFilesList) {
      if (Object.keys(pullFilesList).length > 0) {
        setTableData(pullFilesList.content);
        setTotalItems(Number(`${pullFilesList.totalPages}0`));
        setNumberOfElements(Number(pullFilesList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [pullFilesList]);

  const handleClose = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };
  const onSubmitForm = () => {
    if (pullFileParams?.search) {
      fetchPullFiles();
    }
  };

  let logineduserArray = '';
  let formattedRoles = '';

  const triggerSearch = (field, data) => {
    switch (field) {
      case FILTER_TYPE.SERVICES:
        logineduserArray = userInfo?.userRoles?.find((item) => (item?.code === data?.code));
        formattedRoles = logineduserArray?.roles?.map((role, index) => {
          let response = {};
          if (role !== EMPLOYEE_ROLES.OPERATOR) {
            response = { id: index, name: role };
          }
          return response;
        });
        setLoginedUserRoles(formattedRoles?.length > 0 && formattedRoles?.filter((value) => Object.keys(value).length !== 0));

        setPullFileParams({
          ...pullFileParams,
          serviceCode: data?.code || null,
          search: true
        });
        break;
      case FILTER_TYPE.FILE_NO:
        setPullFileParams({
          ...pullFileParams,
          fileNo: data || null,
          search: true
        });
        break;
      case FILTER_TYPE.APPLICANT_NAME:
        setPullFileParams({
          ...pullFileParams,
          applicantName: data || null,
          search: true
        });
        break;
      default:
        break;
    }
  };

  const onRowCheck = (data) => {
    const { selectedRow } = data;
    const file = selectedFile?.length > 0 ? JSON.parse(JSON.stringify(selectedFile)) : [];
    const findFile = file.findIndex((item) => item === selectedRow.fileNo);
    if (findFile > -1) {
      file.splice(findFile, 1);
    } else {
      file.push(selectedRow?.fileNo);
    }

    setSelectedFile(file);
  };

  const pullFile = () => {
    if (loginedUserRoles?.length > 1) {
      setOpenRoleModal(!openRoleModal);
    } else {
      const data = {
        fileNos: selectedFile,
        pullReason,
        role: loginedUserRoles[0]?.name,
        officeId: userInfo?.officeId
      };
      updatePullFile(data);
      setValue('pullReason', null);
      setTableData([]);
      setSelectedFile(null);
      reset();
    }
  };
  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  return (
    <div>
      <form
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div className="flex gap-4 items-center mb-2">
          <div className="flex-none">
            <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
          </div>
          <div className="grow-[1]">
            <h4 style={styles.head}>
              <strong>
                {t('pullFile')}
              </strong>
            </h4>
          </div>
        </div>
        <Card>
          <div className="m-8">
            <FormWrapper>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="service"
                  type="select"
                  label={t('serviceName')}
                  placeholder={t('select')}
                  control={control}
                  options={formatedServiceDropdownArrayWithCurrentUser || []}
                  optionKey="id"
                  errors={errors}
                  isClearable
                  handleChange={(data) => triggerSearch(FILTER_TYPE.SERVICES, data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="fileNumber"
                  type="text"
                  label={t('fileNumber')}
                  placeholder={t('fileNumber')}
                  control={control}
                  errors={errors}
                  handleChange={(event) => triggerSearch(FILTER_TYPE.FILE_NO, event?.target?.value)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-4 col-span-12">
                <FormController
                  name="applicantName"
                  type="text"
                  label={t('applicantName')}
                  placeholder={t('applicantName')}
                  control={control}
                  errors={errors}
                  handleChange={(event) => triggerSearch(FILTER_TYPE.APPLICANT_NAME, event?.target?.value)}
                />
              </div>
            </FormWrapper>
            <div className="col-span-12 text-right">
              <Button variant="secondary" type="submit" size="sm">
                {t('search')}
              </Button>
            </div>
            <div className="mt-4">
              {tableData && tableData.length > 0 && (
                <CommonTable
                  tableData={tableData}
                  columns={columns}
                  onRowCheck={onRowCheck}
                  activeRows={activeRows}
                  onPageClick={onPageClick}
                  itemsPerPage={10}
                  totalItems={totalItems}
                  currentPage={page}
                  paginationEnabled
                  numberOfElements={numberOfElements}
                />
              )}
            </div>
            {selectedFile?.length > 0 && (
              // <FormWrapper>
              <div className="col-span-12 mt-10">
                <FormController
                  name="pullReason"
                  type="textarea"
                  label={t('pullReason')}
                  placeholder={t('pullReason')}
                  control={control}
                  errors={errors}
                  required
                />
              </div>
              // </FormWrapper>
            )}
          </div>
        </Card>
        {tableData && tableData.length > 0 && (
          <div className="col-span-12 flex justify-end items-center space-x-4 mt-5 mb-10 pb-5">
            <Button
              variant="secondary_outline"
              size="sm"
              mr={3}
              onClick={handleClose}
            >
              {t('back')}
            </Button>
            <Button variant="secondary" size="sm" isDisabled={!selectedFile || !pullReason} onClick={pullFile}>
              {t('pull')}
            </Button>
          </div>

        )}
      </form>
      <RoleModal loginedUserRoles={loginedUserRoles} selectedFile={selectedFile} pullReason={pullReason} updatePullFile={updatePullFile} openRoleModal={openRoleModal} setOpenRoleModal={setOpenRoleModal} />

      <PullFileStatus pullStatus={pullStatus} />

    </div>

  );
};
const mapStateToProps = createStructuredSelector({
  files: getFiles,
  departmentsDropdown: getDepartments,
  serviceDropdownList: getServicesDropdown,
  userInfo: getUserInfo,
  pullFileParams: getPullFileParams,
  pullFilesList: getPullFileList,
  pullStatus: getPullStatus
});
const mapDispatchToProps = (dispatch) => ({
  fetchDepartments: () => dispatch(commonActions.fetchDepartments()),
  fetchPullFiles: () => dispatch(actions.fetchPullFiles()),
  updatePullFile: (data) => dispatch(actions.updatePullFile(data)),
  setPullFileParams: (data) => dispatch(sliceActions.setPullFileParams(data)),
  fetchServiceDetails: () => dispatch(commonActions.fetchServiceDetails())
});

export default connect(mapStateToProps, mapDispatchToProps)(PullFile);
