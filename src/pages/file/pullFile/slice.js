import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { STORAGE_KEYS } from 'common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  servicesSearchList: {},
  seatList: {},
  pullFileParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    fileNo: null,
    applicantName: null,
    serviceCode: null,
    fileStatus: FILE_STATUS_FOR_API_PARAMS.RUNNING,
    search: false,
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID)

  },
  pullStatus: { flag: false, data: [] }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setServicesSearchList: (state, { payload }) => {
      _.set(state, 'servicesSearchList', payload);
    },
    setPullFileParams: (state, { payload }) => {
      _.set(state, 'pullFileParams', payload);
    },
    setPullFiles: (state, { payload }) => {
      _.set(state, 'pullFileList', payload);
    },
    setPullStatus: (state, { payload }) => {
      _.set(state, 'pullStatus', payload);
    }
  }
});

export const { actions, reducer } = slice;
