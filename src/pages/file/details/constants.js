import { baseApiURL } from 'utils/http';

export const STATE_REDUCER_KEY = 'summary-details';

export const DOCUMENT_VIEW_URL = `${baseApiURL}/file-management-services/draft-pdf?draftId=:draftId&template=:template`;
export const DRAFT_PDF_URL = `${baseApiURL}/file-management-services/draft-pdf`;
export const DOCUMENT_ENCLOUSURE_URL = `${baseApiURL}/file-management-services/preview-enclousure-documents`;

export const fileColors = {
  0: {
    bgColor: '#BCE2F7',
    textColor: '#6596CF'
  },
  1: {
    bgColor: '#FBE2FF',
    textColor: '#CF87BB'
  },
  2: {
    bgColor: '#CBF2C1',
    textColor: '#7BBC6A'
  },
  3: {
    bgColor: '#F0D0FF',
    textColor: '#784B8D'
  }
};
export const MERGE = {
  MERGING: 'MERGING',
  UNMERGE: 'Un-merge',
  LINK: 'Link',
  MERGE: 'Merge',
  LINKED: 'LINKED',
  UNLINK: 'UNLINK'
};

export const RECEIVER_TYPE = {
  INDIVIDUAL: 'Individual',
  INSTITUTION: 'Institution',
  ELECTED_REPRESENTATIVES: 'Elected Representatives'
};

export const PULL_TYPES = {
  PULL_DRAFT: 'Pull Draft',
  PULL_NOTES: 'Pull Notes',
  ADD_ENCLOSURE: 'Add Enclosure',
  COPY_TO: 'Copy To'
};

export const DRAFT_SAVE = {
  CREATED: 'CREATED',
  VERIFIED: 'VERIFIED',
  APPROVED: 'APPROVED',
  PENDING: 'PENDING',
  RETURNED: 'RETURNED'
};

export const CONFIRMATION_TYPE = {
  RETURN: 'return',
  REJECT: 'reject',
  APPROVE: 'approve',
  MAKE_INACTIVE: 'make_inactive'
};

export const MORE_ACTION_DATA = [
  {
    id: 1,
    name: PULL_TYPES.PULL_DRAFT
  },
  {
    id: 2,
    name: PULL_TYPES.PULL_NOTES
  },
  {
    id: 3,
    name: PULL_TYPES.ADD_ENCLOSURE
  },
  {
    id: 4,
    name: PULL_TYPES.COPY_TO
  }
];

export const MORE_ACTION_DATA_OTHERS = [
  {
    id: 1,
    name: PULL_TYPES.PULL_DRAFT
  },
  {
    id: 2,
    name: PULL_TYPES.PULL_NOTES
  }
];

export const CUSTODIAN_CHANGE = 'CUSTODIAN_CHANGE';
export const CHILD_FILE = 'CHILD_FILE';
export const LEGACY_FILE = 'LEGACY_FILE';
export const IKM_FILE_COUNTER = 'IKM_FILE_COUNTER';
export const IKM_FILE_ARISING = 'IKM_FILE_ARISING';

export const CERTIFICATE_SERVICE_CODES = [
  'BFLP07',
  'CWPA01',
  'BPPA06',
  'BFLP03',
  'BFSH01',
  'BFSH02',
  'BFSH03',
  'BFLP04',
  'BFLP05',
  'BFMI07',
  'BFHP02',
  'BFHP03',
  'BFTR01',
  'BFTR02',
  'BPPA07',
  'BFIF08'
];

export const DRAFT_FILTERS = [
  {
    id: 1,
    name: 'Pending Drafts'
  },
  {
    id: 2,
    name: 'Approved Drafts'
  },
  {
    id: 3,
    name: 'All Drafts'
  }
];

export const SIGN_TYPES = [
  {
    id: 1,
    name: 'E sign',
    disabled: true
  },
  {
    id: 2,
    name: 'Digital sign',
    disabled: false
  }
];

export const FINANCE_TYPE = {
  DEMAND: 'DEMAND',
  CLAIM: 'CLAIM',
  PAY_ORDER: 'PAY_ORDER',
  PAYMENT: 'PAYMENT',
  SALARY_PAYMENT: 'SALARY_PAYMENT',
  RECOVERY_PAYMENT: 'RECOVERY_PAYMENT',
  IMPREST_CLAIM: 'IMPREST_CLAIM',
  ADVANCE_CLAIM: 'ADVANCE_CLAIM',
  JOURNAL_ENTRY: 'JOURNAL',
  CONTRA_ENTRY: 'CONTRA'
};
