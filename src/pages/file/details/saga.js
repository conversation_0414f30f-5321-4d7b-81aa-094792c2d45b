import React from 'react';
import {
  all, takeLatest, fork, put, take, call, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { Toast, t } from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import { BASE_PATH, FINANCE_BASE_PATH } from 'common/constants';
import { DRAFT_STATUS, NOTE_STATUS } from 'pages/common/constants';
import { getCorrespondTypeDropdown, getUserInfo } from 'pages/common/selectors';
import { routeRedirect } from 'utils/common';
import { API_URL } from 'common';
import { ACTION_TYPES, fetchPartialNotes, updateEsignFlag } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { actions as counterActions } from '../../counter/new/slice';

import {
  getDraftPreviewFlag,
  getFileDetails,
  getMergeBackButton,
  getPullSearchDraftParams,
  // getPartialNotes,
  getPullSearchNotesParams,
  getPullSearchParams,
  getUnMergeFilesParams
} from './selector';
import {
  // checkDraftSave,
  getDraftStage
} from './helper';

const { successTost, errorTost } = Toast;

export function* fetchFileDetails({ payload = {} }) {
  const mergeBackButtonFlag = yield select(getMergeBackButton);
  yield fork(handleAPIRequest, api.fetchFileDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_FILE_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_FILE_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_FILE_DETAILS_SUCCESS) {
    const queryString = window.location.search;
    const params = new URLSearchParams(queryString);

    if (mergeBackButtonFlag) {
      if (responsePayLoad?.data?.moduleCode === 'CR') {
        window.location.href = `${window?.location?.origin}/ui/${responsePayLoad?.data?.url}?submoduleCode=${responsePayLoad?.data?.submoduleCode}`;
      } else {
        let url = `${window.location.origin}/ui/${responsePayLoad?.data?.url}`;

        if (params !== null && typeof params === 'object') {
          const filteredParams = new URLSearchParams(params);
          filteredParams.delete('show');
          const queryStringVal = filteredParams.toString();
          url += queryStringVal ? `?${queryStringVal}` : '';
        }
        window.location.href = url;
      }
    } else {
      yield put(sliceActions.setFileDetails(responsePayLoad.data));
    }
  }
}

export function* fetchSummaryDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSummaryDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID_SUCCESS) {
    yield put(sliceActions.setSummaryDetails(responsePayLoad.data));
  }
}

export function* fetchModulesById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchModulesById, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_MODULES_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_MODULES_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_MODULES_BY_ID_SUCCESS) {
    yield put(sliceActions.setModule(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchSubModulesById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSubModulesById, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SUCCESS) {
    yield put(sliceActions.setSubModule(_.get(responsePayLoad, 'data', {})));
  }
}

export function* fetchServiceById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchServicesById, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_SERVICES_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS) {
    yield call(fetchSubModulesById, { payload: responsePayLoad?.data[0]?.subModuleId });
    yield call(fetchModulesById, { payload: responsePayLoad?.data[0]?.moduleId });
    yield put(sliceActions.setService(responsePayLoad.data[0]));
  }
}

export function* fetchPendingActions({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchPendingActions, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID_SUCCESS) {
    yield put(sliceActions.setPendingActions(responsePayLoad.data));
  }
}

export function* fetchApplicantDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchApplicantDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID_SUCCESS) {
    yield put(sliceActions.setApplicantDetails(responsePayLoad.data));
  }
}

export function* fetchPendingDraftDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchPendingDraftDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID_SUCCESS) {
    yield put(sliceActions.setPendingDraftDetails(responsePayLoad.data));
  }
}

export function* fetchDraftPreview({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchDraftPreview, payload);
}

export function* fetchPartialNotesDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchPartialNotesDetails, payload);
  const res = yield take([ACTION_TYPES.FETCH_PARTIAL_NOTES_SUCCESS, ACTION_TYPES.FETCH_PARTIAL_NOTES_FAILURE]);
  if (res.type === ACTION_TYPES.FETCH_PARTIAL_NOTES_SUCCESS) {
    yield put(sliceActions.setPartialNotes(res?.payload?.data));
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* fetchDraftExistsOrNot({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchDraftExistsOrNot, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT_SUCCESS,
    ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT_SUCCESS) {
    yield put(sliceActions.setDraftExistsOrNotFlag(responsePayLoad.data));
  }
}

export function* saveNoteWithoutDoc({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveNoteWithoutDoc, payload);
  const { saveButton = null, handleRedirect = () => { } } = payload;
  const res = yield take([ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC_SUCCESS, ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC_FAILURE]);
  if (res.type === ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC_SUCCESS) {
    // setTimeout(() => {
    //   localStorage.removeItem('notes-unsaved');
    // }, 500);
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'save-note' }));
    yield call(successTost, { title: 'success', description: res?.payload?.message || t('saveSuccess') });
    yield put(sliceActions.setNoteId(res?.payload?.data));
    const userInfo = yield select(getUserInfo);
    const notesSearchRequest = {
      fileNo: payload?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    // yield call(fetchDraftExistsOrNot, { payload: { fileNo: payload?.fileNo } });
    yield put(fetchPartialNotes(notesSearchRequest));
    if (saveButton === 'main-submit') {
      const fileDetails = yield select(getFileDetails);
      yield put(
        commonSliceActions.setAlertAction({
          open: true,
          variant: 'success',
          message: 'Do you want to go to summary?',
          title: t('success'),
          backwardActionText: t('no'),
          forwardActionText: t('yes'),
          forwardAction: () => handleRedirect(fileDetails?.url)
        })
      );
    }
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'save-note' }));
    const { payload: { error: { response: { data: { message = '' } = {} } = {} } = {} } = {} } = res;
    yield call(errorTost, { id: t('error'), title: message || t('error') });
  }
}

export function* saveCopyTo({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveCopyTo, payload);
  const { type } = yield take([ACTION_TYPES.SAVE_COPY_TO_SUCCESS, ACTION_TYPES.SAVE_COPY_TO_FAILURE]);
  if (type === ACTION_TYPES.SAVE_COPY_TO_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success') });
    yield put(sliceActions.setDraftId(''));
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* fetchDraftById({ payload = {}, handleNewPopup = () => { } }) {
  yield fork(handleAPIRequest, api.fetchDraftById, payload);
  const { type, payload: responsePayLoad = {} } = yield take([ACTION_TYPES.FETCH_DRAFT_BY_ID_SUCCESS, ACTION_TYPES.FETCH_DRAFT_BY_ID_FAILURE]);

  if (type === ACTION_TYPES.FETCH_DRAFT_BY_ID_SUCCESS) {
    const { data = [] } = yield select(getCorrespondTypeDropdown);
    const filteredCorrespondType = data.find((item) => item.id === responsePayLoad?.data?.draftType)?.code;
    handleNewPopup({
      id: payload,
      draftNo: responsePayLoad?.data?.draftNo,
      date: responsePayLoad?.data?.createdAt,
      draftStage: getDraftStage(responsePayLoad?.data?.draftStage),
      correspondenceType: filteredCorrespondType,
      isESigned: responsePayLoad?.data?.esigned,
      fileNo: responsePayLoad?.data?.fileNo,
      isEditable: responsePayLoad?.data?.isEditable,
      isActive: responsePayLoad?.data?.active,
      postId: responsePayLoad?.data?.postId,
      isDigitalSIgned: responsePayLoad?.data?.digitalSigned
    });
  }
}

const formatErrors = (data) => {
  const values = Object.values(data);
  return values[0];
};

export function* saveDraft({ payload = {} }) {
  const formData = new FormData();
  const {
    // autoNote, handleEsign = () => { },
    handlePreview = () => { },
    handleNewPopup = () => { },
    searchParamsData = 'draft-page'
    // handleBackwardAction = () => { }
  } = payload;
  if (payload?.enclosureFiles?.length > 0) {
    payload?.enclosureFiles?.map((doc) => formData.append('enclosureFiles', doc));
  } else {
    const emptyBlob = new Blob([''], { type: 'application/octet-stream' });
    const emptyFile = new File([emptyBlob], 'empty.txt', { type: 'application/octet-stream' });
    formData.append('enclosureFiles', emptyFile);
  }
  formData.append(
    payload?.id ? 'request' : 'draftCreateRequest',
    new Blob([JSON.stringify(payload?.draftCreateRequest)], {
      type: 'application/json'
    })
  );

  if (payload?.id) {
    formData.append('id', payload?.id);
    if (payload?.saveStatus === DRAFT_STATUS.RETURNED) {
      yield fork(handleAPIRequest, api.saveDraftReturn, formData);
    } else {
      yield fork(handleAPIRequest, api.editDraft, formData);
    }
  } else {
    yield fork(handleAPIRequest, api.saveDraft, formData);
  }

  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_DRAFT_SUCCESS,
    ACTION_TYPES.SAVE_DRAFT_FAILURE
  ]);

  if (type === ACTION_TYPES.SAVE_DRAFT_SUCCESS) {
    const flag = yield select(getDraftPreviewFlag);

    if (payload?.id) {
      yield call(fetchDraftById, {
        payload: payload?.id,
        handleNewPopup // : responsePayLoad?.data?.draftStage === 'APPROVED' ? () => { } : handleNewPopup
      });
      yield put(sliceActions.setDraftPreviewFlag(!flag));
    }

    if (responsePayLoad?.data?.draftId && responsePayLoad?.data?.draftId !== payload?.id) {
      yield call(fetchDraftById, {
        payload: responsePayLoad?.data?.draftId,
        handleNewPopup // : responsePayLoad?.data?.draftStage === 'APPROVED' ? () => { } : handleNewPopup
      });
      yield put(sliceActions.setDraftPreviewFlag(!flag));
    }

    if (responsePayLoad?.data?.draftId) {
      if (payload?.draftCreateRequest?.action === 'proceed') { // && responsePayLoad?.data?.draftStage !== 'APPROVED'
        handlePreview();
        yield put(sliceActions.setDraftNewPreviewUpdateFlag(true));
      }

      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.draftCreateRequest?.fileNo}/draft/${responsePayLoad?.data?.draftId}?from=${searchParamsData}`,
          active: true
        })
      );
    } else if (responsePayLoad?.data) {
      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.draftCreateRequest?.fileNo}/draft/${responsePayLoad?.data}?from=${searchParamsData}`,
          active: true
        })
      );
    }

    // if (responsePayLoad?.data?.draftStage === 'APPROVED') {
    //   yield put(
    //     commonSliceActions.setAlertAction({
    //       open: true,
    //       variant: 'success',
    //       message: checkDraftSave(autoNote?.action),
    //       title: t('success'),
    //       backwardActionText: autoNote?.action === 'Approve' ? t('no') : t('ok'),
    //       forwardActionText: autoNote?.action === 'Approve' ? t('yes') : null,
    //       forwardAction: () => (autoNote?.action === 'Approve' ? handleEsign({ draftId: responsePayLoad?.data?.draftId || responsePayLoad?.data, draftNo: autoNote?.draftNo }) : null),
    //       forwardActionId: 'digital-sign',
    //       backwardAction: () => handleBackwardAction(searchParamsData)
    //     })
    //   );
    // }

    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'saveDraft' }));
    yield put(commonSliceActions.setActionTriggered({ loading: true, id: 'draft-preview' }));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'saveDraft' }));
    const {
      error: {
        response: {
          data: {
            errors
          }
        }
      }
    } = responsePayLoad;

    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'createDraft' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: errors ? formatErrors(errors) : t('failed'),
        title: t('draftUpdate'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* fetchSenderDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSenderDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SENDER_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_SENDER_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_SENDER_DETAILS_SUCCESS) {
    yield put(sliceActions.setSendersDetails(responsePayLoad));
  }
}

export function* fetchReceiverDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchReceiverDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_RECEIVER_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_RECEIVER_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_RECEIVER_DETAILS_SUCCESS) {
    yield put(sliceActions.setReceiverDetails(responsePayLoad));
  }
}

export function* fetchApplicant({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchApplicant, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_APPLICANT_BY_ID_SUCCESS,
    ACTION_TYPES.FETCH_APPLICANT_BY_ID_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_APPLICANT_BY_ID_SUCCESS) {
    yield put(sliceActions.setApplicant(responsePayLoad.data));
  }
}

export function* saveAddress({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveAddress, payload);
  const { type } = yield take([ACTION_TYPES.SAVE_ADDRESS_SUCCESS, ACTION_TYPES.SAVE_ADDRESS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_ADDRESS_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success') });
    yield put(sliceActions.setDraftId(''));
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* saveEnclosureData({ payload = {} }) {
  const formData = new FormData();
  payload.enclosureFiles.map((file) => formData.append('enclosureFiles', file));
  formData.append(
    'uploadDetails',
    new Blob([JSON.stringify(payload.uploadDetails)], {
      type: 'application/json'
    })
  );
  yield fork(handleAPIRequest, api.saveEnclosureApi, formData);
  const { type } = yield take([ACTION_TYPES.SAVE_ENCLOSURE_SUCCESS, ACTION_TYPES.SAVE_ENCLOSURE_FAILURE]);
  if (type === ACTION_TYPES.SAVE_ENCLOSURE_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success') });
    yield put(sliceActions.setDraftId(''));
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* fetchWardMemberDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchWardMemberDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS_SUCCESS) {
    yield put(sliceActions.setWardDetails(responsePayLoad.data));
  }
}

export function* saveUnHoldFileData({ payload = {} }) {
  const { saveData, backwardAction = () => { } } = payload;
  yield fork(handleAPIRequest, api.saveUnHoldFileData, saveData);
  const { type } = yield take([ACTION_TYPES.SAVE_UN_HOLD_FILE_SUCCESS, ACTION_TYPES.SAVE_UN_HOLD_FILE_FAILURE]);
  if (type === ACTION_TYPES.SAVE_UN_HOLD_FILE_SUCCESS) {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: t('fileMovedToCustodianSuccessfully'),
        title: t('moveToCustodian'),
        backwardActionText: t('ok'),
        backwardAction: () => backwardAction()
      })
    );
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* fetchFileDocuments({ payload = {} }) {
  let url = API_URL.INBOX.FETCH_FILE_DOCUMENTS.replace(':fileNo', payload);

  const queryString = window.location.search;
  const params = new URLSearchParams(queryString);
  const fromValue = params.get('from');

  if ((fromValue === 'search-files') || (fromValue === 'outbox-list') || (fromValue === 'watch-file')) {
    url = API_URL.INBOX.FETCH_DOCUMENTS_FROM_FILE_SEARCH.replace(':fileNo', payload);
  }

  yield fork(handleAPIRequest, api.fetchFileDocuments, url);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_FILE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.FETCH_FILE_DOCUMENTS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_FILE_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setNoteCardDetails(responsePayLoad?.data));
    if (responsePayLoad?.data?.length > 0) {
      yield put(commonSliceActions.setDocumentId({ docId: responsePayLoad?.data?.content?.notesDocumentId || responsePayLoad?.data?.content?.fileId, from: responsePayLoad?.data?.content?.notesDocumentId ? 'note' : 'inward' }));
    }
  } else {
    yield put(commonSliceActions.setNoteCardDetails([]));
    yield put(commonSliceActions.setDocumentId(null));
  }
}

export function* fetchNotes({ payload = {} }) {
  let url = API_URL.INBOX.FETCH_NOTES;
  let updatedParams = payload;

  const queryString = window.location.search;
  const params = new URLSearchParams(queryString);
  const fromValue = params.get('from');

  if ((fromValue === 'search-files') || (fromValue === 'outbox-list') || (fromValue === 'watch-file')) {
    url = API_URL.INBOX.FETCH_NOTES_FOR_FILE_SEARCH_SUMMARY;
    updatedParams = { fileNo: payload?.fileNo };
  }
  yield fork(handleAPIRequest, api.fetchNotes, url, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_NOTES_SUCCESS,
    ACTION_TYPES.FETCH_NOTES_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_NOTES_SUCCESS) {
    yield put(sliceActions.setNotes(responsePayLoad.data));
    yield put(sliceActions.setNoteId(''));
    if (responsePayLoad?.data?.content?.length > 0) {
      yield put(commonSliceActions.setSummaryCollapseMenuFlag(false));
    } else {
      yield call(fetchFileDocuments, { payload: payload?.fileNo });
      yield put(commonSliceActions.setSummaryCollapseMenuFlag(true));
    }
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* fetchAllNotes({ payload = {} }) {
  let url = API_URL.INBOX.FETCH_ALL_NOTES;
  let apiParams = payload;

  const queryString = window.location.search;
  const params = new URLSearchParams(queryString);
  const fromValue = params.get('from');

  if ((fromValue === 'search-files') || (fromValue === 'outbox-list') || (fromValue === 'watch-file')) {
    url = API_URL.INBOX.FETCH_NOTES_FOR_FILE_SEARCH_NOTE_PAGE;
    apiParams = { fromNoteNo: payload?.fromNoteNo, toNoteNo: payload?.toNoteNo, fileNo: payload?.fileNo };
  }
  const updatedParams = _.omitBy(apiParams, _.isNil);
  yield fork(handleAPIRequest, api.fetchAllNotes, url, updatedParams);
  const { type } = yield take([
    ACTION_TYPES.FETCH_ALL_NOTES_SUCCESS,
    ACTION_TYPES.FETCH_ALL_NOTES_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_ALL_NOTES_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'fetch-all-notes' }));
    // yield call(fetchFileDocuments, { payload: payload?.fileNo });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'fetch-all-notes' }));
  }
}

export function* saveNoteDocuments({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveNoteDocuments, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_NOTE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_NOTE_DOCUMENTS_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_NOTE_DOCUMENTS_SUCCESS) {
    yield put(sliceActions.setSavedNote(responsePayLoad.data));
    yield call(successTost, { id: t('saved'), title: t('success') });
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* saveNote({ payload = {} }) {
  const formData = new FormData();
  const { setShowAttachment = () => { }, setDocuments = () => { } } = payload;
  if (payload.docs.length > 0) {
    payload.docs.map((doc) => formData.append('notesDocs', doc));
  } else {
    const emptyBlob = new Blob([''], { type: 'application/octet-stream' });
    const emptyFile = new File([emptyBlob], 'empty.txt', { type: 'application/octet-stream' });
    formData.append('notesDocs', emptyFile);
  }
  formData.append(
    'request',
    new Blob([JSON.stringify(payload.request)], {
      type: 'application/json'
    })
  );
  yield fork(handleAPIRequest, api.saveNote, formData);
  const { payload: { message, data } = {}, type } = yield take([
    ACTION_TYPES.SAVE_NOTE_FAILURE,
    ACTION_TYPES.SAVE_NOTE_SUCCESS
  ]);
  if (type === ACTION_TYPES.SAVE_NOTE_SUCCESS) {
    // setTimeout(() => {
    //   localStorage.removeItem('notes-unsaved');
    // }, 500);
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'save-note' }));
    yield call(successTost, { title: 'success', description: message || t('saveSuccess') });
    setDocuments(null);
    setShowAttachment(false);
    const userInfo = yield select(getUserInfo);
    yield put(sliceActions.setNoteId(data.notesId));
    const notesSearchRequest = {
      fileNo: payload?.request?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    yield put(fetchPartialNotes(notesSearchRequest));
    // yield call(fetchFileDocuments, { payload: payload?.request?.fileNo });
    yield call(fetchFileDocuments, { payload: payload?.request?.fileNo });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'save-note' }));
  }
}

export function* fetchEnquiryDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchEnquiryDetails, payload);
}

export function* fetchEnquiryDoc({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchEnquiryDoc, payload);
}

export function* saveEnquiry({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveEnquiry, payload);
  const { type } = yield take([ACTION_TYPES.SAVE_ENQUIRY_SUCCESS, ACTION_TYPES.SAVE_ENQUIRY_FAILURE]);
  if (type === ACTION_TYPES.SAVE_ENQUIRY_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
    yield call(fetchEnquiryDetails, { payload: payload.fileNo });
    yield call(fetchEnquiryDoc, { payload: payload.fileNo });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'createEnquiry' }));
  }
}

export function* saveEnquiryDoc({ payload = {} }) {
  const { enquiryDocs, request } = payload;
  const formData = new FormData();
  enquiryDocs.map((doc) => formData.append('enquiryDocs', doc));
  formData.append(
    'request',
    new Blob([JSON.stringify(request)], {
      type: 'application/json'
    })
  );

  yield fork(handleAPIRequest, api.saveEnquiryDoc, formData);
  const { type } = yield take([ACTION_TYPES.SAVE_ENQUIRY_DOC_SUCCESS, ACTION_TYPES.SAVE_ENQUIRY_DOC_FAILURE]);
  if (type === ACTION_TYPES.SAVE_ENQUIRY_DOC_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
    yield call(fetchEnquiryDetails, { payload: request.fileNo });
    yield call(fetchEnquiryDoc, { payload: request.fileNo });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'createEnquiry' }));
  }
}

export function* updateEnquiryText({ payload = {} }) {
  yield fork(handleAPIRequest, api.updateEnquiryText, payload);
  const { type } = yield take([ACTION_TYPES.UPDATE_ENQUIRY_TEXT_SUCCESS, ACTION_TYPES.UPDATE_ENQUIRY_TEXT_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_ENQUIRY_TEXT_SUCCESS) {
    yield call(successTost, { id: t('update'), title: t('success'), description: t('enquirySaveSuccess') });
    yield call(fetchEnquiryDetails, { payload: payload.fileNo });
    yield call(fetchEnquiryDoc, { payload: payload.fileNo });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'createEnquiry' }));
  }
}

export function* updateEnquiryDoc({ payload = {} }) {
  const { enquiryDocs, request } = payload;
  const formData = new FormData();
  formData.append('enquiryDocs', enquiryDocs);
  formData.append(
    'request',
    new Blob([JSON.stringify(request)], {
      type: 'application/json'
    })
  );

  const dataSend = { data: formData, fileNo: request.fileNo };

  yield fork(handleAPIRequest, api.updateEnquiryDoc, dataSend);
  const { type } = yield take([ACTION_TYPES.UPDATE_ENQUIRY_DOC_SUCCESS, ACTION_TYPES.UPDATE_ENQUIRY_DOC_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_ENQUIRY_DOC_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
    yield call(fetchEnquiryDetails, { payload: request.fileNo });
    yield call(fetchEnquiryDoc, { payload: request.fileNo });
  }
}

export function* fetchYear({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchYears, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_YEAR_SUCCESS,
    ACTION_TYPES.FETCH_YEAR_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_YEAR_SUCCESS) {
    yield put(sliceActions.setYears(responsePayLoad.data));
  }
}

export function* fetchSameSeatMergeFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSameSeatMergeFiles, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES_SUCCESS,
    ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES_SUCCESS) {
    yield put(sliceActions.setSameSeatMergeFiles(responsePayLoad.data));
  }
}

export function* fetchMergedFiles() {
  const apiParams = yield select(getUnMergeFilesParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const finalParams = _.omit(updatedParams, ['search']);

  yield fork(handleAPIRequest, api.fetchMergedFiles, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_MERGED_FILES_SUCCESS,
    ACTION_TYPES.FETCH_MERGED_FILES_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_MERGED_FILES_SUCCESS) {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'un-merge-file-search' }));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'merge-action' }));
    yield put(sliceActions.setMergedFiles(responsePayLoad.data));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'un-merge-file-search' }));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'merge-action' }));
    yield put(sliceActions.setMergedFiles([]));
  }
}
export function* fetchPullNotesDetails() {
  const apiParams = yield select(getPullSearchParams);
  const userInfo = yield select(getUserInfo);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const finalParams = _.omit(updatedParams, ['search']);
  finalParams.officeId = userInfo?.id;
  yield fork(handleAPIRequest, api.fetchPullNotesDetails, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PULL_NOTES_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_PULL_NOTES_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_PULL_NOTES_DETAILS_SUCCESS) {
    yield put(sliceActions.setPullNotesDetails(responsePayLoad?.data));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'pull-search' }));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'pull-search' }));
    yield put(sliceActions.setPullNotesDetails([]));
  }
}
export function* mergeFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.mergeFiles, payload);
  const { subFileNos } = payload;
  const merged = _.join(subFileNos, [',']);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.MERGE_FILES_SUCCESS,
    ACTION_TYPES.MERGE_FILES_FAILURE
  ]);
  if (type === ACTION_TYPES.MERGE_FILES_SUCCESS) {
    // const partialNote = yield select(getPartialNotes);
    // const partialUpdate = partialNote?.notes || '';
    // const concatNote = partialUpdate.concat(`<br/> ${merged} ${t('filesAreMergedToMainFile')}`);
    const request = {
      fileNo: payload?.mainFileNo
      // notes: concatNote
    };
    yield call(saveNoteWithoutDoc, { payload: request });
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: `${merged} ${t('filesAreMergedToMainFile')}`,
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect(`ui/file-management/file/${payload?.mainFileNo}/merge-file`)
      })
    );
    yield call(fetchPullNotesDetails);
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: responsePayLoad?.error?.response?.data?.message,
        title: t('failed'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* fetchMergeLink({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchMergedLinked, payload);
}

export function* fetchDraft({ payload = {} }) {
  let url = API_URL.INBOX.FETCH_DRAFT.replace(':fileNo', payload?.fileNo);
  let updatedParams = payload;

  const queryString = window.location.search;
  const params = new URLSearchParams(queryString);
  const fromValue = params.get('from');

  if ((fromValue === 'search-files') || (fromValue === 'outbox-list') || (fromValue === 'watch-file')) {
    url = API_URL.INBOX.FETCH_DRAFT_FROM_FILE_SEARCH.replace(':fileNo', payload?.fileNo);
    updatedParams = { fileNo: payload?.fileNo };
  }

  yield fork(handleAPIRequest, api.fetchDraft, url, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DRAFT_SUCCESS,
    ACTION_TYPES.FETCH_DRAFT_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_DRAFT_SUCCESS) {
    if (responsePayLoad.data?.length > 0) {
      yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'draft-loading' }));
      yield put(sliceActions.setDraftDetails(responsePayLoad.data));
    } else {
      yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'draft-loading' }));
      yield put(sliceActions.setDraftDetails([]));
    }
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'draft-loading' }));
    yield put(sliceActions.setDraftDetails([]));
  }
}

export function* linkFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.linkFiles, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.LINK_FILES_SUCCESS,
    ACTION_TYPES.LINK_FILES_FAILURE
  ]);
  // const { linkFileNos } = payload;
  // const merged = _.join(linkFileNos, [',']);
  if (type === ACTION_TYPES.LINK_FILES_SUCCESS) {
    // const partialNote = yield select(getPartialNotes);
    // const partialUpdate = partialNote?.notes || '';
    // const concatNote = partialUpdate.concat(`<br/> ${merged} ${t('filesAreLinkedToMainFile')}`);
    // const request = {
    //   fileNo: payload?.mainFileNo,
    //   notes: concatNote
    // };
    // yield call(saveNoteWithoutDoc, { payload: request });
    yield call(fetchPullNotesDetails);
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: responsePayLoad?.data?.message,
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect(`ui/file-management/file/${payload?.mainFileNo}/file-link`)
      })
    );
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: responsePayLoad?.data?.message,
        title: t('Failed'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* fetchFunctionalGroup({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchFunctionalGroup, payload);
}

export function* fetchFunctions({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchFunctions, payload);
}

export function* fetchPullDraftDetails() {
  const apiParams = yield select(getPullSearchDraftParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  yield fork(handleAPIRequest, api.fetchPullDraftDetails, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS_SUCCESS) {
    yield put(sliceActions.setPullDraftDetails(responsePayLoad.data));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'pull-search' }));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'pull-search' }));
  } else {
    const { error: { response: { data: { errors: { PullDraftDenied = '' } = {} } = {} } = {} } = {} } = responsePayLoad;
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'pull-search' }));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'pull-search' }));
    if (PullDraftDenied) {
      yield put(
        commonSliceActions.setAlertAction({
          open: true,
          variant: 'error',
          message: PullDraftDenied,
          title: t('failed'),
          backwardActionText: t('ok')
        })
      );
    }
    yield put(sliceActions.setPullDraftDetails([]));
  }
}

export function* searchPullNotesDetails() {
  const apiParams = yield select(getPullSearchNotesParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.searchPullNotesDetails, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS_SUCCESS,
    ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS_SUCCESS) {
    yield put(sliceActions.setSearchPullNotesDetails(responsePayLoad.data));
  } else {
    yield call(errorTost, { title: t('error'), description: responsePayLoad?.payload?.message });
  }
}

export function* savePullNotes({ payload = {} }) {
  yield fork(handleAPIRequest, api.savePullNotes, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_PULL_NOTES_SUCCESS,
    ACTION_TYPES.SAVE_PULL_NOTES_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_PULL_NOTES_SUCCESS) {
    yield call(successTost, {
      title: t('success'),
      description: responsePayLoad?.payload?.message || t('pullNoteSaveSuccess')
    });
  } else {
    yield call(errorTost, { title: t('error'), description: responsePayLoad?.payload?.message });
  }
}

export function* searchPullDraftDetails() {
  const apiParams = yield select(getPullSearchNotesParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.searchPullDraftDetails, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS_SUCCESS,
    ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS_SUCCESS) {
    yield put(sliceActions.setSearchPullDraftDetails(responsePayLoad.data));
  } else {
    yield call(errorTost, { title: t('error'), description: responsePayLoad?.payload?.message });
  }
}

export function* unMergeFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.unMergeFiles, payload);
  const { subFileNos } = payload;
  const merged = _.join(subFileNos, [',']);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.UN_MERGE_FILES_SUCCESS,
    ACTION_TYPES.UN_MERGE_FILES_FAILURE
  ]);
  if (type === ACTION_TYPES.UN_MERGE_FILES_SUCCESS) {
    yield call(fetchMergedFiles);
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'merge-action' }));
    // const partialNote = yield select(getPartialNotes);
    // const partialUpdate = partialNote?.notes || '';
    // const concatNote = partialUpdate.concat(`<br/> ${merged} ${t('filesAreUnmergedFromMainFile')}`);
    // const request = {
    //   fileNo: payload?.mainFileNo,
    //   notes: concatNote
    // };
    // yield call(saveNoteWithoutDoc, { payload: request });
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: `${merged} ${t('filesAreUnmergedFromMainFile')}`,
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect(`ui/file-management/file/${payload?.mainFileNo}/unmerge`)
      })
    );
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: responsePayLoad?.error?.response?.data?.message,
        title: t('failed'),
        backwardActionText: t('ok')
      })
    );
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'merge-action' }));
  }
}

export function* fetchMappedSeats({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchMappedSeats, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_MAPPED_SEATS_SUCCESS,
    ACTION_TYPES.FETCH_MAPPED_SEATS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_MAPPED_SEATS_SUCCESS) {
    let respObj = {};
    const responsArray = responsePayLoad?.data?.map((val) => {
      return val?.postResponse?.map((res) => {
        respObj = { ...res, functionalGroupName: val?.functionalGroupName, name: res?.postNameInEng };
        return respObj;
      });
    });
    yield put(sliceActions.setMappedSeats(responsArray?.flat()));
  } else {
    const { error: { response: { data: { error = {} } = {} } = {} } = {} } = responsePayLoad;
    yield put(sliceActions.setMappedSeats([]));
    yield call(errorTost, { id: t('error'), title: t('error'), description: error });
  }
}

export function* saveChildFile({ payload = {} }) {
  const updatedParams = _.omitBy(payload, _.isNil);

  yield fork(handleAPIRequest, api.saveChildFile, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_CHILD_FILE_SUCCESS,
    ACTION_TYPES.SAVE_CHILD_FILE_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_CHILD_FILE_SUCCESS) {
    const { data: { message = {} } = {} } = responsePayLoad;
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message,
        title: t('childFile'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect(`ui/file-management/file/${payload?.fileNo}/child`)
      })
    );
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'childFile' }));
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'childFile' }));
  }
}

export function* saveDraftReturn({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveDraftReturn, payload);
  const { type } = yield take([ACTION_TYPES.SAVE_DRAFT_RETURN_SUCCESS, ACTION_TYPES.SAVE_DRAFT_RETURN_FAILURE]);
  if (type === ACTION_TYPES.SAVE_DRAFT_RETURN_SUCCESS) {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: t('draftSuccessfullyReturned'),
        title: t('draftReturn'),
        backwardActionText: t('ok')
      })
    );
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'draftReturn' }));
    if (payload?.id) {
      yield call(fetchDraftById, { payload: payload?.id });
    }
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: t('draftReturnFailed'),
        title: t('draftReturn'),
        backwardActionText: t('ok')
      })
    );
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'draftReturn' }));
  }
}

export function* generateDemand({ payload = {} }) {
  yield fork(handleAPIRequest, api.generateDemand, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.GENERATE_DEMAND_SUCCESS,
    ACTION_TYPES.GENERATE_DEMAND_FAILURE
  ]);
  if (type === ACTION_TYPES.GENERATE_DEMAND_SUCCESS) {
    if (responsePayLoad?.data?.demand?.id) {
      window.location.href = `${FINANCE_BASE_PATH}/file-receipt-generation/${responsePayLoad?.data?.demand?.id}`;
    }
  }
}
export function* fetchApplication({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchApplication, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.FETCH_APPLICATION_SERVICE_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_APPLICATION_SERVICE_SUCCESS) {
    yield put(counterActions.setActiveCounterFormData(responsePayLoad?.data));
  }
}

export function* fetchBeneficiaryDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchBeneficiaryDetails, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_BENEFICIARY_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_BENEFICIARY_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_BENEFICIARY_DETAILS_SUCCESS) {
    yield put(sliceActions.setBeneficiaryData(responsePayLoad?.data));
  }
}

export function* saveComplete({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveComplete, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
    ACTION_TYPES.COMPLETE_SAVE_FAILURE
  ]);
  if (type === ACTION_TYPES.COMPLETE_SAVE_SUCCESS) {
    yield call(successTost, { id: 'saved', title: 'Success', description: 'success' });
    yield put(commonSliceActions.setAcknowledgeTriggered({ loading: false, id: 'counterCreate' }));
    yield call(fetchApplication, { payload: responsePayLoad?.data });
  }
}
export function* unLinkFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.unLinkFiles, payload);
  const { subFileNos } = payload;
  const linked = _.join(subFileNos, [',']);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.UN_LINK_FILES_SUCCESS,
    ACTION_TYPES.UN_LINK_FILES_FAILURE
  ]);
  if (type === ACTION_TYPES.UN_LINK_FILES_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'unlink-action' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: `${linked} ${t('filesAreUnLinkedFromMainFile')}`,
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => routeRedirect(`ui/file-management/file/${payload?.mainFileNo}/un-link-file`)
      })
    );
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: responsePayLoad?.error?.response?.data?.message,
        title: t('failed'),
        backwardActionText: t('ok')
      })
    );
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'unlink-action' }));
  }
}

export function* saveReOpenFile({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveReOpenFile, payload);
  const {
    type, payload: responsePayLoad = {}
  } = yield take([
    ACTION_TYPES.SAVE_RE_OPEN_FILE_SUCCESS,
    ACTION_TYPES.SAVE_RE_OPEN_FILE_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_RE_OPEN_FILE_FAILURE) {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: responsePayLoad?.error?.response?.data?.message,
        title: t('failed'),
        backwardActionText: t('ok')
      })
    );
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'unlink-action' }));
  }
}

export function* fetchFinanceExistsOrNot({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchFinanceExistsOrNot, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT_SUCCESS,
    ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT_SUCCESS) {
    yield put(sliceActions.setFinanceExistsOrNotFlag(responsePayLoad.data));
  }
}

export function* fetchChildFiles({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchChildFiles, payload);
}

export function* updateCustodianChange({ payload = {} }) {
  yield fork(handleAPIRequest, api.updateCustodianChange, payload);
  const { type } = yield take([
    ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE_SUCCESS,
    ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE_FAILURE
  ]);
  if (type === ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'efileGeneralDetailsCreate' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: false
      })
    );
    routeRedirect('ui/home/<USER>/dashboard/files');
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'efileGeneralDetailsCreate' }));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('custodianChangeFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchUserDetails({ payload = {} }) {
  const {
    officeId, routeKey1, routeKe1Value, routeKeysArray, serviceCode, role
  } = payload;
  const params = {
    officeId, routeKey1, routeKe1Value, serviceCode, role
  };
  const body = routeKeysArray;

  yield fork(handleAPIRequest, api.fetchUserDetails, params, body);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_USER_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_USER_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_USER_DETAILS_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'fetchUserDetails' }));
    yield put(sliceActions.setUserDetails(responsePayLoad.data));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'fetchUserDetails' }));
    yield put(sliceActions.setUserDetails([]));
  }
}

export function* fetchBPRouteKey({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchBPRouteKey, payload);
}

export function* fetchUsersByFileno({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchUsersByFileno, payload);
}

export function* fetchCertificate({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchCertificate, payload);
}

export function* deLinkInward({ payload = {} }) {
  yield fork(handleAPIRequest, api.deLinkInward, payload);
  const { type } = yield take([
    ACTION_TYPES.DE_LINK_INWARD_SUCCESS,
    ACTION_TYPES.DE_LINK_INWARD_FAILURE
  ]);

  if (type === ACTION_TYPES.DE_LINK_INWARD_SUCCESS) {
    yield call(fetchFileDetails, { payload: payload.fileNo });
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('inwardDeLinkedSuccess'), title: t('success'), backwardActionText: t('ok')
    }));
  } else {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('inwardDeLinkedFailed'), title: t('error'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchBeneficiary({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchBeneficiary, payload);
}

export function* saveBeneficiary(props) {
  const { payload } = props;
  const { from, handleCloseBen, fetchRequest } = payload;
  yield fork(handleAPIRequest, api.saveBeneficiary, payload);
  const { type } = yield take([
    ACTION_TYPES.SAVE_BENEFICIARY_SUCCESS,
    ACTION_TYPES.SAVE_BENEFICIARY_FAILURE
  ]);
  if (type === ACTION_TYPES.SAVE_BENEFICIARY_SUCCESS) {
    yield call(fetchBeneficiary, { payload: fetchRequest });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'save-beneficiary' }));
    handleCloseBen();
    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'success',
      message: from === 'save' ? t('beneficiarySavedSuccessfully') : t('beneficiaryUpdatedSuccessfully'),
      title: t('success'),
      backwardActionText: t('ok')
      // forwardActionText: !data?.beneficiaryDetailsRequest?.length > 0 ? t('addNew') : '',
      // forwardAction: () => routeRedirect(`ui/file-management/file/${data?.fileNo}/beneficiary/new`)
    }));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'save-beneficiary' }));
  }
}

export function* fetchBeneficiaryById({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchBeneficiaryById, payload);
}

export function* deleteBeneficiary({ payload = {} }) {
  yield fork(handleAPIRequest, api.deleteBeneficiary, payload);
  const { type } = yield take([
    ACTION_TYPES.DELETE_BENEFICARY_SUCCESS,
    ACTION_TYPES.DELETE_BENEFICARY_FAILURE
  ]);

  if (type === ACTION_TYPES.DELETE_BENEFICARY_SUCCESS) {
    yield call(fetchBeneficiary, { payload: payload.params });
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: t('deleteBeneficiarySuccess'), title: t('success'), backwardActionText: t('ok')
    }));
  } else {
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('deleteBeneficiaryFailed'), title: t('error'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchServicePostRoutes({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchServicePostRoutes, payload);
}

export function* listCompletedNotes({ payload = {} }) {
  yield fork(handleAPIRequest, api.listCompletedNotes, payload);
}

export function* listCompletedNotesDocuments({ payload = {} }) {
  yield fork(handleAPIRequest, api.listCompletedNotesDocuments, payload);
}
export function* deletDraft({ payload = {} }) {
  const { onSuccessCallback, ...rest } = payload;

  yield fork(handleAPIRequest, api.deletDraft, { ...rest });
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.DELETE_DRAFT_SUCCESS,
    ACTION_TYPES.DELETE_DRAFT_FAILURE
  ]);
  if (type === ACTION_TYPES.DELETE_DRAFT_SUCCESS) {
    onSuccessCallback?.();
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: responsePayLoad?.message, title: t('success'), backwardActionText: t('ok')
    }));
    yield call(fetchDraft, { payload: { fileNo: payload?.fileNo, status: 'ALL' } });
  }
}

export function* deleteNoteRef({ payload = {} }) {
  yield fork(handleAPIRequest, api.deleteNoteRef, payload);
  const { type } = yield take([
    ACTION_TYPES.DELETE_NOTE_REF_SUCCESS,
    ACTION_TYPES.DELETE_NOTE_REF_FAILURE
  ]);
  if (type === ACTION_TYPES.DELETE_NOTE_REF_SUCCESS) {
    yield call(successTost, { title: 'success', description: 'Reference Deleted Successfully' });
    const userInfo = yield select(getUserInfo);
    const notesSearchRequest = {
      fileNo: payload?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    yield put(fetchPartialNotes(notesSearchRequest));
  } else {
    yield call(errorTost, { id: t('error'), title: 'Reference Delete failed' });
  }
  yield put(commonSliceActions.setAlertAction({ open: false }));
}

export function* draftActions({ payload = {} }) {
  const {
    draftId, fileNo, action, close = () => { }, handleCheckDateTime = () => { },
    handleBackwardAction = () => { }, location, searchParamsDetails,
    handleEsignComponent = () => { }
  } = payload;

  const requestData = { draftId, action, fileNo };

  yield fork(handleAPIRequest, api.draftActions, requestData);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.DRAFT_ACTIONS_SUCCESS,
    ACTION_TYPES.DRAFT_ACTIONS_FAILURE
  ]);
  if (type === ACTION_TYPES.DRAFT_ACTIONS_SUCCESS) {
    // yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'confirm-ds-save' }));
    const userInfo = yield select(getUserInfo);
    yield put(sliceActions.setDraftNumber(responsePayLoad?.data?.draftNo));

    const notesSearchRequest = {
      fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      yield put(fetchPartialNotes(notesSearchRequest));
    }

    yield put(sliceActions.setActionOccured(true));

    yield call(fetchDraft, { payload: { fileNo, status: 'ALL' } });
    yield call(fetchAllNotes, { payload: { fileNo, noteStatus: NOTE_STATUS.COMPLETED } });
    yield call(fetchFileDetails, { payload: fileNo });

    yield call(successTost, { id: 'saved', title: t('success'), description: 'Draft updated successfully' });

    close();
    const handleTimeOut = () => {
      return React.createElement(handleEsignComponent);
    };
    if (responsePayLoad?.data?.draftStage === 'APPROVED') {
      yield put(sliceActions.setDraftNewPreviewUpdateFlag(true));
      yield put(
        commonSliceActions.setAlertAction({
          open: true,
          variant: 'success',
          message: `${t('draftApprovedSuccessfully')}!<br/> ${t('DoYouWantToDigitalySignTheDocument')}?<br/> <div style="font-size: 12px"> ${t('noteafterDigitalSignatureNoUpdatesCanBeDone')} </>`,
          title: `${t('draftApprovedSuccessfully')}`,
          backwardActionText: t('no'),
          forwardActionText: t('yes'),
          forwardAction: () => {
            return handleCheckDateTime({ id: responsePayLoad?.data?.draftId, draftNo: responsePayLoad?.data?.draftNo });
          },
          forwardActionId: 'digital-sign',
          backwardAction: () => handleBackwardAction(searchParamsDetails),
          content: handleTimeOut(),
          closeOnOverlayClick: false,
          closeOnEsc: false
        })
      );
    } else if (location.pathname.includes('summary')) {
      window.location.reload();
    } else if (searchParamsDetails === 'summary') {
      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.fileNo}/summary`,
          active: true
        })
      );
    } else {
      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.fileNo}/notes?show=1`,
          active: true
        })
      );
    }
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'confirm-ds-save' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: 'Draft Update Failed',
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* draftMakeInActive({ payload = {} }) {
  yield fork(handleAPIRequest, api.draftMakeInActive, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE_SUCCESS,
    ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE_FAILURE
  ]);
  if (type === ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE_SUCCESS) {
    const userInfo = yield select(getUserInfo);
    yield put(sliceActions.setDraftNumber(responsePayLoad?.data?.draftNo));

    yield put(sliceActions.setActionOccured(true));

    yield call(fetchDraft, { payload: { fileNo: payload?.fileNo, status: 'ALL' } });
    yield call(fetchAllNotes, { payload: { fileNo: payload?.fileNo, noteStatus: NOTE_STATUS.COMPLETED } });
    yield call(fetchFileDetails, { payload: payload?.fileNo });

    const notesSearchRequest = {
      fileNo: payload?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      yield put(fetchPartialNotes(notesSearchRequest));
    }

    yield call(successTost, { id: 'saved', title: t('success'), description: 'Draft updated successfully' });
    payload?.close();

    if (payload?.location.pathname.includes('summary')) {
      window.location.reload();
    } else if (payload?.searchParamsDetails === 'summary') {
      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.fileNo}/summary`,
          active: true
        })
      );
    } else {
      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.fileNo}/notes?show=1`,
          active: true
        })
      );
    }
  } else {
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: 'Draft Update Failed',
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
  }
}

export function* fetchESign({ payload }) {
  yield fork(handleAPIRequest, api.featchESignApi, payload);
  const { type } = yield take([
    ACTION_TYPES.FETCH_E_SIGN_SUCCESS,
    ACTION_TYPES.FETCH_E_SIGN_FAILURE
  ]);

  yield put(commonSliceActions.setAlertAction({ forwardActionId: 'digital-sign', open: false }));
  if (type === ACTION_TYPES.FETCH_E_SIGN_SUCCESS) {
    yield put(commonSliceActions.navigateTo({ to: `${BASE_PATH}/esign/form` }));
  }
}

export function* updateEsignFlagSaga({ payload }) {
  yield fork(handleAPIRequest, api.updateEsignFlagSaga, payload);
  const userInfo = yield select(getUserInfo);
  const { type } = yield take([
    ACTION_TYPES.UPDATE_E_SIGN_FLAG_SUCCESS,
    ACTION_TYPES.UPDATE_E_SIGN_FLAG_FAILURE
  ]);

  if (type === ACTION_TYPES.UPDATE_E_SIGN_FLAG_SUCCESS) {
    const notesSearchRequest = {
      fileNo: payload?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      yield put(fetchPartialNotes(notesSearchRequest));
    }

    yield call(fetchDraft, { payload: { fileNo: payload?.fileNo, status: 'ALL' } });
    yield call(fetchAllNotes, { payload: { fileNo: payload?.fileNo, noteStatus: NOTE_STATUS.COMPLETED } });
    yield call(fetchFileDetails, { payload: payload?.fileNo });
    // yield put(sliceActions.setDraftNumber(payload?.draftNo));
    // yield put(sliceActions.setActionOccured(true));
  }
}

export function* fetchESignStatusCheck({ payload }) {
  const {
    officeCode, moduleUid, fileNo
  } = payload;
  const data = { officeCode, moduleUid };
  yield fork(handleAPIRequest, api.fetchESignStatusCheck, data);
  const { type } = yield take([
    ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK_SUCCESS,
    ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK_FAILURE
  ]);

  if (type === ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK_SUCCESS) {
    yield put(updateEsignFlag({ fileNo, draftId: moduleUid }));
  }
}

export function* fetchForwardPlusRoleForActions({ payload }) {
  yield fork(handleAPIRequest, api.fetchForwardPlusRoleForActions, payload?.fileNo);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_SUCCESS,
    ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_SUCCESS) {
    yield put(sliceActions.setForwardPlusRoleForActions(responsePayLoad?.data));
  }
}

export default function* inboxSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_FILE_DETAILS, fetchFileDetails),
    takeLatest(ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID, fetchSummaryDetails),
    takeLatest(ACTION_TYPES.FETCH_SERVICES_BY_ID, fetchServiceById),
    takeLatest(ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID, fetchApplicantDetails),
    takeLatest(ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID, fetchPendingActions),
    takeLatest(ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID, fetchPendingDraftDetails),
    takeLatest(ACTION_TYPES.SAVE_DRAFT, saveDraft),
    takeLatest(ACTION_TYPES.FETCH_SENDER_DETAILS, fetchSenderDetails),
    takeLatest(ACTION_TYPES.FETCH_RECEIVER_DETAILS, fetchReceiverDetails),
    takeLatest(ACTION_TYPES.FETCH_APPLICANT_BY_ID, fetchApplicant),
    takeLatest(ACTION_TYPES.SAVE_ADDRESS, saveAddress),
    takeLatest(ACTION_TYPES.SAVE_ENCLOSURE, saveEnclosureData),
    takeLatest(ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS, fetchWardMemberDetails),
    takeLatest(ACTION_TYPES.FETCH_NOTES, fetchNotes),
    takeLatest(ACTION_TYPES.FETCH_ALL_NOTES, fetchAllNotes),
    takeLatest(ACTION_TYPES.SAVE_UN_HOLD_FILE, saveUnHoldFileData),
    takeLatest(ACTION_TYPES.SAVE_NOTE_DOCUMENTS, saveNoteDocuments),
    takeLatest(ACTION_TYPES.SAVE_NOTE, saveNote),
    takeLatest(ACTION_TYPES.SAVE_ADDRESS, saveCopyTo),
    takeLatest(ACTION_TYPES.SAVE_ENQUIRY, saveEnquiry),
    takeLatest(ACTION_TYPES.SAVE_ENQUIRY_DOC, saveEnquiryDoc),
    takeLatest(ACTION_TYPES.FETCH_ENQUIRY_DETAILS, fetchEnquiryDetails),
    takeLatest(ACTION_TYPES.UPDATE_ENQUIRY_TEXT, updateEnquiryText),
    takeLatest(ACTION_TYPES.FETCH_ENQUIRY_DOC, fetchEnquiryDoc),
    takeLatest(ACTION_TYPES.UPDATE_ENQUIRY_DOC, updateEnquiryDoc),
    takeLatest(ACTION_TYPES.FETCH_MERGE_LINK, fetchMergeLink),
    takeLatest(ACTION_TYPES.FETCH_DRAFT, fetchDraft),
    takeLatest(ACTION_TYPES.FETCH_DRAFT_BY_ID, fetchDraftById),
    takeLatest(ACTION_TYPES.FETCH_YEAR, fetchYear),
    takeLatest(ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES, fetchSameSeatMergeFiles),
    takeLatest(ACTION_TYPES.FETCH_MERGED_FILES, fetchMergedFiles),
    takeLatest(ACTION_TYPES.MERGE_FILES, mergeFiles),
    takeLatest(ACTION_TYPES.FETCH_MERGE_LINK, fetchMergeLink),
    takeLatest(ACTION_TYPES.LINK_FILES, linkFiles),
    takeLatest(ACTION_TYPES.FETCH_FUNCTIONAL_GROUP, fetchFunctionalGroup),
    takeLatest(ACTION_TYPES.FETCH_FUNCTIONS, fetchFunctions),
    takeLatest(ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC, saveNoteWithoutDoc),
    takeLatest(ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS, fetchPullDraftDetails),
    takeLatest(ACTION_TYPES.FETCH_PULL_NOTES_DETAILS, fetchPullNotesDetails),
    takeLatest(ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS, searchPullNotesDetails),
    takeLatest(ACTION_TYPES.SAVE_PULL_NOTES, savePullNotes),
    takeLatest(ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS, searchPullDraftDetails),
    takeLatest(ACTION_TYPES.FETCH_FILE_DOCUMENTS, fetchFileDocuments),
    takeLatest(ACTION_TYPES.FETCH_DRAFT_PREVIEW, fetchDraftPreview),
    takeLatest(ACTION_TYPES.UN_MERGE_FILES, unMergeFiles),
    takeLatest(ACTION_TYPES.FETCH_PARTIAL_NOTES, fetchPartialNotesDetails),
    takeLatest(ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT, fetchDraftExistsOrNot),
    takeLatest(ACTION_TYPES.FETCH_MAPPED_SEATS, fetchMappedSeats),
    takeLatest(ACTION_TYPES.SAVE_CHILD_FILE, saveChildFile),
    takeLatest(ACTION_TYPES.SAVE_DRAFT_RETURN, saveDraftReturn),
    takeLatest(ACTION_TYPES.COMPLETE_SAVE, saveComplete),
    takeLatest(ACTION_TYPES.GENERATE_DEMAND, generateDemand),
    takeLatest(ACTION_TYPES.FETCH_APPLICATION_SERVICE, fetchApplication),
    takeLatest(ACTION_TYPES.FETCH_BENEFICIARY_DETAILS, fetchBeneficiaryDetails),
    takeLatest(ACTION_TYPES.UN_LINK_FILES, unLinkFiles),
    takeLatest(ACTION_TYPES.SAVE_RE_OPEN_FILE, saveReOpenFile),
    takeLatest(ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT, fetchFinanceExistsOrNot),
    takeLatest(ACTION_TYPES.FETCH_CHILD_FILES, fetchChildFiles),
    takeLatest(ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE, updateCustodianChange),
    takeLatest(ACTION_TYPES.FETCH_USER_DETAILS, fetchUserDetails),
    takeLatest(ACTION_TYPES.FETCH_BP_ROUTE_KEY, fetchBPRouteKey),
    takeLatest(ACTION_TYPES.FETCH_USERS_BY_FILE_NO, fetchUsersByFileno),
    takeLatest(ACTION_TYPES.FETCH_CERTIFICATE, fetchCertificate),
    takeLatest(ACTION_TYPES.DE_LINK_INWARD, deLinkInward),
    takeLatest(ACTION_TYPES.SAVE_BENEFICIARY, saveBeneficiary),
    takeLatest(ACTION_TYPES.FETCH_BENEFICIARY, fetchBeneficiary),
    takeLatest(ACTION_TYPES.FETCH_BENEFICIARY_BY_ID, fetchBeneficiaryById),
    takeLatest(ACTION_TYPES.DELETE_BENEFICARY, deleteBeneficiary),
    takeLatest(ACTION_TYPES.FETCH_SERVICE_POST_ROUTES, fetchServicePostRoutes),
    takeLatest(ACTION_TYPES.LIST_COMPLETED_NOTES, listCompletedNotes),
    takeLatest(ACTION_TYPES.LIST_COMPLETED_NOTES_DOCUMENTS, listCompletedNotesDocuments),
    takeLatest(ACTION_TYPES.DELETE_DRAFT, deletDraft),
    takeLatest(ACTION_TYPES.DELETE_NOTE_REF, deleteNoteRef),

    takeLatest(ACTION_TYPES.DRAFT_ACTIONS, draftActions),
    takeLatest(ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE, draftMakeInActive),
    takeLatest(ACTION_TYPES.FETCH_E_SIGN, fetchESign),
    takeLatest(ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK, fetchESignStatusCheck),
    takeLatest(ACTION_TYPES.UPDATE_E_SIGN_FLAG, updateEsignFlagSaga),
    takeLatest(ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS, fetchForwardPlusRoleForActions)
  ]);
}
