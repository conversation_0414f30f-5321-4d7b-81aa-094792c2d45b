import * as yup from 'yup';

import { t } from 'common/components';
import { DEFAULT_COUNTRY } from 'common/constants';
import {
  AADHAAR,
  EMAIL, EN, EN_NUMERIC, EN_SPACE, ML, MOBILE, MOBILE_INTERNATIONAL
} from 'common/regex';

export const CreateDraftSchema = yup.object().shape({
  draftType: yup.string().required('Draft type is Required'),
  // subject: yup.string()
  //   .max(250)
  //   .required('Subject is required'),
  subject: yup.string()
    .when(['draftType'], (draftType, schema) => {
      if (Number(draftType[0]) !== 11 && Number(draftType[0]) !== 13) {
        return schema.required('Please enter Subject').test(
          'no-leading-space-after-html-removal',
          'Please enter valid Subject',
          (value) => {
            if (!value) return true;
            const cleanedText = value.replace(/<[^>]*>/g, '');
            return !/^\s*$/.test(cleanedText);
          }
        );
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  draftText: yup
    .string()
    .required('Draft is required')
    .test(
      'no-leading-space-after-html-removal',
      'Please enter valid draft',
      (value) => {
        if (!value) return true;
        const cleanedText = value.replace(/<[^>]*>/g, '');
        return !/^\s*$/.test(cleanedText);
      }
    ),
  sender: yup.string().when(['draftType'], {
    is: (draftType) => {
      if (Number(draftType) === 2 || Number(draftType) === 13) {
        return true;
      }
      return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: t('sender') }))
  })
});

export const NotesSchema = yup.object().shape({
  notes: yup
    .string()
    .required('Please enter Note')
    .test(
      'no-leading-space-after-html-removal',
      'Please enter valid note',
      (value) => {
        if (!value) return true;
        const cleanedText = value.replace(/<[^>]*>/g, '');
        return !/^\s*$/.test(cleanedText);
      }
    )
});

export const AddAddressSchema = yup.object().shape({
  sender: yup.string().required('Please select Sender'),
  name: yup.string().when(['addressType', 'sameAsApplicant'], {
    is: (addressType, sameAsApplicant) => {
      return (addressType === 1 || addressType === 3) && !sameAsApplicant;
    },
    then: (schema) => schema
      .max(150)
      .required('Please enter Name'),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  }),
  address: yup.string().when('sameAsApplicant', {
    is: false,
    then: (schema) => schema.required('Please enter Address'),
    otherwise: (schema) => schema.notRequired()
  }),
  officeName: yup.string().when(['addressType', 'sameAsApplicant'], {
    is: (addressType, sameAsApplicant) => addressType === 2 && !sameAsApplicant,
    then: (schema) => schema.required('Please enter Office Name'),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  }),
  officerName: yup.string().when(['addressType', 'sameAsApplicant'], {
    is: (addressType, sameAsApplicant) => addressType === 2 && !sameAsApplicant,
    then: (schema) => schema.required('Please enter Officer Name'),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  }),
  wardName: yup.string().when(['addressType'], {
    is: (addressType) => {
      if (addressType === 3) {
        return true;
      }
      return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: t('wardName') })),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  }),
  district: yup.string().when(['addressType'], {
    is: (addressType) => {
      if (addressType === 3) {
        return true;
      }
      return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: t('district') })),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  }),
  localBody: yup.string().when(['addressType'], {
    is: (addressType) => {
      if (addressType === 3) {
        return true;
      }
      return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: t('localBody') })),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  }),
  wardId: yup.string().when(['addressType'], {
    is: (addressType) => {
      if (addressType === 3) {
        return true;
      }
      return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: t('ward') })),
    otherwise: () => yup.string().transform((value) => (value || null)).notRequired()
  })
});

export const EnclosureSchema = yup.object({
  enclosureName: yup.string().max(150, 'Only 150 characters can be entered in the Title').required('Enclosure name is Required'),
  enclosureFile: yup.string().required('Enclosure file is Required')
}).required();

export const MergeSchema = yup.object().shape({
  toDate: yup.string()
    .when('fromDate', (fromDate, schema) => {
      return schema.test({
        test(toDate) {
          if (!fromDate || !toDate) {
            return true; // Validation passes if either fromDate or toDate is not provided
          }
          return new Date(toDate) >= new Date(fromDate);
        },
        message: t('toDateGreaterThanFromDateError') // Customize this message according to your requirements
      });
    })
});

export const CopyToSchema = yup.object({
  name: yup.string().required('Name is required').max(150, 'Only 150 characters can be entered in the Title')
}).required();

export const ChildFileSchema = yup.object({
  service: yup.string().required(t('isRequired', { type: t('service') })),
  comment: yup.string().required(t('isRequired', { type: t('comment') }))
}).required();

export const ReopenFileSchema = yup.object({
  reason: yup.string().required(t('isRequired', { type: t('reason') }))
}).required();

export const beneficiarySchema = yup.object().shape({
  countryId: yup.string()
    .required(t('isRequired', { type: t('country') })),
  stateId: yup.number()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('state') }));
      }
      return schema.notRequired();
    }),
  districtId: yup.number()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('district') }));
      }
      return schema.notRequired();
    }),
  mobileNo: yup.string()
    .when(['countryId', 'beneficiaryType'], (data, schema) => {
      if (data[1] === 'INSTITUTION') {
        return schema.notRequired().nullable().transform((val) => val || null).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` }));
      }
      if (Number(data[0]) === DEFAULT_COUNTRY.id && data[1] === 'INDIVIDUAL') {
        return schema.required(t('isRequired', { type: `${t('mobile')} ${t('number')}` })).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` }));
      }
      return schema.notRequired();
    }),
  internationalMobileNo: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(MOBILE_INTERNATIONAL, t('invalidType', { type: `${t('mobile')} ${t('number')}` })),
  whatsapp: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(MOBILE, t('invalidType', { type: `${t('whatsapp')} ${t('number')}` })),
  documentNo: yup.string()
    .when(['countryId'], (data, schema) => {
      if (Number(data[0]) === DEFAULT_COUNTRY.id) {
        return schema.notRequired(t('isRequired', { type: `${t('aadhar')} ${t('number')}` })).nullable().transform((val) => val || null).matches(AADHAAR, t('invalidType', { type: `${t('aadhar')} ${t('number')}` }));
      }
      return schema.required(t('isRequired', { type: `${t('passport')} ${t('number')}` })).length(9).matches(EN_NUMERIC, t('invalidType', { type: `${t('passport')} ${t('number')}` }));
    }),
  documentType: yup.string()
    .required(t('isRequiNumber(data[0], 10)red', { type: t('document') })),
  beneficiaryName: yup.string()
    .when(['beneficiaryType'], (beneficiaryType, schema) => {
      if (beneficiaryType[0] === 'INDIVIDUAL') {
        return schema.required(t('isRequired', { type: `${t('beneficiary')} ${t('name')}` })).max(150);
      }
      return schema.notRequired();
    }),
  institutionName: yup.string()
    .when(['beneficiaryType'], (beneficiaryType, schema) => {
      if (beneficiaryType[0] === 'INSTITUTION') {
        return schema.required(t('isRequired', { type: `${t('institution')} ${t('name')}` })).max(150);
      }
      return schema.notRequired();
    }),
  // officerName: yup.string()
  //   .when(['beneficiaryType'], (beneficiaryType, schema) => {
  //     if (beneficiaryType[0] === 'INSTITUTION') {
  //       return schema.required(t('isRequired', { type: `${t('officer')} ${t('name')}` })).max(150);
  //     }
  //     return schema.notRequired();
  //   }),
  designation: yup.string()
    .when(['beneficiaryType'], (beneficiaryType, schema) => {
      if (beneficiaryType[0] === 'INSTITUTION') {
        return schema.required(t('isRequired', { type: `${t('designation')} ${t('name')}` })).max(150);
      }
      return schema.notRequired();
    }),
  localBeneficiaryName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('first')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  houseName: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN_SPACE, `${t('house')} ${t('name')} ${t('inEnglishRequired')}`),
  localHouseName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('house')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  doorNo: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null),
  street: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('street')} ${t('inEnglishRequired')}`),
  localPlace: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('localPlace')} ${t('inEnglishRequired')}`),
  mainPlace: yup.string()
    .when(['countryId'], (countryId, schema) => {
      if (Number(countryId[0]) === DEFAULT_COUNTRY.id) {
        return schema.notRequired(t('isRequired', { type: t('mainPlace') })).max(150).matches(EN_NUMERIC, t('useAlphaAndNumOnly'));
      }
      return schema.notRequired(t('isRequired', { type: t('mainPlace') })).max(150).matches(EN, `${t('mainPlace')} ${t('inEnglishRequired')}`);
    }),
  localStreet: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('street')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localLocalPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('localPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localMainPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('mainPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localInstitutionName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('institution')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localOfficeName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localDesignation: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  emailId: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EMAIL, t('invalidType', { type: t('emailId') }))
}).required();

export const noteDocsSchema = yup.object().shape({
  title: yup.string().max(150, 'Only 150 characters can be entered in the Title').required('Please enter Title'),
  type: yup.string().required('Please select Document Type'),
  documents: yup.string().required(t('isRequired', { type: t('documents') }))
});
