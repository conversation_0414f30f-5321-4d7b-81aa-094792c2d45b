import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_SUMMARY_DETAILS_BY_ID: `${STATE_REDUCER_KEY}/FETCH_SUMMARY_DETAILS_BY_ID`,
  FETCH_SUMMARY_DETAILS_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUMMARY_DETAILS_BY_ID_REQUEST`,
  FETCH_SUMMARY_DETAILS_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUMMARY_DETAILS_BY_ID_SUCCESS`,
  FETCH_SUMMARY_DETAILS_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUMMARY_DETAILS_BY_ID_FAILURE`,

  FETCH_SERVICES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID`,
  FETCH_SERVICES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_REQUEST`,
  FETCH_SERVICES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_SUCCESS`,
  FETCH_SERVICES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICES_BY_ID_FAILURE`,

  FETCH_SUB_MODULES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_SUB_BY_ID_MODULES`,
  FETCH_SUB_MODULES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_REQUEST`,
  FETCH_SUB_MODULES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_SUCCESS`,
  FETCH_SUB_MODULES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SUB_MODULES_BY_ID_FAILURE`,

  FETCH_MODULES_BY_ID: `${STATE_REDUCER_KEY}/FETCH_BY_ID_MODULES`,
  FETCH_MODULES_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MODULES_BY_ID_REQUEST`,
  FETCH_MODULES_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MODULES_BY_ID_SUCCESS`,
  FETCH_MODULES_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MODULES_BY_ID_FAILURE`,

  FETCH_APPLICANT_DETAILS_BY_ID: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_DETAILS_BY_ID`,
  FETCH_APPLICANT_DETAILS_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_DETAILS_BY_ID_REQUEST`,
  FETCH_APPLICANT_DETAILS_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_DETAILS_BY_ID_SUCCESS`,
  FETCH_APPLICANT_DETAILS_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_DETAILS_BY_ID_FAILURE`,

  FETCH_PENDING_ACTIONS_BY_ID: `${STATE_REDUCER_KEY}/FETCH_PENDING_ACTIONS_BY_ID`,
  FETCH_PENDING_ACTIONS_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PENDING_ACTIONS_BY_ID_REQUEST`,
  FETCH_PENDING_ACTIONS_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PENDING_ACTIONS_BY_ID_SUCCESS`,
  FETCH_PENDING_ACTIONS_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PENDING_ACTIONS_BY_ID_FAILURE`,

  FETCH_PENDING_DRAFT_DETAILS_BY_ID: `${STATE_REDUCER_KEY}/FETCH_PENDING_DRAFT_DETAILS_BY_ID`,
  FETCH_PENDING_DRAFT_DETAILS_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PENDING_DRAFT_DETAILS_BY_ID_REQUEST`,
  FETCH_PENDING_DRAFT_DETAILS_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PENDING_DRAFT_DETAILS_BY_ID_SUCCESS`,
  FETCH_PENDING_DRAFT_DETAILS_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PENDING_DRAFT_DETAILS_BY_ID_FAILURE`,

  SAVE_DRAFT: `${STATE_REDUCER_KEY}/SAVE_DRAFT`,
  SAVE_DRAFT_REQUEST: `${STATE_REDUCER_KEY}/SAVE_DRAFT_REQUEST`,
  SAVE_DRAFT_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_DRAFT_SUCCESS`,
  SAVE_DRAFT_FAILURE: `${STATE_REDUCER_KEY}/SAVE_DRAFT_FAILURE`,

  FETCH_SENDER_DETAILS: `${STATE_REDUCER_KEY}/FETCH_SENDER_DETAILS`,
  FETCH_SENDER_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SENDER_DETAILS_REQUEST`,
  FETCH_SENDER_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SENDER_DETAILS_SUCCESS`,
  FETCH_SENDER_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SENDER_DETAILS_FAILURE`,

  FETCH_RECEIVER_DETAILS: `${STATE_REDUCER_KEY}/FETCH_RECEIVER_DETAILS`,
  FETCH_RECEIVER_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_RECEIVER_DETAILS_REQUEST`,
  FETCH_RECEIVER_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_RECEIVER_DETAILS_SUCCESS`,
  FETCH_RECEIVER_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_RECEIVER_DETAILS_FAILURE`,

  FETCH_APPLICANT_BY_ID: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_BY_ID`,
  FETCH_APPLICANT_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_BY_ID_REQUEST`,
  FETCH_APPLICANT_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_BY_ID_SUCCESS`,
  FETCH_APPLICANT_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_APPLICANT_BY_ID_FAILURE`,

  SAVE_ADDRESS: `${STATE_REDUCER_KEY}/SAVE_ADDRESS`,
  SAVE_ADDRESS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_ADDRESS_REQUEST`,
  SAVE_ADDRESS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_ADDRESS_SUCCESS`,
  SAVE_ADDRESS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_ADDRESS_FAILURE`,

  SAVE_ENCLOSURE: `${STATE_REDUCER_KEY}/SAVE_ENCLOSURE`,
  SAVE_ENCLOSURE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_ENCLOSURE_REQUEST`,
  SAVE_ENCLOSURE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_ENCLOSURE_SUCCESS`,
  SAVE_ENCLOSURE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_ENCLOSURE_FAILURE`,

  FETCH_WARD_MEMBER_DETAILS: `${STATE_REDUCER_KEY}/FETCH_WARD_MEMBER_DETAILS`,
  FETCH_WARD_MEMBER_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_WARD_MEMBER_DETAILS_REQUEST`,
  FETCH_WARD_MEMBER_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_WARD_MEMBER_DETAILS_SUCCESS`,
  FETCH_WARD_MEMBER_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_WARD_MEMBER_DETAILS_FAILURE`,

  FETCH_NOTES: `${STATE_REDUCER_KEY}/FETCH_NOTES`,
  FETCH_NOTES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_NOTES_REQUEST`,
  FETCH_NOTES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_NOTES_SUCCESS`,
  FETCH_NOTES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_NOTES_FAILURE`,

  FETCH_ALL_NOTES: `${STATE_REDUCER_KEY}/FETCH_ALL_NOTES`,
  FETCH_ALL_NOTES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ALL_NOTES_REQUEST`,
  FETCH_ALL_NOTES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ALL_NOTES_SUCCESS`,
  FETCH_ALL_NOTES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ALL_NOTES_FAILURE`,

  SAVE_UN_HOLD_FILE: `${STATE_REDUCER_KEY}/SAVE_UN_HOLD_FILE`,
  SAVE_UN_HOLD_FILE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_UN_HOLD_FILE_REQUEST`,
  SAVE_UN_HOLD_FILE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_UN_HOLD_FILE_SUCCESS`,
  SAVE_UN_HOLD_FILE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_UN_HOLD_FILE_FAILURE`,

  SAVE_NOTE_DOCUMENTS: `${STATE_REDUCER_KEY}/SAVE_NOTE_DOCUMENTS`,
  SAVE_NOTE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/SAVE_NOTE_DOCUMENTS_REQUEST`,
  SAVE_NOTE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_NOTE_DOCUMENTS_SUCCESS`,
  SAVE_NOTE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/SAVE_NOTE_DOCUMENTS_FAILURE`,

  SAVE_NOTE: `${STATE_REDUCER_KEY}/SAVE_NOTE`,
  SAVE_NOTE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_NOTE_REQUEST`,
  SAVE_NOTE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_NOTE_SUCCESS`,
  SAVE_NOTE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_NOTE_FAILURE`,

  FETCH_FILE_DETAILS: `${STATE_REDUCER_KEY}/FETCH_FILE_DETAILS`,
  FETCH_FILE_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILE_DETAILS_REQUEST`,
  FETCH_FILE_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILE_DETAILS_SUCCESS`,
  FETCH_FILE_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILE_DETAILS_FAILURE`,

  // enquiry
  SAVE_ENQUIRY: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY`,
  SAVE_ENQUIRY_REQUEST: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_REQUEST`,
  SAVE_ENQUIRY_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_SUCCESS`,
  SAVE_ENQUIRY_FAILURE: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_FAILURE`,

  SAVE_ENQUIRY_DOC: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_DOC`,
  SAVE_ENQUIRY_DOC_REQUEST: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_DOC_REQUEST`,
  SAVE_ENQUIRY_DOC_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_DOC_SUCCESS`,
  SAVE_ENQUIRY_DOC_FAILURE: `${STATE_REDUCER_KEY}/SAVE_ENQUIRY_DOC_FAILURE`,

  FETCH_ENQUIRY_DETAILS: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DETAILS`,
  FETCH_ENQUIRY_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DETAILS_REQUEST`,
  FETCH_ENQUIRY_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DETAILS_SUCCESS`,
  FETCH_ENQUIRY_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DETAILS_FAILURE`,

  UPDATE_ENQUIRY_TEXT: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_TEXT`,
  UPDATE_ENQUIRY_TEXT_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_TEXT_REQUEST`,
  UPDATE_ENQUIRY_TEXT_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_TEXT_SUCCESS`,
  UPDATE_ENQUIRY_TEXT_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_TEXT_FAILURE`,

  FETCH_ENQUIRY_DOC: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DOC`,
  FETCH_ENQUIRY_DOC_REQUEST: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DOC_REQUEST`,
  FETCH_ENQUIRY_DOC_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DOC_SUCCESS`,
  FETCH_ENQUIRY_DOC_FAILURE: `${STATE_REDUCER_KEY}/FETCH_ENQUIRY_DOC_FAILURE`,

  SAVE_COPY_TO: `${STATE_REDUCER_KEY}/SAVE_COPY_TO`,
  SAVE_COPY_TO_REQUEST: `${STATE_REDUCER_KEY}/SAVE_COPY_TO_REQUEST`,
  SAVE_COPY_TO_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_COPY_TO_SUCCESS`,
  SAVE_COPY_TO_FAILURE: `${STATE_REDUCER_KEY}/SAVE_COPY_TO_FAILURE`,

  UPDATE_ENQUIRY_DOC: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_DOC`,
  UPDATE_ENQUIRY_DOC_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_DOC_REQUEST`,
  UPDATE_ENQUIRY_DOC_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_DOC_SUCCESS`,
  UPDATE_ENQUIRY_DOC_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_ENQUIRY_DOC_FAILURE`,

  FETCH_YEAR: `${STATE_REDUCER_KEY}/FETCH_YEAR`,
  FETCH_YEAR_REQUEST: `${STATE_REDUCER_KEY}/FETCH_YEAR_REQUEST`,
  FETCH_YEAR_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_YEAR_SUCCESS`,
  FETCH_YEAR_FAILURE: `${STATE_REDUCER_KEY}/FETCH_YEAR_FAILURE`,

  FETCH_MERGE_SAME_SEAT_FILES: `${STATE_REDUCER_KEY}/FETCH_MERGE_SAME_SEAT_FILES`,
  FETCH_MERGE_SAME_SEAT_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MERGE_SAME_SEAT_FILES_REQUEST`,
  FETCH_MERGE_SAME_SEAT_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MERGE_SAME_SEAT_FILES_SUCCESS`,
  FETCH_MERGE_SAME_SEAT_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MERGE_SAME_SEAT_FILES_FAILURE`,

  FETCH_MERGED_FILES: `${STATE_REDUCER_KEY}/FETCH_MERGED_FILES`,
  FETCH_MERGED_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MERGED_FILES_REQUEST`,
  FETCH_MERGED_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MERGED_FILES_SUCCESS`,
  FETCH_MERGED_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MERGED_FILES_FAILURE`,

  MERGE_FILES: `${STATE_REDUCER_KEY}/MERGE_FILES`,
  MERGE_FILES_REQUEST: `${STATE_REDUCER_KEY}/MERGE_FILES_REQUEST`,
  MERGE_FILES_SUCCESS: `${STATE_REDUCER_KEY}/MERGE_FILES_SUCCESS`,
  MERGE_FILES_FAILURE: `${STATE_REDUCER_KEY}/MERGE_FILES_FAILURE`,

  FETCH_MERGE_LINK: `${STATE_REDUCER_KEY}/FETCH_MERGE_LINK`,
  FETCH_MERGE_LINK_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MERGE_LINK_REQUEST`,
  FETCH_MERGE_LINK_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MERGE_LINK_SUCCESS`,
  FETCH_MERGE_LINK_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MERGE_LINK_FAILURE`,

  FETCH_DRAFT: `${STATE_REDUCER_KEY}/FETCH_DRAFT`,
  FETCH_DRAFT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_REQUEST`,
  FETCH_DRAFT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DRAFT_SUCCESS`,
  FETCH_DRAFT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DRAFT_FAILURE`,

  FETCH_DRAFT_BY_ID: `${STATE_REDUCER_KEY}/FETCH_DRAFT_BY_ID`,
  FETCH_DRAFT_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_BY_ID_REQUEST`,
  FETCH_DRAFT_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DRAFT_BY_ID_SUCCESS`,
  FETCH_DRAFT_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DRAFT_BY_ID_FAILURE`,

  LINK_FILES: `${STATE_REDUCER_KEY}/LINK_FILES`,
  LINK_FILES_REQUEST: `${STATE_REDUCER_KEY}/LINK_FILES_REQUEST`,
  LINK_FILES_SUCCESS: `${STATE_REDUCER_KEY}/LINK_FILES_SUCCESS`,
  LINK_FILES_FAILURE: `${STATE_REDUCER_KEY}/LINK_FILES_FAILURE`,

  FETCH_FUNCTIONAL_GROUP: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP`,
  FETCH_FUNCTIONAL_GROUP_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP_REQUEST`,
  FETCH_FUNCTIONAL_GROUP_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP_SUCCESS`,
  FETCH_FUNCTIONAL_GROUP_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONAL_GROUP_FAILURE`,

  FETCH_FUNCTIONS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS`,
  FETCH_FUNCTIONS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS_REQUEST`,
  FETCH_FUNCTIONS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS_SUCCESS`,
  FETCH_FUNCTIONS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FUNCTIONS_FAILURE`,

  SAVE_NOTE_WITHOUT_DOC: `${STATE_REDUCER_KEY}/SAVE_NOTE_WITHOUT_DOC`,
  SAVE_NOTE_WITHOUT_DOC_REQUEST: `${STATE_REDUCER_KEY}/SAVE_NOTE_WITHOUT_DOC_REQUEST`,
  SAVE_NOTE_WITHOUT_DOC_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_NOTE_WITHOUT_DOC_SUCCESS`,
  SAVE_NOTE_WITHOUT_DOC_FAILURE: `${STATE_REDUCER_KEY}/SAVE_NOTE_WITHOUT_DOC_FAILURE`,

  FETCH_PULL_DRAFT_DETAILS: `${STATE_REDUCER_KEY}/FETCH_PULL_DRAFT_DETAILS`,
  FETCH_PULL_DRAFT_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PULL_DRAFT_DETAILS_REQUEST`,
  FETCH_PULL_DRAFT_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PULL_DRAFT_DETAILS_SUCCESS`,
  FETCH_PULL_DRAFT_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PULL_DRAFT_DETAILS_FAILURE`,

  FETCH_PULL_NOTES_DETAILS: `${STATE_REDUCER_KEY}/FETCH_PULL_NOTES_DETAILS`,
  FETCH_PULL_NOTES_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PULL_NOTES_DETAILS_REQUEST`,
  FETCH_PULL_NOTES_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PULL_NOTES_DETAILS_SUCCESS`,
  FETCH_PULL_NOTES_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PULL_NOTES_DETAILS_FAILURE`,

  SEARCH_PULL_NOTES_DETAILS: `${STATE_REDUCER_KEY}/SEARCH_PULL_NOTES_DETAILS`,
  SEARCH_PULL_NOTES_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SEARCH_PULL_NOTES_DETAILS_REQUEST`,
  SEARCH_PULL_NOTES_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SEARCH_PULL_NOTES_DETAILS_SUCCESS`,
  SEARCH_PULL_NOTES_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SEARCH_PULL_NOTES_DETAILS_FAILURE`,

  SAVE_PULL_NOTES: `${STATE_REDUCER_KEY}/SAVE_PULL_NOTES`,
  SAVE_PULL_NOTES_REQUEST: `${STATE_REDUCER_KEY}/SAVE_PULL_NOTES_REQUEST`,
  SAVE_PULL_NOTES_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_PULL_NOTES_SUCCESS`,
  SAVE_PULL_NOTES_FAILURE: `${STATE_REDUCER_KEY}/SAVE_PULL_NOTES_FAILURE`,

  SEARCH_PULL_DRAFT_DETAILS: `${STATE_REDUCER_KEY}/SEARCH_PULL_DRAFT_DETAILS`,
  SEARCH_PULL_DRAFT_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/SEARCH_PULL_DRAFT_DETAILS_REQUEST`,
  SEARCH_PULL_DRAFT_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/SEARCH_PULL_DRAFT_DETAILS_SUCCESS`,
  SEARCH_PULL_DRAFT_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/SEARCH_PULL_DRAFT_DETAILS_FAILURE`,

  FETCH_FILE_DOCUMENTS: `${STATE_REDUCER_KEY}/FETCH_FILE_DOCUMENTS`,
  FETCH_FILE_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILE_DOCUMENTS_REQUEST`,
  FETCH_FILE_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILE_DOCUMENTS_SUCCESS`,
  FETCH_FILE_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILE_DOCUMENTS_FAILURE`,

  FETCH_DRAFT_PREVIEW: `${STATE_REDUCER_KEY}/FETCH_DRAFT_PREVIEW`,
  FETCH_DRAFT_PREVIEW_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DRAFT_PREVIEW_REQUEST`,
  FETCH_DRAFT_PREVIEW_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DRAFT_PREVIEW_SUCCESS`,
  FETCH_DRAFT_PREVIEW_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DRAFT_PREVIEW_FAILURE`,

  UN_MERGE_FILES: `${STATE_REDUCER_KEY}/UN_MERGE_FILES`,
  UN_MERGE_FILES_REQUEST: `${STATE_REDUCER_KEY}/UN_MERGE_FILES_REQUEST`,
  UN_MERGE_FILES_SUCCESS: `${STATE_REDUCER_KEY}/UN_MERGE_FILES_SUCCESS`,
  UN_MERGE_FILES_FAILURE: `${STATE_REDUCER_KEY}/UN_MERGE_FILES_FAILURE`,

  FETCH_PARTIAL_NOTES: `${STATE_REDUCER_KEY}/FETCH_PARTIAL_NOTES`,
  FETCH_PARTIAL_NOTES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PARTIAL_NOTES_REQUEST`,
  FETCH_PARTIAL_NOTES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PARTIAL_NOTES_SUCCESS`,
  FETCH_PARTIAL_NOTES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PARTIAL_NOTES_FAILURE`,

  FETCH_DARFT_EXISTS_OR_NOT: `${STATE_REDUCER_KEY}/FETCH_DARFT_EXISTS_OR_NOT`,
  FETCH_DARFT_EXISTS_OR_NOT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DARFT_EXISTS_OR_NOT_REQUEST`,
  FETCH_DARFT_EXISTS_OR_NOT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DARFT_EXISTS_OR_NOT_SUCCESS`,
  FETCH_DARFT_EXISTS_OR_NOT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DARFT_EXISTS_OR_NOT_FAILURE`,

  FETCH_MAPPED_SEATS: `${STATE_REDUCER_KEY}/FETCH_MAPPED_SEATS`,
  FETCH_MAPPED_SEATS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MAPPED_SEATS_REQUEST`,
  FETCH_MAPPED_SEATS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MAPPED_SEATS_SUCCESS`,
  FETCH_MAPPED_SEATS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MAPPED_SEATS_FAILURE`,

  SAVE_CHILD_FILE: `${STATE_REDUCER_KEY}/SAVE_CHILD_FILE`,
  SAVE_CHILD_FILE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_CHILD_FILE_REQUEST`,
  SAVE_CHILD_FILE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_CHILD_FILE_SUCCESS`,
  SAVE_CHILD_FILE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_CHILD_FILE_FAILURE`,

  SAVE_DRAFT_RETURN: `${STATE_REDUCER_KEY}/SAVE_DRAFT_RETURN`,
  SAVE_DRAFT_RETURN_REQUEST: `${STATE_REDUCER_KEY}/SAVE_DRAFT_RETURN_REQUEST`,
  SAVE_DRAFT_RETURN_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_DRAFT_RETURN_SUCCESS`,
  SAVE_DRAFT_RETURN_FAILURE: `${STATE_REDUCER_KEY}/SAVE_DRAFT_RETURN_FAILURE`,

  GENERATE_DEMAND: `${STATE_REDUCER_KEY}/GENERATE_DEMAND`,
  GENERATE_DEMAND_REQUEST: `${STATE_REDUCER_KEY}/GENERATE_DEMAND_REQUEST`,
  GENERATE_DEMAND_SUCCESS: `${STATE_REDUCER_KEY}/GENERATE_DEMAND_SUCCESS`,
  GENERATE_DEMAND_FAILURE: `${STATE_REDUCER_KEY}/GENERATE_DEMAND_FAILURE`,

  COMPLETE_SAVE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE`,
  COMPLETE_SAVE_REQUEST: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_REQUEST`,
  COMPLETE_SAVE_SUCCESS: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_SUCCESS`,
  COMPLETE_SAVE_FAILURE: `${STATE_REDUCER_KEY}/COMPLETE_SAVE_FAILURE`,

  FETCH_APPLICATION_SERVICE: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE`,
  FETCH_APPLICATION_SERVICE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE_REQUEST`,
  FETCH_APPLICATION_SERVICE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE_SUCCESS`,
  FETCH_APPLICATION_SERVICE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_APPLICATION_SERVICE_FAILURE`,

  FETCH_BENEFICIARY_DETAILS: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_DETAILS`,
  FETCH_BENEFICIARY_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_DETAILS_REQUEST`,
  FETCH_BENEFICIARY_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_DETAILS_SUCCESS`,
  FETCH_BENEFICIARY_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_DETAILS_FAILURE`,

  UN_LINK_FILES: `${STATE_REDUCER_KEY}/UN_LINK_FILES`,
  UN_LINK_FILES_REQUEST: `${STATE_REDUCER_KEY}/UN_LINK_FILES_REQUEST`,
  UN_LINK_FILES_SUCCESS: `${STATE_REDUCER_KEY}/UN_LINK_FILES_SUCCESS`,
  UN_LINK_FILES_FAILURE: `${STATE_REDUCER_KEY}/UN_LINK_FILES_FAILURE`,

  SAVE_RE_OPEN_FILE: `${STATE_REDUCER_KEY}/SAVE_RE_OPEN_FILE`,
  SAVE_RE_OPEN_FILE_REQUEST: `${STATE_REDUCER_KEY}/SAVE_RE_OPEN_FILE_REQUEST`,
  SAVE_RE_OPEN_FILE_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_RE_OPEN_FILE_SUCCESS`,
  SAVE_RE_OPEN_FILE_FAILURE: `${STATE_REDUCER_KEY}/SAVE_RE_OPEN_FILE_FAILURE`,

  FETCH_FINANCE_EXISTS_OR_NOT: `${STATE_REDUCER_KEY}/FETCH_FINANCE_EXISTS_OR_NOT`,
  FETCH_FINANCE_EXISTS_OR_NOT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FINANCE_EXISTS_OR_NOT_REQUEST`,
  FETCH_FINANCE_EXISTS_OR_NOT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FINANCE_EXISTS_OR_NOT_SUCCESS`,
  FETCH_FINANCE_EXISTS_OR_NOT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FINANCE_EXISTS_OR_NOT_FAILURE`,

  FETCH_CHILD_FILES: `${STATE_REDUCER_KEY}/FETCH_CHILD_FILES`,
  FETCH_CHILD_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_CHILD_FILES_REQUEST`,
  FETCH_CHILD_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_CHILD_FILES_SUCCESS`,
  FETCH_CHILD_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_CHILD_FILES_FAILURE`,

  UPDATE_CUSTODIAN_CHANGE: `${STATE_REDUCER_KEY}/UPDATE_CUSTODIAN_CHANGE`,
  UPDATE_CUSTODIAN_CHANGE_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_CUSTODIAN_CHANGE_REQUEST`,
  UPDATE_CUSTODIAN_CHANGE_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_CUSTODIAN_CHANGE_SUCCESS`,
  UPDATE_CUSTODIAN_CHANGE_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_CUSTODIAN_CHANGE_FAILURE`,

  FETCH_USER_DETAILS: `${STATE_REDUCER_KEY}/FETCH_USER_DETAILS`,
  FETCH_USER_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_USER_DETAILS_REQUEST`,
  FETCH_USER_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_USER_DETAILS_SUCCESS`,
  FETCH_USER_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_USER_DETAILS_FAILURE`,

  // BP CUSTODIAN CHANGE FETCH
  FETCH_BP_ROUTE_KEY: `${STATE_REDUCER_KEY}/FETCH_BP_ROUTE_KEY`,
  FETCH_BP_ROUTE_KEY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BP_ROUTE_KEY_REQUEST`,
  FETCH_BP_ROUTE_KEY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BP_ROUTE_KEY_SUCCESS`,
  FETCH_BP_ROUTE_KEY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BP_ROUTE_KEY_FAILURE`,

  FETCH_USERS_BY_FILE_NO: `${STATE_REDUCER_KEY}/FETCH_USERS_BY_FILE_NO`,
  FETCH_USERS_BY_FILE_NO_REQUEST: `${STATE_REDUCER_KEY}/FETCH_USERS_BY_FILE_NO_REQUEST`,
  FETCH_USERS_BY_FILE_NO_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_USERS_BY_FILE_NO_SUCCESS`,
  FETCH_USERS_BY_FILE_NO_FAILURE: `${STATE_REDUCER_KEY}/FETCH_USERS_BY_FILE_NO_FAILURE`,

  FETCH_CERTIFICATE: `${STATE_REDUCER_KEY}/FETCH_CERTIFICATE`,
  FETCH_CERTIFICATE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_CERTIFICATE_REQUEST`,
  FETCH_CERTIFICATE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_CERTIFICATE_SUCCESS`,
  FETCH_CERTIFICATE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_CERTIFICATE_FAILURE`,

  DE_LINK_INWARD: `${STATE_REDUCER_KEY}/DE_LINK_INWARD`,
  DE_LINK_INWARD_REQUEST: `${STATE_REDUCER_KEY}/DE_LINK_INWARD_REQUEST`,
  DE_LINK_INWARD_SUCCESS: `${STATE_REDUCER_KEY}/DE_LINK_INWARD_SUCCESS`,
  DE_LINK_INWARD_FAILURE: `${STATE_REDUCER_KEY}/DE_LINK_INWARD_FAILURE`,

  SAVE_BENEFICIARY: `${STATE_REDUCER_KEY}/SAVE_BENEFICIARY`,
  SAVE_BENEFICIARY_REQUEST: `${STATE_REDUCER_KEY}/SAVE_BENEFICIARY_REQUEST`,
  SAVE_BENEFICIARY_SUCCESS: `${STATE_REDUCER_KEY}/SAVE_BENEFICIARY_SUCCESS`,
  SAVE_BENEFICIARY_FAILURE: `${STATE_REDUCER_KEY}/SAVE_BENEFICIARY_FAILURE`,

  FETCH_BENEFICIARY: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY`,
  FETCH_BENEFICIARY_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_REQUEST`,
  FETCH_BENEFICIARY_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_SUCCESS`,
  FETCH_BENEFICIARY_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_FAILURE`,

  FETCH_BENEFICIARY_BY_ID: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_BY_ID`,
  FETCH_BENEFICIARY_BY_ID_REQUEST: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_BY_ID_REQUEST`,
  FETCH_BENEFICIARY_BY_ID_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_BY_ID_SUCCESS`,
  FETCH_BENEFICIARY_BY_ID_FAILURE: `${STATE_REDUCER_KEY}/FETCH_BENEFICIARY_BY_ID_FAILURE`,

  DELETE_BENEFICARY: `${STATE_REDUCER_KEY}/DELETE_BENEFICARY`,
  DELETE_BENEFICARY_REQUEST: `${STATE_REDUCER_KEY}/DELETE_BENEFICARY_REQUEST`,
  DELETE_BENEFICARY_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_BENEFICARY_SUCCESS`,
  DELETE_BENEFICARY_FAILURE: `${STATE_REDUCER_KEY}/DELETE_BENEFICARY_FAILURE`,

  FETCH_SERVICE_POST_ROUTES: `${STATE_REDUCER_KEY}/FETCH_SERVICE_POST_ROUTES`,
  FETCH_SERVICE_POST_ROUTES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_POST_ROUTES_REQUEST`,
  FETCH_SERVICE_POST_ROUTES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_POST_ROUTES_SUCCESS`,
  FETCH_SERVICE_POST_ROUTES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_POST_ROUTES_FAILURE`,

  LIST_COMPLETED_NOTES: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES`,
  LIST_COMPLETED_NOTES_REQUEST: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_REQUEST`,
  LIST_COMPLETED_NOTES_SUCCESS: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_SUCCESS`,
  LIST_COMPLETED_NOTES_FAILURE: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_FAILURE`,

  LIST_COMPLETED_NOTES_DOCUMENTS: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_DOCUMENTS`,
  LIST_COMPLETED_NOTES_DOCUMENTS_REQUEST: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_DOCUMENTS_REQUEST`,
  LIST_COMPLETED_NOTES_DOCUMENTS_SUCCESS: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_DOCUMENTS_SUCCESS`,
  LIST_COMPLETED_NOTES_DOCUMENTS_FAILURE: `${STATE_REDUCER_KEY}/LIST_COMPLETED_NOTES_DOCUMENTS_FAILURE`,

  DELETE_DRAFT: `${STATE_REDUCER_KEY}/DELETE_DRAFT`,
  DELETE_DRAFT_REQUEST: `${STATE_REDUCER_KEY}/DELETE_DRAFT_REQUEST`,
  DELETE_DRAFT_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_DRAFT_SUCCESS`,
  DELETE_DRAFT_FAILURE: `${STATE_REDUCER_KEY}/DELETE_DRAFT_FAILURE`,

  DELETE_NOTE_REF: `${STATE_REDUCER_KEY}/DELETE_NOTE_REF`,
  DELETE_NOTE_REF_REQUEST: `${STATE_REDUCER_KEY}/DELETE_NOTE_REF_REQUEST`,
  DELETE_NOTE_REF_SUCCESS: `${STATE_REDUCER_KEY}/DELETE_NOTE_REF_SUCCESS`,
  DELETE_NOTE_REF_FAILURE: `${STATE_REDUCER_KEY}/DELETE_NOTE_REF_FAILURE`,

  DRAFT_ACTIONS: `${STATE_REDUCER_KEY}/DRAFT_ACTIONS`,
  DRAFT_ACTIONS_REQUEST: `${STATE_REDUCER_KEY}/DRAFT_ACTIONS_REQUEST`,
  DRAFT_ACTIONS_SUCCESS: `${STATE_REDUCER_KEY}/DRAFT_ACTIONS_SUCCESS`,
  DRAFT_ACTIONS_FAILURE: `${STATE_REDUCER_KEY}/DRAFT_ACTIONS_FAILURE`,

  DRAFT_MAKE_IN_ACTIVE: `${STATE_REDUCER_KEY}/DRAFT_MAKE_IN_ACTIVE`,
  DRAFT_MAKE_IN_ACTIVE_REQUEST: `${STATE_REDUCER_KEY}/DRAFT_MAKE_IN_ACTIVE_REQUEST`,
  DRAFT_MAKE_IN_ACTIVE_SUCCESS: `${STATE_REDUCER_KEY}/DRAFT_MAKE_IN_ACTIVE_SUCCESS`,
  DRAFT_MAKE_IN_ACTIVE_FAILURE: `${STATE_REDUCER_KEY}/DRAFT_MAKE_IN_ACTIVE_FAILURE`,

  FETCH_E_SIGN: `${STATE_REDUCER_KEY}/FETCH_E_SIGN`,
  FETCH_E_SIGN_REQUEST: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_REQUEST`,
  FETCH_E_SIGN_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_SUCCESS`,
  FETCH_E_SIGN_FAILURE: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_FAILURE`,

  FETCH_E_SIGN_STATUS_CHECK: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_STATUS_CHECK`,
  FETCH_E_SIGN_STATUS_CHECK_REQUEST: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_STATUS_CHECK_REQUEST`,
  FETCH_E_SIGN_STATUS_CHECK_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_STATUS_CHECK_SUCCESS`,
  FETCH_E_SIGN_STATUS_CHECK_FAILURE: `${STATE_REDUCER_KEY}/FETCH_E_SIGN_STATUS_CHECK_FAILURE`,

  UPDATE_E_SIGN_FLAG: `${STATE_REDUCER_KEY}/UPDATE_E_SIGN_FLAG`,
  UPDATE_E_SIGN_FLAG_REQUEST: `${STATE_REDUCER_KEY}/UPDATE_E_SIGN_FLAG_REQUEST`,
  UPDATE_E_SIGN_FLAG_SUCCESS: `${STATE_REDUCER_KEY}/UPDATE_E_SIGN_FLAG_SUCCESS`,
  UPDATE_E_SIGN_FLAG_FAILURE: `${STATE_REDUCER_KEY}/UPDATE_E_SIGN_FLAG_FAILURE`,

  FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS: `${STATE_REDUCER_KEY}/FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS`,
  FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_REQUEST`,
  FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_SUCCESS`,
  FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_FAILURE`

};

// fetch application summary details
export const fetchSummaryDetails = createAction(ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID);
export const fetchServicesById = createAction(ACTION_TYPES.FETCH_SERVICES_BY_ID);
export const fetchApplicantDetails = createAction(ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID);
export const fetchPendingActions = createAction(ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID);
export const fetchPendingDraftDetails = createAction(ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID);
export const saveDraft = createAction(ACTION_TYPES.SAVE_DRAFT);
export const fetchSenderDetails = createAction(ACTION_TYPES.FETCH_SENDER_DETAILS);
export const fetchReceiverDetails = createAction(ACTION_TYPES.FETCH_RECEIVER_DETAILS);
export const fetchApplicant = createAction(ACTION_TYPES.FETCH_APPLICANT_BY_ID);
export const saveAddress = createAction(ACTION_TYPES.SAVE_ADDRESS);
export const saveEnclosureData = createAction(ACTION_TYPES.SAVE_ENCLOSURE);
export const fetchWardMemberDetails = createAction(ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS);
export const fetchNotes = createAction(ACTION_TYPES.FETCH_NOTES);
export const fetchAllNotes = createAction(ACTION_TYPES.FETCH_ALL_NOTES);
export const saveUnHoldFile = createAction(ACTION_TYPES.SAVE_UN_HOLD_FILE);
export const saveNoteDocuments = createAction(ACTION_TYPES.SAVE_NOTE_DOCUMENTS);
export const saveNote = createAction(ACTION_TYPES.SAVE_NOTE);
export const saveCopyTo = createAction(ACTION_TYPES.SAVE_COPY_TO);
export const saveEnquiry = createAction(ACTION_TYPES.SAVE_ENQUIRY);
export const saveEnquiryDoc = createAction(ACTION_TYPES.SAVE_ENQUIRY_DOC);
export const fetchFileDetails = createAction(ACTION_TYPES.FETCH_FILE_DETAILS);
export const fetchBeneficiaryDetails = createAction(ACTION_TYPES.FETCH_BENEFICIARY_DETAILS);
export const fetchEnquiryDetails = createAction(ACTION_TYPES.FETCH_ENQUIRY_DETAILS);
export const updateEnquiryText = createAction(ACTION_TYPES.UPDATE_ENQUIRY_TEXT);
export const fetchEnquiryDoc = createAction(ACTION_TYPES.FETCH_ENQUIRY_DOC);
export const updateEnquiryDoc = createAction(ACTION_TYPES.UPDATE_ENQUIRY_DOC);
export const fetchYears = createAction(ACTION_TYPES.FETCH_YEAR);
export const searchMergeFile = createAction(ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES);
export const fetchMergedFiles = createAction(ACTION_TYPES.FETCH_MERGED_FILES);
export const mergeFiles = createAction(ACTION_TYPES.MERGE_FILES);
export const fetchMergeLink = createAction(ACTION_TYPES.FETCH_MERGE_LINK);
export const fetchDraft = createAction(ACTION_TYPES.FETCH_DRAFT);
export const fetchDraftById = createAction(ACTION_TYPES.FETCH_DRAFT_BY_ID);
export const linkFiles = createAction(ACTION_TYPES.LINK_FILES);
export const fetchFunctionalGroup = createAction(ACTION_TYPES.FETCH_FUNCTIONAL_GROUP);
export const fetchFunctions = createAction(ACTION_TYPES.FETCH_FUNCTIONS);
export const saveNoteWithoutDoc = createAction(ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC);
export const fetchPullDraftDetails = createAction(ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS);

export const fetchPullNotesDetails = createAction(ACTION_TYPES.FETCH_PULL_NOTES_DETAILS);
export const searchPullNotesDetails = createAction(ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS);
export const savePullNotes = createAction(ACTION_TYPES.SAVE_PULL_NOTES);
export const searchPullDraftDetails = createAction(ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS);
export const fetchFileDocuments = createAction(ACTION_TYPES.FETCH_FILE_DOCUMENTS);
export const fetchDraftPreview = createAction(ACTION_TYPES.FETCH_DRAFT_PREVIEW);
export const unMergeFiles = createAction(ACTION_TYPES.UN_MERGE_FILES);
export const fetchPartialNotes = createAction(ACTION_TYPES.FETCH_PARTIAL_NOTES);
export const fetchDraftExistsOrNot = createAction(ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT);

export const fetchMappedSeats = createAction(ACTION_TYPES.FETCH_MAPPED_SEATS);
export const saveChildFile = createAction(ACTION_TYPES.SAVE_CHILD_FILE);
export const saveDraftReturn = createAction(ACTION_TYPES.SAVE_DRAFT_RETURN);
export const generateDemand = createAction(ACTION_TYPES.GENERATE_DEMAND);
export const fetchApplicationService = createAction(ACTION_TYPES.FETCH_APPLICATION_SERVICE);
export const saveComplete = createAction(ACTION_TYPES.COMPLETE_SAVE);
export const fetchLinkedFiles = createAction(ACTION_TYPES.FETCH_LINKED_FILES);
export const unLinkFiles = createAction(ACTION_TYPES.UN_LINK_FILES);
export const saveReOpenFile = createAction(ACTION_TYPES.SAVE_RE_OPEN_FILE);
export const fetchFinanceExistsOrNot = createAction(ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT);
export const fetchChildFiles = createAction(ACTION_TYPES.FETCH_CHILD_FILES);
export const updateCustodianChangeDetails = createAction(ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE);

export const fetchUserDetails = createAction(ACTION_TYPES.FETCH_USER_DETAILS);
export const fetchBPRouteKey = createAction(ACTION_TYPES.FETCH_BP_ROUTE_KEY);
export const fetchUsersByFileNo = createAction(ACTION_TYPES.FETCH_USERS_BY_FILE_NO);
export const fetchCertificate = createAction(ACTION_TYPES.FETCH_CERTIFICATE);
export const deLinkInward = createAction(ACTION_TYPES.DE_LINK_INWARD);
export const saveBeneficiary = createAction(ACTION_TYPES.SAVE_BENEFICIARY);
export const fetchBeneficiary = createAction(ACTION_TYPES.FETCH_BENEFICIARY);
export const fetchBeneficiaryById = createAction(ACTION_TYPES.FETCH_BENEFICIARY_BY_ID);
export const deleteBeneficiary = createAction(ACTION_TYPES.DELETE_BENEFICARY);
export const fetchServicePostRoutes = createAction(ACTION_TYPES.FETCH_SERVICE_POST_ROUTES);
export const listCompletedNotes = createAction(ACTION_TYPES.LIST_COMPLETED_NOTES);
export const listCompletedNotesDocuments = createAction(ACTION_TYPES.LIST_COMPLETED_NOTES_DOCUMENTS);
export const deleteDraft = createAction(ACTION_TYPES.DELETE_DRAFT);
export const deleteNoteRef = createAction(ACTION_TYPES.DELETE_NOTE_REF);

export const handleMakeInActive = createAction(ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE);
export const handleAction = createAction(ACTION_TYPES.DRAFT_ACTIONS);

export const fetchEsign = createAction(ACTION_TYPES.FETCH_E_SIGN);
export const fetchEsignStatusCheck = createAction(ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK);
export const updateEsignFlag = createAction(ACTION_TYPES.UPDATE_E_SIGN_FLAG);
export const fetchForwardPlusRoleForDraftActions = createAction(ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS);
