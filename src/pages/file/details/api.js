import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchFileDetails = (data) => {
  return {
    url: API_URL.INBOX.FETCH_FILE_DETAILS.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILE_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_FILE_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_FILE_DETAILS_FAILURE
      ]
    },
    data
  };
};

export const fetchSummaryDetails = (data) => {
  const replace = `?inwardId=${data}`;
  return {
    url: API_URL.INBOX.FETCH_SUMMARY_DETAILS_BY_ID.replace('query', replace),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_SUMMARY_DETAILS_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchServicesById = (data) => {
  return {
    url: API_URL.INBOX.SERVICE_BY_SERVICE_CODE.replace(':code', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_SERVICES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_SERVICES_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchSubModulesById = (data) => {
  return {
    url: API_URL.COMMON.SUB_MODULES_BY_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_SUB_MODULES_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchModulesById = (data) => {
  return {
    url: API_URL.COMMON.MODULES_BY_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MODULES_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_MODULES_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_MODULES_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchPendingActions = (data) => {
  return {
    url: API_URL.INBOX.FETCH_PENDING_ACTIONS_BY_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_PENDING_ACTIONS_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchApplicantDetails = (data) => {
  const replace = `?fileNo=${data}`;
  return {
    url: API_URL.INBOX.FETCH_APPLICANT_DETAILS_BY_ID.replace('query', replace),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_APPLICANT_DETAILS_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const fetchPendingDraftDetails = (data) => {
  const replace = `?fileNo=${data}`;
  return {
    url: API_URL.INBOX.FETCH_DRAFT_BY_ID.replace('query', replace),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_PENDING_DRAFT_DETAILS_BY_ID_FAILURE
      ]
    },
    data
  };
};

export const saveDraft = (data) => {
  return {
    url: API_URL.INBOX.SAVE_DRAFT,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DRAFT_REQUEST,
        ACTION_TYPES.SAVE_DRAFT_SUCCESS,
        ACTION_TYPES.SAVE_DRAFT_FAILURE
      ],
      data
    }
  };
};

export const editDraft = (data) => {
  const id = data.get('id');

  return {
    url: API_URL.INBOX.EDIT_DRAFT.replace(':draftid', id),
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DRAFT_REQUEST,
        ACTION_TYPES.SAVE_DRAFT_SUCCESS,
        ACTION_TYPES.SAVE_DRAFT_FAILURE
      ],
      data
    }
  };
};

export const saveDraftReturn = (data) => {
  const id = data.get('id');
  return {
    url: API_URL.INBOX.SAVE_DRAFT_RETURN.replace(':draftId', id),
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_DRAFT_REQUEST,
        ACTION_TYPES.SAVE_DRAFT_SUCCESS,
        ACTION_TYPES.SAVE_DRAFT_FAILURE
      ],
      data
    }
  };
};

export const fetchSenderDetails = () => {
  return {
    url: API_URL.INBOX.SENDERS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SENDER_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_SENDER_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_SENDER_DETAILS_FAILURE
      ]
    }
  };
};

export const fetchWardMemberDetails = (data) => {
  return {
    url: API_URL.INBOX.WARDMEMBER.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_WARD_MEMBER_DETAILS_FAILURE
      ]
    }
  };
};

export const fetchReceiverDetails = () => {
  return {
    url: API_URL.INBOX.RECEIVER,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_RECEIVER_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_RECEIVER_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_RECEIVER_DETAILS_FAILURE
      ]
    }
  };
};

export const fetchApplicant = (data) => {
  const replace = `?fileNo=${data}`;
  return {
    url: API_URL.INBOX.FETCH_APPLICANT_BY_ID.replace('query', replace),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_APPLICANT_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_APPLICANT_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_APPLICANT_BY_ID_FAILURE
      ]
    }
  };
};

export const saveAddress = (data) => {
  return {
    url: API_URL.INBOX.SAVE_ADDRESS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_ADDRESS_REQUEST,
        ACTION_TYPES.SAVE_ADDRESS_SUCCESS,
        ACTION_TYPES.SAVE_ADDRESS_FAILURE
      ],
      data
    }
  };
};

export const saveEnclosureApi = (data) => {
  return {
    url: API_URL.INBOX.SAVE_ENCLOSURE,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_ENCLOSURE_REQUEST,
        ACTION_TYPES.SAVE_ENCLOSURE_SUCCESS,
        ACTION_TYPES.SAVE_ENCLOSURE_FAILURE
      ],
      data
    }
  };
};

export const saveUnHoldFileData = (data) => {
  return {
    url: API_URL.INBOX.SAVE_UN_HOLD_FILE,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.SAVE_UN_HOLD_FILE_REQUEST,
        ACTION_TYPES.SAVE_UN_HOLD_FILE_SUCCESS,
        ACTION_TYPES.SAVE_UN_HOLD_FILE_FAILURE
      ],
      data
    }
  };
};

export const saveNoteDocuments = (data) => {
  return {
    url: API_URL.INBOX.SAVE_NOTE_DOCUMENTS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_NOTE_DOCUMENTS_REQUEST,
        ACTION_TYPES.SAVE_NOTE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.SAVE_NOTE_DOCUMENTS_FAILURE
      ],
      data
    }
  };
};

export const fetchNotes = (url, params) => {
  return {
    url,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_NOTES_REQUEST,
        ACTION_TYPES.FETCH_NOTES_SUCCESS,
        ACTION_TYPES.FETCH_NOTES_FAILURE
      ],
      params
    }
  };
};

export const fetchAllNotes = (url, params) => {
  return {
    url,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ALL_NOTES_REQUEST,
        ACTION_TYPES.FETCH_ALL_NOTES_SUCCESS,
        ACTION_TYPES.FETCH_ALL_NOTES_FAILURE
      ],
      params
    }
  };
};

export const saveNote = (data) => {
  return {
    url: API_URL.INBOX.SAVE_NOTE,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_NOTE_REQUEST,
        ACTION_TYPES.SAVE_NOTE_SUCCESS,
        ACTION_TYPES.SAVE_NOTE_FAILURE
      ],
      data
    }
  };
};

export const saveEnquiry = (data) => {
  return {
    url: API_URL.INBOX.ENQUIRY.SAVE_ENQUIRY,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_ENQUIRY_REQUEST,
        ACTION_TYPES.SAVE_ENQUIRY_SUCCESS,
        ACTION_TYPES.SAVE_ENQUIRY_FAILURE
      ],
      data
    }
  };
};

export const saveEnquiryDoc = (data) => {
  return {
    url: API_URL.INBOX.ENQUIRY.SAVE_ENQUIRY_DOC,
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.SAVE_ENQUIRY_DOC_REQUEST,
        ACTION_TYPES.SAVE_ENQUIRY_DOC_SUCCESS,
        ACTION_TYPES.SAVE_ENQUIRY_DOC_FAILURE
      ],
      data
    }
  };
};

export const fetchEnquiryDetails = (data) => {
  return {
    url: API_URL.INBOX.ENQUIRY.FETCH_ENQUIRY_DETAILS.replace('?query', `?fileNo=${data}`),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ENQUIRY_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_ENQUIRY_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_ENQUIRY_DETAILS_FAILURE
      ]
    }
  };
};

export const updateEnquiryText = (payload) => {
  const { fileNo, data } = payload;
  return {
    url: API_URL.INBOX.ENQUIRY.UPDATE_ENQUIRY_TEXT.replace(':fileNo', fileNo),
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_ENQUIRY_TEXT_REQUEST,
        ACTION_TYPES.UPDATE_ENQUIRY_TEXT_SUCCESS,
        ACTION_TYPES.UPDATE_ENQUIRY_TEXT_FAILURE
      ],
      data
    }
  };
};
export const saveCopyTo = (data) => {
  return {
    url: API_URL.INBOX.SAVE_ADDRESS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_COPY_TO_REQUEST,
        ACTION_TYPES.SAVE_COPY_TO_SUCCESS,
        ACTION_TYPES.SAVE_COPY_TO_FAILURE
      ],
      data
    }
  };
};

export const fetchEnquiryDoc = (data) => {
  return {
    url: API_URL.INBOX.ENQUIRY.FETCH_ENQUIRY_DOC.replace('?query', `?fileNo=${data}`),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_ENQUIRY_DOC_REQUEST,
        ACTION_TYPES.FETCH_ENQUIRY_DOC_SUCCESS,
        ACTION_TYPES.FETCH_ENQUIRY_DOC_FAILURE
      ]
    }
  };
};

export const updateEnquiryDoc = (dataSend) => {
  const { data, fileNo } = dataSend;
  return {
    url: API_URL.INBOX.ENQUIRY.UPDATE_ENQUIRY_DOC.replace(':fileNo', fileNo),
    method: REQUEST_METHOD.MULTIPART,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_ENQUIRY_DOC_REQUEST,
        ACTION_TYPES.UPDATE_ENQUIRY_DOC_SUCCESS,
        ACTION_TYPES.UPDATE_ENQUIRY_DOC_FAILURE
      ],
      data

    }
  };
};

export const fetchYears = (data) => {
  return {
    url: API_URL.COMMON.FETCH_YEARS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_YEAR_REQUEST,
        ACTION_TYPES.FETCH_YEAR_SUCCESS,
        ACTION_TYPES.FETCH_YEAR_FAILURE
      ]
    },
    data
  };
};

export const fetchSameSeatMergeFiles = (data) => {
  return {
    url: API_URL.MERGE.FETCH_MERGE_SAME_SEAT_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES_REQUEST,
        ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES_SUCCESS,
        ACTION_TYPES.FETCH_MERGE_SAME_SEAT_FILES_FAILURE
      ]
    },
    data
  };
};

export const fetchMergedFiles = (params) => {
  return {
    url: API_URL.MERGE.MERGED_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MERGED_FILES_REQUEST,
        ACTION_TYPES.FETCH_MERGED_FILES_SUCCESS,
        ACTION_TYPES.FETCH_MERGED_FILES_FAILURE
      ],
      params
    }
  };
};

export const mergeFiles = (data) => {
  return {
    url: API_URL.INBOX.MERGE_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.MERGE_FILES_REQUEST,
        ACTION_TYPES.MERGE_FILES_SUCCESS,
        ACTION_TYPES.MERGE_FILES_FAILURE
      ],
      data
    }
  };
};

export const fetchMergedLinked = (data) => {
  return {
    url: API_URL.INBOX.FETCH_LINKED_MERGED.replace(':fileNo', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MERGE_LINK_REQUEST,
        ACTION_TYPES.FETCH_MERGE_LINK_SUCCESS,
        ACTION_TYPES.FETCH_MERGE_LINK_FAILURE
      ]
    }
  };
};

export const fetchDraft = (url, params) => {
  return {
    url,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DRAFT_REQUEST,
        ACTION_TYPES.FETCH_DRAFT_SUCCESS,
        ACTION_TYPES.FETCH_DRAFT_FAILURE
      ],
      params
    }
  };
};

export const fetchDraftById = (data) => {
  return {
    url: API_URL.INBOX.FETCH_DRAFT_BY_ID.replace('?query', `?draftUuid=${data}`),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DRAFT_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_DRAFT_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_DRAFT_BY_ID_FAILURE
      ]
    }
  };
};

export const linkFiles = (data) => {
  return {
    url: API_URL.INBOX.LINK_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.LINK_FILES_REQUEST,
        ACTION_TYPES.LINK_FILES_SUCCESS,
        ACTION_TYPES.LINK_FILES_FAILURE
      ],
      data
    }
  };
};

export const fetchFunctionalGroup = () => {
  return {
    url: API_URL.COMMON.FUNCTIONAL_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_REQUEST,
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_SUCCESS,
        ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_FAILURE
      ]
    }
  };
};

export const fetchFunctions = () => {
  return {
    url: API_URL.INBOX.FUNCTIONS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FUNCTIONS_REQUEST,
        ACTION_TYPES.FETCH_FUNCTIONS_SUCCESS,
        ACTION_TYPES.FETCH_FUNCTIONS_FAILURE
      ]
    }
  };
};

export const saveNoteWithoutDoc = (data) => {
  return {
    url: API_URL.INBOX.SAVE_NOTE_WITHOUT_DOC,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC_REQUEST,
        ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC_SUCCESS,
        ACTION_TYPES.SAVE_NOTE_WITHOUT_DOC_FAILURE
      ],
      data
    }
  };
};

export const fetchPullDraftDetails = (params) => {
  return {
    url: API_URL.PULL.FETCH_PULL_DRAFT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_PULL_DRAFT_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const fetchPullNotesDetails = (params) => {
  return {
    url: API_URL.SEARCH_FILES.FETCH_SEARCH_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PULL_NOTES_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_PULL_NOTES_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_PULL_NOTES_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const searchPullNotesDetails = (params) => {
  return {
    url: API_URL.INBOX.FETCH_NOTES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS_REQUEST,
        ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS_SUCCESS,
        ACTION_TYPES.SEARCH_PULL_NOTES_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const savePullNotes = (data) => {
  return {
    url: API_URL.PULL.SAVE_PULL_NOTE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_PULL_NOTES_REQUEST,
        ACTION_TYPES.SAVE_PULL_NOTES_SUCCESS,
        ACTION_TYPES.SAVE_PULL_NOTES_FAILURE
      ],
      data
    }
  };
};

export const searchPullDraftDetails = (params) => {
  return {
    url: API_URL.PULL.SEARCH_PULL_DRAFT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS_REQUEST,
        ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS_SUCCESS,
        ACTION_TYPES.SEARCH_PULL_DRAFT_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const fetchFileDocuments = (url) => {
  return {
    url,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILE_DOCUMENTS_REQUEST,
        ACTION_TYPES.FETCH_FILE_DOCUMENTS_SUCCESS,
        ACTION_TYPES.FETCH_FILE_DOCUMENTS_FAILURE
      ]
    }
  };
};

export const fetchDraftPreview = (params) => {
  return {
    url: API_URL.INBOX.FETCH_DRAFT_PREVIEW,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DRAFT_PREVIEW_REQUEST,
        ACTION_TYPES.FETCH_DRAFT_PREVIEW_SUCCESS,
        ACTION_TYPES.FETCH_DRAFT_PREVIEW_FAILURE
      ],
      params
    }
  };
};

export const unMergeFiles = (data) => {
  return {
    url: API_URL.MERGE.UN_MERGE_FILES,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UN_MERGE_FILES_REQUEST,
        ACTION_TYPES.UN_MERGE_FILES_SUCCESS,
        ACTION_TYPES.UN_MERGE_FILES_FAILURE
      ],
      data
    }
  };
};

export const fetchPartialNotesDetails = (params) => {
  return {
    url: API_URL.INBOX.FETCH_PARTIAL_NOTES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PARTIAL_NOTES_REQUEST,
        ACTION_TYPES.FETCH_PARTIAL_NOTES_SUCCESS,
        ACTION_TYPES.FETCH_PARTIAL_NOTES_FAILURE
      ],
      params
    }
  };
};

export const fetchDraftExistsOrNot = (data) => {
  return {
    url: API_URL.INBOX.DRAFT_EXISTS_OR_NOT.replace(':fileNo', data.fileNo),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT_REQUEST,
        ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT_SUCCESS,
        ACTION_TYPES.FETCH_DARFT_EXISTS_OR_NOT_FAILURE
      ]
    }
  };
};

export const fetchMappedSeats = (params) => {
  return {
    url: API_URL.INBOX.MAPPED_SEATS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MAPPED_SEATS_REQUEST,
        ACTION_TYPES.FETCH_MAPPED_SEATS_SUCCESS,
        ACTION_TYPES.FETCH_MAPPED_SEATS_FAILURE
      ],
      params
    }
  };
};

export const saveChildFile = (data) => {
  return {
    url: API_URL.INBOX.SAVE_CHILD,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_CHILD_FILE_REQUEST,
        ACTION_TYPES.SAVE_CHILD_FILE_SUCCESS,
        ACTION_TYPES.SAVE_CHILD_FILE_FAILURE
      ],
      data
    }
  };
};

export const generateDemand = (data) => {
  return {
    url: API_URL.COUNTER.GENERATE_DEMANT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.GENERATE_DEMAND_REQUEST,
        ACTION_TYPES.GENERATE_DEMAND_SUCCESS,
        ACTION_TYPES.GENERATE_DEMAND_FAILURE
      ],
      data
    }
  };
};
export const fetchApplication = (data) => {
  return {
    url: API_URL.COUNTER.FETCH_APPLICATION.replace('inwardIdParams', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_APPLICATION_SERVICE_REQUEST,
        ACTION_TYPES.FETCH_APPLICATION_SERVICE_SUCCESS,
        ACTION_TYPES.FETCH_APPLICATION_SERVICE_FAILURE
      ]
    },
    data
  };
};

export const fetchBeneficiaryDetails = (params) => {
  return {
    url: API_URL.COMMON.FETCH_BENEFICIARY,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BENEFICIARY_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_BENEFICIARY_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_BENEFICIARY_DETAILS_FAILURE
      ],
      params
    }
  };
};
export const saveComplete = (data) => {
  return {
    url: API_URL.COUNTER.SAVE_COMPLETE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.COMPLETE_SAVE_REQUEST,
        ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
        ACTION_TYPES.COMPLETE_SAVE_FAILURE
      ],
      data
    }
  };
};

export const unLinkFiles = (data) => {
  return {
    url: API_URL.MERGE.UN_LINK_FILES,
    method: REQUEST_METHOD.PUT,
    payload: {
      types: [
        ACTION_TYPES.UN_LINK_FILES_REQUEST,
        ACTION_TYPES.UN_LINK_FILES_SUCCESS,
        ACTION_TYPES.UN_LINK_FILES_FAILURE
      ],
      data
    }
  };
};

export const saveReOpenFile = (data) => {
  return {
    url: API_URL.FILE_RE_OPEN.RE_OPEN,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_RE_OPEN_FILE_REQUEST,
        ACTION_TYPES.SAVE_RE_OPEN_FILE_SUCCESS,
        ACTION_TYPES.SAVE_RE_OPEN_FILE_FAILURE
      ],
      data
    }
  };
};

export const fetchFinanceExistsOrNot = (data) => {
  return {
    url: API_URL.INBOX.FINANCE_EXISTS_OR_NOT,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT_REQUEST,
        ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT_SUCCESS,
        ACTION_TYPES.FETCH_FINANCE_EXISTS_OR_NOT_FAILURE
      ],
      data
    }
  };
};

export const fetchChildFiles = (data) => {
  return {
    url: API_URL.INBOX.FETCH_CHILD_FILE.replace(':fileNo', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_CHILD_FILES_REQUEST,
        ACTION_TYPES.FETCH_CHILD_FILES_SUCCESS,
        ACTION_TYPES.FETCH_CHILD_FILES_FAILURE
      ]
    }
  };
};

export const updateCustodianChange = (data) => {
  return {
    url: API_URL.INBOX.UPDATE_CUSTODIAN_CHANGE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE_REQUEST,
        ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE_SUCCESS,
        ACTION_TYPES.UPDATE_CUSTODIAN_CHANGE_FAILURE
      ],
      data
    }
  };
};

export const fetchUserDetails = (params, data) => {
  return {
    url: API_URL.COMMON.FETCH_COUNTER_OPERATOR_WITH_ROLE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_USER_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_USER_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_USER_DETAILS_FAILURE
      ],
      params,
      data
    }
  };
};

export const fetchBPRouteKey = (params) => {
  return {
    url: API_URL.COMMON.FETCH_BP_ROUTE_KEY,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BP_ROUTE_KEY_REQUEST,
        ACTION_TYPES.FETCH_BP_ROUTE_KEY_SUCCESS,
        ACTION_TYPES.FETCH_BP_ROUTE_KEY_FAILURE
      ],
      params
    }
  };
};

export const fetchUsersByFileno = (payload) => {
  const { params, data } = payload;
  return {
    url: API_URL.COMMON.FETCH_USERS_BY_FILE_NO,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_USERS_BY_FILE_NO_REQUEST,
        ACTION_TYPES.FETCH_USERS_BY_FILE_NO_SUCCESS,
        ACTION_TYPES.FETCH_USERS_BY_FILE_NO_FAILURE
      ],
      params,
      data
    }
  };
};

export const fetchCertificate = (data) => {
  return {
    url: API_URL.COMMON.FETCH_CERTIFICATE.replace(':fileNo', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_CERTIFICATE_REQUEST,
        ACTION_TYPES.FETCH_CERTIFICATE_SUCCESS,
        ACTION_TYPES.FETCH_CERTIFICATE_SUCCESS
      ]
    }
  };
};

export const deLinkInward = (data) => {
  return {
    url: API_URL.INBOX.DE_LINK_INWARD,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DE_LINK_INWARD_REQUEST,
        ACTION_TYPES.DE_LINK_INWARD_SUCCESS,
        ACTION_TYPES.DE_LINK_INWARD_FAILURE
      ],
      data
    }
  };
};

export const saveBeneficiary = (props) => {
  const { data } = props;
  return {
    url: API_URL.INBOX.SAVE_BENEFICIARY,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SAVE_BENEFICIARY_REQUEST,
        ACTION_TYPES.SAVE_BENEFICIARY_SUCCESS,
        ACTION_TYPES.SAVE_BENEFICIARY_FAILURE
      ],
      data
    }
  };
};

export const fetchBeneficiary = (params) => {
  return {
    url: API_URL.INBOX.FETCH_BENEFICIARY,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BENEFICIARY_REQUEST,
        ACTION_TYPES.FETCH_BENEFICIARY_SUCCESS,
        ACTION_TYPES.FETCH_BENEFICIARY_FAILURE
      ],
      params
    }
  };
};

export const fetchBeneficiaryById = (data) => {
  return {
    url: API_URL.INBOX.FETCH_BENEFICIARY_BY_ID.replace(':id', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_BENEFICIARY_BY_ID_REQUEST,
        ACTION_TYPES.FETCH_BENEFICIARY_BY_ID_SUCCESS,
        ACTION_TYPES.FETCH_BENEFICIARY_BY_ID_FAILURE
      ]
    }
  };
};

export const deleteBeneficiary = (payload) => {
  const { fileNo, data } = payload;
  return {
    url: API_URL.INBOX.DELETE_BENEFICIARY.replace(':fileNo', fileNo),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_BENEFICARY_REQUEST,
        ACTION_TYPES.DELETE_BENEFICARY_SUCCESS,
        ACTION_TYPES.DELETE_BENEFICARY_FAILURE
      ],
      data
    }
  };
};

export const fetchServicePostRoutes = (params) => {
  return {
    url: API_URL.INBOX.FETCH_SERVICE_POST_ROUTES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_POST_ROUTES_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_POST_ROUTES_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_POST_ROUTES_FAILURE
      ],
      params
    }
  };
};

export const listCompletedNotes = (params) => {
  return {
    url: API_URL.INBOX.LIST_COMPLETED_NOTES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.LIST_COMPLETED_NOTES_REQUEST,
        ACTION_TYPES.LIST_COMPLETED_NOTES_SUCCESS,
        ACTION_TYPES.LIST_COMPLETED_NOTES_FAILURE
      ],
      params
    }
  };
};

export const listCompletedNotesDocuments = (params) => {
  return {
    url: API_URL.INBOX.LIST_COMPLETED_NOTES_DOCUMENTS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.LIST_COMPLETED_NOTES_DOCUMENTS_REQUEST,
        ACTION_TYPES.LIST_COMPLETED_NOTES_DOCUMENTS_SUCCESS,
        ACTION_TYPES.LIST_COMPLETED_NOTES_DOCUMENTS_FAILURE
      ],
      params
    }
  };
};

export const deletDraft = (params) => {
  return {
    url: API_URL.INBOX.DELETE_DRAFT.replace(':fileNo', params?.fileNo).replace(':draftId', params?.draftId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_DRAFT_REQUEST,
        ACTION_TYPES.DELETE_DRAFT_SUCCESS,
        ACTION_TYPES.DELETE_DRAFT_FAILURE
      ],
      params
    }
  };
};

export const deleteNoteRef = (data) => {
  return {
    url: API_URL.INBOX.DELETE_NOTE_REF.replace(':notesId', data?.notesId).replace(':referenceId', data?.referenceId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DELETE_NOTE_REF_REQUEST,
        ACTION_TYPES.DELETE_NOTE_REF_SUCCESS,
        ACTION_TYPES.DELETE_NOTE_REF_FAILURE
      ]
    }
  };
};

export const draftActions = (data) => {
  return {
    url: API_URL.INBOX.DRAFT_ACTIONS.replace(':id', data?.draftId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DRAFT_ACTIONS_REQUEST,
        ACTION_TYPES.DRAFT_ACTIONS_SUCCESS,
        ACTION_TYPES.DRAFT_ACTIONS_FAILURE
      ],
      data: { fileNo: data?.fileNo, action: data?.action }
    }
  };
};

export const draftMakeInActive = (data) => {
  return {
    url: API_URL.INBOX.DRAFT_MAKE_INACTIVE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE_REQUEST,
        ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE_SUCCESS,
        ACTION_TYPES.DRAFT_MAKE_IN_ACTIVE_FAILURE
      ],
      data: { fileNo: data?.fileNo, draftNo: data?.draftNo, active: data?.active }
    }
  };
};

export const featchESignApi = (data) => {
  return {
    url: API_URL.INBOX.FETCH_E_SIGN,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_E_SIGN_REQUEST,
        ACTION_TYPES.FETCH_E_SIGN_SUCCESS,
        ACTION_TYPES.FETCH_E_SIGN_FAILURE
      ],
      data
    }
  };
};

export const fetchESignStatusCheck = (data) => {
  return {
    url: API_URL.INBOX.FETCH_E_SIGN_STATUS_CHECK,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK_REQUEST,
        ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK_SUCCESS,
        ACTION_TYPES.FETCH_E_SIGN_STATUS_CHECK_FAILURE
      ],
      data
    }
  };
};

export const updateEsignFlagSaga = (data) => {
  return {
    url: API_URL.E_SIGN.SAVE_E_SIGNED_DRAFT.replace(':fileNo', data?.fileNo).replace(':draftId', data?.draftId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.UPDATE_E_SIGN_FLAG_REQUEST,
        ACTION_TYPES.UPDATE_E_SIGN_FLAG_SUCCESS,
        ACTION_TYPES.UPDATE_E_SIGN_FLAG_FAILURE
      ]
    }
  };
};

export const fetchForwardPlusRoleForActions = (data) => {
  return {
    url: API_URL.INBOX.FETCH_FORWARD_PLUS_ROLE_FOR_DRAFT_ACTIONS.replace(':fileNo', data),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_REQUEST,
        ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_SUCCESS,
        ACTION_TYPES.FETCH_FORWARD_PLUS_ROLE_FOR_ACTIONS_FAILURE
      ]
    }
  };
};
