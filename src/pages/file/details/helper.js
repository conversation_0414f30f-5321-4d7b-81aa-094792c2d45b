import {
  DEFAULT_COUNTRY, DEFAULT_DISTRICT, DEFAULT_STATE, EMPLOYEE_ROLES
} from 'common/constants';
import { t } from 'i18next';
import { DOCUMENT_TYPE } from 'pages/counter/new/constants';
import { DRAFT_SAVE } from './constants';

export const checkRoleDraft = (data) => {
  const { draftNo, draftType, action } = data;

  switch (action) {
    case 'Create':
      return `<br/>${t('draftCreatedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    case 'Verify':
      return `<br/>${t('draftVerifiedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    case 'Approve':
      return `<br/>${t('draftApprovedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    case 'reject':
      return `<br/>${t('draftRejectedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    case 'return':
      return `<br/>${t('draftReturnedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    case 'Recommend':
      return `<br/>${t('draftRecommendedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    case 'approved':
      return `<br/>${t('draftApprovedWith')} ${t('draftNumber')}: ${draftNo} and ${t('draftTypeSmall')}: ${draftType}`;
    default:
      return null;
  }
};

export const checkRole = (roles) => {
  if (roles?.includes(EMPLOYEE_ROLES.VERIFIER)) {
    return t('verify');
  } if (roles?.includes(EMPLOYEE_ROLES.APPROVER)) {
    return t('approve');
  } if (roles?.includes(EMPLOYEE_ROLES.RECOMMEND)) {
    return t('recommend');
  }
  return t('create');
};

export const checkRoleByFileRole = (roles, routeChangeRole) => {
  if (roles?.includes(EMPLOYEE_ROLES.OPERATOR) || routeChangeRole === EMPLOYEE_ROLES.OPERATOR) {
    return t('create');
  } if (roles?.includes(EMPLOYEE_ROLES.VERIFIER) || routeChangeRole === EMPLOYEE_ROLES.VERIFIER) {
    return t('verify');
  } if (roles?.includes(EMPLOYEE_ROLES.APPROVER) || routeChangeRole === EMPLOYEE_ROLES.APPROVER) {
    return t('approve');
  } if (roles?.includes(EMPLOYEE_ROLES.RECOMMEND) || routeChangeRole === EMPLOYEE_ROLES.RECOMMEND) {
    return t('recommend');
  }
  return t('create');
};

export const checkRoleDraftSave = (roles) => {
  if (roles?.includes(EMPLOYEE_ROLES.VERIFIER)) {
    return DRAFT_SAVE.VERIFIED;
  } if (roles?.includes(EMPLOYEE_ROLES.APPROVER)) {
    return DRAFT_SAVE.APPROVED;
  }
  return DRAFT_SAVE.CREATED;
};

export const checkRoleDraftSaveConfirm = (roles) => {
  if (roles?.includes(EMPLOYEE_ROLES.VERIFIER)) {
    return t('areYouSureWanttoVerifyDraft');
  } if (roles?.includes(EMPLOYEE_ROLES.APPROVER)) {
    return t('areYouSureWanttoApproveDraft');
  }
  return t('areYouSureWanttoCreateDraft');
};

export const checkDraftSave = (status) => {
  switch (status) {
    case 'Create':
      return t('draftCreatedSuccessfully');
    case 'Verify':
      return t('draftVerifiedSuccessfully');
    case 'Approve':
      return `${t('draftApprovedSuccessfully')}!<br/> ${t('DoYouWantToDigitalySignTheDocument')}?<br/> <div style="font-size: 12px"> ${t('noteafterDigitalSignatureNoUpdatesCanBeDone')} </>`;
    case 'reject':
      return t('draftSuccessfullyRejected');
    case 'return':
      return t('draftSuccessfullyReturned');
    case 'Recommend':
      return t('draftRecommendedSuccessfully');
    default:
      return t('draftSavedSuccessfully');
  }
};

export const checkDraftSaveFailed = (status) => {
  switch (status) {
    case DRAFT_SAVE.CREATED:
      return t('draftCreateFailed');
    case DRAFT_SAVE.VERIFIED:
      return t('draftVerificationFailed');
    case DRAFT_SAVE.APPROVED:
      return t('draftApproveFailed');
    case DRAFT_SAVE.RETURNED:
      return t('draftReturnFailed');
    default:
      return t('draftSaveFailed');
  }
};

export const beneficiaryDefaultValues = {
  beneficiaryType: 'INDIVIDUAL',
  countryId: DEFAULT_COUNTRY.id,
  stateId: DEFAULT_STATE.id,
  districtId: DEFAULT_DISTRICT.id,
  countryCode: '',
  documentType: 1,
  documentNo: '',
  beneficiaryName: '',
  localBeneficiaryName: null,
  houseName: '',
  localHouseName: null,
  postoffice: null,
  pincode: null,
  street: null,
  localPlace: null,
  mainPlace: '',
  localStreet: null,
  localLocalPlace: null,
  localMainPlace: null,
  emailId: null,
  categoryId: null,
  mobileNo: null,
  isWhatsappSame: false,
  whatsappNo: null,
  internationalMobileNo: null,
  // institutions
  institutionName: null,
  localInstitutionName: null,
  localOfficeName: null,
  localDesignation: null,
  landLine: null,
  referenceNo: null,
  institutionDate: null,
  officerName: null,
  designation: null,
  accountId: null,
  gstNo: null,
  panNo: null
};

export const beneFiciarySaveFormat = (data) => {
  let docType;
  switch (Number(data.documentType)) {
    case 1:
      docType = DOCUMENT_TYPE.AADHAR;
      break;
    case 2:
      docType = DOCUMENT_TYPE.UUID;
      break;
    default:
      docType = DOCUMENT_TYPE.PASSPORT;
      break;
  }

  return ({
    ...data,
    [docType]: data.documentNo
  });
};

export const formatResponseDraftAddress = (input) => {
  const sender = input.senderId;
  const receiver = input.receiverId;
  const { fileNo } = input;

  const addresses = [];

  input?.draftAddress?.forEach((draft) => {
    const applicantAddresses = draft.applicantAddresses.map((applicant) => {
      if (draft?.dispatchAddressMode?.length > 0) {
        return draft.dispatchAddressMode.map((val) => {
          if (applicant?.addressNo === val?.addressNo) {
            return {
              name: applicant.name,
              address: applicant.address,
              modeOfDispatch: val?.modeOfDispatch,
              officeType: val?.officeType,
              functionalGroupId: val?.functionalGroupId,
              dispatchClerkPostId: val?.dispatchClerkPostId,
              dispatchClerkName: val?.dispatchClerkName,
              dispatchClerkPenNo: val?.dispatchClerkPenNo,
              addressNo: applicant?.addressNo,
              officeName: applicant?.officeName,
              officerName: applicant?.officerName,
              districtName: applicant?.districtName,
              localBody: applicant?.localBody,
              localBodyName: applicant?.localBodyName,
              wardId: applicant?.wardId,
              wardName: applicant?.wardName

            };
          }
          return null;
        });
      }
      return {
        name: applicant.name,
        address: applicant.address,
        addressNo: applicant?.addressNo,
        officeName: applicant?.officeName,
        officerName: applicant?.officerName,
        districtName: applicant?.districtName,
        localBody: applicant?.localBody,
        localBodyName: applicant?.localBodyName,
        wardId: applicant?.wardId,
        wardName: applicant?.wardName
      };
    });

    const fatteredArray = applicantAddresses?.flat();
    const filteredArray = fatteredArray?.filter((item) => item !== null && item !== undefined);

    const addressObj = {
      applicantAddresses: filteredArray?.length > 0 ? filteredArray : [],
      addressType: draft.addressType,
      fileNo
    };

    addresses.push(addressObj);
  });

  const transformedObject = {
    sender,
    receiver,
    addresses
  };

  return transformedObject;
};

export const formatAddressDetailsBasedOnReceiver = (formatedSubmitAddress) => {
  const uniqueAddressTypes = new Map();

  formatedSubmitAddress?.flat()?.forEach((item) => {
    if (uniqueAddressTypes?.has(item.addressType)) {
      const existingItem = uniqueAddressTypes.get(item.addressType);

      const newDispatchAddressMode = item.dispatchAddressMode?.map((mode, index) => ({
        ...mode,
        addressNo: existingItem.dispatchAddressMode.length + index + 1
      }));

      const newApplicantAddresses = item.applicantAddresses?.map((address, index) => ({
        ...address,
        addressNo: existingItem.applicantAddresses.length + index + 1
      }));

      existingItem.dispatchAddressMode = existingItem.dispatchAddressMode.concat(newDispatchAddressMode);
      existingItem.applicantAddresses = existingItem.applicantAddresses.concat(newApplicantAddresses);
    } else {
      const newItem = {
        ...item,
        dispatchAddressMode: item.dispatchAddressMode?.map((mode, index) => ({
          ...mode,
          addressNo: index + 1
        })),
        applicantAddresses: item.applicantAddresses?.map((address, index) => ({
          ...address,
          addressNo: index + 1
        }))
      };
      uniqueAddressTypes.set(item.addressType, newItem);
    }
  });

  const result = Array.from(uniqueAddressTypes.values());

  return result;
};

export const getDraftStage = (stage) => {
  switch (stage) {
    case 1:
      return 'CREATED';
    case 2:
      return 'VERIFIED';
    case 3:
      return 'APPROVED';
    case 4:
      return 'PENDING';
    case 5:
      return 'RETURNED';
    case 6:
      return 'REJECT';
    case 7:
      return 'RECOMMENDED';
    case 8:
      return 'DELETED';
    default:
      return 'CREATED';
  }
};

export const getDocument = async (url, tokenVal, body) => {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${tokenVal}`,
        Accept: '*/*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });
    const arrayBuffer = await response.arrayBuffer();
    const blob = new Blob([new Uint8Array(arrayBuffer)], { type: 'application/pdf' });
    const blobUrl = window.URL.createObjectURL(blob);
    return { data: blobUrl, status: 'success' };
  } catch (error) {
    return { data: error, status: 'failed' };
  }
};

export const formatCopyToAddress = (inputArray, officeType, functionalGroupId) => {
  // const dispatchAddressMode = filteredAddress.flatMap((item) => item?.applicantAddresses
  //   ?.filter((values) => inputArray.some((input) => input.addressNo === values.addressNo))
  //   .map((values) => ({
  //     addressNo: values.addressNo,
  //     modeOfDispatch: values.modeOfDispatch,
  //     officeType: values.officeType,
  //     functionalGroupId: values.functionalGroupId,
  //     dispatchClerkName: values.dispatchClerkName,
  //     dispatchClerkPostId: values.dispatchClerkPostId,
  //     dispatchClerkPenNo: values.dispatchClerkPenNo
  //   })) || []);

  const dispatchAddressMode = inputArray?.map((values) => {
    return {
      addressNo: values.addressNo,
      modeOfDispatch: values.modeOfDispatch || null,
      officeType: officeType || null,
      functionalGroupId: functionalGroupId || null,
      dispatchClerkName: values.dispatchClerkName || null,
      dispatchClerkPostId: values.user || null,
      dispatchClerkPenNo: values.dispatchClerkPenNo
    };
  });

  const draftCopyToAddressRequests = inputArray.map((item, index) => ({
    addressNo: index + 1,
    name: item.name,
    address: item.address
  }));
  return [
    {
      dispatchAddressMode,
      draftCopyToAddressRequests
    }
  ];
};

export const resetDraftCopyToAddress = (copyToAddress) => {
  const arr = copyToAddress?.draftCopyToList?.map((item) => {
    const correspondedDispatch = copyToAddress?.dispatchAddressMode?.find((d) => d?.addressNo === item?.addressNo);

    return {
      ...item,
      modeOfDispatch: correspondedDispatch?.modeOfDispatch,
      officeType: correspondedDispatch?.officeType,
      functionalGroupId: correspondedDispatch?.functionalGroupId,
      user: correspondedDispatch?.dispatchClerkPostId,
      dispatchClerkName: correspondedDispatch?.dispatchClerkName,
      dispatchClerkPenNo: correspondedDispatch?.dispatchClerkPenNo,
      isDispatchFieldOpen: Boolean(correspondedDispatch?.modeOfDispatch)
    };
  });

  return arr;
};
