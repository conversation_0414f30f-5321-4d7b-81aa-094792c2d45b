import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getInboxNew = (state) => state[STATE_REDUCER_KEY];

const summaryDetails = (state) => state?.summaryDetails;
export const getSummaryDetails = flow(getInboxNew, summaryDetails);

const service = (state) => state?.service;
export const getService = flow(getInboxNew, service);

const subModule = (state) => state?.subModule;
export const getSubmodule = flow(getInboxNew, subModule);

const module = (state) => state?.module;
export const getModule = flow(getInboxNew, module);

const bpModuleCitizenAddress = (state) => state?.bpModuleCitizenAddress;
export const getBpModuleCitizenAddress = flow(getInboxNew, bpModuleCitizenAddress);

const statusAndNotes = (state) => state?.statusAndNotes;
export const getStatusAndNotes = flow(getInboxNew, statusAndNotes);

const applicantDetails = (state) => state?.applicantDetails;
export const getApplicantDetails = flow(getInboxNew, applicantDetails);

const pendingActions = (state) => state?.pendingActions;
export const getPendingActions = flow(getInboxNew, pendingActions);

const pendingDraftDetails = (state) => state?.pendingDraftDetails;
export const getPendingDraftDetails = flow(getInboxNew, pendingDraftDetails);

const sendersDetails = (state) => state?.sendersDetails;
export const getSendersDetails = flow(getInboxNew, sendersDetails);

const receiverDetails = (state) => state?.receiverDetails;
export const getReceiversDetails = flow(getInboxNew, receiverDetails);

const applicantAddress = (state) => state?.applicantAddress;
export const getApplicantAddress = flow(getInboxNew, applicantAddress);

const wardDetails = (state) => state?.wardDetails;
export const getWardDetails = flow(getInboxNew, wardDetails);

const notes = (state) => state?.notes;
export const getNotes = flow(getInboxNew, notes);

const draftId = (state) => state?.draftId;
export const getDraftId = flow(getInboxNew, draftId);

const savedNote = (state) => state?.savedNote;
export const getSavedNote = flow(getInboxNew, savedNote);

const savedNoteId = (state) => state?.noteId;
export const getSavedNoteId = flow(getInboxNew, savedNoteId);

const fileDetails = (state) => state?.fileDetails;
export const getFileDetails = flow(getInboxNew, fileDetails);

const enquiryDetails = (state) => state?.enquiryDetails;
export const getEnquiryDetails = flow(getInboxNew, enquiryDetails);

const enquiryDocuments = (state) => state?.enquiryDocuments;
export const getEnquiryDocuments = flow(getInboxNew, enquiryDocuments);

const years = (state) => state?.years;
export const getYears = flow(getInboxNew, years);

const sameSeatMergeFiles = (state) => state?.sameSeatMergeFiles;
export const getSameSeatMergeFiles = flow(getInboxNew, sameSeatMergeFiles);

const mergedFiles = (state) => state?.mergedFiles;
export const getMergedFiles = flow(getInboxNew, mergedFiles);

const mergeLinkFiles = (state) => state?.mergeLinkFiles;
export const getMergeLinkFiles = flow(getInboxNew, mergeLinkFiles);

const mergeLinkActive = (state) => state?.mergeLinkActive;
export const getMergeLinkActive = flow(getInboxNew, mergeLinkActive);

const mergeLinkActiveId = (state) => state?.mergeLinkActiveId;
export const getMergeLinkActiveId = flow(getInboxNew, mergeLinkActiveId);

const draftList = (state) => state?.draftList;
export const getDraftList = flow(getInboxNew, draftList);

const activeDraft = (state) => state?.activeDraft;
export const getActiveDraft = flow(getInboxNew, activeDraft);

const draftDataById = (state) => state?.draftDataById;
export const getDraftDataById = flow(getInboxNew, draftDataById);

const holdFileId = (state) => state?.activeHoldFileId;
export const getHoldFileId = flow(getInboxNew, holdFileId);

const functionalGroup = (state) => state?.functionalGroup;
export const getFunctionalGroup = flow(getInboxNew, functionalGroup);

const functions = (state) => state?.functions;
export const getFunctions = flow(getInboxNew, functions);

const pullSearchParams = (state) => state?.pullSearchParams;
export const getPullSearchParams = flow(getInboxNew, pullSearchParams);

const pullDraftDetails = (state) => state?.pullDraftDetails;
export const getPullDraftDetails = flow(getInboxNew, pullDraftDetails);

const pullNotesDetails = (state) => state?.pullNotesDetails;
export const getPullNotesDetails = flow(getInboxNew, pullNotesDetails);

const searchedPullNotesDetails = (state) => state?.searchedPullNotesDetails;
export const getSearchedPullNotesDetails = flow(getInboxNew, searchedPullNotesDetails);

const pullSearchNotesParams = (state) => state?.setPullSearchNotesParams;
export const getPullSearchNotesParams = flow(getInboxNew, pullSearchNotesParams);

const pullSearchDraftParams = (state) => state?.setPullSearchDraftParams;
export const getPullSearchDraftParams = flow(getInboxNew, pullSearchDraftParams);

const searchedPullDraftDetails = (state) => state?.searchedPullDraftDetails;
export const getSearchedPullDraftDetails = flow(getInboxNew, searchedPullDraftDetails);

const fileDocuments = (state) => state?.fileDocuments;
export const getFileDocuments = flow(getInboxNew, fileDocuments);

const selectedCorrespondence = (state) => state?.selectedCorrespondence;
export const getSelectedCorrespondence = flow(getInboxNew, selectedCorrespondence);

const draftPreview = (state) => state?.draftPreview;
export const getDraftPreview = flow(getInboxNew, draftPreview);

const activeNote = (state) => state?.activeNote;
export const getActiveNote = flow(getInboxNew, activeNote);

const partialNotes = (state) => state?.partialNotes;
export const getPartialNotes = flow(getInboxNew, partialNotes);

const draftExistsOrNot = (state) => state?.draftExistsOrNot;
export const getDraftExistsOrNot = flow(getInboxNew, draftExistsOrNot);

const mappedSeats = (state) => state?.mappedSeats;
export const getMappedSeats = flow(getInboxNew, mappedSeats);

const pulledNote = (state) => state?.pulledNote;
export const getPulledNote = flow(getInboxNew, pulledNote);

const mergeBackButton = (state) => state?.mergeBackButton;
export const getMergeBackButton = flow(getInboxNew, mergeBackButton);

const beneficiaryList = (state) => state?.beneficiary;
export const getBeneficiary = flow(getInboxNew, beneficiaryList);

const draftPreviewFlag = (state) => state?.draftPreviewFlag;
export const getDraftPreviewFlag = flow(getInboxNew, draftPreviewFlag);

const mergeActionConfirmation = (state) => state?.mergeActionConfirmation;
export const getMergeActionConfirmation = flow(getInboxNew, mergeActionConfirmation);

const linkActionConfirmation = (state) => state?.linkActionConfirmation;
export const getLinkActionConfirmation = flow(getInboxNew, linkActionConfirmation);

const reOpenStatus = (state) => state?.reOpenStatus;
export const getReopenStatus = flow(getInboxNew, reOpenStatus);

const financeExistsOrNot = (state) => state?.financeExistsOrNot;
export const getFinanceExistsOrNot = flow(getInboxNew, financeExistsOrNot);

const childFiles = (state) => state?.childFiles;
export const getChildFiles = flow(getInboxNew, childFiles);

const activeBenAccordian = (state) => state?.activeBenAccordian;
export const getActiveBenAccordian = flow(getInboxNew, activeBenAccordian);

const custodianUserDetails = (state) => state?.custodianUserDetails;
export const getCustodianUserDetails = flow(getInboxNew, custodianUserDetails);

const bpRouteKeys = (state) => state?.bpRouteKeys;
export const getBpRouteKeys = flow(getInboxNew, bpRouteKeys);

const fileUsers = (state) => state?.fileUsers;
export const getFileUsers = flow(getInboxNew, fileUsers);

const certificate = (state) => state?.certificate;
export const getCertificate = flow(getInboxNew, certificate);

const beneficiary = (state) => state?.beneficiary;
export const getBeneficiaryList = flow(getInboxNew, beneficiary);

const beneficiaryById = (state) => state?.beneficiaryById;
export const getBeneficiaryById = flow(getInboxNew, beneficiaryById);

const noteExpand = (state) => state?.noteExpand;
export const getNoteExpand = flow(getInboxNew, noteExpand);

const dragEnabled = (state) => state?.dragEnabled;
export const getDragEnabled = flow(getInboxNew, dragEnabled);

const servicePostRoutes = (state) => state?.servicePostRoutes;
export const getServicePostRoutes = flow(getInboxNew, servicePostRoutes);

const unMergeFilesParams = (state) => state?.unMergeFilesParams;
export const getUnMergeFilesParams = flow(getInboxNew, unMergeFilesParams);

const completedNotes = (state) => state?.completedNotes;
export const getCompletedNotes = flow(getInboxNew, completedNotes);

const completedNotesDocuments = (state) => state?.completedNotesDocuments;
export const getCompletedNotesDocuments = flow(getInboxNew, completedNotesDocuments);

const noteRefTrigger = (state) => state?.noteRefTrigger;
export const getNoteRefTrigger = flow(getInboxNew, noteRefTrigger);

const draftFilterActiveIndex = (state) => state?.draftFilterActiveIndex;
export const getDraftFilterActiveIndex = flow(getInboxNew, draftFilterActiveIndex);

const expandAllApprovedDraft = (state) => state?.expandAllApprovedDraft;
export const getExpandAllApprovedDraft = flow(getInboxNew, expandAllApprovedDraft);

const allNotes = (state) => state?.allNotes;
export const getAllNotes = flow(getInboxNew, allNotes);

const draftNumber = (state) => state?.draftNumber;
export const getDraftNumber = flow(getInboxNew, draftNumber);

const showingAllDocs = (state) => state?.showingAllDocs;
export const getShowingAllDocs = flow(getInboxNew, showingAllDocs);

const actionOccured = (state) => state?.actionOccured;
export const getActionOccured = flow(getInboxNew, actionOccured);

const approveAndDsData = (state) => state?.approveAndDsData;
export const getApproveAndDsData = flow(getInboxNew, approveAndDsData);

const isDigitalSignAction = (state) => state?.isDigitalSignAction;
export const getIsDigitalSignAction = flow(getInboxNew, isDigitalSignAction);

const draftNewPreviewUpdateFlag = (state) => state?.draftNewPreviewUpdateFlag;
export const getDraftNewPreviewUpdateFlag = flow(getInboxNew, draftNewPreviewUpdateFlag);

const eSignCreate = (state) => state?.eSignCreate;
export const getESignCreate = flow(getInboxNew, eSignCreate);

const isOnSelectNote = (state) => state?.isOnSelectNote;
export const getIsOnSelectNote = flow(getInboxNew, isOnSelectNote);

const forwardPlusRoleForActions = (state) => state?.forwardPlusRoleForActions;
export const getForwardPlusRoleForActions = flow(getInboxNew, forwardPlusRoleForActions);
