import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { MERGE, STATE_REDUCER_KEY } from './constants';
import { ACTION_TYPES } from './actions';

const initialState = {
  summaryDetails: {},
  mergeLinkActive: false,
  mergeLinkActiveId: '',
  draftList: [],
  activeDraft: [],
  draftDataById: {},
  setPullSearchNotesParams: {
    page: 0,
    size: 3,
    fileNo: '',
    sortDirection: 'desc',
    search: false,
    noteStatus: 'COMPLETED'
  },
  setPullSearchDraftParams: {
    page: 0,
    size: 10,
    postId: '',
    sortDirection: 'desc',
    officeId: '',
    fileNo: ''
  },
  activeNote: {},
  selectedCorrespondence: {},
  mergeBackButton: false,
  draftPreviewFlag: false,
  mergeActionConfirmation: false,
  activeBenAccordian: 0,
  noteExpand: false,
  dragEnabled: {
    id: null,
    status: false
  },
  unMergeFilesParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    fileType: MERGE.MERGING,
    search: false
  },
  noteRefTrigger: null,
  draftFilterActiveIndex: 3,
  expandAllApprovedDraft: false,
  showingAllDocs: false,
  actionOccured: false,
  isDigitalSignAction: false,
  draftNewPreviewUpdateFlag: false
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setSummaryDetails: (state, { payload }) => {
      _.set(state, 'summaryDetails', payload);
    },
    setService: (state, { payload }) => {
      _.set(state, 'service', payload);
    },
    setSubModule: (state, { payload }) => {
      _.set(state, 'subModule', payload);
    },
    setModule: (state, { payload }) => {
      _.set(state, 'module', payload);
    },
    setPendingActions: (state, { payload }) => {
      _.set(state, 'pendingActions', payload[0].data);
    },
    setStatusAndNotes: (state, { payload }) => {
      _.set(state, 'statusAndNotes', payload);
    },
    setApplicantDetails: (state, { payload }) => {
      _.set(state, 'applicantDetails', payload);
    },
    setPendingDraftDetails: (state, { payload }) => {
      _.set(state, 'pendingDraftDetails', payload);
    },
    setSendersDetails: (state, { payload }) => {
      _.set(state, 'sendersDetails', payload);
    },
    setReceiverDetails: (state, { payload }) => {
      _.set(state, 'receiverDetails', payload);
    },
    setApplicant: (state, { payload }) => {
      _.set(state, 'applicantAddress', payload);
    },
    setWardDetails: (state, { payload }) => {
      _.set(state, 'wardDetails', payload);
    },
    setNotes: (state, { payload }) => {
      _.set(state, 'notes', payload);
    },
    setDraftId: (state, { payload }) => {
      _.set(state, 'draftId', payload);
    },
    setSavedNote: (state, { payload }) => {
      _.set(state, 'savedNote', payload);
    },
    setNoteId: (state, { payload }) => {
      _.set(state, 'noteId', payload);
    },
    setFileDetails: (state, { payload }) => {
      _.set(state, 'fileDetails', payload);
    },
    setYears: (state, { payload }) => {
      _.set(state, 'years', payload);
    },
    setSameSeatMergeFiles: (state, { payload }) => {
      _.set(state, 'sameSeatMergeFiles', payload);
    },
    setMergedFiles: (state, { payload }) => {
      _.set(state, 'mergedFiles', payload);
    },
    setMergeLinkActive: (state, { payload }) => {
      _.set(state, 'mergeLinkActive', payload);
    },
    setMergeLinkActiveId: (state, { payload }) => {
      _.set(state, 'mergeLinkActiveId', payload);
    },
    setActiveDraft: (state, { payload }) => {
      _.set(state, 'activeDraft', payload);
    },
    setPullSearchParams: (state, { payload }) => {
      _.set(state, 'pullSearchParams', payload);
    },
    setPullDraftDetails: (state, { payload }) => {
      _.set(state, 'pullDraftDetails', payload);
    },
    setPullNotesDetails: (state, { payload }) => {
      _.set(state, 'pullNotesDetails', payload);
    },
    setSearchPullNotesDetails: (state, { payload }) => {
      _.set(state, 'searchedPullNotesDetails', payload);
    },
    setPullSearchNotesParams: (state, { payload }) => {
      _.set(state, 'setPullSearchNotesParams', payload);
    },
    setPullSearchDraftParams: (state, { payload }) => {
      _.set(state, 'setPullSearchDraftParams', payload);
    },
    setSearchPullDraftDetails: (state, { payload }) => {
      _.set(state, 'searchedPullDraftDetails', payload);
    },
    setSelectedCorrespondence: (state, { payload }) => {
      _.set(state, 'selectedCorrespondence', payload);
    },
    setActiveNote: (state, { payload }) => {
      _.set(state, 'activeNote', payload);
    },
    setPartialNotes: (state, { payload }) => {
      _.set(state, 'partialNotes', payload);
    },
    setDraftExistsOrNotFlag: (state, { payload }) => {
      _.set(state, 'draftExistsOrNot', payload);
    },
    setMappedSeats: (state, { payload }) => {
      _.set(state, 'mappedSeats', payload);
    },
    setPulledNote: (state, { payload }) => {
      _.set(state, 'pulledNote', payload);
    },
    setDraftList: (state, { payload }) => {
      _.set(state, 'draftList', payload);
    },
    setDraftDataById: (state, { payload }) => {
      _.set(state, 'draftDataById', payload);
    },
    setMergedBackButton: (state, { payload }) => {
      _.set(state, 'mergeBackButton', payload);
    },
    setBeneficiaryData: (state, { payload }) => {
      _.set(state, 'beneficiary', payload);
    },
    setDraftPreviewFlag: (state, { payload }) => {
      _.set(state, 'draftPreviewFlag', payload);
    },
    setMergeActionConfirmation: (state, { payload }) => {
      _.set(state, 'mergeActionConfirmation', payload);
    },
    setLinkActionConfirmation: (state, { payload }) => {
      _.set(state, 'linkActionConfirmation', payload);
    },
    setFinanceExistsOrNotFlag: (state, { payload }) => {
      _.set(state, 'financeExistsOrNot', payload);
    },
    setActiveBenAccordian: (state, { payload }) => {
      _.set(state, 'activeBenAccordian', payload);
    },
    setUserDetails: (state, { payload }) => {
      _.set(state, 'custodianUserDetails', payload);
    },
    setNoteExpand: (state, { payload }) => {
      _.set(state, 'noteExpand', payload);
    },
    setDragEnabled: (state, { payload }) => {
      _.set(state, 'dragEnabled', payload);
    },
    setUnMergeFilesParams: (state, { payload }) => {
      _.set(state, 'unMergeFilesParams', payload);
    },
    setNoteRefTrigger: (state, { payload }) => {
      _.set(state, 'noteRefTrigger', payload);
    },
    setDraftFilterActiveIndex: (state, { payload }) => {
      _.set(state, 'draftFilterActiveIndex', payload);
    },

    setExpandAllApprovedDraft: (state, { payload }) => {
      _.set(state, 'expandAllApprovedDraft', payload);
    },
    setDraftDetails: (state, { payload }) => {
      _.set(state, 'draftList', payload);
    },
    setDraftNumber: (state, { payload }) => {
      _.set(state, 'draftNumber', payload);
      _.set(state, 'actionOccured', false);
    },
    setShowingAllDocs: (state, { payload }) => {
      _.set(state, 'showingAllDocs', payload);
    },
    setActionOccured: (state, { payload }) => {
      _.set(state, 'actionOccured', payload);
    },
    setApproveAndDsData: (state, { payload }) => {
      _.set(state, 'approveAndDsData', payload);
    },
    setIsDigitalSignAction: (state, { payload }) => {
      _.set(state, 'isDigitalSignAction', payload);
    },
    setDraftNewPreviewUpdateFlag: (state, { payload }) => {
      _.set(state, 'draftNewPreviewUpdateFlag', payload);
    },
    setIsOnSelectNote: (state, { payload }) => {
      _.set(state, 'isOnSelectNote', payload);
    },
    setForwardPlusRoleForActions: (state, { payload }) => {
      _.set(state, 'forwardPlusRoleForActions', payload);
    }

  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.FETCH_FILE_DETAILS_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileDetails', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_ENQUIRY_DETAILS_SUCCESS, (state, { payload }) => {
        _.set(state, 'enquiryDetails', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_ENQUIRY_DOC_SUCCESS, (state, { payload }) => {
        _.set(state, 'enquiryDocuments', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_MERGE_LINK_SUCCESS, (state, { payload }) => {
        _.set(state, 'mergeLinkFiles', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_DRAFT_SUCCESS, (state, { payload }) => {
        _.set(state, 'draftList', payload.data?.length > 0 ? payload.data : []);
      })
      .addCase(ACTION_TYPES.FETCH_DRAFT_BY_ID_SUCCESS, (state, { payload }) => {
        _.set(state, 'draftDataById', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_FUNCTIONAL_GROUP_SUCCESS, (state, { payload }) => {
        _.set(state, 'functionalGroup', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_FUNCTIONS_SUCCESS, (state, { payload }) => {
        _.set(state, 'functions', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_FILE_DOCUMENTS_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileDocuments', payload.data);
      })
      .addCase(ACTION_TYPES.FETCH_DRAFT_PREVIEW_SUCCESS, (state, { payload }) => {
        _.set(state, 'draftPreview', payload.data);
      })
      .addCase(ACTION_TYPES.SAVE_RE_OPEN_FILE_SUCCESS, (state, { payload }) => {
        _.set(state, 'reOpenStatus', payload);
      })
      .addCase(ACTION_TYPES.FETCH_CHILD_FILES_SUCCESS, (state, { payload }) => {
        _.set(state, 'childFiles', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_BP_ROUTE_KEY_SUCCESS, (state, { payload }) => {
        _.set(state, 'bpRouteKeys', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_USERS_BY_FILE_NO_SUCCESS, (state, { payload }) => {
        _.set(state, 'fileUsers', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_CERTIFICATE_SUCCESS, (state, { payload }) => {
        _.set(state, 'certificate', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_BENEFICIARY_SUCCESS, (state, { payload }) => {
        _.set(state, 'beneficiary', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_BENEFICIARY_BY_ID_SUCCESS, (state, { payload }) => {
        _.set(state, 'beneficiaryById', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_SERVICE_POST_ROUTES_SUCCESS, (state, { payload }) => {
        _.set(state, 'servicePostRoutes', payload?.data);
      })
      .addCase(ACTION_TYPES.LIST_COMPLETED_NOTES_SUCCESS, (state, { payload }) => {
        _.set(state, 'completedNotes', payload?.data);
      })
      .addCase(ACTION_TYPES.LIST_COMPLETED_NOTES_DOCUMENTS_SUCCESS, (state, { payload }) => {
        _.set(state, 'completedNotesDocuments', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_ALL_NOTES_SUCCESS, (state, { payload }) => {
        _.set(state, 'allNotes', payload?.data);
      })
      .addCase(ACTION_TYPES.FETCH_E_SIGN_SUCCESS, (state, { payload }) => {
        _.set(state, 'eSignCreate', payload);
      });
  }
});

export const { actions, reducer } = slice;
