import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { convertToLocalDate } from 'utils/date';
import {
  t
} from 'common/components';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DATE_FORMAT, FILE_ROLE } from 'pages/common/constants';
import { getActionTriggered, getServicesDropdown, getUserInfo } from 'pages/common/selectors';
import { createStructuredSelector } from 'reselect';
import * as commonActions from 'pages/common/actions';
import _ from 'lodash';
import RoutingKeys from 'pages/common/components/GeneralDetails/RoutingKeys';
import { numberNull } from 'utils/numberNull';
import * as actions from '../actions';
import {
  getCustodianUserDetails, getFileDetails, getMappedSeats, getServicePostRoutes
} from '../selector';
import { actions as sliceActions } from '../slice';
import { CHILD_FILE } from '../constants';

const ChildFile = ({
  setFormTitle,
  setFileHeader,
  mappedSeats,
  saveChildFile,
  setActionTriggered,
  fetchServicesOptions,
  fileDetails,
  fetchUserDetails,
  fetchFileDetails,
  custodianUserDetails,
  setAlertAction
}) => {
  const [post, setPost] = useState([]);
  const [routeKeyObj, setRouteKeyObj] = useState({});
  const [serviceCodeDetails, setServiceCodeDetails] = useState('');

  const params = useParams();

  useEffect(() => {
    if (custodianUserDetails) {
      setPost(
        custodianUserDetails?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName} - ${ps.penNo}`
        }))
      );
    } else {
      setPost([]);
    }
  }, [custodianUserDetails]);

  useEffect(() => {
    fetchFileDetails(params?.fileNo);
  }, [params?.fileNo]);

  useEffect(() => {
    setFormTitle({ title: t('childFile'), variant: 'normal' });
    setFileHeader([{
      label: t('fileNumber'),
      value: params.fileNo
    }, {
      label: t('date'),
      value: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL)
    }
    ]);
  }, []);

  useEffect(() => {
    fetchServicesOptions();
  }, []);

  const onSaveChildFile = (data) => {
    const savaData = {
      fileNo: params?.fileNo,
      moduleCode: data?.serviceObj?.moduleId,
      subModuleCode: data?.serviceObj?.subModuleId,
      serviceCode: data?.serviceObj?.code,
      postId: data?.user,
      notes: data?.remarks
    };
    setActionTriggered({ loading: true, id: 'childFile' });
    setAlertAction({
      open: false
    });
    saveChildFile(savaData);
  };

  useEffect(() => {
    if (mappedSeats?.length > 0) {
      setPost(mappedSeats?.map((ps) => ({ ...ps, name: `${ps.postNameInEng} - ${ps.employeeName} - ${ps.penNo}` })));
    }
  }, [mappedSeats]);

  const generalRouting = (data) => ([
    { routeKey2: 2, routeKeyValue: numberNull(Number(data?.occupancy)) },
    { routeKey2: 3, routeKeyValue: numberNull(Number(data?.buildingUsage)) },
    { routeKey2: 5, routeKeyValue: numberNull(Number(data?.buildingArea)) },
    { routeKey2: 6, routeKeyValue: numberNull(Number(data?.doorNo) || null) },
    { routeKey2: 9, routeKeyValue: numberNull(Number(data?.fund)) },
    { routeKey2: 10, routeKeyValue: numberNull(Number(data?.designation)) },
    { routeKey2: 11, routeKeyValue: numberNull(Number(data?.deductionHead)) },
    { routeKey2: 13, routeKeyValue: numberNull(Number(data?.officeType)) },
    { routeKey2: 15, routeKeyValue: numberNull(Number(data?.functionalGroup)) },
    { routeKey2: 18, routeKeyValue: numberNull(Number(data?.localBodyPropertyType)) },
    { routeKey2: 19, routeKeyValue: numberNull(Number(data?.meetingType)) },
    { routeKey2: 20, routeKeyValue: numberNull(Number(data?.functions)) },
    { routeKey2: 21, routeKeyValue: numberNull(Number(data?.billType)) },
    { routeKey2: 22, routeKeyValue: numberNull(Number(data?.establishmentType)) },
    { routeKey2: 23, routeKeyValue: numberNull(Number(data?.mission)) },
    { routeKey2: 24, routeKeyValue: numberNull(Number(data?.professionalTaxType)) },
    { routeKey2: 25, routeKeyValue: numberNull(Number(data?.typeOfAudit)) },
    { routeKey2: 26, routeKeyValue: numberNull(Number(data?.amountFromClaim)) },
    { routeKey2: 27, routeKeyValue: numberNull(Number(data?.estimateAmount)) },
    { routeKey2: 29, routeKeyValue: numberNull(Number(data?.wasteManagementId)) }
  ]);

  function wardKeySet(ward) {
    if (ward) {
      return 1;
    }
    return null;
  }

  function wardSet(ward, genward) {
    if (ward) {
      return ward;
    } if (numberNull(Number(genward))) {
      return 1;
    }
    return null;
  }

  const handleFetchUser = () => {
    if (routeKeyObj) {
      const routeKeysArray = _.filter(_.omitBy(generalRouting(routeKeyObj), _.isNil), (el) => !_.isNull(el?.routeKeyValue));
      setActionTriggered({ loading: true, id: 'fetchUserDetails' });
      fetchUserDetails({
        role: FILE_ROLE.OPERATOR,
        officeId: fileDetails?.officeId,
        serviceCode: serviceCodeDetails,
        routeKey1: wardKeySet(routeKeyObj?.ward),
        routeKe1Value: wardSet(routeKeyObj?.ward, null),
        routeKeysArray
      });
    }
  };

  const getConfirmation = (data) => {
    setAlertAction({
      open: true,
      variant: 'warning',
      message: t('areYouSureWantToCreateChildFile'),
      title: t('warning'),
      backwardActionText: t('cancel'),
      forwardActionText: t('confirm'),
      forwardAction: () => onSaveChildFile(data)
    });
  };

  return (
    <div className="bg-white p-5 rounded-lg">

      <RoutingKeys
        from={CHILD_FILE}
        activeIndex
        post={post}
        officeCode={fileDetails?.officeId}
        handleFetchUser={handleFetchUser}
        setRouteKeyObj={setRouteKeyObj}
        routingSave={getConfirmation}
        setServiceCodeDetails={setServiceCodeDetails}
        serviceCodeDetails={serviceCodeDetails}
      />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  mappedSeats: getMappedSeats,
  actionTriggered: getActionTriggered,
  serviceDropdown: getServicesDropdown,
  servicePostRoutes: getServicePostRoutes,
  fileDetails: getFileDetails,
  custodianUserDetails: getCustodianUserDetails
});

const mapDispatchToProps = (dispatch) => ({
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchMappedSeats: (data) => dispatch(actions.fetchMappedSeats(data)),
  saveChildFile: (data) => dispatch(actions.saveChildFile(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchServicesOptions: () => dispatch(commonActions.fetchServicesDetails()),
  setMappedSeats: (data) => dispatch(sliceActions.setMappedSeats(data)),
  fetchServicePostRoutes: (data) => dispatch(actions.fetchServicePostRoutes(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  fetchUserDetails: (data) => dispatch(actions.fetchUserDetails(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(ChildFile);
