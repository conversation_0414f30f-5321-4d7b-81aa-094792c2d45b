#draggable-btn{
    cursor: move;
    z-index: 10;
}
.floating-note-box{
    padding: 0 20px 15px 20px;
    border-radius: 10px;
    background: #fff;
    border: 1px solid #eee;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 65px;
    left: 15px;
}
.floating-note-box-head{
    font-size: 13px;
    font-weight: 500;
}
.floating-note-actions{
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: space-between;
    margin-top: 10px;
    gap: 10px
}
.floating-note-actions button{
    width: 100%;
    text-align: left;
    border-top: 1px solid #eee;
    border-radius: 0;
}
.floating-note-actions button:first-child{
    border-top: 0px solid #fff;
}
.input-rich-container.rich-editor{
    z-index: 0;
}

.input-rich-container.rich-editor .tip-tap-toolbar .tool-section input[type="color"] {
    width: 18px !important;
    height: 18px !important;
    border-radius: 3px;
}
.notes-edit .input-rich-container.rich-editor fieldset:is(:hover, :focus, .active){
    border: 1px solid rgb(232, 236, 238) !important;
    outline: none !important;
}
.notes-edit.have-partial .input-rich-container.rich-editor fieldset{
    border-radius: 0 0 8px 8px !important;
}

.input-rich-container.rich-editor .tip-tap-toolbar .tool-section{
    align-items: center !important;
}
/*
 #notes-listing-scroll ::-webkit-scrollbar {
    width: 3px;
    height: 5px;
  }


  #notes-listing-scroll:hover ::-webkit-scrollbar {
    width: 8px;
    height: 18px;
  }
  #notes-listing-scroll:hover ::-webkit-scrollbar-track{
    background: #e7eff5
  }
  #notes-listing-scroll .scroll-dots{
    display: none;
  }
  #notes-listing-scroll:hover .scroll-dots{
    display: block;
  } */

  #notes-listing-scroll #notes-cover-scroll::-webkit-scrollbar {
    width: 7px;
  }
  #notes-listing-scroll #notes-cover-scroll::-webkit-scrollbar-thumb {
    /* background: linear-gradient(to right, rgba(0,0,0,0) 50%, #00b2ec 50%); */
    background: #00b2ec;
    border: 2px solid #fff;
    border-radius: 2rem;
  }
  #notes-listing-scroll #notes-cover-scroll::-webkit-scrollbar-track{
    background: rgba(0, 0, 0, 0);
  }
  #notes-listing-scroll:hover #notes-cover-scroll::-webkit-scrollbar-track{
    background: #e7eff5
  }
  #notes-listing-scroll:hover #notes-cover-scroll::-webkit-scrollbar-thumb {
    background: #00b2ec;
    border: 0px solid #e7eff5;
    border-radius: 10rem;
  }
  #notes-listing-scroll .scroll-dots{
    display: none;
  }
  #notes-listing-scroll:hover .scroll-dots{
    display: block;
  }

  #notes-listing-scroll #notes-cover-scroll::-webkit-scrollbar-track-piece:start {
    background: transparent;
  }

  #notes-listing-scroll #notes-cover-scroll::-webkit-scrollbar-track-piece:end {
    background: transparent;
  }