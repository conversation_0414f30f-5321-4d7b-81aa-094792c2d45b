import {
  Menu, MenuButton, MenuItem, MenuList
} from '@ksmartikm/ui-components';
import DropdownIcons from 'assets/DropdownIcons';
import React from 'react';

const SubFilesDropdown = ({
  type = '', options = [], icon = '', handleActions = () => { },
  disabled = false
}) => {
  return (
    !disabled && (
    <Menu>
      <MenuButton className="p-2 border border-[#E8ECEE] rounded-md focus:outline-none focus:ring-[#E8ECEE] bg-transparent w-[120px]" onClick={(e) => disabled && e.preventDefault()}>
        <div className="flex gap-3">
          <div className="flex-none">{icon}</div>
          <div className="text-[#09327B] text-[14px] font-normal flex-grow">{type}</div>
          <div className="flex-none pt-[9px]"><DropdownIcons /></div>
        </div>
      </MenuButton>
      <MenuList>
        {options?.map((option) => (
          <MenuItem as="a" onClick={() => handleActions(option, type)} className="p-2" key={option?.id}>
            {option?.fileNo}
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
    )
  );
};

export default SubFilesDropdown;
