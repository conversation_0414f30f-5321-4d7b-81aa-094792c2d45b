import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  t, CustomTab, Button
} from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import DocumentPreview from 'common/components/DocumentPreview';
import WorkFlow from 'pages/workFlow/components';
import { FILE_STATUS_FOR_API_PARAMS, NOTE_STATUS } from 'pages/common/constants';
import {
  getIsOneDocumentSelect, getNoteCardDetails, getPostIdByPenNoDetails, getSummaryCollapseMenuFlag, getUserInfo
} from 'pages/common/selectors';
import { BASE_PATH } from 'common/constants';
import DocumentNewExpandComponents from 'common/components/DocumentPreview/DocumentNewExpandComponents';
import UnHoldFile from 'pages/workFlow/components/UnHoldFile';
import { actions as sliceActions } from '../../slice';
import ListNotes from './ListNotes';
import * as actions from '../../actions';
import {
  getAllNotes,
  getDraftExistsOrNot,
  getFileDetails, getMergeLinkActive, getMergeLinkActiveId,
  getPartialNotes,
  getShowingAllDocs
} from '../../selector';
import Draft from './draft';
import { applicantName } from '../helper';
import ShowAllDocuments from './ShowAllDocuments';
import ReOpenReasonModal from '../summaryDetails/ReOpenReasonModal';

const Notes = (props) => {
  const {
    setFormTitle,
    setFileHeader,
    fetchFileDetails,
    fileDetails,
    fetchPartialNotes,
    userInfo,
    setDraftDataById,
    mergeLinkActive,
    mergeLinkActiveId,
    noteCardDetails,
    fetchDraftExistsOrNot,
    draftExistsOrNot,
    setDraftFilterActiveIndex,
    isOneDocumentSelect,
    setIsOneDocumentSelect,
    setNoteCardDetails,
    allNotes,
    showingAllDocs,
    postIdByPenNoDetails,
    saveUnHoldFile,
    setShowingAllDocs,
    setIsOnSelectNote,
    setDocumentId,
    partialNotes
  } = props;
  const params = useParams();
  const [activeIndex, setActiveIndex] = useState(0);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [openNewExpand, setOpenNewExpand] = useState(false);
  // const [flowAction, setFlowAction] = useState(false);
  const [full, setFull] = useState(false);
  const [reOpenModal, setReOpenModal] = useState(false);
  const [openUnHoldFile, setOpenUnHoldFile] = useState(false);
  const [activeHoldFileId, setActiveHoldFileId] = useState('');

  const paramsValues = new URLSearchParams(window.location.search);

  const handleReopenFile = () => {
    setReOpenModal(!reOpenModal);
  };

  const handleCloseUnHoldFile = () => {
    setOpenUnHoldFile(false);
  };

  const handleUnHoldFile = () => {
    setOpenUnHoldFile(true);
  };

  useEffect(() => {
    if (fileDetails?.fileHoldId) {
      setActiveHoldFileId(fileDetails?.fileHoldId);
    }
  }, [fileDetails]);

  useEffect(() => {
    if (params) {
      if (params?.fileNo) {
        setDraftDataById();
        const notesSearchRequest = {
          fileNo: mergeLinkActive ? mergeLinkActiveId : params?.fileNo,
          assigner: userInfo?.assigner,
          noteStatus: NOTE_STATUS.PARTIAL
        };
        fetchFileDetails(mergeLinkActive ? mergeLinkActiveId : params?.fileNo);
        if (userInfo?.assigner) {
          fetchPartialNotes(notesSearchRequest);
        }
      }
    }
  }, [params, userInfo, mergeLinkActiveId, mergeLinkActive]);

  // useEffect(() => {
  //   if (searchParams.get('flowaction')) {
  //     if (searchParams.get('flowaction') === 'routechange') {
  //       setFlowAction(true);
  //     } else {
  //       setFlowAction(false);
  //     }
  //   } else {
  //     setFlowAction(false);
  //   }
  // }, [searchParams.get('flowaction')]);

  useEffect(() => {
    setFormTitle({ title: t('noteFile'), variant: 'normal' });
    if (fileDetails?.fileNo === params?.fileNo) {
      setFileHeader([{
        label: t('fileNumber'),
        value: params.fileNo
      }, {
        label: t('role'),
        value: fileDetails?.role
      }, {
        label: t('service'),
        value: fileDetails?.serviceName
      },
      {
        label: t('applicantName'),
        value: fileDetails?.inwardDetails ? applicantName(fileDetails?.inwardDetails) : ''
      }
      ]);
    }
  }, [JSON.stringify(fileDetails)]);

  useEffect(() => {
    if (params?.fileNo) {
      fetchDraftExistsOrNot({ fileNo: params?.fileNo });
      setNoteCardDetails([]);
      setIsOneDocumentSelect('');
    }
  }, [params?.fileNo]);

  useEffect(() => {
    if (searchParams.get('ref') !== 'yes') {
      if (draftExistsOrNot) {
        setDraftFilterActiveIndex(1);
        const processedParams = typeof paramsValues === 'string'
          ? paramsValues
          : new URLSearchParams(paramsValues).toString();

        const filteredParams = processedParams
          .split('&')
          .filter((param) => !param.startsWith('show='))
          .join('&');
        const showValue = draftExistsOrNot ? '1' : '0';
        const queryString = filteredParams ? `&${filteredParams}` : '';
        const targetUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=${showValue}${queryString}`;

        navigate(targetUrl);
      } else if (showingAllDocs === false) {
        const findDetails = allNotes?.noteDetails?.find((item) => item.noteNo === Number(allNotes?.lastNoteNo));
        if (findDetails?.notesDocsDetails?.length > 0) {
          setNoteCardDetails(findDetails?.notesDocsDetails || []);
          setIsOnSelectNote(findDetails?.notesId);
          setShowingAllDocs(false);
          const docId = findDetails?.notesDocsDetails[0].content?.notesDocumentId || findDetails?.notesDocsDetails[0].content?.fileId;
          const fromType = findDetails?.notesDocsDetails[0].content?.notesDocumentId ? 'note' : 'inward';
          setIsOneDocumentSelect({ docId, noteNo: allNotes?.lastNoteNo });
          setDocumentId({ docId, from: fromType });
        } else if (partialNotes?.notesDocsDetails?.length > 0) {
          setNoteCardDetails(partialNotes?.notesDocsDetails || []);
          setShowingAllDocs(false);
          const docId = partialNotes?.notesDocsDetails[0].content?.notesDocumentId || partialNotes?.notesDocsDetails[0].content?.fileId;
          const fromType = partialNotes?.notesDocsDetails[0].content?.notesDocumentId ? 'note' : 'inward';
          setDocumentId({ docId, from: fromType });
        }
      }
    }
  }, [draftExistsOrNot, allNotes?.noteDetails]);

  const getDocumentHead = () => {
    if (isOneDocumentSelect?.docId) {
      return `Document from para ${isOneDocumentSelect?.noteNo}`;
    } if (allNotes?.totalNotes === 0 && noteCardDetails?.length > 0) {
      return t('documents');
    } if (allNotes?.totalNotes === 0 && noteCardDetails?.length === 0) {
      return t('documents');
    } if (allNotes?.totalNotes > 0 && noteCardDetails?.length > 0 && !showingAllDocs) {
      return t('allDocument');
    } if (allNotes?.totalNotes > 0 && noteCardDetails?.length === 0) {
      return t('documents');
    } if (allNotes?.totalNotes > 0 && noteCardDetails?.length > 0 && showingAllDocs) {
      return t('allDocument');
    } return null;
  };

  const getDocumentContents = () => {
    if (isOneDocumentSelect?.docId) {
      return (
        <DocumentPreview
          isOneDocumentSelect={isOneDocumentSelect}
          setIsOneDocumentSelect={setIsOneDocumentSelect}
          setNoteCardDetails={setNoteCardDetails}
          expandEnable
          setOpenNewExpand={setOpenNewExpand}
          openNewExpand={openNewExpand}
          setFull={setFull}
          full={full}
        />
      );
    } if (allNotes?.totalNotes === 0 && noteCardDetails?.length > 0) {
      return <DocumentPreview />;
    } if (allNotes?.totalNotes === 0 && noteCardDetails?.length === 0) {
      return <DocumentPreview />;
    } if (allNotes?.totalNotes > 0 && noteCardDetails?.length > 0 && !showingAllDocs) {
      return (
        <ShowAllDocuments
          mergeLinkActive={mergeLinkActive}
          mergeLinkActiveId={mergeLinkActiveId}
          fileNo={params?.fileNo}
        />
      );
    } if (allNotes?.totalNotes > 0 && noteCardDetails?.length === 0) {
      return (
        <ShowAllDocuments
          mergeLinkActive={mergeLinkActive}
          mergeLinkActiveId={mergeLinkActiveId}
          fileNo={params?.fileNo}
        />
      );
    } if (allNotes?.totalNotes > 0 && noteCardDetails?.length > 0 && showingAllDocs) {
      return (
        <DocumentPreview
          expandEnable
          setOpenNewExpand={setOpenNewExpand}
          openNewExpand={openNewExpand}
          setFull={setFull}
          full={full}
        />
      );
    } return null;
  };

  const tabs = [
    { title: getDocumentHead(), content: getDocumentContents() },
    { title: t('draft'), content: <Draft /> }
  ];

  useEffect(() => {
    if (searchParams.get('show')) {
      setActiveIndex(Number(searchParams.get('show')));
    } else {
      setActiveIndex(0);
    }
  }, [searchParams.get('show')]);

  const handleTabsChange = (data) => {
    const filteredParams = new URLSearchParams(paramsValues);
    filteredParams.delete('show');

    let url = `?show=${data?.index}`;
    if (searchParams.get('flowaction')) {
      url += `&flowaction=${searchParams.get('flowaction')}`;
    }
    if (filteredParams.toString()) {
      url += `&${filteredParams.toString()}`;
    }
    navigate(url);
    setActiveIndex(data?.index);
  };

  // useEffect(() => {
  //   if (!searchParams.get('ref')) {
  //     setTimeout(() => {
  //       scrollToTop('note-bottom-scroll');
  //     }, 2000);
  //   }
  // }, []);

  return (
    <div className="mb-10">
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white pl-3 py-3 rounded-lg">
          <ListNotes fileDetails={fileDetails} />
        </div>
        <div className="bg-white px-4 py-4 rounded-lg file-note-docs relative">
          <div id="note-draft-top-scroll" className="" />
          <CustomTab data={tabs} handleChange={handleTabsChange} currentIndex={activeIndex} />
        </div>
      </div>
      {/* {flowAction
        && ( */}
      {/* <div className="bg-white px-10 py-10 rounded-lg mb-10 mt-5">
        <WorkFlow
          status={fileDetails?.status}
          code={fileDetails?.serviceCode}
          activeHoldFileId={fileDetails?.fileHoldId}
          fileDetails={fileDetails}
        />
      </div> */}
      {fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.CLOSED && fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.HOLD && postIdByPenNoDetails?.includes(fileDetails?.postId)
        ? (
          <div className="bg-white rounded-lg mb-10 mt-4">
            <WorkFlow
              status={fileDetails?.status}
              code={fileDetails?.serviceCode}
              activeHoldFileId={fileDetails?.fileHoldId}
              fileDetails={fileDetails}
              from="note-file"
            />

          </div>
        ) : (
          <div className="col-span-12 text-right p-4">

            {/* <BackButton /> */}

            {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.CLOSED && (mergeLinkActiveId === '' || mergeLinkActiveId === null)
              && (
                <Button
                  variant="secondary_outline"
                  className="mx-2"
                  onClick={handleReopenFile}
                >
                  {t('reOpen')}
                </Button>
              )}

            {(fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD || fileDetails?.stage === FILE_STATUS_FOR_API_PARAMS.PARKED) && postIdByPenNoDetails?.includes(fileDetails?.postId)
              && (
                <Button
                  variant="secondary_outline"
                  className="mx-2"
                  onClick={handleUnHoldFile}
                >
                  {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage !== FILE_STATUS_FOR_API_PARAMS.PARKED && t('moveToCustodian')}
                  {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage === FILE_STATUS_FOR_API_PARAMS.PARKED && t('revoke')}
                </Button>
              )}

            <UnHoldFile
              openUnHoldFile={openUnHoldFile}
              handleCloseUnHoldFile={handleCloseUnHoldFile}
              params={params}
              saveUnHoldFile={saveUnHoldFile}
              activeHoldFileId={activeHoldFileId}
              fileDetails={fileDetails}
            />
          </div>
        )}
      <ReOpenReasonModal open={reOpenModal} setReOpenModal={setReOpenModal} />
      {/* )} */}
      {/* <div id="note-bottom-scroll" /> */}
      {openNewExpand && full && (
        <DocumentNewExpandComponents
          noteCardDetails={noteCardDetails}
          full={full}
          openNewExpand={openNewExpand}
          setOpenNewExpand={setOpenNewExpand}
          setFull={setFull}
        />
      )}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  userInfo: getUserInfo,
  mergeLinkActive: getMergeLinkActive,
  mergeLinkActiveId: getMergeLinkActiveId,
  noteCardDetails: getNoteCardDetails,
  draftExistsOrNot: getDraftExistsOrNot,
  isOneDocumentSelect: getIsOneDocumentSelect,
  summaryCollapseMenuFlag: getSummaryCollapseMenuFlag,
  allNotes: getAllNotes,
  showingAllDocs: getShowingAllDocs,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  partialNotes: getPartialNotes
});

const mapDispatchToProps = (dispatch) => ({
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data)),
  setDraftDataById: (data) => dispatch(sliceActions.setDraftDataById(data)),
  fetchDraftExistsOrNot: (data) => dispatch(actions.fetchDraftExistsOrNot(data)),
  setDraftFilterActiveIndex: (data) => dispatch(sliceActions.setDraftFilterActiveIndex(data)),
  setIsOneDocumentSelect: (data) => dispatch(commonSliceActions.setIsOneDocumentSelect(data)),
  setNoteCardDetails: (data) => dispatch(commonSliceActions.setNoteCardDetails(data)),
  saveUnHoldFile: (data) => dispatch(actions.saveUnHoldFile(data)),
  setShowingAllDocs: (data) => dispatch(sliceActions.setShowingAllDocs(data)),
  setIsOnSelectNote: (data) => dispatch(sliceActions.setIsOnSelectNote(data)),
  setDocumentId: (data) => dispatch(commonSliceActions.setDocumentId(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Notes);
