import { yupResolver } from '@hookform/resolvers/yup';
import {
  Button,
  IconButton,
  Modal, ModalBody, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader, ModalOverlay
} from '@ksmartikm/ui-components';
import { FormController, t } from 'common/components';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { dark, light, secondary } from 'utils/color';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import AttachmentIcon from 'assets/Attachment';
import CloseSolidIcon from 'assets/CloseSolid';
import * as actions from 'pages/file/details/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered, getAllDocumentsType } from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import PreviewLocal from 'common/components/DocumentPreview/PreviewLocal';
import { noteDocsSchema } from '../../validate';

const AddDocument = (props) => {
  const {
    showAttachment,
    setShowAttachment,
    fecthAllDocumentTypes,
    documentTypeDropdown,
    fileNo = null,
    saveNote,
    noteId = null,
    setActionTriggered,
    actionTriggered,
    notes = null
  } = props;

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch
  } = useForm({
    defaultValues: {
      title: null,
      type: null,
      documents: null
    },
    resolver: yupResolver(noteDocsSchema),
    mode: 'onChange'
  });

  const [documents, setDocuments] = useState([]);
  // const [validateDoc, setValidateDoc] = useState(false);
  const [open, setOpen] = useState(false);
  const [previewData, setPreviewData] = useState(null);

  useEffect(() => {
    fecthAllDocumentTypes();
    reset({
      title: null,
      type: null,
      documents: null
    });
    setDocuments([]);
  }, [showAttachment]);

  const handleSelectDocument = (data) => {
    if (!data) {
      return;
    }
    if (data) {
      setDocuments([...documents, data]);
    }
    // setValue('documents', null);
  };

  const handleRemove = (index) => {
    setDocuments(documents.splice(0, index));
    setValue('documents', '');
    setValue('type', null);
    setValue('title', null);
  };

  const onSubmitForm = (data) => {
    if (documents?.length === 0) {
      // setValidateDoc(true);
    } else {
      const note = {
        request: {
          fileNo,
          notesId: noteId,
          getDocumentType: data.type,
          title: data.title,
          notes
        },
        docs: [],
        setShowAttachment,
        setDocuments
      };
      documents.map((item) => {
        note.docs.push(item);
        return true;
      });
      saveNote(note);
      setActionTriggered({ loading: true, id: 'save-note' });
    }
    // setTimeout(() => {
    //   setValidateDoc(false);
    // }, 3000);
  };

  const handleChange = (data) => {
    if (data) {
      if (data?.name.includes('Other Documents')) {
        setValue('title', '');
      } else {
        setValue('title', data?.name);
      }
    } else {
      setValue('title', '');
    }
  };

  const handlePreview = (data) => {
    setPreviewData({ file: data });
    setOpen(true);
  };

  return (
    <div>
      <Modal
        isOpen={showAttachment}
        size="xl"
        onClose={() => { setPreviewData(null); setShowAttachment(false); }}
        className="custom-form-modal"
        closeOnOverlayClick={false}
        closeOnEsc={false}
        scrollBehavior="outside"
        blockScrollOnMount={false}
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader
            className="text-center rounded-tl-lg rounded-tr-lg mb-5"
            style={{ background: light, color: dark }}
          >
            <h4 size="md">{t('addAttachments')}</h4>
          </ModalHeader>

          <ModalBody>
            <div className="px-5 pb-5">
              <form
                onSubmit={handleSubmit(onSubmitForm)}
                id="add-doc"
              >
                <div className="col-span-12 mt-5 mb-5">
                  <FormController
                    name="type"
                    type="select"
                    label={t('type')}
                    control={control}
                    options={documentTypeDropdown || []}
                    handleChange={(data) => handleChange(data)}
                    optionKey="id"
                    required
                    errors={errors}
                  />

                </div>
                <div className="col-span-12 mt-5 mb-5">
                  <div>

                    <div className="mb-5">
                      <FormController
                        name="title"
                        type="text"
                        label={t('title')}
                        control={control}
                        required
                        errors={errors}
                        disabled={watch('type') !== 43}
                      />
                    </div>
                  </div>
                  <div className="col-span-12 mt-5 mb-5">

                    <FormController
                      name="documents"
                      type="file"
                      accept="image,pdf,excel,doc"
                      label="upload"
                      placeholder={t('dropOrChooseFilesToUpload')}
                      control={control}
                      errors={errors}
                      handleChange={(data) => handleSelectDocument(data)}
                      optionKey="image"
                    />
                  </div>
                </div>

                <div className="flex gap-3 flex-wrap mt-5">
                  {/* {validateDoc
                    && <Alert style={{ borderRadius: '10px' }} status="warning"><AlertIcon />{t('pleaseAttachDocuments')}</Alert>} */}
                  {documents?.map((item, index) => (
                    <div className="rounded-lg flex items-center font-semibold relative" style={{ color: dark, background: light }}>
                      <Button display="flex" gap={3} variant="unstyled" p={3} onClick={() => handlePreview(item)}> <AttachmentIcon /> {item?.name} </Button>
                      <div className="absolute right-[-30px] top-[-20px]">
                        <IconButton variant="unstyled" onClick={() => handleRemove(index)} icon={<CloseSolidIcon color={secondary} />} />
                      </div>
                    </div>
                  ))}
                </div>
              </form>
            </div>
          </ModalBody>
          <ModalFooter
            className="text-center rounded-tl-lg rounded-tr-lg gap-3"
            style={{ background: light, color: dark }}
          >
            <Button variant="secondary_outline" onClick={() => setShowAttachment(false)} isDisabled={actionTriggered?.id === 'save-note' && actionTriggered?.loading}>{t('cancel')}</Button>
            <Button variant="secondary" type="submit" form="add-doc" isLoading={actionTriggered?.id === 'save-note' && actionTriggered?.loading}>{t('add')}</Button>
          </ModalFooter>
        </ModalContent>

      </Modal>
      <PreviewLocal
        open={open}
        close={() => setOpen(false)}
        previewItem={previewData}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  documentTypeDropdown: getAllDocumentsType,
  actionTriggered: getActionTriggered
});
const mapDispatchToProps = (dispatch) => ({
  saveNote: (data) => dispatch(actions.saveNote(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fecthAllDocumentTypes: (data) => dispatch(commonActions.fecthAllDocumentTypes(data))

});
export default connect(mapStateToProps, mapDispatchToProps)(AddDocument);
