import {
  Button, IconButton, Modal, ModalBody, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader, ModalOverlay, Spinner, Tooltip
} from '@ksmartikm/ui-components';
import CloseNew from 'assets/CloseNew';
import CloseOutlineIcon from 'assets/CloseOutline';
import NoNotesIcon from 'assets/NoNotesIcon';
import { t } from 'common/components';
import DocumentPreview from 'common/components/DocumentPreview';
import DocumentNewExpandComponents from 'common/components/DocumentPreview/DocumentNewExpandComponents';
import NoteCard from 'common/components/NotesCard/NoteCard';
import DocumentView from 'pages/common/components/DocumentView';
import DraftView from 'pages/common/components/DraftView';
import NoteReferences from 'pages/common/components/NoteReferences';
import React, { useState } from 'react';
import { dark, light, primary } from 'utils/color';

const NoteExpansionModel = ({
  openModal, handleClose = () => { }, showParaEdit,
  goToRef, togglePara, paraEdit, search, setSearch = () => { }, setShowDrop = () => { },
  showDrop, dropRef, completedNotes, triggerParaSearch, allNotes, loadMoreNotes,
  noteMore, actionTriggred, notesData, isOnSelectNote, setIsOnSelectNote,
  setDraftItems, setOpen, handleRefNavigate, from, noteCardDetails
}) => {
  const [isOpenDocumentModal, setIsOpenDocumentModal] = useState(false);
  const [openNewExpand, setOpenNewExpand] = useState(false);
  const [full, setFull] = useState(false);

  return (
    <>
      <Modal isOpen={openModal} size="full" onClose={handleClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>

            <div className="flex items-center justify-end w-full pt-2">
              <div className="flex-none pr-[20px]">
                {!showParaEdit
                  && (
                    <Button
                      variant="unstyled"
                      color={primary}
                      ref={goToRef}
                      onClick={togglePara}
                    >Go to
                    </Button>
                  )}

                <div className={`${showParaEdit ? 'block' : 'hidden'} relative w-[100px]`}>
                  <div className="flex border rounded-lg">
                    <input
                      className="w-[100px] rounded-lg px-3"
                      ref={paraEdit}
                      type="text"
                      value={search}
                      onChange={(event) => {
                        setSearch(event.target.value);
                        setShowDrop(true);
                      }}
                      onClick={() => setShowDrop(true)}
                    />
                  </div>
                  {showDrop
                    && (
                      <div className="shadow-lg rounded-lg py-5 mb-5 absolute w-[calc(100%-3px)] max-h-[150px] z-20 t-0 r-0 l-0 block bg-white" ref={dropRef}>
                        <ul className="max-h-[100px] w-full overflow-y-scroll pl-5 pr-1">
                          {completedNotes?.length > 0 && completedNotes?.filter((item) => String(item?.noteNo)?.toLowerCase()?.includes(search?.toLowerCase()))?.map(((item) => (
                            <li
                              key={item?.noteId}
                              aria-hidden
                              onClick={() => triggerParaSearch(item?.noteNo)}
                              className="p-1 cursor-pointer text-center"
                            >{item?.noteNo}
                            </li>
                          )))}
                        </ul>
                      </div>
                    )}
                </div>

              </div>

              <Tooltip label={t('close')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleClose}
                  leftIcon={<CloseOutlineIcon width="21px" height="21px" />}
                />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="col-span-12">
              <div className="flex-grow text-center pb-3">
                {allNotes?.firstNoteNo < noteMore && (
                  <button
                    className={`rounded-xl px-5 text-[${primary}]`}
                    style={{ border: `1px solid ${primary}` }}
                    onClick={() => loadMoreNotes(noteMore - 10)}
                  >
                    Previous Notes: {(noteMore - 10) > 0 ? noteMore - 10 : 1} to {noteMore - 1}
                  </button>
                )}
              </div>

              <div className="col-span-12">
                {actionTriggred?.loading && actionTriggred?.id === 'fetch-all-notes' ? (
                  <div className="w-full flex justify-center p-20">
                    <Spinner style={{ marginTop: '20px', marginLeft: '-90px' }} />
                  </div>
                ) : (
                  <div>
                    {notesData?.length === 0 ? (
                      <div className="text-center min-h-[200px]">
                        <div className="w-[150px] mx-auto m-10 py-10">
                          <NoNotesIcon width="150px" />
                        </div>
                        {t('noNotesFound')}
                      </div>
                    ) : (
                      <div className="w-full pr-3" id="notes-listing-scroll">
                        {notesData.map((item) => (
                          <div id={`notes-no-${item?.noteNo}`} key={item?.noteNo} className="mb-[11px]">
                            <NoteCard
                              key={item.id}
                              item={item}
                              isOnSelectNote={isOnSelectNote}
                              setIsOnSelectNote={setIsOnSelectNote}
                              from={from === 'create-draft' ? 'create-draft' : 'note'}
                              attachments={
                                (
                                  <DocumentView
                                    notesDocsDetails={item?.notesDocsDetails}
                                    noteNo={item?.noteNo}
                                    from="create-draft"
                                    setIsOpenDocumentModal={setIsOpenDocumentModal}
                                    isOpenDocumentModal={isOpenDocumentModal}
                                  />
                                )
                              }
                              draftDetails={
                                (
                                  <DraftView
                                    draftDetails={item?.draftDetails}
                                    from="note"
                                    setDraftItems={setDraftItems}
                                    setOpen={setOpen}
                                  />
                                )
                              }
                              noteReferences={
                                (
                                  <NoteReferences
                                    noteReferences={item?.noteReferences}
                                    handleRefNavigate={handleRefNavigate}
                                  />
                                )
                              }
                              lastNote={allNotes?.lastNoteNo}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
      {
        isOpenDocumentModal && (
          <Modal isOpen={isOpenDocumentModal} size="3xl" onClose={() => setIsOpenDocumentModal(!isOpenDocumentModal)} closeOnOverlayClick={false} closeOnEsc={false}>
            <ModalOverlay />
            <ModalContent>
              <ModalHeader p={0} className="text-center rounded-[10px]" style={{ background: light, color: dark }}>
                <div className="flex">
                  <div className="flex-none">
                    <h4 size="md" className="p-4 text-[18px] rounded-t-lg">
                      {t('document')}
                    </h4>
                  </div>
                  <div className="flex-grow" />
                  <div className="flex-none justify-end pt-[15px] pr-[15px]">
                    <Tooltip label={t('close')}>
                      <IconButton variant="unstyled" onClick={() => { setIsOpenDocumentModal(!isOpenDocumentModal); }} leftIcon={<CloseNew />} />
                    </Tooltip>
                  </div>
                </div>

              </ModalHeader>
              <ModalBody>
                <DocumentPreview
                  preview={noteCardDetails}
                  from="summary"
                  expandEnable
                  setOpenNewExpand={setOpenNewExpand}
                  openNewExpand={openNewExpand}
                  setFull={setFull}
                  full={full}
                />
              </ModalBody>
              <ModalFooter className="rounded-[10px]" style={{ background: light, color: dark }}>
                <div className="w-full text-right space-x-4">
                  <Button
                    variant="secondary_outline"
                    size="sm"
                    onClick={() => setIsOpenDocumentModal(!isOpenDocumentModal)}
                  >
                    {t('close')}
                  </Button>

                </div>
              </ModalFooter>
            </ModalContent>
          </Modal>
        )
      }
      {openNewExpand && full && (
        <DocumentNewExpandComponents
          noteCardDetails={noteCardDetails}
          full={full}
          openNewExpand={openNewExpand}
          setOpenNewExpand={setOpenNewExpand}
          setFull={setFull}
        />
      )}
    </>
  );
};

export default NoteExpansionModel;
