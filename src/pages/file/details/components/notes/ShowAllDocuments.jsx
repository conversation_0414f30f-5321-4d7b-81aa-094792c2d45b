import NoDocumentSelected from 'assets/NoDocumentSelected';
import { Button, t } from 'common/components';
import React from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import * as actions from '../../actions';
import { actions as sliceActions } from '../../slice';

const ShowAllDocuments = ({
  fetchFileDocuments,
  mergeLinkActive,
  mergeLinkActiveId,
  fileNo,
  setShowingAllDocs
}) => {
  return (
    <div className="text-center min-h-[200px]">
      <div className="w-[200px] mx-auto mt-40">
        <NoDocumentSelected />
      </div>
      <div className="pl-[50px] pt-[10px]">
        {t('noDocumentSelected')}
      </div>
      <div className="pl-[50px] pt-[10px]">
        <Button
          onClick={() => {
            fetchFileDocuments(mergeLinkActive ? mergeLinkActiveId : fileNo);
            setShowingAllDocs(true);
          }}
          variant="primary"
          className="w-[145px]"
        >
          <span className="text-[13px]">{t('showAllDocuments')}</span>
        </Button>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
});

const mapDispatchToProps = (dispatch) => ({
  fetchFileDocuments: (data) => dispatch(actions.fetchFileDocuments(data)),
  setShowingAllDocs: (data) => dispatch(sliceActions.setShowingAllDocs(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ShowAllDocuments);
