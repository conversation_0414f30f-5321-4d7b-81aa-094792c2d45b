import { Button } from '@ksmartikm/ui-components';
import CloseIcon from 'assets/Close';
import ImageIcon from 'assets/Image';
import { DocumentSvg, ExcelSvg, PdfSvg } from 'common/components/DocumentPreview/Icons';
import { DOCUMENT_TYPES } from 'common/constants';
import React from 'react';
import { secondary } from 'utils/color';

const DocumentThumps = ({
  fileType, handleDeleteClick
}) => {
  const previewBox = () => {
    switch (fileType) {
      case DOCUMENT_TYPES.PDF:
        return <PdfSvg />;
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return <ExcelSvg />;
      case DOCUMENT_TYPES.WORD:
        return <DocumentSvg />;
      default:
        return <ImageIcon />;
    }
  };

  return (
    <div>
      <div className="preview-image mt-3">
        <Button className="preview-icon-button">
          {previewBox()}
        </Button>

        <button onClick={handleDeleteClick} aria-label="button">
          <CloseIcon color={secondary} />
        </button>
      </div>
    </div>
  );
};

export default DocumentThumps;
