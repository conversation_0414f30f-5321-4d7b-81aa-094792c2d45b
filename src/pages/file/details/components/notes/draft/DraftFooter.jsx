import DownloadArrowIcon from 'assets/DownloadArrowIcon';
import ExpandNew from 'assets/ExpandNew';
import PrinterBoldIcon from 'assets/PrinterBoldIcon';
import Zoom from 'assets/Zoom';
import ZoomDown from 'assets/ZoomDown';
import MoreButtonIcon from 'assets/MoreButtonIcon';
import RejectIcon from 'assets/RejectIcon';
import ReviseIcon from 'assets/ReviseIcon';
import MakeInactiveIcon from 'assets/MakeInactiveIcon';
import ApproveActionIcon from 'assets/ApproveActionIcon';
import DraftActionIcon from 'assets/DraftActionIcon';
import DigitalSignatureIcon from 'assets/DigitalSignatureIcon';
import MinimizeNew from 'assets/MinimizeNew';
import { EMPLOYEE_ROLES } from 'common/constants';
import {
  Button, Menu, MenuButton, MenuList, t
} from 'common/components';
import { checkRoleByFileRole } from 'pages/file/details/helper';
import {
  ActionButton,
  ButtonWrapperCard,
  CustomMenuItem,
  StyledIconButton
} from './HelperComponents';

const {
  OPERATOR, APPROVER, VERIFIER, RECOMMEND
} = EMPLOYEE_ROLES;

const DraftFooter = ({
  draftItems,
  fileDetails,
  forwardPlusRoleForActions,
  dsDraftItems,
  postIdsRef,
  postIds,
  mergeLinkActiveId,
  showConfirmDsRef,
  showConfirmDs,
  actionTriggered,
  full,
  handleSubmit = () => {},
  handleConfirmDs,
  handleCheckDateTime,
  handleApproveAndDsData,
  handleFull,
  handleZoomIn,
  handleZoomOut,
  setValue,
  handleNavigate,
  draftButtons,
  postIdByPenNoDetails
}) => {
  const isApprovedAndUnsigned = () => {
    return (
      (dsDraftItems?.draftStage === 'APPROVED'
        || dsDraftItems?.status === 'APPROVED')
      && (dsDraftItems?.isDigitalSigned === false
        || draftItems?.isDigitalSigned === false)
      && (postIdsRef.current?.includes(dsDraftItems?.postId)
        || postIds?.includes(dsDraftItems?.postId))
      && (mergeLinkActiveId === '' || mergeLinkActiveId === null)
      && (dsDraftItems?.isESigned === false || draftItems?.isESigned === false)
    );
  };

  const hasVerifierOrHigherRole = () => {
    return (
      [VERIFIER, APPROVER, RECOMMEND].includes(fileDetails?.role)
      || [VERIFIER, APPROVER, RECOMMEND].includes(forwardPlusRoleForActions)
    );
  };

  const renderViewControls = () => (
    <div className="flex gap-2">
      <ButtonWrapperCard>
        <ActionButton
          onClick={handleZoomIn}
          icon={<Zoom stroke="#5C6E93" w="18" h="18" />}
        />
        <ActionButton
          onClick={handleZoomOut}
          icon={<ZoomDown stroke="#5C6E93" w="18" h="18" />}
        />
        <ActionButton
          onClick={handleFull}
          icon={
            full ? (
              <MinimizeNew stroke="#5C6E93" w="18" h="18" />
            ) : (
              <ExpandNew stroke="#5C6E93" w="18" h="18" />
            )
          }
        />
      </ButtonWrapperCard>
      <ButtonWrapperCard>
        <ActionButton
          onClick={(e) => handleNavigate('download', e)}
          icon={<DownloadArrowIcon />}
        />
        <ActionButton
          onClick={(e) => handleNavigate('print', e)}
          icon={<PrinterBoldIcon />}
        />
      </ButtonWrapperCard>
    </div>
  );

  const renderSignatureButtons = () => {
    if (!isApprovedAndUnsigned()) return null;

    if (
      (showConfirmDsRef.current || showConfirmDs)
      && postIdByPenNoDetails?.includes(fileDetails?.postId)
      && (mergeLinkActiveId === '' || mergeLinkActiveId === null)
    ) {
      return (
        <Button
          py="6px"
          px="13px"
          fontSize="13px"
          variant="secondary"
          isLoading={
            actionTriggered?.loading
            && actionTriggered?.id === 'confirm-ds-save'
          }
          leftIcon={<DigitalSignatureIcon color="#fff" w="18" h="18" />}
          onClick={handleConfirmDs}
        >
          {t('confirmDS')}
        </Button>
      );
    }

    return (
      <>
        <StyledIconButton
          label="ds"
          loading={
            actionTriggered?.loading && actionTriggered?.id === 'digital-sign'
          }
          icon={<DigitalSignatureIcon h="16" w="16" />}
          onClick={() => handleCheckDateTime(dsDraftItems)}
        />
        <StyledIconButton
          label="es"
          loading={actionTriggered?.loading && actionTriggered?.id === 'e-sign'}
          icon={<DigitalSignatureIcon h="16" w="16" />}
          onClick={() => {
            setValue('signType', 1);
            handleApproveAndDsData({
              id: draftItems?.id,
              draftNo: draftItems?.draftNo
            });
          }}
        />
      </>
    );
  };

  const renderActionMenu = () => (
    <Menu>
      <MenuButton>
        <StyledIconButton
          label="more"
          icon={<MoreButtonIcon h="18" w="18" />}
        />
      </MenuButton>
      <MenuList
        p={0}
        style={{
          position: 'absolute',
          bottom: full ? '4px' : '48px',
          transform: 'translateY(-100%)',
          background: '#ffffff',
          border: 'none',
          borderRadius: '8px',
          boxShadow: 'none',
          minWidth: '170px',
          maxWidth: '165px',
          display: 'flex',
          gap: '10px',
          flexDirection: 'column'
        }}
      >
        {(fileDetails?.role === APPROVER
          || forwardPlusRoleForActions === APPROVER) && (
          <CustomMenuItem
            icon={<RejectIcon w="20" h="20" />}
            label="reject"
            onClick={() => handleSubmit('reject')}
          />
        )}
        {isApprovedAndUnsigned() && hasVerifierOrHigherRole() && (
          <CustomMenuItem
            icon={<ReviseIcon w="18" h="18" />}
            label="revise"
            onClick={() => handleSubmit('Return')}
          />
        )}
        <CustomMenuItem
          icon={<MakeInactiveIcon w="20" h="20" />}
          label={draftItems?.isActive ? 'makeInActive' : 'makeActive'}
          onClick={() => handleSubmit('makeInactive')}
        />
        {(fileDetails?.role === VERIFIER
          || fileDetails?.role === RECOMMEND
          || forwardPlusRoleForActions === VERIFIER
          || forwardPlusRoleForActions === RECOMMEND) && (
          <CustomMenuItem
            icon={<ApproveActionIcon w="20" h="20" allowHoverStyle={false} />}
            label="approve"
            onClick={() => handleSubmit('approved')}
          />
        )}
      </MenuList>
    </Menu>
  );

  const renderActionButtons = () => {
    return (
      <div className="flex-grow flex justify-end items-center gap-2">
        {renderSignatureButtons()}

        {draftButtons()
          && dsDraftItems?.isEditable
          && postIdByPenNoDetails?.includes(fileDetails?.postId)
          && (mergeLinkActiveId === '' || mergeLinkActiveId === null) && (
            <>
              {!showConfirmDs
                && (fileDetails?.role === OPERATOR
                  || forwardPlusRoleForActions === OPERATOR) && (
                  <StyledIconButton
                    label={draftItems?.isActive ? 'makeInActive' : 'makeActive'}
                    icon={<MakeInactiveIcon h="16" w="16" />}
                    onClick={() => handleSubmit('makeInactive')}
                  />
              )}
              {!showConfirmDs
                && ((fileDetails && fileDetails?.role !== OPERATOR)
                  || (forwardPlusRoleForActions
                    && forwardPlusRoleForActions !== OPERATOR))
                && renderActionMenu()}
              {!showConfirmDs
                && !isApprovedAndUnsigned()
                && hasVerifierOrHigherRole() && (
                  <StyledIconButton
                    label="revise"
                    icon={<ReviseIcon h="16" w="16" />}
                    onClick={() => handleSubmit('Return')}
                  />
              )}
              {!showConfirmDs && (
                <Button
                  py="6px"
                  px="13px"
                  fontSize="13px"
                  variant="secondary"
                  leftIcon={<DraftActionIcon w="18" h="18" />}
                  onClick={() => handleSubmit('proceed')}
                >
                  {checkRoleByFileRole(
                    fileDetails?.role,
                    forwardPlusRoleForActions
                  )}
                </Button>
              )}
            </>
        )}
      </div>
    );
  };

  return (
    <div className="flex items-center py-3 px-5">
      {renderViewControls()}
      {renderActionButtons()}
    </div>
  );
};

export default DraftFooter;
