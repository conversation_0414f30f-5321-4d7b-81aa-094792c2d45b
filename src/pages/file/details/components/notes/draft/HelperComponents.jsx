import { MenuItem, Spinner } from '@ksmartikm/ui-components';
import { t } from 'common/components';
import { memo } from 'react';

export const RoundedButton = memo(({ icon, variant = 'outline', onClick }) => {
  const baseClasses = 'flex items-center justify-center rounded-full w-8 h-8 transition-colors';

  const variants = {
    filled: 'bg-[#F2F4F7] hover:bg-[#E7EFF5] text-blue-600',
    outlined: 'border-[2px] border-[#E7EFF5] text-gray-600 hover:bg-[#F2F4F7]'
  };

  return (
    <button onClick={onClick} className={`${baseClasses} ${variants[variant]}`}>
      {icon}
    </button>
  );
});

export const StyledIconButton = memo(
  ({
    loading = false, icon, label, onClick
  }) => {
    return (
      <button
        disabled={loading}
        onClick={onClick}
        className={`flex border border-[#EEEEEE] rounded-[8px] px-3 py-[6px] shadow-sm ${
          loading ? 'bg-gray-100 px-5 cursor-not-allowed' : ''
        }`}
      >
        <div className="flex items-center gap-2">
          {loading ? (
            <Spinner width={5} height={5} />
          ) : (
            <>
              {icon}
              <span className="font-semibold text-sm">{t(label)}</span>
            </>
          )}
        </div>
      </button>
    );
  }
);

export const ActionButton = memo(({ icon, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="w-5 h-5 flex items-center justify-center bg-white transition-colors duration-200"
    >
      {icon}
    </button>
  );
});

export const ButtonWrapperCard = memo(({ children }) => {
  return (
    <div className="bg-white max-h-max border-[1px] border-[#E7EFF5] rounded-[8px] px-1 py-1 flex gap-3 shadow-sm">
      {children}
    </div>
  );
});

export const CustomMenuItem = memo(({ icon, label, onClick }) => {
  return (
    <MenuItem
      style={{
        background: '#F9FAFC',
        border: '1px solid #E7E7E7',
        padding: '8px 16px',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        height: '40px'
      }}
    >
      <div
        aria-hidden
        className="flex-1 flex items-center gap-3 cursor-pointer"
        onClick={onClick}
      >
        <div>{icon}</div>
        <span className="text-[#232F50] text-sm font-semibold">{t(label)}</span>
      </div>
    </MenuItem>
  );
});
