import { getESignCreate } from 'pages/file/details/selector';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

const EsignForm = ({ htmlRender }) => {
  const [html, setHTML] = useState({ __html: '' });

  useEffect(() => {
    setHTML({ __html: htmlRender?.data });

    setTimeout(() => {
      document.getElementById('frmesign').submit();
    }, 2000);
  }, [htmlRender]);

  // eslint-disable-next-line react/no-danger
  return <div dangerouslySetInnerHTML={html} />;
};

const mapStateToProps = createStructuredSelector({
  htmlRender: getESignCreate
});

const mapDispatchToProps = () => ({});

export default connect(mapStateToProps, mapDispatchToProps)(EsignForm);
