import React from 'react';
import { connect } from 'react-redux';
import { t } from 'common/components';
import { createStructuredSelector } from 'reselect';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { BASE_PATH } from 'common/constants';
import {
  getDraftFilterActiveIndex,
  getDraftNumber,
  getExpandAllApprovedDraft,
  getFileDetails,
  getMergeLinkActiveId
} from 'pages/file/details/selector';
import { draftCreateMenu } from 'common/menuHandler';
import { getPostIdByPenNoDetails } from 'pages/common/selectors';
import DraftPagination from 'common/components/DraftPagination';
import { AddIconWithCircle } from 'assets/Svg';
import DraftFilterSelect from 'pages/common/components/DraftFilterSelect';
import { DRAFT_FILTERS, DRAFT_STATUS_CONFIG } from 'pages/common/constants';
import hexToRGBA from 'utils/hexToRGBA';

const DraftCount = (props) => {
  const {
    draftList,
    fileDetails,
    mergeLinkActiveId,
    postIdByPenNoDetails,
    page: draftPage,
    onPageChange,
    draftStatus,
    showPreviousPermanentButton,
    onDraftStatusChange
  } = props;
  const params = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const handleNewDraft = () => {
    navigate(`${BASE_PATH}/file/${params?.fileNo}/draft`);
  };

  const getPageColor = (page, { isActive, role }) => {
    let statusColor = '#ccc';

    if (role === 'permanent') {
      statusColor = DRAFT_STATUS_CONFIG.PENDING.color;
    } else {
      const currentDraft = draftList[page - 1];
      const lastDraft = currentDraft?.drafts?.at(-1);
      statusColor = DRAFT_STATUS_CONFIG[lastDraft?.status]?.color || '#ccc';
    }

    return {
      color: '#fff',
      border: isActive ? 'none' : '',
      borderColor: isActive ? '' : statusColor,
      backgroundColor: hexToRGBA(statusColor, isActive ? 1 : 0.6)
    };
  };

  return (
    <div className="flex flex-wrap gap-2 items-center w-full mt-3">
      <div className="flex-grow flex items-center gap-2">
        {draftList?.length > 1 && (
          <DraftPagination
            goToLastPageOnGroupChange
            showPreviousPermanentButton={showPreviousPermanentButton}
            maxVisibleButtons={3}
            currentPage={draftPage}
            totalPages={draftList?.length}
            onPageChange={onPageChange}
            formatPageNumber={(page) => {
              const currentDraft = draftList[page - 1];
              return currentDraft?.draftNo;
            }}
            getPageColor={getPageColor}
          />
        )}
        <DraftFilterSelect
          selected={draftStatus}
          options={DRAFT_FILTERS}
          onChange={onDraftStatusChange}
        />
      </div>
      <div>
        {draftCreateMenu(
          fileDetails?.status,
          fileDetails?.role,
          searchParams.get('flowaction')
        )
          && postIdByPenNoDetails?.includes(fileDetails?.postId)
          && !mergeLinkActiveId && (
            <button
              onClick={handleNewDraft}
              className="text-[#E83A7A] text-[14px] font-bold border-[1px] border-[#FDEBF2] rounded-[8px] py-2 px-2 flex items-center gap-2 transition-all duration-500 hover:bg-[#FCEAF1] hover:border-[#FADAE4]"
            >
              <AddIconWithCircle
                width="18"
                height="18"
                stroke="#E83A7A"
                strokeWidth="4"
              />
              <span>{`${t('new')} ${t('draft')}`}</span>
            </button>
        )}
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  mergeLinkActiveId: getMergeLinkActiveId,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  draftFilterActiveIndex: getDraftFilterActiveIndex,
  expandAllApprovedDraft: getExpandAllApprovedDraft,
  draftNumber: getDraftNumber
});

const mapDispatchToProps = () => ({});

export default connect(mapStateToProps, mapDispatchToProps)(DraftCount);
