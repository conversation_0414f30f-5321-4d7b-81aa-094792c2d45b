import React, {
  useEffect, useState, useCallback, useMemo
} from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getDraftFilterActiveIndex,
  getDraftList,
  getDraftNumber,
  getMergeLinkActive,
  getMergeLinkActiveId
} from 'pages/file/details/selector';
import { fetchDraft as fetchDraftAction } from 'pages/file/details/actions';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from 'pages/file/details/slice';
import { DRAFT_STATUS } from 'pages/common/constants';
import DraftList from './DraftList';
import DraftCount from './DraftCount';

const Draft = ({
  draftList,
  mergeLinkActiveId,
  mergeLinkActive,
  fetchDraft,
  draftNumber,
  setActionTriggered,
  setDraftNumber,
  draftFilterActiveIndex,
  setDraftFilterActiveIndex
}) => {
  const params = useParams();

  const [page, setPage] = useState(1);
  const [draftStatus, setDraftStatus] = useState('ALL');

  const [isShowActionPendingDrafts, setIsShowActionPendingDrafts] = useState(false);

  const actionTakenDrafts = useMemo(
    () => draftList?.filter((dr) => dr?.draftNo !== 0),
    [draftList]
  );

  const actionPendingDrafts = useMemo(
    () => draftList?.filter((dr) => dr?.draftNo === 0),
    [draftList]
  );

  const currentDraft = useMemo(() => {
    return isShowActionPendingDrafts
      ? actionPendingDrafts[0]
      : actionTakenDrafts[page - 1];
  }, [actionTakenDrafts, page, isShowActionPendingDrafts]);

  const handlePageChange = useCallback(
    (newPage, e) => {
      if (e?.role === 'permanent') {
        setIsShowActionPendingDrafts(true);
        setPage(newPage);
      } else {
        setIsShowActionPendingDrafts(false);
        setPage(newPage);
      }
    },
    [isShowActionPendingDrafts]
  );

  const handleDraftStatusChange = useCallback((e) => {
    setDraftNumber(null);
    setDraftStatus(e?.target?.value);
  }, []);

  useEffect(() => {
    if (draftFilterActiveIndex) {
      let newStatus = 'ALL';

      if (draftFilterActiveIndex === 1) {
        newStatus = DRAFT_STATUS.PENDING;
      } else if (draftFilterActiveIndex === 2) {
        newStatus = DRAFT_STATUS.APPROVED;
      }

      setDraftStatus(newStatus);
    }
  }, [draftFilterActiveIndex]);

  useEffect(() => {
    if (draftNumber) {
      setDraftFilterActiveIndex(3);
      setPage(draftNumber);
    } else {
      setPage(actionTakenDrafts?.length);
    }
  }, [draftNumber, actionTakenDrafts]);

  useEffect(() => {
    if (mergeLinkActiveId || params?.fileNo) {
      setActionTriggered({ loading: true, id: 'draft-loading' });
      setIsShowActionPendingDrafts(false);
      fetchDraft({
        fileNo: mergeLinkActiveId || params?.fileNo,
        status: draftStatus
      });
    }
  }, [draftStatus, mergeLinkActiveId, mergeLinkActive, params]);

  return (
    <div className="flex flex-col gap-4">
      <DraftCount
        page={page}
        draftList={actionTakenDrafts}
        showPreviousPermanentButton={!!actionPendingDrafts?.length}
        draftStatus={draftStatus}
        onPageChange={handlePageChange}
        onDraftStatusChange={handleDraftStatusChange}
      />
      <DraftList
        currentDraft={currentDraft}
        setIsShowActionPendingDrafts={setIsShowActionPendingDrafts}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  draftList: getDraftList,
  draftNumber: getDraftNumber,
  mergeLinkActiveId: getMergeLinkActiveId,
  mergeLinkActive: getMergeLinkActive,
  draftFilterActiveIndex: getDraftFilterActiveIndex
});

const mapDispatchToProps = (dispatch) => ({
  fetchDraft: (data) => dispatch(fetchDraftAction(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setDraftFilterActiveIndex: (data) => dispatch(sliceActions.setDraftFilterActiveIndex(data)),
  setDraftNumber: (data) => dispatch(sliceActions.setDraftNumber(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Draft);
