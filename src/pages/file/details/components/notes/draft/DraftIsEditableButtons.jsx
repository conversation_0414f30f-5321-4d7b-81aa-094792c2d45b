import ApproveActionIcon from 'assets/ApproveActionIcon';
import DraftActionIcon from 'assets/DraftActionIcon';
import MakeInactiveIcon from 'assets/MakeInactiveIcon';
import MoreButtonIcon from 'assets/MoreButtonIcon';
import RejectIcon from 'assets/RejectIcon';
import ReviseIcon from 'assets/ReviseIcon';
import {
  Button, Menu, MenuButton, MenuItem, MenuList, t
} from 'common/components';
import { EMPLOYEE_ROLES } from 'common/constants';
import { checkRoleByFileRole } from 'pages/file/details/helper';
import React from 'react';

const DraftIsEditableButtons = ({
  fileDetails, handleSubmit = () => { },
  draftItems, forwardPlusRoleForActions
}) => {
  return (

    <>
      {
        (fileDetails?.role === EMPLOYEE_ROLES.OPERATOR || forwardPlusRoleForActions === EMPLOYEE_ROLES.OPERATOR)
          ? (
            <Button
              onClick={() => handleSubmit('makeInactive')}
              variant="outline"
              style={{
                height: '48px', borderRadius: '8px', background: '#F9FAFC', border: '1px solid #E7E7E7'
              }}
              className="mx-2"
              // isLoading={actionTriggered?.id === CONFIRMATION_TYPE.MAKE_INACTIVE && actionTriggered?.loading}
              leftIcon={<MakeInactiveIcon />}
            >
              {draftItems?.isActive ? t('makeInActive') : t('makeActive')}
            </Button>
          )
          : (
            <Menu>
              <MenuButton
                style={{
                  width: '137px', height: '48px', borderRadius: '8px', background: '#F9FAFC', border: '1px solid #E7E7E7'
                }}
              >
                <div className="flex gap-4 justify-center"><MoreButtonIcon /> <span style={{ color: '#323232', fontSize: '16px', fontWeight: 'bold' }}>More</span></div>
              </MenuButton>
              <MenuList
                style={{
                  position: 'absolute',
                  bottom: '48px', // Adjusts the position above the button
                  transform: 'translateY(-100%)', // Aligns the menu list on top
                  marginBottom: '15px',
                  background: '#FFFFFF', // Ensures the background of the list is white
                  border: 'none',
                  borderRadius: '8px',
                  boxShadow: 'none',
                  maxWidth: '165px',
                  display: 'flex',
                  gap: '10px',
                  flexDirection: 'column'
                }}
              >
                {
                  (fileDetails?.role === EMPLOYEE_ROLES.APPROVER || forwardPlusRoleForActions === EMPLOYEE_ROLES.APPROVER)
                  && (
                    <MenuItem
                      style={{
                        background: '#F9FAFC',
                        border: '1px solid #E7E7E7',
                        padding: '8px 16px',
                        borderRadius: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        // justifyContent: 'center',
                        height: '48px'
                      }}
                    >
                      <div className="flex gap-3 pl-[20px]" aria-hidden onClick={() => handleSubmit('reject')}>
                        <RejectIcon /> <span style={{ color: '#323232', fontSize: '16px', fontWeight: 'bold' }}>{t('reject')}</span>
                      </div>
                    </MenuItem>
                  )
                }
                {
                  (
                    (
                      fileDetails?.role === EMPLOYEE_ROLES.VERIFIER
                      || fileDetails?.role === EMPLOYEE_ROLES.APPROVER
                      || fileDetails?.role === EMPLOYEE_ROLES.RECOMMEND
                    )
                    || (
                      forwardPlusRoleForActions === EMPLOYEE_ROLES.VERIFIER
                      || forwardPlusRoleForActions === EMPLOYEE_ROLES.APPROVER
                      || forwardPlusRoleForActions === EMPLOYEE_ROLES.RECOMMEND
                    )
                  ) && (
                    <MenuItem
                      style={{
                        background: '#F9FAFC',
                        border: '1px solid #E7E7E7',
                        padding: '8px 16px',
                        borderRadius: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        // justifyContent: 'center',
                        height: '48px'
                      }}
                    >
                      <div className="flex gap-5 pl-[15px]" aria-hidden onClick={() => handleSubmit('Return')}>
                        <ReviseIcon /> <span style={{ color: '#323232', fontSize: '16px', fontWeight: 'bold' }}>{t('revise')}</span>
                      </div>
                    </MenuItem>
                  )
                }
                <MenuItem
                  style={{
                    background: '#F9FAFC',
                    border: '1px solid #E7E7E7',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    // justifyContent: 'center',
                    height: '48px'
                  }}
                >
                  <div className="flex gap-4 pl-[15px]" aria-hidden onClick={() => handleSubmit('makeInactive')}>
                    <MakeInactiveIcon /> <span style={{ color: '#323232', fontSize: '16px', fontWeight: 'bold' }}>{draftItems?.isActive ? t('makeInActive') : t('makeActive')}</span>
                  </div>
                </MenuItem>

                {
                  (
                    (
                      fileDetails?.role === EMPLOYEE_ROLES.VERIFIER || fileDetails?.role === EMPLOYEE_ROLES.RECOMMEND
                    )
                    || (
                      forwardPlusRoleForActions === EMPLOYEE_ROLES.VERIFIER || forwardPlusRoleForActions === EMPLOYEE_ROLES.RECOMMEND
                    )
                  ) && (
                    <MenuItem
                      style={{
                        background: '#F9FAFC',
                        border: '1px solid #E7E7E7',
                        padding: '8px 16px',
                        borderRadius: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        // justifyContent: 'center',
                        height: '48px'
                      }}
                    >
                      <div className="flex gap-5 pl-[15px]" aria-hidden onClick={() => handleSubmit('approved')}>
                        <ApproveActionIcon /> <span style={{ color: '#323232', fontSize: '16px', fontWeight: 'bold' }}>{t('approve')}</span>
                      </div>
                    </MenuItem>
                  )
                }
              </MenuList>
            </Menu>
          )
      }

      <Button
        onClick={() => handleSubmit('proceed')}
        style={{ width: '147px' }}
        variant="secondary"
        className="mx-2"
        border="none"
        leftIcon={(
          <DraftActionIcon />
        )}
      >
        {checkRoleByFileRole(fileDetails?.role, forwardPlusRoleForActions)}
      </Button>
    </>

  );
};

export default DraftIsEditableButtons;
