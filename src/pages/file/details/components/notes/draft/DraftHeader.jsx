import CloseNew from 'assets/CloseNew';
import Edit from 'assets/Edit';
import { Flex } from 'common/components';
import { DATE_FORMAT, DRAFT_STATUS } from 'pages/common/constants';
import { memo } from 'react';
import { convertToLocalDate } from 'utils/date';
import { RoundedButton } from './HelperComponents';

const DraftHeader = memo(
  ({
    dsDraftItems,
    fileDetails,
    postIdByPenNoDetails,
    handleCloseBtn,
    handleEdit
  }) => {
    const statusWithDate = () => {
      const date = convertToLocalDate(
        dsDraftItems?.date,
        DATE_FORMAT.DATE_LOCAL_STANDARD
      );

      switch (dsDraftItems?.status || dsDraftItems?.draftStage) {
        case DRAFT_STATUS.CREATED:
          return `Created on ${date}`;
        case DRAFT_STATUS.VERIFIED:
          return `Verified on ${date}`;
        case DRAFT_STATUS.APPROVED:
          return `Approved on ${date}`;
        case DRAFT_STATUS.REJECTED:
          return `Rejected on ${date}`;
        case DRAFT_STATUS.RECOMMENDED:
          return `Recommended on ${date}`;
        default:
          return `Created on ${date}`;
      }
    };

    return (
      <Flex className="px-2 py-2">
        <div className="flex-grow flex items-center gap-2">
          <h2 className="font-bold">Draft {dsDraftItems?.draftNo}</h2>
          <span className="text-[#5C6E93] text-[12px]">{statusWithDate()}</span>
        </div>
        <div className="flex items-center gap-2">
          {dsDraftItems?.isEditable
            && !dsDraftItems?.isDigitalSIgned
            && postIdByPenNoDetails?.includes(fileDetails?.postId) && (
              <RoundedButton
                variant="outlined"
                icon={<Edit />}
                onClick={handleEdit}
              />
          )}
          <RoundedButton
            variant="filled"
            icon={<CloseNew />}
            onClick={handleCloseBtn}
          />
        </div>
      </Flex>
    );
  }
);

export default DraftHeader;
