import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback
} from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useNavigate, useParams } from 'react-router-dom';
import {
  getDraftFilterActiveIndex,
  getFileDetails,
  getMergeLinkActive,
  getMergeLinkActiveId
} from 'pages/file/details/selector';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import {
  getActionTriggered,
  getCorrespondTypeDropdown,
  getPostIdByPenNoDetails,
  getUserInfo
} from 'pages/common/selectors';
import { Button, t } from 'common/components';
import NoNotesIcon from 'assets/NoNotesIcon';
import * as commonActions from 'pages/common/actions';
import {
  actions as sliceCommonActions,
  actions as commonSliceActions
} from 'pages/common/slice';
import _ from 'lodash';
import { DRAFT_PDF_URL } from 'pages/file/details/constants';
import { generatePdf } from 'hooks/generatePdf';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { actions as sliceActions } from 'pages/file/details/slice';
import { Spinner } from '@ksmartikm/ui-components';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import { getDocument } from 'pages/file/details/helper';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';
import DraftCard from 'common/components/DraftCard';
import DraftNewPreview from './DraftNewPreview';
import * as actions from '../../../actions';

const DraftList = (props) => {
  const navigate = useNavigate();
  const params = useParams();

  const {
    fileDetails,
    correspondTypeDropdown,
    fetchCorrespondTypeDetails,
    userInfo,
    setAlertAction,
    deleteDraft,
    actionTriggered,
    postIdByPenNoDetails,
    mergeLinkActiveId,
    currentDraft,
    setIsShowActionPendingDrafts
  } = props;

  const [open, setOpen] = useState(false);
  const [draftItems, setDraftItems] = useState();
  const openRef = useRef(open);

  const [showPreviousDraftVersions, setShowPreviousDraftVersions] = useState(false);
  const [hoveredDraftId, setHoveredDraftId] = useState(null);
  const [activeDraftIds, setActiveDraftIds] = useState([]);

  const visibleDraftVersions = useMemo(() => {
    if (!currentDraft?.drafts?.length) return [];

    const arr = showPreviousDraftVersions
      ? currentDraft.drafts
      : [currentDraft.drafts.at(-1)];

    setActiveDraftIds([arr.at(-1)?.id]);

    return arr;
  }, [currentDraft, showPreviousDraftVersions]);

  const toggleShowPreviousDraftVersions = useCallback(() => {
    setShowPreviousDraftVersions(!showPreviousDraftVersions);
  }, [showPreviousDraftVersions]);

  const handleCardClick = useCallback(
    (draft) => {
      if (activeDraftIds.includes(draft?.id)) {
        setOpen(true);
        setDraftItems(draft);
      } else {
        setActiveDraftIds([...activeDraftIds, draft?.id]);
      }
    },
    [activeDraftIds]
  );

  const isDraftExpanded = useCallback(
    (draftId) => {
      return hoveredDraftId === draftId || activeDraftIds.includes(draftId);
    },
    [hoveredDraftId, activeDraftIds]
  );

  useEffect(() => {
    fetchCorrespondTypeDetails();
  }, []);

  // Commented for maybe use it later
  // const handleEdit = (data) => {
  //   navigate(`${BASE_PATH}/file/${params.fileNo}/draft/${data.id}`);
  // };

  const handlePreview = async ({ item, type }) => {
    if (item?.isESigned === true) {
      const generate = getDocument(
        `${baseApiURL}/${API_URL.E_SIGN.GET_E_SIGNED_DATA}`,
        localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN),
        { officeCode: userInfo?.officeId, moduleUid: item?.id }
      );
      const { data, status } = await generate.then((result) => result);
      if (status === 'success') {
        if (type === 'preview') {
          setOpen(true);
          openRef.current = true;
        } else if (type === 'print') {
          printBlob(data);
        } else {
          downloadBlob({
            blob: data,
            fileName: `KSUITE-DRAFT-${item?.correspondenceType}.pdf`
          });
        }
      }
    } else {
      const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${item?.id}&officeId=${
        userInfo?.officeId
      }&template=${item.correspondenceType.toLowerCase()}`;
      const generate = generatePdf({ url: urlDraftPdf });
      const { data, status } = await generate.then((result) => result);
      if (status === 'success') {
        if (type === 'preview') {
          setOpen(true);
          openRef.current = true;
        } else if (type === 'print') {
          printBlob(data);
        } else {
          downloadBlob({
            blob: data,
            fileName: `KSUITE-DRAFT-${item?.correspondenceType}.pdf`
          });
        }
      }
    }
  };

  const close = () => {
    setOpen(false);
    openRef.current = false;
  };

  const checkCorresdence = (data) => {
    if (data) {
      const getName = _.find(correspondTypeDropdown?.data, { id: data });
      if (_.keys(getName).length > 0) {
        return getName.name;
      }
      return null;
    }
    return null;
  };

  const handleNewDraft = () => {
    navigate(`${BASE_PATH}/file/${params?.fileNo}/draft`);
  };

  const handleDelete = (data, event) => {
    event.stopPropagation();
    setAlertAction({
      open: true,
      variant: 'alert',
      message: `${t('doYouWantToDelete')}`,
      title: t('confirmation'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      closeOnOverlayClick: false,
      closeOnEsc: false,
      forwardAction: () => deleteDraft({
        fileNo: data?.fileNo,
        draftId: data?.id,
        onSuccessCallback: () => setIsShowActionPendingDrafts(false)
      })
    });
  };

  const isLoading = actionTriggered?.id === 'draft-loading' && actionTriggered?.loading;
  const hasDraftVersions = visibleDraftVersions?.length > 0;
  const shouldShowCreateDraftButton = postIdByPenNoDetails?.includes(fileDetails?.postId)
    && (mergeLinkActiveId === '' || mergeLinkActiveId === null)
    && fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.CLOSED
    && fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.HOLD
    && fileDetails?.stage !== FILE_STATUS_FOR_API_PARAMS.PARKED
    && fileDetails?.stage !== FILE_STATUS_FOR_API_PARAMS.ROUTE_CHANGE;

  return (
    <>
      <div className="flex flex-col gap-4">
        {currentDraft?.drafts?.length > 1 && (
          <div>
            <button
              className="flex items-center gap-2 transition-all duration-300"
              onClick={toggleShowPreviousDraftVersions}
            >
              <KeyboardDownArrow
                className={`${
                  !showPreviousDraftVersions ? '-rotate-90' : 'rotate-0'
                } transform transition-transform duration-200 group-hover:scale-110`}
              />
              <span className="text-[#5C6E93] font-semibold text-sm">
                {`${
                  showPreviousDraftVersions
                    ? 'Hide Previous Versions'
                    : 'Show Previous Versions'
                } (${(currentDraft?.drafts?.length || 0) - 1})`}
              </span>
            </button>
          </div>
        )}
        <div>
          {isLoading && (
            <div className="flex items-center justify-center min-h-52">
              <Spinner />
            </div>
          )}

          {!isLoading
            && hasDraftVersions
            && visibleDraftVersions.map((item, i) => (
              <div
                key={item?.id}
                className={`transform transition-all duration-500 ease-in-out ${
                  showPreviousDraftVersions
                    ? `opacity-100 translate-y-0 delay-[${i * 100}ms]`
                    : 'opacity-100'
                }`}
              >
                <DraftCard
                  draftVersion={item}
                  isDelete={item?.draftNo === 0}
                  isExpanded={isDraftExpanded(item?.id)}
                  onClick={() => handleCardClick(item)}
                  onMouseEnter={() => setHoveredDraftId(item?.id)}
                  onMouseLeave={() => setHoveredDraftId(null)}
                  correspondence={checkCorresdence(item?.draftType)}
                  handleDelete={(e) => handleDelete({ fileNo: item?.fileNo, id: item?.id }, e)}
                  handlePreview={handlePreview}
                />
              </div>
            ))}

          {!isLoading && !hasDraftVersions && shouldShowCreateDraftButton && (
            <div className="text-center min-h-[700px]">
              <div className="w-[300px] mx-auto mb-[20px] pt-10">
                <NoNotesIcon width="300px" />
              </div>
              <Button
                variant="secondary"
                type="submit"
                size="xs"
                onClick={handleNewDraft}
                style={{ width: '109.45px' }}
              >
                {t('createDraft')}
              </Button>
            </div>
          )}
        </div>
      </div>
      <DraftNewPreview
        open={open || openRef.current}
        close={close}
        draftItems={draftItems}
        fileDetails={fileDetails}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  mergeLinkActive: getMergeLinkActive,
  userInfo: getUserInfo,
  fileDetails: getFileDetails,
  correspondTypeDropdown: getCorrespondTypeDropdown,
  draftFilterActiveIndex: getDraftFilterActiveIndex,
  actionTriggered: getActionTriggered,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  mergeLinkActiveId: getMergeLinkActiveId
});

const mapDispatchToProps = (dispatch) => ({
  fetchCorrespondTypeDetails: () => dispatch(commonActions.fetchCorrespondTypeDetails()),
  setTriggerPrint: (data) => dispatch(sliceCommonActions.setTriggerPrint(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  deleteDraft: (data) => dispatch(actions.deleteDraft(data)),
  setExpandAllApprovedDraft: (data) => dispatch(sliceActions.setExpandAllApprovedDraft(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftList);
