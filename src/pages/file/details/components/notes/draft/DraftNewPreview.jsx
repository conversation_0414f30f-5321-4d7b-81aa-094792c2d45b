import {
  <PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dalContent,
  <PERSON>dalOverlay,
  PdfViewer,
  Spinner
} from '@ksmartikm/ui-components';
import { FormController, t } from 'common/components';
import { ZoomComponent } from 'common/components/Zoom/Zoom';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import { generatePdf } from 'hooks/generatePdf';
import { DRAFT_STATUS, NOTE_STATUS } from 'pages/common/constants';
import {
  getActionTriggered,
  getDigitalSignConfirmation,
  getPostIdByPenNoDetails,
  getUserInfo
} from 'pages/common/selectors';
import { DRAFT_PDF_URL, SIGN_TYPES } from 'pages/file/details/constants';
import React, { useEffect, useState, useRef } from 'react';
import { connect } from 'react-redux';
import {
  useParams,
  useNavigate,
  useLocation,
  useSearchParams
} from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import dayjs from 'dayjs';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from 'pages/file/details/slice';
import * as dsActions from 'pages/profile/ds/actions';
import { blobUrlToBase64 } from 'utils/common';
import * as commonActions from 'pages/common/actions';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { useForm } from 'react-hook-form';
import { getDocument } from 'pages/file/details/helper';
import {
  getActionOccured,
  getApproveAndDsData,
  getDraftList,
  getDraftNewPreviewUpdateFlag,
  getFileDetails,
  getForwardPlusRoleForActions,
  getIsDigitalSignAction,
  getMergeLinkActiveId
} from '../../../selector';
import * as actions from '../../../actions';
import DraftHeader from './DraftHeader';
import DraftFooter from './DraftFooter';

const DraftNewPreview = ({
  open,
  close = () => {},
  draftItems,
  userInfo,
  fileDetails,
  actionTriggered,
  handleMakeInActive,
  handleAction,
  fetchDraftById,
  setAlertAction,
  setActionTriggered,
  fetchAllEnroll,
  signPdf,
  saveSignedDraft,
  setDraftNumber,
  fetchDraft,
  fetchAllNotes,
  fetchFileDetails,
  approveAndDsData,
  setApproveAndDsData,
  setActionOccured,
  actionOccured,
  draftList,
  setDigitalSignConfirmation,
  digitalSignConfirmation,
  isDigitalSignAction,
  setIsDigitalSignAction,
  postIdByPenNoDetails,
  mergeLinkActiveId,
  draftNewPreviewUpdateFlag,
  setDraftNewPreviewUpdateFlag,
  fetchEsign,
  forwardPlusRoleForActions
}) => {
  const { control, setValue, watch } = useForm({
    mode: 'all',
    defaultValues: {
      signType: 2
    }
  });
  const [full, setFull] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [loading, setLoading] = useState(true);
  const [draftPreview, setDraftPreview] = useState({});
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [postIds, setPostIds] = useState([]);
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const [showConfirmDs, setShowConfirmDs] = useState(false);
  const [dsPreview, setDsPreview] = useState({});
  const [searchParams] = useSearchParams('');
  const showConfirmDsRef = useRef(showConfirmDs);
  const postIdsRef = useRef(postIds);
  const openRef = useRef(open);
  const [dsDraftItems, setDsDraftItems] = useState(draftItems);
  const draftPreviewRef = useRef(draftPreview);
  const handleCloseRef = useRef(false);

  useEffect(() => {
    if (draftItems) {
      setDsDraftItems(draftItems);
      if (draftItems?.isDigitalSIgned) {
        setActionTriggered({ id: 'confirm-ds-save', loading: true });
      }
    }
  }, [draftItems]);

  useEffect(() => {
    if (!dsDraftItems) {
      setDsDraftItems(draftItems);
    }
    if (draftItems?.isDigitalSIgned || dsDraftItems?.isDigitalSIgned) {
      setActionTriggered({ id: 'confirm-ds-save', loading: true });
    }
  }, [dsDraftItems]);

  const searchParamsDetails = searchParams?.get('from');

  const handleFull = () => {
    setFull(!full);
  };

  // eslint-disable-next-line consistent-return
  const handlePreview = async (draftId, type, forSign) => {
    let forDigitalSign = false;
    let correspondenceType = 'other';

    if (forSign !== undefined || forSign !== null) {
      forDigitalSign = forSign;
    } else if (
      dsDraftItems?.status === DRAFT_STATUS.APPROVED
      || dsDraftItems?.draftStage === DRAFT_STATUS.APPROVED
      || draftItems?.status === DRAFT_STATUS.APPROVED
      || draftItems?.draftStage === DRAFT_STATUS.APPROVED
    ) {
      forDigitalSign = true;
    }
    if (type) {
      correspondenceType = type;
    }
    if (draftId !== null && draftId !== 'undefined' && draftId !== undefined) {
      setLoading(true);
      if (draftItems?.isESigned === true) {
        const generate = getDocument(
          `${baseApiURL}/${API_URL.E_SIGN.GET_E_SIGNED_DATA}`,
          localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN),
          { officeCode: userInfo?.officeId, moduleUid: draftId }
        );
        const { data, status } = await generate.then((result) => result);
        if (status === 'success') {
          setLoading(false);
          setDraftPreview(data);
          draftPreviewRef.current = data;
        } else {
          setLoading(false);
        }
      } else {
        const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${draftId}&officeId=${
          userInfo?.officeId
        }&template=${correspondenceType?.toLowerCase()}&forDigitalSign=${forDigitalSign}`;
        const generate = generatePdf({ url: urlDraftPdf });
        const { data, status } = await generate.then((result) => result);
        if (status === 'success') {
          setLoading(false);
          setDraftPreview(data);
          draftPreviewRef.current = data;
          // eslint-disable-next-line no-return-await
          return await data;
        }
      }
    }
  };

  useEffect(() => {
    if (draftItems?.id && draftItems?.correspondenceType) {
      handlePreview(
        draftItems?.id,
        draftItems?.correspondenceType,
        draftItems?.status === DRAFT_STATUS.APPROVED
          || draftItems?.draftStage === DRAFT_STATUS.APPROVED
      );
    }
  }, [
    open,
    draftItems?.id,
    draftItems?.correspondenceType,
    draftNewPreviewUpdateFlag
  ]);

  useEffect(() => {
    setShowConfirmDs(false);
    showConfirmDsRef.current = false;
  }, []);

  useEffect(() => {
    if (openRef.current === false) {
      showConfirmDsRef.current = false;
      setShowConfirmDs(false);
    } else {
      setShowConfirmDs(showConfirmDsRef.current);
    }
  }, [showConfirmDsRef.current, openRef.current]);

  useEffect(() => {
    openRef.current = open;
    handleCloseRef.current = false;
    if (dsDraftItems?.isDigitalSIgned === false && isDigitalSignAction) {
      setIsDigitalSignAction(false);
    } else if (dsDraftItems?.isDigitalSIgned) {
      setIsDigitalSignAction(true);
    }
  }, [open]);

  const preSetData = () => {
    showConfirmDsRef.current = true;
    postIdsRef.current = [...postIds, dsDraftItems?.postId];
    openRef.current = true;
    handleCloseRef.current = false;
    if (dsDraftItems?.isDigitalSIgned) {
      setIsDigitalSignAction(true);
      setActionTriggered({ id: 'confirm-ds-save', loading: true });
    }
  };

  const callBackEsign = async (data) => {
    setShowConfirmDs(true);
    showConfirmDsRef.current = true;
    // await handlePreview(
    //   data?.params?.draftId,
    //   dsDraftItems?.correspondenceType,
    //   true
    // );
    setDsPreview({ blob: data.blob, params: data.params });
    setApproveAndDsData({});
    setDsDraftItems(
      draftList
        .find((item) => item?.draftNo === data.params.draftNo)
        ?.drafts.find((item) => item?.id === data.params.draftId)
    );
    setActionTriggered({ id: 'digital-sign', loading: false });
    preSetData();
    setAlertAction({ open: false });
  };

  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.2, 5));
  };

  const handleZoomOut = (event) => {
    event.preventDefault();
    setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1));
  };

  const handleEdit = () => {
    showConfirmDsRef.current = false;
    openRef.current = false;
    handleCloseRef.current = true;
    close();
    setShowConfirmDs(false);
    setDigitalSignConfirmation({
      open: false
    });
    const from = location?.pathname?.includes('/summary')
      ? 'summary'
      : 'draft-page';
    navigate(
      `${BASE_PATH}/file/${params.fileNo}/draft/${dsDraftItems?.id}?from=${
        from || 'draft-page'
      }`
    );
    if (location.pathname.includes('/draft')) {
      fetchDraftById(dsDraftItems?.id);
    }
  };
  const handleEsign = async (data) => {
    setActionTriggered({ id: 'digital-sign', loading: true });
    blobUrlToBase64(draftPreviewRef.current).then((base64Data) => {
      if (base64Data) {
        const newPayload = {
          base64: base64Data,
          xtop: 200,
          ytop: 300,
          xbottom: 380,
          ybottom: 350,
          pageno: 0,
          reason: 'KSMART'
        };
        signPdf({
          pdfRequest: newPayload,
          draftRequest: {
            fileNo: params?.fileNo,
            draftNo: data?.draftNo,
            draftId: data?.draftId || params?.draftid
          },
          callBackEsign
        });
      }
    });
  };
  const handleCloseForDsLoader = () => {
    setActionTriggered({ id: 'digital-sign', loading: false });
    setAlertAction({ open: false });
    setActionTriggered({ loading: false, id: 'confirm-ds-save' });
  };

  const handleConfirm = async (data) => {
    await handlePreview(
      data?.draftId || params?.draftid,
      dsDraftItems?.correspondenceType,
      true
    );

    setActionTriggered({ id: 'digital-sign', loading: true });
    setTimeout(() => {
      if (Object.keys(draftPreviewRef.current).length > 0) {
        fetchAllEnroll({
          from: 'draft-save',
          handleEsign: () => handleEsign(data),
          handleCloseForDsLoader
        });
      }
    }, 100);
  };

  const draftButtons = () => {
    if (
      dsDraftItems?.isDigitalSigned === false
      && dsDraftItems?.isEditable === false
    ) {
      return false;
    }
    if (
      dsDraftItems?.isDigitalSigned === false
      && (dsDraftItems?.status === 'APPROVED'
        || dsDraftItems?.draftStage === 'APPROVED')
    ) {
      return true;
    }
    if (
      dsDraftItems?.isDigitalSigned === false
      && (dsDraftItems?.status !== 'APPROVED'
        || dsDraftItems?.draftStage !== 'APPROVED')
    ) {
      return true;
    }
    if (
      dsDraftItems?.isDigitalSigned === true
      && (dsDraftItems?.status === 'APPROVED'
        || dsDraftItems?.draftStage === 'APPROVED')
    ) {
      return false;
    }
    if (params?.draftid) {
      return true;
    }
    if (forwardPlusRoleForActions) {
      return true;
    }
    return false;
  };

  const handleCheckDateTime = async (data) => {
    // setIsDsLoading(true);
    setActionTriggered({ id: 'digital-sign', loading: true });
    const response = await fetch(
      `${baseApiURL}/${API_URL.E_SIGN.DATE_CHECK}?datetime=${dayjs().format(
        'DD/MM/YYYY'
      )}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    const res = await response.json();
    if (res?.payload?.isSame) {
      setLoading({ loading: true, id: params?.draftid });
      handleConfirm({
        draftId: data?.draftId || data?.id,
        draftNo: data?.draftNo
      });
      setTimeout(() => {
        setIsDigitalSignAction(true);
      }, 300);
    } else {
      // setIsDsLoading(false);
      setTimeout(() => setIsDigitalSignAction(false), 300);
      setAlertAction({ open: false });
      setActionTriggered({ id: 'digital-sign', loading: false });
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t(
          'systemDateNoteUpdated'
        )} <br/><div style="font-size: 14px"> ${t('pleaseCheck')} </div>`,
        title: t('warning'),
        backwardActionText: t('close')
      });
    }
  };

  const handleBackwardAction = (val) => {
    setDraftNewPreviewUpdateFlag(true);
    fetchDraft({ fileNo: params?.fileNo, status: 'ALL' });
    fetchAllNotes({
      fileNo: params?.fileNo,
      noteStatus: NOTE_STATUS.COMPLETED
    });

    close();
    if (location.pathname.includes('summary')) {
      setAlertAction({ open: false });
      navigate(0);
    } else if (val === 'summary') {
      setAlertAction({ open: false });
      navigate(`${BASE_PATH}/file/${params?.fileNo}/summary`);
    } else {
      setAlertAction({ open: false });
      navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
    }
  };

  useEffect(() => {
    if (approveAndDsData?.draftId && approveAndDsData?.draftNo) {
      setTimeout(() => handleCheckDateTime(approveAndDsData), 300);
    }
  }, [actionOccured, approveAndDsData]);

  useEffect(() => {
    if (userInfo) {
      const roles = userInfo?.userRoles || [];
      const postIdsFormate = roles?.map((item) => item?.postId);
      setPostIds(postIdsFormate || []);
    }
  }, [userInfo]);

  const navigateToActiveDraft = () => {
    // setDraftNewPreviewUpdateFlag(true);
    setDraftNumber(dsDraftItems?.draftNo);
    setActionOccured(true);
    setAlertAction({ open: false });
    setActionTriggered({ id: 'confirm-ds-save', loading: false });

    fetchDraft({ fileNo: params?.fileNo, status: 'ALL' });
    fetchFileDetails(params?.fileNo);
    fetchAllNotes({
      fileNo: params?.fileNo,
      noteStatus: NOTE_STATUS.COMPLETED
    });
    openRef.current = false;
    handleCloseRef.current = true;

    close();
    setTimeout(() => {
      navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
    }, 100);
  };

  const callBackESignConfirm = () => {
    close();
    setActionTriggered({ id: 'confirm-ds-save', loading: true });
    openRef.current = false;
    handleCloseRef.current = true;
    setActionTriggered({ id: 'confirm-ds-save', loading: true });
    setTimeout(() => {
      // if (open === false && openRef.current === false) {
      setAlertAction({
        open: true,
        variant: 'success',
        message: t('draftUpdatedwithDigitalSignature'),
        title: t('success'),
        forwardActionText: t('ok'),
        forwardAction: () => navigateToActiveDraft(),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
      // } else setActionTriggered({ id: 'confirm-ds-save', loading: true });
    }, 300);
  };

  const handleConfirmDs = () => {
    setActionTriggered({ loading: true, id: 'confirm-ds-save' });
    saveSignedDraft({
      blob: dsPreview?.blob,
      params: dsPreview?.params,
      navigateToActiveDraft: callBackESignConfirm
    });
    // showConfirmDsRef.current = false;
    // openRef.current = false;
    // setShowConfirmDs(false);
    // close();
  };

  const handleEsignType = (val) => {
    setValue('signType', Number(val));
  };

  const handleApproveAndDsData = async (data) => {
    if (watch('signType') === 1) {
      const baseUrl = `ui/file-management/file/${params?.fileNo}/notes?show=1`;
      const urlParams = new URLSearchParams();
      urlParams.set('for', 'esign');
      urlParams.set('moduleId', data?.id || params?.draftid);
      urlParams.set('draftNo', data?.draftNo);
      const returnUrl = `${baseUrl}&${urlParams.toString()}`;

      const previewUrl = await handlePreview(
        data?.id || params?.draftid,
        dsDraftItems?.correspondenceType || 'others',
        true
      );

      let eSignPayload = {};
      await blobUrlToBase64(previewUrl).then((base64Data) => {
        if (base64Data) {
          eSignPayload = {
            officeCode: userInfo?.officeId,
            pdfBase64: base64Data,
            moduleUid: data?.id || params?.draftid,
            returnUrl,
            isCustomized: true
          };
        }
      });
      fetchEsign(eSignPayload);
      setValue('signType', 1);
    } else {
      handlePreview(
        data?.id || params?.draftid,
        dsDraftItems?.correspondenceType || 'others',
        true
      );
      setTimeout(() => {
        setApproveAndDsData({
          draftId: data?.id || params?.draftid,
          draftNo: data?.draftNo
        });
        setActionOccured(true);
      }, 500);
    }
  };
  const handleClose = () => {
    openRef.current = false;
    handleCloseRef.current = true;
    close();
  };

  const handleEsignComponent = () => {
    return (
      <div>
        <p className="text-center">{t('DoYouWantToSign')}?</p>
        <div className="flex justify-center">
          <div className="flex justify-center items-center">
            <FormController
              name="signType"
              type="radio"
              control={control}
              options={SIGN_TYPES}
              optionKey="id"
              handleChange={(data) => handleEsignType(data)}
            />
          </div>
        </div>
      </div>
    );
  };

  const handleSubmit = (type) => {
    if (type === 'makeInactive') {
      handleMakeInActive({
        fileNo: params.fileNo,
        draftNo: dsDraftItems?.draftNo,
        active: !dsDraftItems?.isActive,
        close,
        location,
        searchParamsDetails
      });
    } else {
      handleAction({
        draftId: dsDraftItems?.id,
        fileNo: params.fileNo,
        action: type,
        close: handleClose,
        handleCheckDateTime: handleApproveAndDsData,
        handleBackwardAction,
        location,
        searchParamsDetails,
        handleEsignComponent
      });
    }
  };

  const previewData = () => {
    return digitalSignConfirmation?.payload?.blob
      && (dsDraftItems?.draftStage === 'APPROVED'
        || dsDraftItems?.status === 'APPROVED')
      && isDigitalSignAction ? (
        <PdfViewer data={digitalSignConfirmation?.payload?.blob} />
      ) : (
        <ZoomComponent image={draftPreviewRef.current} type="pdf" zoom={zoom} />
      );
  };

  const handleNavigate = async (type, e) => {
    e.stopPropagation();
    let flag = false;
    if (
      draftItems?.isDigitalSigned === false
      && draftItems?.isESigned === false
    ) {
      flag = false;
    }
    if (
      draftItems?.isDigitalSigned === true
      || draftItems?.isESigned === true
    ) {
      flag = true;
    }
    await handlePreview(draftItems?.id, draftItems?.correspondenceType, flag);
    setTimeout(() => {
      if (type === 'print') {
        printBlob(draftPreviewRef.current);
      } else {
        downloadBlob({
          blob: draftPreviewRef.current,
          fileName: `KSUITE-DRAFT-${draftItems?.correspondenceType}.pdf`
        });
      }
    }, 300);
  };

  const handleCloseBtn = () => {
    if (actionTriggered?.loading && actionTriggered?.id === 'digital-sign') setActionTriggered({ loading: false, id: 'digital-sign' });
    if (actionTriggered?.loading && actionTriggered?.id === 'confirm-ds-save') setActionTriggered({ loading: false, id: 'confirm-ds-save' });
    setLoading(false);
    setTimeout(() => {
      close();
      setShowConfirmDs(false);
      showConfirmDsRef.current = false;
      openRef.current = false;
    }, 300);
  };

  return (
    <Modal
      size={full && 'full'}
      isOpen={!handleCloseRef.current && (openRef.current || open)}
    >
      <ModalOverlay />
      <ModalContent style={{ maxWidth: full ? '100%' : '662px' }}>
        <DraftHeader
          dsDraftItems={dsDraftItems}
          fileDetails={fileDetails}
          postIdByPenNoDetails={postIdByPenNoDetails}
          handleCloseBtn={handleCloseBtn}
          handleEdit={handleEdit}
        />
        <ModalBody p={0} className="mt-2">
          <div
            style={{
              height: full ? 'calc(100vh - 115px)' : 'calc(100vh - 245px)'
            }}
            className="overflow-y-auto mx-2"
          >
            {loading
            || (actionTriggered?.loading
              && actionTriggered?.id === 'digital-sign') ? (
                <div className="flex items-center justify-center h-full">
                  <Spinner style={{ margin: '30px 28px' }} />
                </div>
              ) : (
                previewData()
              )}
          </div>
        </ModalBody>
        <DraftFooter
          actionTriggered={actionTriggered}
          showConfirmDs={showConfirmDs}
          showConfirmDsRef={showConfirmDsRef}
          mergeLinkActiveId={mergeLinkActiveId}
          postIds={postIds}
          postIdsRef={postIdsRef}
          dsDraftItems={dsDraftItems}
          draftItems={draftItems}
          fileDetails={fileDetails}
          forwardPlusRoleForActions={forwardPlusRoleForActions}
          full={full}
          postIdByPenNoDetails={postIdByPenNoDetails}
          handleSubmit={handleSubmit}
          handleConfirmDs={handleConfirmDs}
          handleCheckDateTime={handleCheckDateTime}
          setValue={setValue}
          handleApproveAndDsData={handleApproveAndDsData}
          handleFull={handleFull}
          handleZoomIn={handleZoomIn}
          handleZoomOut={handleZoomOut}
          handleNavigate={handleNavigate}
          draftButtons={draftButtons}
        />
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  approveAndDsData: getApproveAndDsData,
  actionOccured: getActionOccured,
  draftList: getDraftList,
  digitalSignConfirmation: getDigitalSignConfirmation,
  isDigitalSignAction: getIsDigitalSignAction,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  fileDetails: getFileDetails,
  mergeLinkActiveId: getMergeLinkActiveId,
  draftNewPreviewUpdateFlag: getDraftNewPreviewUpdateFlag,
  forwardPlusRoleForActions: getForwardPlusRoleForActions
});

const mapDispatchToProps = (dispatch) => ({
  handleMakeInActive: (data) => dispatch(actions.handleMakeInActive(data)),
  handleAction: (data) => dispatch(actions.handleAction(data)),
  fetchDraftById: (data) => dispatch(actions.fetchDraftById(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchAllEnroll: (data) => dispatch(dsActions.fetchAllEnroll(data)),
  signPdf: (data) => dispatch(commonActions.signPdf(data)),
  saveSignedDraft: (data) => dispatch(commonActions.saveSignedDraft(data)),
  setDraftNumber: (data) => dispatch(sliceActions.setDraftNumber(data)),
  fetchDraft: (data) => dispatch(actions.fetchDraft(data)),
  fetchAllNotes: (data) => dispatch(actions.fetchAllNotes(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setActionOccured: (data) => dispatch(sliceActions.setActionOccured(data)),
  setApproveAndDsData: (data) => dispatch(sliceActions.setApproveAndDsData(data)),
  setDigitalSignConfirmation: (data) => dispatch(commonSliceActions.setDigitalSignConfirmation(data)),
  setIsDigitalSignAction: (data) => dispatch(sliceActions.setIsDigitalSignAction(data)),
  setDraftNewPreviewUpdateFlag: (data) => dispatch(sliceActions.setDraftNewPreviewUpdateFlag(data)),
  fetchEsign: (data) => dispatch(actions.fetchEsign(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftNewPreview);
