import {
  <PERSON>u, MenuButton, MenuItem, MenuList
} from '@ksmartikm/ui-components';
import BlueTick from 'assets/BlueTick';
import ThreeDots from 'assets/ThreeDots';
import React from 'react';

const DraftFilter = ({
  buttonRef, selectedFilter, menuWidth, DRAFT_FILTERS,
  handleDraftFilters
}) => {
  return (
    <Menu>
      <MenuButton
        ref={buttonRef}
        className="text-[#5C6E93] text-[14px] font-medium p-[10px] cursor-pointer border border-[#00B2EB] rounded-lg flex items-center justify-between gap-3"
        style={{
          width: '190px',
          paddingTop: '8px', // Optional: Adjust spacing if needed
          paddingBottom: '8px' // Optional: Adjust spacing if needed
        }}
      >
        <div className="flex items-center gap-2">
          <div className="flex-none">
            <BlueTick />
          </div>
          <div className="flex-grow">{selectedFilter}</div>
          <div className="flex-none"><ThreeDots /></div>
        </div>

      </MenuButton>
      <MenuList
        style={{
          borderRadius: '20px',
          overflow: 'hidden',
          border: 'none',
          boxShadow: '0 0rem 3rem rgba(0, 0, 0, 0.3), inset 0 -1px 0 rgba(255, 255, 255, 0.15)',
          minWidth: menuWidth
        }}
      >
        {DRAFT_FILTERS?.filter((val) => val?.name !== selectedFilter)?.map((item, index, array) => (
          <>
            <MenuItem
              as="a"
              key={item?.id}
              onClick={() => handleDraftFilters(item?.id)}
              className="cursor-pointer text-[#454545] text-[14px] font-medium"
              style={{
                position: 'relative',
                backgroundColor: 'transparent',
                color: '#454545',
                cursor: 'pointer'
              }}
            >
              <div style={{ padding: '10px 12px 10px 30px' }}>
                <span>{item?.name}</span>
              </div>
            </MenuItem>
            {index < array.length - 1 && (
              <div
                style={{
                  width: 'calc(100% - 50px)',
                  height: '1px',
                  backgroundColor: '#e0e0e0',
                  margin: '0 auto'
                }}
              />
            )}
          </>
        ))}
        {/* Add a pseudo-element for the dropdown arrow if needed */}
        <div
          style={{
            position: 'absolute',
            top: '-7px', // Position arrow above the menu
            right: '10px', // Align arrow to the right
            width: '0',
            height: '0',
            borderLeft: '10px solid transparent', // Adjust thickness
            borderRight: '10px solid transparent', // Adjust thickness
            borderBottom: '10px solid #ffffff' // Arrow color and thickness
          }}
        />
      </MenuList>
    </Menu>
  );
};

export default DraftFilter;
