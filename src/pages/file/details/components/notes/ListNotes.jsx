import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON>
} from '@ksmartikm/ui-components';
import React, { useState, useEffect, useRef } from 'react';
import { t } from 'common/components';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import Tags from 'common/components/Tag';
import { primary } from 'utils/color';
import { FILE_STATUS_FOR_API_PARAMS, NOTE_STATUS } from 'pages/common/constants';
import DownArrow from 'assets/DownArrow';
import NoteCard from 'common/components/NotesCard/NoteCard';
import {
  getActionTriggered, getDocumentId, getNoteCardDetails, getPostIdByPenNoDetails, getUserInfo
} from 'pages/common/selectors';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import NoNotesIcon from 'assets/NoNotesIcon';
import DocumentView from 'pages/common/components/DocumentView';
import DraftView from 'pages/common/components/DraftView';
import NoteReferences from 'pages/common/components/NoteReferences';
import { actions as commonSliceActions } from 'pages/common/slice';
import { BASE_PATH } from 'common/constants';
import LinkIcons from 'assets/LinkedIcons';
import MergedIcons from 'assets/MergedIcons';
import ChildFileIcons from 'assets/ChildFileIcons';
import FullScreenIcon from 'assets/FullScreen';
import * as actions from '../../actions';
import { actions as sliceActions } from '../../slice';
import {
  getSavedNoteId, getMergeLinkFiles, getMergeLinkActive, getMergeLinkActiveId, getChildFiles,
  getNoteExpand,
  getDragEnabled,
  getNoteRefTrigger,
  getAllNotes,
  getCompletedNotes,
  getIsOnSelectNote
} from '../../selector';
import CreateNotes from './Create';
import './style.css';
import DraftNewPreview from './draft/DraftNewPreview';
import SubFilesDropdown from './SubFilesDropdown';
import NoteExpansionModel from './NoteExpansionModel';

const ListNotes = (props) => {
  const {
    fetchAllNotes,
    allNotes,
    setNotes,
    fileDetails,
    fetchMergeLink,
    mergeLinkFiles,
    mergeLinkActive,
    setMergeLinkActive,
    fetchFileDetails,
    setMergeLinkActiveId,
    mergeLinkActiveId,
    fetchDraft,
    childFiles,
    fetchChildFiles,
    setNoteExpand,
    noteExpand,
    dragEnabled,
    setNoteRefTrigger,
    postIdByPenNoDetails,
    setDocumentId,
    setNoteCardDetails,
    listCompletedNotes,
    completedNotes,
    noteRefTrigger,
    actionTriggred,
    setActionTriggered,
    from = null,
    isOpenDocumentModal,
    setIsOpenDocumentModal = () => { },
    fetchFileDocuments,
    setShowingAllDocs,
    setIsOnSelectNote,
    isOnSelectNote,
    noteCardDetails
  } = props;

  const params = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [notesData, setNotesData] = useState([]);
  const [showAttachment, setShowAttachment] = useState(false);
  const [linkedOptions, setLinkedOptions] = useState([]);
  const [mergedOptions, setMergedOptions] = useState([]);
  const [childFileOptions, setChildFileOptions] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selected, setSelected] = useState('');
  const scrollableDivRef = useRef(null);
  const [showParaEdit, setShowParaEdit] = useState(false);
  const [search, setSearch] = useState('');
  const [showDrop, setShowDrop] = useState(false);
  const paraEdit = useRef(null);
  const dropRef = useRef(null);
  const goToRef = useRef(null);

  const [isAtEnd, setIsAtEnd] = useState(false);
  const [noteMore, setNoteMore] = useState(null);
  const [open, setOpen] = useState(false);
  const [draftItems, setDraftItems] = useState();
  const [selectedDropdown, setSelectedDropdown] = useState(null);
  const openRef = useRef(open);
  const [openModal, setOpenModal] = useState(false);
  const handleClose = () => {
    setOpenModal(false);
  };

  const handleFull = () => {
    setOpenModal(true);
  };

  const close = () => {
    setOpen(false);
    openRef.current = false;
  };

  useEffect(() => {
    if (params?.fileNo) {
      fetchMergeLink(params?.fileNo);
      fetchChildFiles(params?.fileNo);
    }
  }, [params?.fileNo]);

  useEffect(() => {
    openRef.current = open;
  }, [open]);

  useEffect(() => {
    setNotesData([]);
    setNotes([]);
  }, [mergeLinkActiveId]);

  const scrollTop = () => {
    scrollableDivRef.current.scrollTo({
      top: 0, // Scroll up by 100px
      behavior: 'smooth' // Smooth scroll effect
    });
  };

  const scrollBottom = () => {
    scrollableDivRef.current?.scrollTo({
      top: scrollableDivRef.current.scrollHeight, // Scroll down by 100px
      behavior: 'smooth' // Smooth scroll effect
    });
  };

  useEffect(() => {
    if (searchParams.get('page')) {
      scrollToTop('note_scroll_top');
      setTimeout(() => {
        const parentElement = document.getElementById('notes-cover-scroll');
        const childElement = document.getElementById(`notes-no-${Number(searchParams.get('page')) + 1}`);
        if (parentElement && childElement) {
          const parentTop = parentElement.getBoundingClientRect().top;
          const childTop = childElement.getBoundingClientRect().top;
          parentElement.scrollTop += childTop - parentTop;
        }
      }, 1000);
    }
  }, [searchParams]);

  useEffect(() => {
    if (searchParams.get('page')) {
      const findIndex = allNotes?.noteDetails?.findIndex((item) => item.noteNo === (Number(searchParams.get('page')) + 1));
      if (findIndex > -1) {
        setNoteCardDetails(allNotes?.noteDetails[findIndex].notesDocsDetails || []);
      }
    }
  }, [searchParams, allNotes]);

  useEffect(() => {
    if (allNotes?.noteDetails?.length > 0) {
      const newNotes = allNotes?.noteDetails || [];

      // Combine current notes and new notes, ensuring no duplicates by checking noteNo
      const combinedNotes = [...notesData, ...newNotes].reduce((acc, note) => {
        if (!acc.find((existingNote) => existingNote.noteNo === note.noteNo)) {
          acc.push(note);
        }
        return acc;
      }, []);
      const sortedNotes = combinedNotes?.sort((a, b) => a.noteNo - b.noteNo);
      setNotesData(sortedNotes); // Set the combined, unique notes
      setNoteMore(allNotes?.noteDetails[0]?.noteNo);

      setTimeout(() => {
        const parentElement = document.getElementById('notes-cover-scroll');

        let childElementId;
        const noteDetailsLength = allNotes?.noteDetails?.length;

        if (search) {
          childElementId = `notes-no-${Number(search)}`;
        } else if (allNotes?.noteDetails?.length > 0) {
          childElementId = `notes-no-${Number(allNotes?.noteDetails[Number(noteDetailsLength) - 1]?.noteNo)}`;
        }

        const childElement = childElementId ? document.getElementById(childElementId) : null;

        if (parentElement && childElement) {
          childElement.scrollIntoView({
            behavior: 'smooth', // adds smooth scroll effect
            block: 'center' // aligns the element vertically in the center
          });
        }
      }, 300);
    } else {
      setNotesData([]);
    }
  }, [allNotes]);

  useEffect(() => {
    if (mergeLinkFiles) {
      const merged = mergeLinkFiles?.MERGING?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      const linked = mergeLinkFiles?.LINKED?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      setMergedOptions(merged);
      setLinkedOptions(linked);
    }
  }, [mergeLinkFiles]);

  useEffect(() => {
    if (childFiles) {
      const child = childFiles?.childFileNos?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      setChildFileOptions(child);
    }
  }, [childFiles]);

  useEffect(() => {
    if (selectedFiles?.length > 0) {
      setMergeLinkActive(true);
    } else {
      setMergeLinkActive(false);
    }
  }, [selectedFiles]);

  const handleFiles = (data, field) => {
    setSelected(field);
    setMergeLinkActiveId(data?.id);
    fetchDraft({ fileNo: data?.id, status: 'ALL' });
    setSelectedFiles([data]);
    setSelectedDropdown(field);
    fetchFileDocuments(data?.id);
  };

  const handleDeleteFiles = () => {
    setSelected('');
    setSelectedFiles([]);
    setMergeLinkActive(false);
    setMergeLinkActiveId(null);
    fetchFileDetails(params.fileNo);
    fetchDraft({ fileNo: params?.fileNo, status: 'ALL' });
    setSelectedDropdown(null);
    fetchFileDocuments(params?.fileNo);
  };

  useEffect(() => {
    if (mergeLinkActiveId || params.fileNo) {
      setNotesData([]);
      setActionTriggered({ loading: true, id: 'fetch-all-notes' });
      fetchAllNotes({
        fileNo: mergeLinkActiveId || params.fileNo, noteStatus: NOTE_STATUS.COMPLETED
      });
    }
  }, [params?.fileNo, mergeLinkActiveId]);

  const handleToggleExpand = () => {
    setNoteExpand(!noteExpand);
    if (!noteExpand) {
      scrollToTop('scroll-end');
    }
  };

  const handleRefNavigate = (event, data) => {
    event.stopPropagation();
    setNoteRefTrigger({
      noteNo: data?.noteOrApplicationNumber, refId: data?.id
    });
    setShowingAllDocs(false);
    setDocumentId({ docId: data?.content?.notesDocumentId || data?.content?.fileId, from: data?.content?.notesDocumentId ? 'note' : 'inward' });
    navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=0&ref=yes&page=${Number(data?.noteOrApplicationNumber) - 1}&doc=${data?.content?.notesDocumentId || data?.content?.fileId}`);
  };

  const handleScroll = () => {
    const div = scrollableDivRef.current;
    // Check if the user has scrolled to the bottom
    if (div.scrollTop === 0) {
      setIsAtEnd(true);
    } else {
      setIsAtEnd(false);
    }
  };

  const togglePara = () => {
    setSearch('');
    setShowParaEdit(true);
    setShowDrop(true);
    listCompletedNotes({ fileNo: mergeLinkActiveId || params.fileNo });
    setTimeout(() => {
      paraEdit.current.focus();
    }, 100);
  };

  const triggerParaSearch = (val) => {
    setActionTriggered({ loading: true, id: 'fetch-all-notes' });
    setSearch(String(val));
    setNotesData([]);
    fetchAllNotes({
      fileNo: mergeLinkActiveId || params.fileNo, fromNoteNo: val, toNoteNo: allNotes?.lastNoteNo
    });
    setShowParaEdit(false);
  };

  // const handleSelectNoteNo = (data) => {
  //   setSearch(String(data));
  //   setShowDrop(false);
  // };

  // const handleClear = () => {
  //   setSearch('');
  //   setShowDrop(false);
  //   setShowParaEdit(false);
  // };

  useEffect(() => {
    function handleClickOutside(event) {
      // Check if the click is outside of the referenced div
      if (dropRef.current && !dropRef.current.contains(event.target)) {
        setShowDrop(false); // Hide the div when clicked outside
        setShowParaEdit(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const loadMoreNotes = () => {
    setSearch('');
    setActionTriggered({ loading: true, id: 'fetch-all-notes' });
    fetchAllNotes({
      fileNo: mergeLinkActiveId || params.fileNo, fromNoteNo: (noteMore - 10) > 0 ? noteMore - 10 : 1, toNoteNo: noteMore - 1
    });
  };

  useEffect(() => {
    if (noteRefTrigger?.noteNo) {
      setNotesData([]);
      setActionTriggered({ loading: true, id: 'fetch-all-notes' });
      fetchAllNotes({
        fileNo: mergeLinkActiveId || params.fileNo, fromNoteNo: noteRefTrigger?.noteNo, toNoteNo: allNotes?.lastNoteNo
      });
      setSearch(noteRefTrigger?.noteNo);
    }
  }, [noteRefTrigger]);

  const noteContents = () => {
    return (
      <>
        <div className="flex-grow text-center pb-3">
          {isAtEnd && allNotes?.firstNoteNo < noteMore
            && (
              <button className={`rounded-xl px-5 text-[${primary}]`} style={{ border: `1px solid ${primary}` }} onClick={() => loadMoreNotes()}>
                Previous Notes : {(noteMore - 10) > 0 ? noteMore - 10 : 1} to {noteMore - 1}
              </button>
            )}
        </div>
        {
          notesData?.length === 0 ? (
            <div className="text-center min-h-[200px]">
              <div className="w-[150px] mx-auto m-10 py-10">
                <NoNotesIcon width="150px" />
              </div>
              {t('noNotesFound')}
            </div>
          ) : (
            <div className="relative w-full pr-3" id="notes-listing-scroll">
              <div
                className="absolute scroll-dots"
                style={{
                  top: '-15px', right: '9px', textAlign: 'center'
                }}
              >
                <IconButton
                  size="xs"
                  variant="unstyled"
                  onClick={() => scrollTop()}
                  minHeight="0"
                  minWidth="0"
                  icon={(
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 7L6 5L4 7" stroke="#00B2EC" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  )}
                />
              </div>

              <div
                ref={scrollableDivRef}
                onScroll={handleScroll}
                id="notes-cover-scroll"
                className="flex flex-col gap-3 rounded-xl"
                style={{
                  width: '100%',
                  borderBottom: '1px inset rgba(0, 0, 0, 0.2)',
                  maxHeight: noteExpand || (dragEnabled?.id === 'note' && dragEnabled?.status === true) || mergeLinkActiveId ? '740px' : '310px',
                  overflowY: 'auto',
                  paddingRight: '5px'
                }}
              >
                {notesData.map((item) => (
                  <div id={`notes-no-${item?.noteNo}`} key={item?.noteNo}>
                    <NoteCard
                      key={item.id}
                      item={item}
                      isOnSelectNote={isOnSelectNote}
                      setIsOnSelectNote={setIsOnSelectNote}
                      from={from === 'create-draft' ? 'create-draft' : 'note'}
                      attachments={<DocumentView notesDocsDetails={item?.notesDocsDetails} noteNo={item?.noteNo} from={from} setIsOpenDocumentModal={setIsOpenDocumentModal} isOpenDocumentModal={isOpenDocumentModal} />}
                      draftDetails={<DraftView draftDetails={item?.draftDetails} from="note" setDraftItems={setDraftItems} setOpen={setOpen} />}
                      noteReferences={<NoteReferences noteReferences={item?.noteReferences} handleRefNavigate={handleRefNavigate} />}
                      lastNote={allNotes?.lastNoteNo}
                    />
                  </div>
                ))}
              </div>

              <div
                className="absolute scroll-dots"
                style={{
                  bottom: '-6px', right: '5px', width: '20px', height: '20px', textAlign: 'center'
                }}
              >
                <IconButton
                  size="xs"
                  variant="unstyled"
                  minHeight="0"
                  minWidth="0"
                  onClick={() => scrollBottom()}
                  icon={(
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 5L6 7L8 5" stroke="#00B2EC" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  )}
                />
              </div>
            </div>

          )
        }
      </>

    );
  };

  return (
    <div className="relative">

      <div className="absolute mt-[-140px]" id="note_scroll_top" />
      <div className="flex gap-5" />
      {/* {!noteExpand && (
        <div className="flex gap-2 mb-2 pr-10">
          <div className="customFileDatePicker">
            <InputGroup style={styles.search}>
              <Input
                value={date}
                onChange={(event) => handleNoteDate(event.target.value)}
                type="date"
                placeholder="dsdsd"
                onfocus="(this.type='date')"
                onblur="(this.type='text')"
                style={styles.search.input}
              />
            </InputGroup>
          </div>
          <div className="flex-grow" />
        </div>
      )} */}
      <div className="flex gap-3 items-center pr-5 pb-4">
        <div className="text-[#09327B] text-[18px] font-semibold">{t('notes')}</div>

        {linkedOptions?.length > 0 && from !== 'create-draft' && (
          <SubFilesDropdown type="Linked" options={linkedOptions} icon={<LinkIcons />} handleActions={(data, type) => handleFiles(data, type)} disabled={selectedDropdown && selectedDropdown !== 'Linked'} />

        )}

        {mergedOptions?.length > 0 && from !== 'create-draft' && (
          <SubFilesDropdown type="Merged" options={mergedOptions} icon={<MergedIcons />} handleActions={(data, type) => handleFiles(data, type)} disabled={selectedDropdown && selectedDropdown !== 'Merged'} />

        )}

        {childFileOptions?.length > 0 && from !== 'create-draft' && (
          <SubFilesDropdown type="Child" options={childFileOptions} icon={<ChildFileIcons />} handleActions={(data, type) => handleFiles(data, type)} disabled={selectedDropdown && selectedDropdown !== 'Child'} />

        )}

        <div className="flex-grow">
          {selectedFiles.map((item) => (
            <Tags key={item.id} label={`${selected} ${item.name}`} value={item.fileNo} item={item} handleDelete={() => handleDeleteFiles()} from="note" />
          ))}
        </div>
        {/* <div className="flex-none">
          <Button
            variant="unstyled"
            color={primary}
          // onClick={togglePara}
          >
            Select Date
          </Button>
        </div> */}
        <Tooltip label={t('fullScreen')}>
          <IconButton
            variant="unstyled"
            onClick={() => handleFull()}
            leftIcon={<FullScreenIcon width="21px" height="21px" />}
          />
        </Tooltip>
        <div className="flex-none pr-[20px]">
          {!showParaEdit
            && (
              <Button
                variant="unstyled"
                color={primary}
                ref={goToRef}
                onClick={togglePara}
              >
                Go to
              </Button>
            )}

          <div className={`${showParaEdit ? 'block' : 'hidden'} relative w-[100px]`}>
            <div className="flex border rounded-lg">
              <input
                className="w-[100px] rounded-lg px-3"
                ref={paraEdit}
                type="text"
                value={search}
                onChange={(event) => {
                  setSearch(event.target.value);
                  setShowDrop(true);
                }}
                onClick={() => setShowDrop(true)}
              />
              {/* <IconButton onClick={() => handleClear()} variant="unstyled" size="xs" icon={<CloseOutlineIcon />} />
              <IconButton bg={primary} size="xs" icon={<DoneIcon color="#fff" />} onClick={() => triggerParaSearch()} /> */}
            </div>
            {showDrop
              && (
                <div className="shadow-lg rounded-lg py-5 mb-5 absolute w-[calc(100%-3px)] max-h-[150px] z-20 t-0 r-0 l-0 block bg-white" ref={dropRef}>
                  <ul className="max-h-[100px] w-full overflow-y-scroll pl-5 pr-1">
                    {completedNotes?.length > 0 && completedNotes?.filter((item) => String(item?.noteNo)?.toLowerCase()?.includes(search?.toLowerCase()))?.map(((item) => (
                      <li
                        key={item?.noteId}
                        aria-hidden
                        onClick={() => triggerParaSearch(item?.noteNo)}
                        className="p-1 cursor-pointer text-center"
                      >{item?.noteNo}
                      </li>
                    )))}
                  </ul>
                </div>
              )}
          </div>

        </div>

        {from !== 'create-draft' && (
          <div className="flex-none">
            <Button variant="unstyled" color={primary} rightIcon={<DownArrow color={primary} className={!noteExpand ? 'rotate-0' : 'rotate-180'} />} onClick={handleToggleExpand}> {!noteExpand ? t('expand') : t('collapse')} </Button>
          </div>
        )}

      </div>
      <div className="col-span-12">
        {
          actionTriggred?.loading && actionTriggred?.id === 'fetch-all-notes' ? (
            <div className="w-full flex justify-center p-20">
              <Spinner style={{ marginTop: '20px', marginLeft: '-90px' }} />
            </div>
          ) : (
            <>
              {
                noteContents()
              }
            </>
          )
        }
        {
          openModal
          && (
          <NoteExpansionModel
            openModal={openModal}
            handleClose={handleClose}
            showParaEdit={showParaEdit}
            goToRef={goToRef}
            togglePara={togglePara}
            paraEdit={paraEdit}
            search={search}
            setSearch={setSearch}
            setShowDrop={setShowDrop}
            showDrop={showDrop}
            dropRef={dropRef}
            completedNotes={completedNotes}
            triggerParaSearch={triggerParaSearch}
            allNotes={allNotes}
            loadMoreNotes={loadMoreNotes}
            noteMore={noteMore}
            actionTriggred={actionTriggred}
            notesData={notesData}
            isOnSelectNote={isOnSelectNote}
            setIsOnSelectNote={setIsOnSelectNote}
            setDraftItems={setDraftItems}
            setOpen={setOpen}
            handleRefNavigate={handleRefNavigate}
            from={from}
            noteCardDetails={noteCardDetails}
          />
          )
        }
      </div>

      {fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.CLOSED && fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.HOLD && !mergeLinkActive && postIdByPenNoDetails?.includes(fileDetails?.postId) && from !== 'create-draft'
        && (
          <div className="pr-3 mt-5">
            <CreateNotes
              showAttachment={showAttachment}
              setShowAttachment={setShowAttachment}
              fileDetails={fileDetails}
              from="create-note"
            />
          </div>
        )}
      <DraftNewPreview
        open={open || openRef.current}
        close={close}
        draftItems={draftItems}
        fileDetails={fileDetails}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  noteId: getSavedNoteId,
  allNotes: getAllNotes,
  mergeLinkFiles: getMergeLinkFiles,
  mergeLinkActive: getMergeLinkActive,
  mergeLinkActiveId: getMergeLinkActiveId,
  childFiles: getChildFiles,
  noteExpand: getNoteExpand,
  dragNoteEnabled: getDragEnabled,
  userInfo: getUserInfo,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  noteRefTrigger: getNoteRefTrigger,
  documentId: getDocumentId,
  completedNotes: getCompletedNotes,
  actionTriggred: getActionTriggered,
  isOnSelectNote: getIsOnSelectNote,
  noteCardDetails: getNoteCardDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchAllNotes: (data) => dispatch(actions.fetchAllNotes(data)),
  fetchDraft: (data) => dispatch(actions.fetchDraft(data)),
  fetchMergeLink: (data) => dispatch(actions.fetchMergeLink(data)),
  setMergeLinkActive: (data) => dispatch(sliceActions.setMergeLinkActive(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setMergeLinkActiveId: (data) => dispatch(sliceActions.setMergeLinkActiveId(data)),
  fetchFileDocuments: (data) => dispatch(actions.fetchFileDocuments(data)),
  setMergedBackButton: (data) => dispatch(sliceActions.setMergedBackButton(data)),
  fetchChildFiles: (data) => dispatch(actions.fetchChildFiles(data)),
  setNoteExpand: (data) => dispatch(sliceActions.setNoteExpand(data)),
  setNotes: (data) => dispatch(sliceActions.setNotes(data)),
  deleteDocuments: (data) => dispatch(actions.deleteDocuments(data)),
  setNoteRefTrigger: (data) => dispatch(sliceActions.setNoteRefTrigger(data)),
  setDocumentNameFromNoteReferences: (data) => dispatch(commonSliceActions.setDocumentNameFromNoteReferences(data)),
  setDocumentId: (data) => dispatch(commonSliceActions.setDocumentId(data)),
  setNoteCardDetails: (data) => dispatch(commonSliceActions.setNoteCardDetails(data)),
  listCompletedNotes: (data) => dispatch(actions.listCompletedNotes(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setShowingAllDocs: (data) => dispatch(sliceActions.setShowingAllDocs(data)),
  setIsOnSelectNote: (data) => dispatch(sliceActions.setIsOnSelectNote(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ListNotes);
