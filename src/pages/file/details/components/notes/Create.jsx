/* eslint-disable jsx-a11y/no-static-element-interactions */
import { connect } from 'react-redux';
import './style.css';
import {
  <PERSON><PERSON>, t, FormController,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  <PERSON>dalFooter,
  Tooltip,
  RichLabel
} from 'common/components';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useState, useEffect, useRef } from 'react';
import { createStructuredSelector } from 'reselect';
import AddCircle from 'assets/addCircle';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered, getNoteDetails, getUserInfo } from 'pages/common/selectors';
import { DRAFT_STATUS, NOTE_STATUS } from 'pages/common/constants';
import * as commonActions from 'pages/common/actions';
import CloseOutlineIcon from 'assets/CloseOutline';
import { AddIconWithCircle } from 'assets/Svg';
import {
  dark, light, primary, secondary
} from 'utils/color';
import AttachmentIcon from 'assets/Attachment';
import { profileColor, profileTextColor } from 'common/components/NotesCard/NoteCard';
import ProfilePic from 'assets/ProfilePic';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import DocumentPreview from 'common/components/DocumentPreview';
import DocumentView from 'pages/common/components/DocumentView';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { getBackgroundColorForDrafts, getColorForDrafts } from 'pages/common/helper';
import DraftApprovedIcon from 'assets/DraftApprovedIcon';
import CloseNew from 'assets/CloseNew';
import { API_URL } from 'common';
import { baseApiURL } from 'utils/http';
import CloseSolidIcon from 'assets/CloseSolid';
import { actions as sliceActions } from '../../slice';
import * as actions from '../../actions';

import { NotesSchema } from '../../validate';
import {
  getCompletedNotes,
  getCompletedNotesDocuments,
  getDraftExistsOrNot,
  getDraftList,
  getDragEnabled,
  getMergeLinkActive,
  getNotes,
  getPartialNotes,
  getSavedNote,
  getSavedNoteId,
  getService
} from '../../selector';

import AddDocument from './AddDocument';
import DraftNewPreview from './draft/DraftNewPreview';

const styles = {
  floatingButton: {
    height: '40px',
    width: '40px',
    borderRadius: '100%',
    fontWeight: '600',
    fontSize: '14px',
    background: '#00b2ec',
    display: 'flex',
    justifyContent: 'center'
  }
};

const CreateNotes = (props) => {
  const {
    saveNote,
    showAttachment,
    setShowAttachment,
    mergeLinkActive,
    updatedNote,
    notes,
    fetchPartialNotes,
    partialNotes,
    userInfo,
    deleteNoteDocuments,
    saveNoteWithoutDoc,
    actionTriggered,
    setActionTriggered,
    listCompletedNotes,
    listCompletedNotesDocuments,
    completedNotes,
    completedNotesDocuments,
    setAlertAction,
    deleteNoteRef,
    fileDetails,
    setNoteRefTrigger,
    from = null,
    setDocumentId,
    setDraftFilterActiveIndex
    // draftExistsOrNot
    // setIsOneDocumentSelect = () => { }
  } = props;

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm({
    mode: 'all',
    defaultValues: {
      notes: '',
      documentAttached: false
    },
    resolver: yupResolver(NotesSchema)
  });

  const params = useParams();
  const navigate = useNavigate();
  const [previewDocuments, setPreviewDocuments] = useState([]);
  const [selectedNoteRef, setSelectedNoteRef] = useState(null);
  const [selectedNoteDocumentRef, setSelectedNoteDocumentRef] = useState(null);
  const [openRefMenu, setOpenRefMenu] = useState(false);
  const [openRefForm, setOpenRefForm] = useState(false);
  const [isOpenDocumentModal, setIsOpenDocumentModal] = useState(false);

  const [open, setOpen] = useState(false);
  const [draftItems, setDraftItems] = useState();
  const openRef = useRef(open);

  const close = () => {
    setOpen(false);
    openRef.current = false;
  };

  useEffect(() => {
    openRef.current = open;
  }, [open]);

  const onHandleOk = () => {
    setAlertAction({ open: false });
    setTimeout(() => {
      scrollToTop('note-draft-top-scroll');
    }, 1000);
    setDraftFilterActiveIndex(1);
    navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
  };

  const handleRedirect = async (url) => {
    const draftExistResponse = await (fetch(`${baseApiURL}/${API_URL.INBOX.DRAFT_EXISTS_OR_NOT.replace(':fileNo', params?.fileNo)}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`
      }
    }));

    const response = await (fetch(`${baseApiURL}/${API_URL.INBOX.FETCH_PENDING_DRAFT_NO.replace(':fileNo', params?.fileNo)}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`
      }
    }));

    const res = await response.json();
    const existRes = await draftExistResponse.json();
    setAlertAction({ open: false });

    if (existRes?.payload === true) {
      const draftNoResult = res?.payload?.length > 1 ? res?.payload?.join(', ') : res?.payload?.[0];

      setAlertAction({
        open: true,
        variant: 'alert',
        message: !draftNoResult || res?.error ? `${t('pendingDraftExists')}, Draft No(s) : Error` : `${t('pendingDraftExists')}, Draft No(s) : ${draftNoResult}`,
        title: t('confirmation'),
        forwardActionText: t('ok'),
        forwardAction: () => onHandleOk(),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else {
      setAlertAction({ open: false });
      setTimeout(() => {
        navigate(`/ui/${url}`);
      }, 300);
    }
  };

  const onSubmitNote = (data, event) => {
    const buttonValue = event.nativeEvent.submitter.value;
    if (previewDocuments.length > 0 || data.notes) {
      setActionTriggered({ loading: true, id: 'save-note' });
      const note = {
        request: {
          fileNo: params.fileNo,
          notes: data?.notes?.replace(/(<p[^>]*>)\s+|\s+(<\/p>)/g, '$1$2') || ''
        },
        docs: [],
        saveButton: from === 'summary' ? null : buttonValue,
        handleRedirect
      };
      previewDocuments.map((previewDoc) => {
        note.docs.push(previewDoc.docData);
        return true;
      });
      if (previewDocuments.length > 0) {
        saveNote(note);
      } else {
        const noteParams = {
          fileNo: params.fileNo,
          notes: data?.notes?.replace(/(<p[^>]*>)\s+|\s+(<\/p>)/g, '$1$2') || '',
          saveButton: from === 'summary' ? null : buttonValue,
          handleRedirect
        };
        // console.log('>>>1>', noteParams);
        saveNoteWithoutDoc(noteParams);
      }
      setPreviewDocuments([]);
      setShowAttachment(false);
      setValue('documentAttached', false);
    }
  };

  useEffect(() => {
    if (notes !== updatedNote) {
      setValue('notes', updatedNote?.replace(/(<p[^>]*>)\s+|\s+(<\/p>)/g, '$1$2'));
    }
  }, [updatedNote]);

  // useEffect(() => {
  //   if (!_.isEmpty(partialNotes)) {
  //     setValue('notes', partialNotes?.notes);
  //   }
  // }, [partialNotes]);

  useEffect(() => {
    const notesSearchRequest = {
      fileNo: params?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      fetchPartialNotes(notesSearchRequest);
    }
  }, []);

  useEffect(() => {
    if (params?.fileNo) {
      listCompletedNotes({ fileNo: params?.fileNo });
      // setIsOneDocumentSelect('');
    }
  }, [params?.fileNo]);

  useEffect(() => {
    if (completedNotes?.length > 0) {
      setSelectedNoteRef(completedNotes[0].noteId);
      listCompletedNotesDocuments({ noteId: completedNotes[0].noteId });
    }
  }, [completedNotes]);

  const onHandleRemove = (data) => {
    const sendData = {
      params: {
        notesId: data?.content?.notesId,
        noteDocumentId: data?.content?.notesDocumentId
      },
      notesSearchRequest: {
        fileNo: params?.fileNo,
        assigner: userInfo?.assigner,
        noteStatus: NOTE_STATUS.PARTIAL
      }
    };
    deleteNoteDocuments(sendData);
  };

  const addRefNote = () => {
    if (!selectedNoteRef) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: 'Please select reference note',
        title: t('confirmation'),
        backwardActionText: t('ok'),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else {
      const find = completedNotesDocuments?.find((item) => item?.documentGeneratedName === selectedNoteDocumentRef
        || item?.docsRequest?.documentName === selectedNoteDocumentRef
        || item?.noteDocumentsId === selectedNoteDocumentRef);
      let requestPayload = {};
      if (find?.isInward === true) {
        requestPayload = {
          fileNo: params.fileNo,
          notes: watch('notes')?.replace(/>\s+</g, '><').trim() || '',
          noteReferenceRequest: [
            {
              referenceNoteId: selectedNoteRef,
              // referenceDocumentId: selectedNoteDocumentRef,
              referenceApplicantDetailsId: find?.applicantDetailsId,
              docsRequest: find?.docsRequest,
              isInward: find?.isInward
            }
          ]
        };
      } else {
        requestPayload = {
          fileNo: params.fileNo,
          notes: watch('notes')?.replace(/(<p[^>]*>)\s+|\s+(<\/p>)/g, '$1$2') || '',
          noteReferenceRequest: [
            {
              referenceNoteId: selectedNoteRef,
              referenceDocumentId: selectedNoteDocumentRef,
              // referenceApplicantDetailsId: find?.applicantDetailsId,
              docsRequest: find?.docsRequest,
              isInward: find?.isInward
            }
          ]
        };
      }
      // console.log('>>>2>', watch('notes')?.replace(/(<p[^>]*>)\s+|\s+(<\/p>)/g, '$1$2'), requestPayload);
      saveNoteWithoutDoc(requestPayload);
    }
  };

  const handleConfirmDelete = (data, event) => {
    event.stopPropagation();
    setAlertAction({
      open: true,
      variant: 'alert',
      message: 'Please confirm to delete attachment',
      title: t('confirmation'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      forwardAction: () => onHandleRemove(data)
    });
  };

  const handleRemoveRef = (data) => {
    deleteNoteRef({
      fileNo: params?.fileNo,
      notesId: partialNotes?.notesId,
      referenceId: data?.id
    });
  };

  const handleConfirmDeleteRef = (data) => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: 'Do you want to delete?',
      title: t('confirmation'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      forwardAction: () => handleRemoveRef(data),
      closeOnOverlayClick: false,
      closeOnEsc: false
    });
  };

  const handleRefNavigate = (data) => {
    setNoteRefTrigger({
      noteNo: data?.noteOrApplicationNumber, refId: data?.id
    });
    setDocumentId({ docId: data?.content?.notesDocumentId || data?.content?.fileId, from: data?.content?.notesDocumentId ? 'note' : 'inward' });
    navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=0&ref=yes&page=${Number(data?.noteOrApplicationNumber) - 1}&doc=${data?.content?.notesDocumentId || data?.content?.fileId}`);
  };

  const handleDraftRedirect = (data) => {
    const newDraftItems = { id: data?.draftId, ...data };
    setDraftItems(newDraftItems);
    setOpen(true);
    // navigate(`${BASE_PATH}/file/${params?.fileNo}/draft/${data?.draftId}?from=note`);
  };

  const [dynamicHeight, setDynamicHeight] = useState(300);
  const isDragging = useRef(false);
  const startY = useRef(0);
  const startHeight = useRef(300);

  const handleMouseUp = () => {
    isDragging.current = false;
    // document.removeEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (!isDragging.current) return;
    const diff = startY.current - e.clientY; // Reverse the calculation
    const newHeight = startHeight.current + diff;
    if (newHeight >= 100) { // Optional: Set a minimum height limit
      setDynamicHeight(newHeight);
    }
  };

  const handleMouseDown = (e) => {
    isDragging.current = true;
    startY.current = e.clientY;
    startHeight.current = dynamicHeight;
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  useEffect(() => {
    const localNote = JSON.parse(localStorage.getItem('notes-unsaved')) || '';
    if (localNote?.fileNo === params?.fileNo) {
      if (partialNotes?.notes) {
        if (partialNotes?.notes !== localNote?.note) {
          setValue('notes', localNote?.note);
        } else {
          setValue('notes', partialNotes?.notes);
        }
      } else {
        setValue('notes', localNote?.note);
      }
    } else
      if (partialNotes?.notes) {
        setValue('notes', partialNotes?.notes);
      }
  }, [partialNotes, params?.fileNo]);

  return (
    <>
      <form id="notes-form" onSubmit={handleSubmit(onSubmitNote)}>
        {partialNotes?.autoNotes && partialNotes?.autoNotes?.length !== 0
          && (

            <div className="flex gap-5 mb-3 rounded-lg p-4 bg-white" style={{ border: '1px solid #E8ECEE' }}>
              <div className="flex-none">
                <div className="rounded-full w-12 h-12 p-3" style={{ background: profileColor(fileDetails?.role) }}><ProfilePic color={profileTextColor(fileDetails?.role)} /></div>
              </div>
              <div className="flex-grow">
                <h4 className="font-semibold text-[14px]">{partialNotes?.assignerName} , {partialNotes?.designation}</h4>
                <div className="w-full">
                  {partialNotes?.autoNotes?.map((item) => {
                    const formatedNote = item?.autoNote?.replace(/^\s*<br\s*(\/)?>/i, '');
                    const formatedNote2 = formatedNote?.replace('<p style="line-height: 2"><br>', '<p style="line-height: 2">');
                    return (
                      <div className="list-none flex items-center justify-between text-[12px] flex-grow text-[#323232] pt-[5px]">
                        <RichLabel value={formatedNote2} className="whitespace-pre-wrap" />

                        {item?.draftDetails && <Button className="rounded-lg px-3 py-2 items-center font-semibold relative" style={{ color: getColorForDrafts(item?.draftDetails?.draftStage), background: getBackgroundColorForDrafts(item?.draftDetails?.draftStage) }} leftIcon={item?.draftDetails?.draftStage === DRAFT_STATUS.APPROVED ? <DraftApprovedIcon /> : <AttachmentIcon width="18px" height="18px" />} onClick={() => handleDraftRedirect(item?.draftDetails)}>  Draft {item?.draftDetails?.draftNo}</Button>}
                        {/* <li key={item?.autoNote} className="list-none mt-2 flex items-center justify-between text-[12px]"> <span>{item?.autoNote}</span> </li> */}
                      </div>
                    );
                  })}

                </div>
              </div>
            </div>
          )}
        <div
          className={`notes-edit col-span-12 full ${partialNotes?.autoNotes?.length > 0 && 'mt-[-20px] have-partial'}`}
        >
          <FormController
            name="notes"
            type="rich"
            placeholder={t('notes')}
            control={control}
            errors={errors}
            disabled={mergeLinkActive}
            required
            id="notes-editor"
            height={`${dynamicHeight}px`}
            onClick={() => { }}
            handleChange={(data) => {
              if (data.replace(/>\s+</g, '><').trim() === '<p style="line-height: 2"></p>') {
                localStorage.removeItem('notes-unsaved');
                setValue('notes', '');
              } else {
                const noteSave = {
                  fileNo: params?.fileNo,
                  note: data?.replace(/(<p[^>]*>)\s+|\s+(<\/p>)/g, '$1$2')
                };
                localStorage.setItem('notes-unsaved', JSON.stringify(noteSave));
              }
            }}
            toolbar={{
              image: false,
              fontSize: true,
              lineHeight: true
            }}
            tableResize
            footer={(
              <div>
                <div className="w-full">
                  <div className="flex flex-wrap gap-5 px-3 items-center mb-5" style={{ color: primary }}>
                    {partialNotes?.noteReferences?.length > 0 && (
                      <div className="flex-none">
                        Ref. Note:
                      </div>
                    )}
                    {partialNotes?.noteReferences?.map((item) => (
                      <div className="flex items-center" size="sm" key={item.name}>
                        <Button onClick={() => handleRefNavigate(item)} variant="link" style={{ color: primary, height: '20px' }}>Note: {item?.noteOrApplicationNumber}{item?.documentName && ', Doc.:'} {item?.documentName}</Button>
                        <IconButton style={{ height: '20px' }} variant="unstyled" onClick={() => handleConfirmDeleteRef(item)} icon={<CloseOutlineIcon strokeWidth="3px" color={secondary} />} />
                      </div>
                    ))}
                  </div>
                  <div className="flex flex-wrap gap-3 px-3">
                    <DocumentView
                      notesDocsDetails={partialNotes?.notesDocsDetails}
                      from={from}
                      setIsOpenDocumentModal={setIsOpenDocumentModal}
                      isOpenDocumentModal={isOpenDocumentModal}
                      handleConfirmDelete={handleConfirmDelete}
                      enableDelete
                    />
                  </div>
                </div>
                <div className="p-3 flex gap-3 items-center w-full">
                  <div className="flex-none">
                    <Button variant="unstyled" style={openRefMenu ? styles.floatingButton : {}} onClick={() => setOpenRefMenu(!openRefMenu)}> {openRefMenu ? <CloseOutlineIcon height="18px" width="18px" /> : <AddIconWithCircle height="45px" width="50px" />} </Button>
                    {openRefMenu
                      && (
                        <div className="floating-note-box drop-shadow">
                          <div className="floating-note-actions">
                            <Button variant="unstyled" onClick={() => { setShowAttachment(true); setOpenRefMenu(false); }}>Attach Document</Button>
                            <Button variant="unstyled" onClick={() => { setOpenRefForm(true); setOpenRefMenu(false); }}>Add Reference</Button>
                          </div>
                        </div>
                      )}
                  </div>
                  {openRefForm && (
                    <div className="flex gap-2 border rounded-lg p-2 items-center bg-[#E7EFF5] h-[48px]">
                      <div className="flex-none">
                        Ref. Note
                      </div>
                      <div className="flex-none">
                        <select className="border bg-[#FFFFFF] rounded-[4px] focus:outline-none w-[120px] h-[34px] pl-3 pr-6" value={selectedNoteRef} onChange={(event) => { setSelectedNoteRef(event.target.value); setSelectedNoteDocumentRef(null); listCompletedNotesDocuments({ noteId: event.target.value }); }}>
                          {completedNotes?.map((item) => <option key={item?.nodeId} value={item?.noteId}>{item?.noteNo}</option>)}
                        </select>
                      </div>
                      <div className="flex-none">
                        <select className="border bg-[#FFFFFF] rounded-[4px] focus:outline-none w-[200px] h-[34px] pl-3 pr-6" value={selectedNoteDocumentRef} onChange={(event) => { setSelectedNoteDocumentRef(event.target.value); }}>
                          <option value="">--select a option--</option>
                          {completedNotesDocuments?.map((item) => <option key={item?.noteDocumentsId} value={item?.noteDocumentsId}>{item?.documentGeneratedName}</option>)}
                        </select>
                      </div>
                      <div className="flex-none">
                        <Button size="xs" variant="primary_outline" onClick={() => addRefNote()} style={{ width: '48px', height: '32px' }} icon={<AddCircle color="#2d3748" />}>Add</Button>
                      </div>
                      <div className="flex-none relative">
                        <IconButton onClick={() => setOpenRefForm(false)} variant="unstyled" style={{ position: 'absolute', top: '-46px' }} icon={<CloseSolidIcon color={secondary} />} />
                      </div>
                    </div>
                  )}
                  <div className="flex-grow" />

                  <div className="flex-none">
                    <Button
                      size="xs"
                      type="submit"
                      variant="secondary_outline"
                      style={{ padding: '10px 20px' }}
                      form="notes-form"
                      isDisabled={mergeLinkActive}
                      isLoading={actionTriggered?.id === 'save-note' && actionTriggered?.loading}
                      value="main-submit"
                    >
                      {t('save')}
                    </Button>
                  </div>
                </div>
              </div>
            )}
            dragEnabled={from !== 'summary'}
            header={
              (
                <div className="flex justify-center pt-1 pb-3">
                  <div className="w-[100px] rounded-lg h-[7px] bg-[#E7EFF5] cursor-ns-resize" onMouseDown={handleMouseDown} />
                </div>
              )
            }
          />
        </div>

      </form>

      <Modal isOpen={isOpenDocumentModal} size="3xl" onClose={() => setIsOpenDocumentModal(!isOpenDocumentModal)} closeOnOverlayClick={false} closeOnEsc={false}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader p={0} className="text-center" style={{ background: light, color: dark }}>
            <div className="flex">
              <div className="flex-none">
                <h4 size="md" className="p-5 rounded-t-lg">
                  {t('document')}
                </h4>
              </div>
              <div className="flex-grow" />
              <div className="flex-none justify-end pt-[15px] pr-[15px]">
                <Tooltip label={t('close')}>
                  <IconButton variant="unstyled" onClick={() => { setIsOpenDocumentModal(!isOpenDocumentModal); }} leftIcon={<CloseNew />} />
                </Tooltip>
              </div>
            </div>
          </ModalHeader>
          <ModalBody>
            <DocumentPreview preview={partialNotes?.notesDocsDetails} from="summary" />
          </ModalBody>
          <ModalFooter style={{ background: light, color: dark }}>
            <div className="w-full text-right space-x-4">
              <Button
                variant="secondary_outline"
                size="sm"
                onClick={() => setIsOpenDocumentModal(!isOpenDocumentModal)}
              >
                {t('close')}
              </Button>

            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <AddDocument
        showAttachment={showAttachment}
        setShowAttachment={setShowAttachment}
        fileNo={params?.fileNo}
        noteId={partialNotes?.noteId}
        notes={watch('notes')}
      />

      <DraftNewPreview
        open={openRef.current || open}
        close={close}
        draftItems={draftItems}
        fileDetails={fileDetails}
      />

    </>
  );
};
const mapStateToProps = createStructuredSelector({
  notes: getNotes,
  savedNote: getSavedNote,
  noteId: getSavedNoteId,
  service: getService,
  mergeLinkActive: getMergeLinkActive,
  updatedNote: getNoteDetails,
  partialNotes: getPartialNotes,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  dragEnabled: getDragEnabled,
  completedNotes: getCompletedNotes,
  completedNotesDocuments: getCompletedNotesDocuments,
  draftList: getDraftList,
  draftExistsOrNot: getDraftExistsOrNot
});

const mapDispatchToProps = (dispatch) => ({
  fetchNotes: (data) => dispatch(actions.fetchNotes(data)),
  saveNoteWithoutDoc: (data) => dispatch(actions.saveNoteWithoutDoc(data)),
  saveNote: (data) => dispatch(actions.saveNote(data)),
  deleteNoteDocuments: (data) => dispatch(commonActions.deleteNoteDocuments(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  setDragEnabled: (data) => dispatch(sliceActions.setDragEnabled(data)),
  listCompletedNotes: (data) => dispatch(actions.listCompletedNotes(data)),
  listCompletedNotesDocuments: (data) => dispatch(actions.listCompletedNotesDocuments(data)),
  deleteNoteRef: (data) => dispatch(actions.deleteNoteRef(data)),
  setNoteRefTrigger: (data) => dispatch(sliceActions.setNoteRefTrigger(data)),
  setDocumentId: (data) => dispatch(commonSliceActions.setDocumentId(data)),
  setNoteCardDetails: (data) => dispatch(commonSliceActions.setNoteCardDetails(data)),
  fetchDraft: (data) => dispatch(actions.fetchDraft(data)),
  setDraftFilterActiveIndex: (data) => dispatch(sliceActions.setDraftFilterActiveIndex(data)),
  setIsOneDocumentSelect: (data) => dispatch(commonSliceActions.setIsOneDocumentSelect(data))

});
export default connect(mapStateToProps, mapDispatchToProps)(CreateNotes);
