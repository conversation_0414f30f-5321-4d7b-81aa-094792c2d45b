import _ from 'lodash';
import { DRAFT_FILTERS } from '../constants';

const applicantName = (data) => {
  if (data?.length > 0) {
    const fullName = data[0]?.applicantDetailsAddress?.length > 0 ? data[0]?.applicantDetailsAddress[0]?.firstName : '';
    const middleName = data[0]?.applicantDetailsAddress?.length > 0 ? data[0]?.applicantDetailsAddress[0]?.middleName : '';
    const lastName = data[0]?.applicantDetailsAddress?.length > 0 ? data[0]?.applicantDetailsAddress[0]?.lastName : '';
    const institutionName = data[0]?.applicantDetailsAddress?.length > 0 ? data[0]?.applicantDetailsAddress[0]?.institutionName : '';
    return (
      institutionName || <> {fullName || ''} {middleName || ''} {lastName || ''} </>
    );
  } return '';
};

const applicantNameTable = (data) => {
  if (data) {
    const fullName = data?.applicantDetailsAddress?.length > 0 ? data?.applicantDetailsAddress[0]?.firstName : '';
    const lastName = data?.applicantDetailsAddress?.length > 0 ? data?.applicantDetailsAddress[0]?.lastName : '';
    const institutionName = data?.applicantDetailsAddress?.length > 0 ? data?.applicantDetailsAddress[0]?.institutionName : '';

    return (
      institutionName || <> {fullName || ''} {lastName || ''} </>
    );
  } return '';
};

const formatApplicantNameTable = (data) => {
  const listData = data?.row;
  let fullName;
  let lastName;
  let middleName;
  let institutionName;

  if (listData?.firstName !== undefined) {
    fullName = listData?.firstName;
  }
  if (listData?.lastName !== undefined) {
    lastName = listData?.lastName;
  }
  if (listData?.middleName !== undefined) {
    middleName = listData?.middleName;
  }
  if (listData?.institutionName !== undefined) {
    institutionName = listData?.institutionName;
  }

  return (
    institutionName || <> {fullName || ''} {middleName || ''} {lastName || ''} </>);
};

const formatAddressDetails = (data) => {
  const fieldData = data?.row;
  return (
    <>

      <div className="flex flex-row justify-start items-left">
        {fieldData?.houseName
          && (
            <span className="text-base not-italic font-normal opacity-80 capitalize  text-[#3C4449] pr-2">
              <strong> {fieldData?.houseName}</strong>
            </span>
          )}
        {fieldData?.street
          && (
            <span className="text-base not-italic font-normal opacity-80 text-[#3C4449] pr-1">
              {fieldData?.houseName && <strong>|  </strong>} {fieldData?.street}
            </span>
          )}
      </div>
      <div className="flex flex-row justify-start items-left">
        {fieldData?.localPlace
          && (
            <span className="text-base not-italic font-normal opacity-80 text-[#3C4449] pr-2">
              {fieldData?.localPlace}
            </span>
          )}
        {fieldData?.mainPlace
          && (
            <span className="text-base not-italic font-normal opacity-80 text-[#3C4449] pr-1">
              {fieldData?.localPlace && <strong>| </strong>} {fieldData?.mainPlace}
            </span>
          )}
      </div>
      {fieldData?.postOffice
        && (
          <div className="flex flex-row justify-start items-left">
            <span className="text-xs not-italic font-normal opacity-80 text-[#3C4449] pr-2">
              <strong>  {fieldData?.postOfficeName}</strong>
            </span>

            <span className="text-xs not-italic font-normal opacity-80 text-[#3C4449] pr-1">
              <strong> - </strong> {fieldData?.pincode}
            </span>
          </div>
        )}
    </>
  );
};

const formatModeOfDispatchDetails = (data, modeOfDispatchArray) => {
  let modeOfDispatch;
  if (data?.row) {
    const cellData = data?.row;

    modeOfDispatch = (
      <div className="text-[14px] font-[400] max-w-[150px] break-keep">{_.filter(modeOfDispatchArray, (el) => el?.id === cellData?.modeOfDispatch)[0]?.name}</div>
    );
  }
  return <div className="block">{modeOfDispatch}</div>;
};

const formatOfficeTypeDetails = (data, officeTypeArray) => {
  let officeType;
  if (data?.row) {
    const cellData = data?.row;

    officeType = (
      <div className="text-[14px] font-[400] max-w-[150px] break-keep">{_.filter(officeTypeArray, (el) => el?.id === cellData?.officeType)[0]?.name}</div>
    );
  }
  return <div className="block">{officeType}</div>;
};

const formatDispatchSectionDetails = (data, dispatchSectionArray) => {
  let dispatchSection;
  if (data?.row) {
    const cellData = data?.row;

    dispatchSection = (
      <div className="text-[14px] font-[400] max-w-[150px] break-keep">{_.filter(dispatchSectionArray, (el) => el?.id === cellData?.functionalGroupId)[0]?.name}</div>
    );
  }
  return <div className="block">{dispatchSection}</div>;
};

const handleFileRole = (data) => {
  switch (data) {
    case 'ROUTE_CHANGE_ROLE':
      return 'Route Change';
    case 'ROUTE_CHANGE':
      return 'Route Change';
    case 'RECOMMENDING_OFFICER':
      return 'Recommending Officer';
    case 'ENQUIRY_OFFICER':
      return 'Enquiry Officer';
    case 'OPERATOR':
      return 'Operator';
    case 'APPROVER':
      return 'Approver';
    case 'VERIFIER':
      return 'Verifier';
    case 'FORWARD_PLUS_ROLE':
      return 'Forward Plus';
    default:
      return data;
  }
};

const handleFileStage = (data) => {
  switch (data) {
    case 'APPLIED':
      return 'Applied';
    case 'REJECTED':
      return 'Rejected';
    case 'PULLED_BACK':
      return 'Pull Back';
    case 'APPROVED':
      return 'Approved';
    case 'RETURN_TO_CUSTODIAN':
      return 'Return to Custodian';
    case 'ENQUIRY':
      return 'Enquiry';
    case 'FOR_DISPOSAL':
      return 'For Disposal';
    case 'ROUTE_CHANGE':
      return 'Route Change';
    case 'FORWARDED_BY_ADMINISTRATOR':
      return 'Forwarded by Administrator';
    case 'INITIATED':
      return 'Initiated';
    case 'VERIFIED':
      return 'Verified';
    case 'RECOMMENDED':
      return 'Recommended';
    case 'RETURNED':
      return 'Returned';
    case 'DISPOSED':
      return 'Disposed';
    case 'FORWARD_PLUS':
      return 'Forward Plus';
    default:
      return data;
  }
};

const getNameById = (id) => {
  const filter = DRAFT_FILTERS.find((item) => item.id === id);
  return filter ? filter.name : '';
};

export {
  applicantName, applicantNameTable, formatApplicantNameTable, formatAddressDetails,
  formatModeOfDispatchDetails, formatOfficeTypeDetails, formatDispatchSectionDetails,
  handleFileRole, handleFileStage, getNameById
};
