import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  CardWithHeader,
  TinyBox,
  ksmThemeStyles,
  FormLabel,
  t
} from 'common/components';
import * as actions from '../../actions';
import { getPendingActions } from '../../selector';

const { colors } = ksmThemeStyles;

const pendingActions = [
  {
    id: 'draft',
    header: t('draft'),
    bgColor: `${colors.pink[100]}`,
    fontColor: `${colors.pink[700]}`,
    value: 0
  },
  {
    id: 'verification',
    header: t('verification'),
    bgColor: `${colors.green[100]}`,
    fontColor: `${colors.green[600]}`,
    value: 0
  },
  {
    id: 'enquiryReports',
    header: t('enquiryReports'),
    bgColor: `${colors.purple[100]}`,
    fontColor: `${colors.purple[600]}`,
    value: 0
  },
  {
    id: 'agenda',
    header: t('agenda'),
    bgColor: `${colors.orange[100]}`,
    fontColor: `${colors.orange[600]}`,
    value: 0
  },
  {
    id: 'demand',
    header: t('demand'),
    bgColor: `${colors.pink[50]}`,
    fontColor: `${colors.pink[500]}`,
    value: 0
  },
  {
    id: 'paymentOrder',
    header: t('paymentOrder'),
    bgColor: `${colors.pink[100]}`,
    fontColor: `${colors.pink[600]}`,
    value: 0
  },
  {
    id: 'payment',
    header: t('payment'),
    bgColor: `${colors.green[50]}`,
    fontColor: `${colors.green[600]}`,
    value: 0
  },
  {
    id: 'claim',
    header: t('claim'),
    bgColor: `${colors.pink[50]}`,
    fontColor: `${colors.pink[600]}`,
    value: 0
  }
];

const PendingActions = (props) => {
  const {
    fetchPendingActions,
    pendingActionsData
  } = props;
  const params = useParams();
  const [pendingData, setPendingData] = useState(pendingActions);
  const [isCountUpdated, setIsCountUpdated] = useState(false);
  useEffect(() => {
    if (params.fileNo) {
      fetchPendingActions(params.fileNo);
    }
  }, [params]);
  useEffect(() => {
    if (pendingActionsData?.length) {
      const pendingArray = pendingData;
      Object.entries(pendingActionsData[0])?.map(([key, value]) => {
        pendingArray.filter((data) => {
          if (data.id === key) {
            // eslint-disable-next-line no-param-reassign
            data.value = value;
          }
          return true;
        });
        return true;
      });
      setPendingData(pendingArray);
      setIsCountUpdated(true);
    }
  }, [pendingActionsData?.length]);

  return (
    <div>
      <CardWithHeader heading="Pending Actions" />
      <div className="grid grid-cols-8">
        {isCountUpdated || pendingData?.map((pending) => (
          <div key={pending.header} className="m-8">
            <div>
              <FormLabel label={pending.header} fontSize="xs" whiteSpace="nowrap" />
            </div>
            <TinyBox bgColor={pending.bgColor} fontColor={pending.fontColor} value={pending.value} />
          </div>
        ))}
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  pendingActionsData: getPendingActions
});
const mapDispatchToProps = (dispatch) => ({
  fetchPendingActions: (data) => dispatch(actions.fetchPendingActions(data))
});
export default connect(mapStateToProps, mapDispatchToProps)(PendingActions);
