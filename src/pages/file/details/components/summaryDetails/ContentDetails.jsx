import React from 'react';

const ContentDetails = ({ data = [], column = '3', warningMessage = '' }) => {
  return (
    <div className={`grid grid-cols-${column} gap-4`}>
      {data?.length > 0 ? data?.map((item) => (
        <div className="p-4 text-[#153171] text-[14px]" key={item?.value}>
          <h3>{item?.title}</h3>
          <p className="text-[#3C4449] text-[16px] pt-[13px]">{item?.value}</p>
        </div>
      )) : (
        <div>
          <span className="px-5">{warningMessage}</span>
        </div>
      )}
    </div>
  );
};

export default ContentDetails;
