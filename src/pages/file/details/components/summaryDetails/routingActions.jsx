/* eslint-disable operator-linebreak */
import { Button } from '@ksmartikm/ui-components';
import Drafts from 'assets/Drafts';
import { RoundedCheck } from 'assets/Svg';
import { t } from 'common/components';
import { BASE_PATH, FINANCE_MODULES } from 'common/constants';
import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { getUserInfo } from 'pages/common/selectors';
import { actions as sliceFileActions } from 'pages/file/details/slice';
import RupeeIcon from 'assets/Rupee';
import * as actions from '../../actions';
import { getDraftExistsOrNot, getFileDetails, getFinanceExistsOrNot } from '../../selector';
import { FINANCE_TYPE } from '../../constants';

const styles = {
  buttonStyle: {
    borderRadius: '8px',
    display: 'flex',
    height: '48px',
    padding: '12px 16px',
    alignItems: 'center',
    gap: '10px'
  },
  buttonLabel: {
    fontSize: '15px',
    color: '#5C6E93'
  }
};

const checkFinanceExistOrNot = (statusArray) => {
  const finalStatus = statusArray?.some((obj) => obj.status);
  const finalRecStatus = statusArray?.some((obj) => obj.recStatus === 'AUTHORISE');
  if (finalRecStatus) {
    return false;
  }
  return finalStatus;
};

const getButtonLabel = (type) => {
  const labelMap = {
    DEMAND: t('demand'),
    CLAIM: t('claim'),
    PAY_ORDER: t('paymentOrder'),
    PAYMENT: t('payment'),
    SALARY_PAYMENT: t('paymentOrder'),
    RECOVERY_PAYMENT: t('paymentOrder'),
    IMPREST_CLAIM: t('imprestClaim'),
    ADVANCE_CLAIM: t('advanceClaim'),
    JOURNAL: t('journalEntry'),
    CONTRA: t('contraEntry')
  };

  return labelMap[type] || t('finance');
};

export const renderFinanceButtons = (financeExistsOrNot, userInfo, handleFinance, backgroundColor) => {
  const financeData = Array.isArray(financeExistsOrNot) && financeExistsOrNot[0];
  return financeData ? (
    <Button
      key={`${financeData.id || financeData.type}-${backgroundColor}`}
      leftIcon={<RupeeIcon />}
      rightIcon={<RoundedCheck color={checkFinanceExistOrNot(financeExistsOrNot) ? '#00B2EC' : '#********'} />}
      className="mr-4"
      style={{ ...styles.buttonStyle, backgroundColor }}
      isDisabled={!checkFinanceExistOrNot(financeExistsOrNot) || userInfo?.userRoles?.length === 0}
      onClick={() => handleFinance(financeData.type)}
    >
      <span style={styles.buttonLabel}>{getButtonLabel(financeData.type)}</span>
    </Button>
  ) : null;
};

const RoutingActions = (props) => {
  const {
    fetchDraftExistsOrNot,
    draftExistsOrNot,
    fileDetails,
    fetchFinanceExistsOrNot,
    financeExistsOrNot,
    userInfo
  } = props;
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const paramsValues = new URLSearchParams(window.location.search);

  // Commented Because of doing same function of All Notes Button
  // const handleNote = () => {
  //   // navigate(`${BASE_PATH}/file/${params.fileNo}/notes?show=0`);

  //   const baseUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=0`;
  //   const paramsValuesCopy = new URLSearchParams(paramsValues);
  //   paramsValuesCopy.delete('show');
  //   const finalUrl = paramsValuesCopy.toString() ? `${baseUrl}&${paramsValuesCopy}` : baseUrl;
  //   navigate(finalUrl);
  // };

  const handleDraft = () => {
    // navigate(`${BASE_PATH}/file/${params.fileNo}/notes?show=1`);

    if (paramsValues) {
      navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1&${paramsValues}`);
    } else {
      navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
    }
  };

  useEffect(() => {
    if (params?.fileNo && userInfo?.id && userInfo?.userRoles?.length > 0 && fileDetails?.role) {
      fetchFinanceExistsOrNot({ fileNo: params?.fileNo, officeCode: userInfo?.id, role: fileDetails?.role });
    }
  }, [params?.fileNo, userInfo, fileDetails?.role]);

  useEffect(() => {
    if (params?.fileNo) {
      fetchDraftExistsOrNot({ fileNo: params?.fileNo });
    }
  }, [params?.fileNo]);

  const checkDraftExistOrNot = (status) => {
    return status === true;
  };

  const handleFinance = () => {
    if (financeExistsOrNot[0]?.type === FINANCE_TYPE.DEMAND) {
      window.location.href = `${FINANCE_MODULES.DEMAND.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.CLAIM) {
      window.location.href = `${FINANCE_MODULES.CLAIM.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.PAY_ORDER) {
      window.location.href = `${FINANCE_MODULES.PAYMENT_ORDER.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.PAYMENT) {
      window.location.href = `${FINANCE_MODULES.PAYMENT.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.SALARY_PAYMENT) {
      window.location.href = `${FINANCE_MODULES.SALARY.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.RECOVERY_PAYMENT) {
      window.location.href = `${FINANCE_MODULES.RECOVERY_PAYMENT.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.IMPREST_CLAIM) {
      window.location.href = `${FINANCE_MODULES.IMPREST_CLAIM.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.ADVANCE_CLAIM) {
      window.location.href = `${FINANCE_MODULES.ADVANCE_CLAIM.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.JOURNAL_ENTRY) {
      window.location.href = `${FINANCE_MODULES.JOURNAL_ENTRY.URL}/${params?.fileNo}`;
    } else if (financeExistsOrNot[0]?.type === FINANCE_TYPE.CONTRA_ENTRY) {
      window.location.href = `${FINANCE_MODULES.CONTRA_ENTRY.URL}/${params?.fileNo}`;
    }
  };

  // eslint-disable-next-line no-shadow

  return (
    <div className="inline-flex col-span-12">
      {renderFinanceButtons(financeExistsOrNot, userInfo, handleFinance, '#FFF')}

      <Button
        leftIcon={<Drafts />}
        rightIcon={<RoundedCheck color={checkDraftExistOrNot(draftExistsOrNot) ? '#00B2EC' : '#********'} />}
        className="mr-4"
        style={styles.buttonStyle}
        onClick={handleDraft}
        isDisabled={!checkDraftExistOrNot(draftExistsOrNot) || searchParams.get('flowaction') === 'routechange'}
      >
        <span style={styles.buttonLabel}>{t('drafts')}</span>
      </Button>

      {/* Commented This button. because of doing same function of All notes Button */}
      {/* <Button
        leftIcon={<NotesButtonIcons />}
        rightIcon={<RoundedCheck color="#00B2EC" />}
        className="mr-4"
        style={styles.buttonStyle}
        onClick={handleNote}
        isDisabled={userInfo?.userRoles?.length === 0}
      >
        <span style={styles.buttonLabel}>{t('noteFile')}</span>
      </Button> */}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  draftExistsOrNot: getDraftExistsOrNot,
  fileDetails: getFileDetails,
  financeExistsOrNot: getFinanceExistsOrNot,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  setMergedBackButton: (data) => dispatch(sliceFileActions.setMergedBackButton(data)),
  fetchDraftExistsOrNot: (data) => dispatch(actions.fetchDraftExistsOrNot(data)),
  fetchFinanceExistsOrNot: (data) => dispatch(actions.fetchFinanceExistsOrNot(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(RoutingActions);
