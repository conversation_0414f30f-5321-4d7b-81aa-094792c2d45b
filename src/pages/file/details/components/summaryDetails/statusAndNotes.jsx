import { connect } from 'react-redux';
import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>ton,
  IconButton,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  <PERSON>dalHeader,
  ModalOverlay,
  Tooltip,
  t
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { FILE_STATUS_FOR_API_PARAMS, NOTE_STATUS } from 'pages/common/constants';
import {
  getActionTriggered, getNoteCardDetails, getPostIdByPenNoDetails, getUserInfo
} from 'pages/common/selectors';
import NoteCard from 'common/components/NotesCard/NoteCard';
import { actions as commonSliceActions } from 'pages/common/slice';
import DocumentView from 'pages/common/components/DocumentView';
import DraftView from 'pages/common/components/DraftView';
import NoteReferences from 'pages/common/components/NoteReferences';
import { BASE_PATH } from 'common/constants';
import DocumentPreview from 'common/components/DocumentPreview';
import { dark, light } from 'utils/color';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import CloseNew from 'assets/CloseNew';
import DocumentNewExpandComponents from 'common/components/DocumentPreview/DocumentNewExpandComponents';
import * as actions from '../../actions';
import { getFileDetails, getNotes, getPartialNotes } from '../../selector';
import { actions as sliceActions } from '../../slice';
import CreateNotes from '../notes/Create';
import DraftNewPreview from '../notes/draft/DraftNewPreview';

const StatusAndNotes = (props) => {
  const {
    fetchNotes,
    statusAndNotes,
    noteId,
    fileDetails,
    fetchPartialNotes,
    userInfo,
    postIdByPenNoDetails,
    setDocumentNameFromNoteReferences,
    noteCardDetails,
    setDocumentId,
    setNoteRefTrigger
  } = props;

  const [newNote, setNewNote] = useState(false);
  const [noteList, setNoteList] = useState();
  const [showAttachment, setShowAttachment] = useState(false);
  const params = useParams();
  const navigate = useNavigate();
  const [isOpenDocumentModal, setIsOpenDocumentModal] = useState(false);
  const [open, setOpen] = useState(false);
  const [draftItems, setDraftItems] = useState();

  const [openNewExpand, setOpenNewExpand] = useState(false);
  const [full, setFull] = useState(false);

  const close = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (params?.fileNo || newNote) {
      fetchNotes({
        fileNo: params.fileNo,
        size: 4,
        page: 0,
        sortDirection: 'desc',
        noteStatus: NOTE_STATUS.COMPLETED
      });
      setNewNote(false);
    }
  }, [params, noteId]);

  useEffect(() => {
    if (statusAndNotes?.content) {
      const reversedNotes = Array.isArray(statusAndNotes?.content) ? [...statusAndNotes.content].reverse() : [];
      setNoteList(reversedNotes);
      if (statusAndNotes?.content?.length > 0) {
        setTimeout(() => {
          scrollToTop(`summary-note-card-${statusAndNotes?.content[0]?.noteNo}`);
        }, 2000);
      }
    } else {
      setTimeout(() => {
        scrollToTop('summary-bottom-scroll');
      }, 2000);
    }
  }, [statusAndNotes]);

  useEffect(() => {
    const notesSearchRequest = {
      fileNo: params?.fileNo,
      assigner: userInfo?.assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (userInfo?.assigner) {
      fetchPartialNotes(notesSearchRequest);
    }
  }, [JSON.stringify(userInfo?.assigner)]);

  const handleRefNavigate = (event, data) => {
    event.stopPropagation();
    setDocumentNameFromNoteReferences(data?.documentName);
    setNoteRefTrigger({
      noteNo: data?.noteOrApplicationNumber,
      refId: data?.id
    });
    setDocumentId({
      docId: data?.content?.notesDocumentId || data?.content?.fileId,
      from: data?.content?.notesDocumentId ? 'note' : 'inward'
    });
    navigate(
      `${BASE_PATH}/file/${params?.fileNo}/notes?show=0&ref=yes&page=${Number(data?.noteOrApplicationNumber) - 1}&doc=${
        data?.content?.notesDocumentId || data?.content?.fileId
      }`
    );
  };

  return (
    <>
      <div className="flex flex-col gap-2 pt-1 pb-3">
        {noteList?.length > 0 ? (
          noteList?.map((item, index) => {
            return (
              <NoteCard
                item={item}
                key={item?.id}
                attachments={(
                  <DocumentView
                    notesDocsDetails={item?.notesDocsDetails}
                    from="summary"
                    setIsOpenDocumentModal={setIsOpenDocumentModal}
                    isOpenDocumentModal={isOpenDocumentModal}
                  />
                )}
                draftDetails={(
                  <DraftView
                    draftDetails={item?.draftDetails}
                    from="summary"
                    setDraftItems={setDraftItems}
                    setOpen={setOpen}
                  />
                )}
                noteReferences={
                  <NoteReferences noteReferences={item?.noteReferences} handleRefNavigate={handleRefNavigate} />
                }
                showFinanceButton={index === noteList.length - 1}
              />
            );
          })
        ) : (
          <div className="bg-[#FFFFFF] rounded-lg overflow-hidden p-1 mb-2 mt-2 text-center relative">
            <div id="summary-bottom-scroll" className="absolute mt-[-250px]" />
            <div className="bg-[#FFFFFF] px-4 py-2 text-[#5C6E93] text-[15px]">{t('noPreviousNotes')}</div>
          </div>
        )}
      </div>

      {fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.CLOSED
        && fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.HOLD
        && postIdByPenNoDetails?.includes(fileDetails?.postId) && (
          <CreateNotes
            showAttachment={showAttachment}
            setShowAttachment={setShowAttachment}
            fileDetails={fileDetails}
            from="summary"
          />
      )}
      {isOpenDocumentModal && (
        <Modal
          isOpen={isOpenDocumentModal}
          size="3xl"
          onClose={() => setIsOpenDocumentModal(!isOpenDocumentModal)}
          closeOnOverlayClick={false}
          closeOnEsc={false}
        >
          <ModalOverlay />
          <ModalContent>
            <ModalHeader p={0} className="text-center rounded-[10px]" style={{ background: light, color: dark }}>
              <div className="flex">
                <div className="flex-none">
                  <h4 size="md" className="p-4 text-[18px] rounded-t-lg">
                    {t('document')}
                  </h4>
                </div>
                <div className="flex-grow" />
                <div className="flex-none justify-end pt-[15px] pr-[15px]">
                  <Tooltip label={t('close')}>
                    <IconButton
                      variant="unstyled"
                      onClick={() => {
                        setIsOpenDocumentModal(!isOpenDocumentModal);
                      }}
                      leftIcon={<CloseNew />}
                    />
                  </Tooltip>
                </div>
              </div>
            </ModalHeader>
            <ModalBody>
              <DocumentPreview
                preview={noteCardDetails}
                from="summary"
                expandEnable
                setOpenNewExpand={setOpenNewExpand}
                openNewExpand={openNewExpand}
                setFull={setFull}
                full={full}
              />
            </ModalBody>
            <ModalFooter className="rounded-[10px]" style={{ background: light, color: dark }}>
              <div className="w-full text-right space-x-4">
                <Button
                  variant="secondary_outline"
                  size="sm"
                  onClick={() => setIsOpenDocumentModal(!isOpenDocumentModal)}
                >
                  {t('close')}
                </Button>
              </div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {open && <DraftNewPreview open={open} close={close} draftItems={draftItems} fileDetails={fileDetails} />}

      {openNewExpand && full && (
        <DocumentNewExpandComponents
          noteCardDetails={noteCardDetails}
          full={full}
          openNewExpand={openNewExpand}
          setOpenNewExpand={setOpenNewExpand}
          setFull={setFull}
        />
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  partialNotes: getPartialNotes,
  statusAndNotes: getNotes,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  noteCardDetails: getNoteCardDetails,
  fileDetails: getFileDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchNotes: (data) => dispatch(actions.fetchNotes(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data)),
  setNoteRefTrigger: (data) => dispatch(sliceActions.setNoteRefTrigger(data)),
  setDocumentNameFromNoteReferences: (data) => dispatch(commonSliceActions.setDocumentNameFromNoteReferences(data)),
  setDocumentId: (data) => dispatch(commonSliceActions.setDocumentId(data))
});
export default connect(mapStateToProps, mapDispatchToProps)(StatusAndNotes);
