import ArrowView from 'assets/ArrowView';
import { t, CustomTable } from 'common/components';
import { CONTENT_TYPE, REQUEST_METHOD, STORAGE_KEYS } from 'common/constants';
import { DATE_FORMAT } from 'pages/common/constants';
import { useState, useEffect } from 'react';
import { convertToLocalDate } from 'utils/date';
import { baseApiURL } from 'utils/http';
import { getActionTriggered } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { IconButton, Spinner } from '@ksmartikm/ui-components';
import CardContentWithHeader from 'common/components/CardContentWithHeader';
import ViewReceiptDetails from './ViewReceiptDetails';

const ReceiptDetails = ({
  applicantDetails, userInfo, setActionTriggered, actionTriggered
}) => {
  const [docPreview, setDocPreview] = useState(null);
  const [viewReceiptDetails, setReceiptDetails] = useState(false);
  const [receiptData, setReceiptData] = useState([]);

  const handleClose = () => {
    setReceiptDetails(false);
  };

  function getDocument(url, token, body) {
    setActionTriggered({ loading: true, id: 'receipt-details' });
    try {
      fetch(url, {
        method: REQUEST_METHOD.POST,
        headers: {
          Accept: CONTENT_TYPE.APPLICATION_JSON,
          Authorization: `Bearer ${token}`,
          'Content-Type': CONTENT_TYPE.APPLICATION_JSON
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setActionTriggered({ loading: false, id: 'receipt-details' });
          setDocPreview(response);
          setReceiptDetails(true);
        });
    } catch (error) {
      setActionTriggered({ loading: false, id: 'receipt-details' });
      setReceiptDetails(false);
    }
  }

  const handlePreview = (val) => {
    let documenturl = '';
    let sendData = {};

    if (val?.filingMode === 'efile') {
      documenturl = `${baseApiURL}/fin-report-services/pdf/ereceipt`;
      sendData = {
        id: val.receiptId
      };
    } else {
      documenturl = `${baseApiURL}/fin-report-services/pdf/receipt`;
      sendData = {
        id: val.receiptId,
        officeCode: userInfo?.id
      };
    }

    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };

  const formatReceiptDate = (fileData) => {
    let receiptDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      receiptDate = <div>{convertToLocalDate(cellData?.receiptDate, DATE_FORMAT.DATE_LOCAL)}</div>;
    }
    return <div className="block">{receiptDate}</div>;
  };

  const tableActions = (row) => {
    return (
      <div>
        {actionTriggered?.id === 'receipt-details' && actionTriggered?.loading ? <Spinner style={{ height: '20px', width: '20px' }} /> : <IconButton variant="unstyled" onClick={() => handlePreview(row?.row)} icon={<ArrowView />} />}
      </div>
    );
  };

  const columns = [
    {
      header: t('slNo'),
      alignment: 'left',
      field: 'slNo',
      cell: ({ rowIndex }) => rowIndex + 1
    },
    {
      header: t('inwardNumber'),
      alignment: 'left',
      field: 'inwardNo'
    },
    {
      header: t('receiptNo'),
      alignment: 'left',
      field: 'receiptNo'
    },
    {
      header: t('receiptDate'),
      alignment: 'left',
      cell: (field) => formatReceiptDate(field)
    },
    {
      header: t('amountInRs'),
      alignment: 'left',
      field: 'fees'
    },
    {
      header: t('view'),
      alignment: 'left',
      cell: tableActions
    }
  ];
  useEffect(() => {
    const inwardApplicants = applicantDetails?.inwardDetails;
    const efileApplicants = applicantDetails?.efileDetails;
    const formatInward = inwardApplicants ? inwardApplicants?.flat() : [];
    const formatEfile = efileApplicants ? efileApplicants?.flat() : [];
    if (inwardApplicants?.length > 0 || efileApplicants?.length > 0) {
      setReceiptData([...formatInward, ...formatEfile]);
    }
  }, [JSON.stringify(applicantDetails)]);

  return (

    <CardContentWithHeader
      title={t('receiptDetailsAtTheTimeOfApplication')}
      content={(
        <>
          <div className="mb-3 text-center">
            {receiptData.filter((item) => item?.receiptNo)?.length > 0 ? (
              <CustomTable tableData={receiptData.filter((item) => item?.receiptNo)} columns={columns} paginationEnabled paginationPosition="end" itemsPerPage={5} />
            ) : (
              <span className="px-5">{t('noReceiptDetailsToDisplay')}</span>
            )}
          </div>
          {viewReceiptDetails && docPreview && <ViewReceiptDetails open={viewReceiptDetails} docPreview={docPreview} handleClose={handleClose} />}
        </>
      )}
    />
  );
};

const mapStateToProps = createStructuredSelector({
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ReceiptDetails);
