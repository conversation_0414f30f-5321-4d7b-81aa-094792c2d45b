import { t } from 'common/components';
import React from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { serverTimeToLocalTime } from 'utils/date';
import { getNotes } from '../../selector';
import { handleFileStage } from '../helper';

const StatusDetails = ({ fileDetails, statusAndNotes }) => {
  return (
    <div className="border rounded border-[#E7EFF5] bg-white flex p-4 mb-2">
      <div className="flex-grow">
        <div className="text-[#153171] text-[14px] font-normal">{t('submittedBy')}<span className="text-[#3C4449] text-[16px] pl-4 font-semibold">{statusAndNotes?.content?.length > 0 ? statusAndNotes?.content[0]?.assignerName : fileDetails?.createdByName}</span></div>
      </div>
      <div className="flex-grow">
        <div className="text-[#153171] text-[14px] font-normal">{t('submittedOn')}<span className="text-[#3C4449] text-[16px] pl-4 font-semibold">{serverTimeToLocalTime(fileDetails?.updatedAt)}</span></div>
      </div>
      <div className="flex-none">
        <div className="text-[#153171] text-[14px] font-normal">{t('currentStatus')}<span className="text-[#3C4449] text-[16px] pl-4 font-semibold">{handleFileStage(fileDetails?.stage)}</span></div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  statusAndNotes: getNotes
});

const mapDispatchToProps = () => ({

});
export default connect(mapStateToProps, mapDispatchToProps)(StatusDetails);
