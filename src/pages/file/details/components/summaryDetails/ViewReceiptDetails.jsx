import { PdfViewer } from '@ksmartikm/ui-components';
import React, { useEffect, useState } from 'react';
import {
  IconButton, Modal, ModalOverlay, ModalContent, ModalBody, Tooltip, t,
  ModalHeader
} from 'common/components';
import CloseOutlineIcon from 'assets/CloseOutline';
import { DOCUMENT_TYPES } from 'common/constants';

const ViewReceiptDetails = ({ open, docPreview, handleClose }) => {
  const [convertedPreviewData, setConvertedPreviewData] = useState('');

  useEffect(() => {
    if (docPreview) {
      const arr = new Uint8Array(docPreview);
      const blob = new Blob([arr], {
        type: DOCUMENT_TYPES.PDF
      });
      const url = window.URL.createObjectURL(blob);
      setConvertedPreviewData(url);
    }
  }, [docPreview]);

  return (
    <Modal isOpen={open} size="4xl" onClose={handleClose} closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <div className="flex items-center justify-end w-full pt-2">
            <Tooltip label={t('close')}>
              <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
            </Tooltip>
          </div>
        </ModalHeader>
        <ModalBody>
          <PdfViewer title="inward" width="100%" data={convertedPreviewData} aria-label="loading" />
        </ModalBody>

      </ModalContent>
    </Modal>
  );
};

export default ViewReceiptDetails;
