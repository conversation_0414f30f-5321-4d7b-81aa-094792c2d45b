import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Modal, ModalOverlay, ModalContent, ModalBody, t
} from 'common/components';
import {
  But<PERSON>, <PERSON>dalFooter, ModalHeader
} from '@ksmartikm/ui-components';
import { dark, light } from 'utils/color';
import draft from 'pages/file/details/components/draft';
import {
  getAmountFromClaim, getBillType, getBuildingProjectType, getBuildingUsage, getBuildUpArea, getDesignation, getDistricts, getDoor, getDoorKey, getEstablishmentType, getEstimateAmount, getFunctionalGroup, getFunctions, getFund, getHeight, getLbBuilding, getLocalBodyPropertyType, getLsgiType, getMeetingType, getMission, getOccupancy, getOfficeType, getProfessionalTaxType, getRecoveryAccountHead, getRegionType, getState, getTypeOfAudit, getWard,
  getWasteManagementType
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { CommonTable } from 'common/components/Table';
import { FILE_ROLE } from 'pages/common/constants';
import RouteKeysIcon from 'assets/RouteKeys';
import * as actions from '../../actions';
import { getFileDetails, getFileUsers } from '../../selector';
import { getKeyFromList } from './routeKeyHelper';

const RouteKeys = (props) => {
  const {
    fileDetails,
    fetchUsersByFileno,
    fileUsers,
    fetchWardsByLocalBodyId,
    fetchLocalBodyPropertyType,
    fetchFunctionalGroup,
    fetchFunctions,
    fetchBuildingUsage,
    fetchDoor,
    fetchBillType,
    fetchEstablishmentType,
    fetchMission,
    fetchProfessionalTaxType,
    fetchTypeofAudit,
    fetchAmountFromClaim,
    fetchDesignation,
    fetchOccupancy,
    fetchEstimateAmount,
    fetchBuildUpArea,
    fetchMeetingType,
    fetchOfficeType,
    fetchFund,
    fetchRecoveryAccountHead,
    fetchHeight,
    functionalGroup,
    establishmentType,
    amountFromClaim,
    fund,
    fetchWasteManagementType,
    lsgiType,
    regionType,
    buildingProjectType,
    fetchLsgiType,
    fetchRegionType,
    fetchBuildingProjectType,
    districtDropdown
  } = props;

  const [routeKeyTableData, setRouteKeyTableData] = useState([]);
  const [open, setOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [routeData, setRouteData] = useState({});

  const close = () => {
    setOpen(false);
  };

  const findKeyIndex = (key) => {
    const routeKey2 = fileDetails?.routeKey2;
    const findIndex = routeKey2?.findIndex((item) => item[key]);
    if (findIndex > -1) {
      return routeKey2[findIndex]?.[key];
    } return null;
  };

  useEffect(() => {
    const data = [
      { key: t('functionalGroup'), value: getKeyFromList(functionalGroup?.data, routeData?.functionalGroupId, 'id') },
      { key: t('establishmentType'), value: getKeyFromList(establishmentType, routeData?.establishmentType, 'id') },
      { key: t('lsgiType'), value: getKeyFromList(lsgiType, routeData?.lsgiType, 'id') },
      { key: t('regionType'), value: getKeyFromList(regionType, routeData?.regionType, 'id') },
      { key: t('buildingProjectType'), value: getKeyFromList(buildingProjectType, routeData?.buildingProjectType, 'id') },
      { key: t('amountFromClaim'), value: getKeyFromList(amountFromClaim, routeData?.amountFromClaim, 'id') },
      { key: t('fund'), value: getKeyFromList(fund, routeData?.fund, 'headId') },
      { key: t('districts'), value: getKeyFromList(districtDropdown, routeData?.districts, 'id') }
    ];

    setRouteKeyTableData(data?.filter((item) => item.value !== null && item.value !== undefined && item.value !== ''));
  }, [
    functionalGroup,
    establishmentType,
    lsgiType,
    regionType,
    buildingProjectType,
    amountFromClaim,
    fund,
    districtDropdown,
    routeData
  ]);

  const handleOpen = () => {
    if (fileDetails?.serviceCode) {
      const data = {
        establishmentType: findKeyIndex('1'),
        lsgiType: findKeyIndex('2'),
        regionType: findKeyIndex('3'),
        buildingProjectType: findKeyIndex('4'),
        fund: findKeyIndex('5'),
        functionalGroupId: findKeyIndex('6'),
        amountFromClaim: findKeyIndex('7'),
        districts: findKeyIndex('8')
      };
      setRouteData(data);

      if (data) {
        if (data?.wardNo) {
          fetchWardsByLocalBodyId(fileDetails?.officeId);
        }
        if (data.localBodyPropertyTypeId) {
          fetchLocalBodyPropertyType();
        }
        if (data.functionalGroupId) {
          fetchFunctionalGroup();
          fetchFunctions(data.functionalGroupId);
        }
        if (data.doorNo) {
          fetchDoor();
        }

        if (data.buildingUsageId) {
          fetchBuildingUsage();
        }

        if (data.designation) {
          fetchDesignation({ serviceCode: fileDetails?.serviceCode, officeId: fileDetails?.officeId });
        }
        if (data.billType) {
          fetchBillType();
        }
        if (data.establishmentType) {
          fetchEstablishmentType();
        }
        if (data.mission) {
          fetchMission();
        }
        if (data.professionalTaxType) {
          fetchProfessionalTaxType();
        }
        if (data.typeOfAudit) {
          fetchTypeofAudit();
        }
        if (data.amountFromClaim) {
          fetchAmountFromClaim();
        }
        if (data.estimateAmount) {
          fetchEstimateAmount();
        }
        if (data.occupancy) {
          fetchOccupancy();
        }
        if (data.buildingArea) {
          fetchBuildUpArea();
        }
        if (data.meetingType) {
          fetchMeetingType();
        }
        if (data.officeType) {
          fetchOfficeType({ officeLbCode: fileDetails?.officeId });
        }
        if (data.fund) {
          fetchFund();
        }
        if (data.deductionHead) {
          fetchRecoveryAccountHead();
        }
        if (data.height === 1) {
          fetchHeight();
        }
        if (data.wasteManagementId === 1) {
          fetchWasteManagementType();
        }
        if (data?.lsgiType) {
          fetchLsgiType();
        }
        if (data?.regionType) {
          fetchRegionType();
        }
        if (data?.buildingProjectType) {
          fetchBuildingProjectType();
        }
      }
    }

    const sendData = {
      params: {
        officeId: fileDetails?.officeId,
        serviceCode: fileDetails?.serviceCode,
        routeKey1: '1',
        routeKe1Value: fileDetails?.routeKey1?.['1']
      },
      data: fileDetails?.routeKey2
    };
    fetchUsersByFileno(sendData);
    setOpen(true);
  };

  useEffect(() => {
    if (fileUsers) {
      if (fileUsers?.length > 0) {
        const operators = fileUsers?.filter((item) => item.role === FILE_ROLE.OPERATOR);
        const enquiryOfficer = fileUsers?.filter((item) => item.role === FILE_ROLE.ENQUIRY_OFFICER);
        const verfiers = fileUsers?.filter((item) => item.role === FILE_ROLE.VERIFIER);
        const recomendingOfficer = fileUsers?.filter((item) => item.role === FILE_ROLE.RECOMMENDING_OFFICER);
        const approvers = fileUsers?.filter((item) => item.role === FILE_ROLE.APPROVER);
        const authorizer = fileUsers?.filter((item) => item.role === FILE_ROLE.AUTHORIZER);
        const others = fileUsers?.filter((item) => item.role !== FILE_ROLE.OPERATOR && item.role !== FILE_ROLE.VERIFIER && item.role !== FILE_ROLE.APPROVER && item.role !== FILE_ROLE.ENQUIRY_OFFICER && item.role !== FILE_ROLE.RECOMMENDING_OFFICER && item.role !== FILE_ROLE.AUTHORIZER);
        setUsers([...operators, ...enquiryOfficer, ...verfiers, ...recomendingOfficer, ...approvers, ...authorizer, ...others]);
      }
    }
  }, [fileUsers]);

  const routeKeyHeaders = [
    {
      header: t('slNo'),
      alignment: 'left',
      field: 'slNo',
      cell: ({ rowIndex }) => rowIndex + 1
    },
    {
      header: t('routeKey'),
      alignment: 'left',
      field: 'key'
    },
    {
      header: t('routeKeyValue'),
      alignment: 'left',
      field: 'value'
    }
  ];

  const employee = (row) => {
    return (
      <div>{row?.row?.employeeName}
        <p>{row?.row?.penNo}</p>
      </div>
    );
  };

  const userHeaders = [
    {
      header: t('slNo'),
      alignment: 'left',
      field: 'slNo',
      cell: ({ rowIndex }) => rowIndex + 1
    },
    {
      header: t('role'),
      alignment: 'left',
      field: 'role'
    },
    {
      header: t('seat'),
      alignment: 'left',
      field: 'designatedSeatName'
    },
    {
      header: t('Employee'),
      alignment: 'left',
      field: 'name',
      cell: employee
    },
    {
      header: t('designation'),
      alignment: 'left',
      field: 'designationName'
    }
  ];

  return (
    <>

      <Button
        variant="link"
        onClick={() => handleOpen()}
        leftIcon={<RouteKeysIcon />}
      // rightIcon={<Info />}
      >
        <span className="text-[#5C6E93] text-[16px] font-bold">{t('routeKeysAndUsers')}</span>
      </Button>
      <Modal isOpen={open} size="6xl" onClose={close} closeOnOverlayClick={false}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader className="rounded-t-lg text-center" style={{ background: light, color: draft }}>
            <h4 size="md">
              {t('routeKeysAndUsers')}
            </h4>
          </ModalHeader>
          <ModalBody p={5} style={{ maxHeight: '500px', overflowY: 'auto' }}>
            <div className="px-5 py-3 rounded-full font-medium mb-3" style={{ background: light, color: dark }}>
              {t('routeKeys')}
            </div>
            <CommonTable
              tableData={routeKeyTableData || []}
              columns={routeKeyHeaders}
            />

            <div className="px-5 py-3 rounded-full font-medium mt-10 mb-3" style={{ background: light, color: dark }}>
              {t('userMapping')}
            </div>
            <CommonTable
              tableData={users || []}
              columns={userHeaders}
            />
          </ModalBody>
          <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
            <div className="flex justify-between items-center w-full">
              <div className="flex items-center gap-6" />
              <div className="flex items-center gap-2 cursor-pointer">
                <Button
                  variant="secondary_outline"
                  onClick={close}
                >
                  {t('close')}
                </Button>
              </div>
            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  fileUsers: getFileUsers,
  doorkey: getDoorKey,
  wardDropdown: getWard,
  localBodyPropertyType: getLocalBodyPropertyType,
  functionalGroup: getFunctionalGroup,
  functions: getFunctions,
  buildingUsage: getBuildingUsage,
  designation: getDesignation,
  billType: getBillType,
  establishmentType: getEstablishmentType,
  mission: getMission,
  professionalTaxType: getProfessionalTaxType,
  typeOfAudit: getTypeOfAudit,
  lbBuilding: getLbBuilding,
  amountFromClaim: getAmountFromClaim,
  occupancy: getOccupancy,
  estimateAmount: getEstimateAmount,
  buildUpArea: getBuildUpArea,
  meetingType: getMeetingType,
  officeType: getOfficeType,
  fund: getFund,
  recoveryAccountHead: getRecoveryAccountHead,
  height: getHeight,
  door: getDoor,
  wasteManagementType: getWasteManagementType,
  lsgiType: getLsgiType,
  regionType: getRegionType,
  buildingProjectType: getBuildingProjectType,
  stateDropdown: getState,
  districtDropdown: getDistricts
});

const mapDispatchToProps = (dispatch) => ({
  fetchUsersByFileno: (data) => dispatch(actions.fetchUsersByFileNo(data)),
  fetchWardsByLocalBodyId: (data) => dispatch(commonActions.fetchWardsByLocalBodyId(data)),
  fetchLocalBodyPropertyType: (data) => dispatch(commonActions.fetchLocalBodyPropertyType(data)),
  fetchFunctionalGroup: (data) => dispatch(commonActions.fetchFunctionalGroup(data)),
  fetchFunctions: (data) => dispatch(commonActions.fetchFunctions(data)),
  fetchBuildingUsage: (data) => dispatch(commonActions.fetchBuildingUsage(data)),
  fetchDoor: (data) => dispatch(commonActions.fetchDoor(data)),
  fetchBillType: (data) => dispatch(commonActions.fetchBillType(data)),
  fetchEstablishmentType: (data) => dispatch(commonActions.fetchEstablishmentType(data)),
  fetchMission: (data) => dispatch(commonActions.fetchMission(data)),
  fetchProfessionalTaxType: (data) => dispatch(commonActions.fetchProfessionalTaxType(data)),
  fetchTypeofAudit: (data) => dispatch(commonActions.fetchTypeofAudit(data)),
  fetchAmountFromClaim: (data) => dispatch(commonActions.fetchAmountFromClaim(data)),
  fetchDesignation: (data) => dispatch(commonActions.fetchDesignation(data)),
  fetchOccupancy: (data) => dispatch(commonActions.fetchOccupancy(data)),
  fetchEstimateAmount: (data) => dispatch(commonActions.fetchEstimateAmount(data)),
  fetchBuildUpArea: (data) => dispatch(commonActions.fetchBuildUpArea(data)),
  fetchMeetingType: (data) => dispatch(commonActions.fetchMeetingType(data)),
  fetchOfficeType: (data) => dispatch(commonActions.fetchOfficeType(data)),
  fetchFund: (data) => dispatch(commonActions.fetchFund(data)),
  fetchRecoveryAccountHead: (data) => dispatch(commonActions.fetchRecoveryAccountHead(data)),
  fetchHeight: (data) => dispatch(commonActions.fetchHeight(data)),
  fetchWasteManagementType: (data) => dispatch(commonActions.fetchWasteManagementType(data)),
  fetchLsgiType: (data) => dispatch(commonActions.fetchLsgiType(data)),
  fetchRegionType: (data) => dispatch(commonActions.fetchRegionType(data)),
  fetchBuildingProjectType: (data) => dispatch(commonActions.fetchBuildingProjectType(data)),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchStates: () => dispatch(commonActions.fetchState())
});

export default connect(mapStateToProps, mapDispatchToProps)(RouteKeys);
