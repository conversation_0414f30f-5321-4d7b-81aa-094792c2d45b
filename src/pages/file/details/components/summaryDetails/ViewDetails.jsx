import {
  BasicCard
} from '@ksmartikm/ui-components';
import { FormWrapper, t, FormModal } from 'common/components';
import React from 'react';
import { useForm } from 'react-hook-form';

const ViewDetails = ({ openViewDetails, viewData, handleViewClose }) => {
  const {
    handleSubmit
  } = useForm({
    mode: 'all'
  });

  const formateWard = (data) => {
    if (data?.wardNameInfo) {
      return `${data?.wardNameInfo?.wardNo} - ${data?.wardNameInfo?.name}`;
    }
    if (data?.wardInfo) {
      return `${data?.wardInfo?.wardNo} - ${data?.wardInfo?.name}`;
    } return data?.wardName;
  };

  const formatDataForBasicCard = (e) => {
    const format = [

      {

        title: t('firstName'),
        value: e?.firstName

      },
      {

        title: t('middleName'),
        value: e?.middleName

      },
      {

        title: t('lastName'),
        value: e?.lastName

      },
      {

        title: t('houseName'),
        value: e?.houseName

      },
      {

        title: t('wardName'),
        value: formateWard(e)

      },
      {

        title: t('doorNo'),
        value: e?.doorNo

      },
      {

        title: t('subNo'),
        value: e?.subNo

      },
      {

        title: t('postOffice'),
        value: e?.postOfficeName

      },
      {

        title: t('pinCode'),
        value: e?.pincode

      },
      {

        title: t('street'),
        value: e?.street

      },
      {

        title: t('localPlace'),
        value: e?.localPlace

      },
      {

        title: t('mainPlace'),
        value: e?.mainPlace

      },
      {

        title: t('emailId'),
        value: e?.emailId

      },
      {

        title: t('aadharNumber'),
        value: e?.aadharNo

      },
      {

        title: t('uuid'),
        value: e?.uuid

      },
      {

        title: t('mobileNumber'),
        value: e?.mobileNo

      },
      {

        title: t('whatsAppNumber'),
        value: e?.whatsappNo

      },
      {

        title: t('category'),
        value: e?.categoryInfo

      },
      {

        title: t('financialStatus'),
        value: e?.financialStatusInfo

      },
      {

        title: t('gender'),
        value: e?.genderType

      },
      {

        title: t('dateOfBirth'),
        value: e?.dateOfBirth

      },
      {

        title: t('income'),
        value: e?.income

      },
      {
        title: t('accountType'),
        value: e?.accountTypeInfo
      },
      {
        title: t('treasuryType'),
        value: e?.treasuryTypeInfo
      },
      {
        title: t('treasuryAccountNo'),
        value: e?.treasuryAccountNo
      },
      {
        title: t('headOfAccount'),
        value: e?.headOfAccount
      },
      {

        title: t('bankName'),
        value: e?.bankName

      },
      {

        title: t('bankBranch'),
        value: e?.bankBranchName

      },
      {

        title: t('ifsc'),
        value: e?.ifsc

      },
      {

        title: t('accountNo'),
        value: e?.accountNo

      },
      {

        title: t('educationalQualification'),
        value: e?.educationalQualification

      },
      {

        title: t('referenceNumber'),
        value: e?.referenceNo

      },
      {

        title: t('date'),
        value: e?.referenceDate

      },
      {

        title: t('institutionName'),
        value: e?.institutionName

      },
      {

        title: t('officerName'),
        value: e?.officerName

      },

      {

        title: t('designation'),
        value: e?.designation

      },
      {

        title: t('landLineNumber'),
        value: e?.landLineNumber

      }

    ];

    return format;
  };
  return (
    <FormModal
      modalTitle={t('applicantDetails')}
      open={openViewDetails}
      close={handleViewClose}
      actionButtonText={t('ok')}
      content={(
        <form
          id="view-form"
          onSubmit={handleSubmit(handleViewClose)}
        >
          <div className="max-h-[400px] overflow-auto">
            <FormWrapper py={false}>
              <div className="col-span-12">
                <BasicCard data={formatDataForBasicCard(viewData)} columnSpace={3} />
              </div>
            </FormWrapper>
          </div>
        </form>
      )}
      formId="view-form"
      type="submit"
      modalSize="5xl"
      closeOnOverlayClick={false}
      closeOnEsc={false}
      closeButtonText=""
    />

  );
};

export default ViewDetails;
