import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>er, ModalOverlay, Button, ModalContent, t, FormController
} from 'common/components';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { yupResolver } from '@hookform/resolvers/yup';
import { actions as commonSliceActions } from 'pages/common/slice';
import { HOME_PATH } from 'common/constants';
import * as actions from '../../actions';
import { ReopenFileSchema } from '../../validate';
import { getReopenStatus } from '../../selector';

const ReOpenReasonModal = ({
  open, setReOpenModal, saveReOpenFile, reOpenStatus, setAlertAction, fileNo = null
}) => {
  const params = useParams();

  const [reason, setReason] = useState(null);
  const {
    setValue, control, formState: { errors }, handleSubmit
  } = useForm({
    mode: 'all',
    defaultValues: {
      reason: ''
    },
    resolver: yupResolver(ReopenFileSchema)
  });

  const handleClose = () => {
    setReOpenModal(false);
  };

  const handleFieldChange = (field, data) => {
    if (field === 'reason') {
      setValue('reason', data?.target?.value);
      setReason(data?.target?.value);
    }
  };

  const reOpenFile = () => {
    const data = {
      fileNo: fileNo || params.fileNo,
      noteUpdateRequest: {
        fileNo: fileNo || params.fileNo,
        noteText: reason
      }
    };
    saveReOpenFile(data);
    handleClose();
  };

  const backwardAction = () => {
    window.location.href = HOME_PATH;
  };

  useEffect(() => {
    if (reOpenStatus?.status === 'SUCCESS') {
      setAlertAction({
        open: true,
        variant: 'success',
        message: reOpenStatus?.data?.message,
        title: t('success'),
        backwardActionText: t('ok'),
        backwardAction: () => backwardAction()
      });
    }
  }, [reOpenStatus]);

  return (
    <Modal isOpen={open} size="3xl" onClose={handleClose} className="custom-form-modal" closeOnOverlayClick={false} closeOnEsc={false}>
      <ModalOverlay />
      <ModalContent>
        <form
          id="re-open-form"
          action="enter"
          onSubmit={handleSubmit(reOpenFile)}
        >
          <ModalHeader>
            <h4 size="md">
              {t('reOpen')}
            </h4>
          </ModalHeader>

          <ModalBody>
            <div className="grid gap-3">
              <div className="col-span-12">
                <FormController
                  name="reason"
                  variant="outlined"
                  type="textarea"
                  label={t('reason')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('reason', data)}
                />
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <div className="col-span-12 flex justify-end items-center space-x-4">
              <Button
                variant="secondary_outline"
                size="sm"
                mr={3}
                onClick={() => {
                  handleClose();
                }}
              >
                {t('cancel')}
              </Button>
              <Button variant="secondary" type="submit" size="sm">
                {t('submit')}
              </Button>
            </div>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
  reOpenStatus: getReopenStatus
});

const mapDispatchToProps = (dispatch) => ({
  saveReOpenFile: (data) => dispatch(actions.saveReOpenFile(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(ReOpenReasonModal);
