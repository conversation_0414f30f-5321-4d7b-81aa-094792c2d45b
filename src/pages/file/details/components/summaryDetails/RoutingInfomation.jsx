import { t } from 'common/components';
import CardContentWithHeader from 'common/components/CardContentWithHeader';
import { useState, useEffect } from 'react';
import ContentDetails from './ContentDetails';

const getValue = (data, key, fallbackKey) => {
  return data[key] || data[fallbackKey] || '';
};

const formatGeneralDetailsInfo = (data = []) => {
  if (!data) return [];
  const fields = [
    { title: t('inwardNumber'), value: `${data?.inwardNo} / ${data?.inwardDate}` },
    { title: t('functionalGroup'), value: getValue(data?.generalDetailsResponses, 'functionalGroupInfo', 'functionalGroupId') },
    { title: t('establishmentType'), value: getValue(data?.generalDetailsResponses, 'establishmentInfo', 'establishmentTypeId') },
    { title: t('lsgiType'), value: getValue(data?.generalDetailsResponses, 'lsgiTypeInfo', 'lsgiTypeId') },
    { title: t('regionType'), value: getValue(data?.generalDetailsResponses, 'regionTypeInfo', 'regionTyInfo') },
    { title: t('buildingProjectType'), value: getValue(data?.generalDetailsResponses, 'buildingProjectTypeInfo', 'buildingProjectTypeId') },
    { title: t('amountFromClaim'), value: getValue(data?.generalDetailsResponses, 'amountFromClaimInfo', 'amountFromClaimId') },
    { title: t('fund'), value: getValue(data?.generalDetailsResponses, 'fundTypeInfo', 'fundTypeId') },
    { title: t('districts'), value: getValue(data?.generalDetailsResponses, 'districtsInfo', 'id') }
  ];

  return fields;
};

const RoutingInfomation = ({ applicantDetails }) => {
  const [inwardTableData, setInwardTableData] = useState([]);

  useEffect(() => {
    const inwardApplicants = applicantDetails?.inwardDetails;
    const efileApplicants = applicantDetails?.efileDetails;
    const formatInward = inwardApplicants ? inwardApplicants?.flat() : [];
    const formatEfile = efileApplicants ? efileApplicants?.flat() : [];
    if (inwardApplicants?.length > 0 || efileApplicants?.length > 0) {
      setInwardTableData([...formatInward, ...formatEfile]);
    }
  }, [JSON.stringify(applicantDetails)]);

  return (
    inwardTableData?.length > 0 && inwardTableData?.map((item) => (
      <div className="mb-3" key={item?.inwardNo}>
        <CardContentWithHeader
          title={t('generalInformation')}
          content={<ContentDetails data={formatGeneralDetailsInfo(item)?.filter((fl) => fl.value !== null && fl.value !== '' && fl.value !== undefined)} column="5" warningMessage={t('noGeneralInformationToDisplay')} />}
        />
      </div>
    ))
  );
};

export default RoutingInfomation;
