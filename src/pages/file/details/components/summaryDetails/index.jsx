import { createStructuredSelector } from 'reselect';
import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import {
  t,
  Button
} from 'common/components';
import _ from 'lodash';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  getFiles, getPostIdByPenNoDetails, getSummaryCollapseMenuFlag, getUserInfo
} from 'pages/common/selectors.js';
import WorkFlow from 'pages/workFlow/components';
import { FILE_STATUS_FOR_API_PARAMS, NOTE_STATUS } from 'pages/common/constants';
import UnHoldFile from 'pages/workFlow/components/UnHoldFile';
import CardContentWithHeader from 'common/components/CardContentWithHeader';
import NoteLayout from 'common/components/NoteLayout';
import { serverTimeToLocalTime } from 'utils/date';
import * as actions from '../../actions';
import {
  getService, getSubmodule, getModule, getFileDetails
} from '../../selector';
import ApplicantDetails from './applicantDetails';
import StatusAndNotes from './statusAndNotes';
import ReOpenReasonModal from './ReOpenReasonModal';
import RoutingInfomation from './RoutingInfomation';
import ReceiptDetails from './ReceiptDetails';
import ContentDetails from './ContentDetails';
import BeneficiaryUpdate from '../beneficiary-update';
import StatusDetails from './StatusDetails';

const SummaryDetails = (props) => {
  const {
    fetchFileDetails,
    fileDetails,
    fetchServicesById,
    service,
    subModule,
    module,
    setFormTitle,
    setFileHeader,
    saveUnHoldFile,
    userInfo,
    postIdByPenNoDetails,
    summaryCollapseMenuFlag,
    fetchAllNotes,
    setActionTriggered,
    setAlertAction
  } = props;
  const {
    reset, setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
      service: '',
      module: '',
      subModule: '',
      title: '',
      description: '',
      workflow: '',
      department: '',
      assignee: ''
    }
  });
  const params = useParams();
  const [openUnHoldFile, setOpenUnHoldFile] = useState(false);
  const [activeHoldFileId, setActiveHoldFileId] = useState('');
  const [reOpenModal, setReOpenModal] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(summaryCollapseMenuFlag);

  useEffect(() => {
    setIsCollapsed(summaryCollapseMenuFlag);
  }, [summaryCollapseMenuFlag]);

  // Toggle function to handle collapse/expand
  const handleToggle = () => {
    setIsCollapsed((prevState) => !prevState);
  };

  useEffect(() => {
    if (params.fileNo) {
      fetchFileDetails(params?.fileNo);
      setActionTriggered({ loading: true, id: 'fetch-all-notes' });
      fetchAllNotes({
        fileNo: params?.fileNo, noteStatus: NOTE_STATUS.COMPLETED
      });
    }
  }, [params]);

  useEffect(() => {
    if (!_.isEmpty(fileDetails)) {
      reset(fileDetails);
    }
    if (fileDetails?.serviceCode) {
      fetchServicesById(fileDetails?.serviceCode.toUpperCase());
    }
    if (fileDetails?.fileHoldId) {
      setActiveHoldFileId(fileDetails?.fileHoldId);
    }
  }, [fileDetails]);

  useEffect(() => {
    if (service) {
      setValue('service', service?.name);
      setValue('subModule', subModule?.name);
      setValue('module', module?.name);
    }
  }, [service]);

  useEffect(() => {
    setFormTitle({ title: t('summary'), variant: 'normal' });
    if (fileDetails?.fileNo === params?.fileNo) {
      setFileHeader([{
        label: t('fileNumber'),
        value: params.fileNo
      },
      {
        label: t('role'),
        value: fileDetails?.role
      },
      {
        label: t('service'),
        value: fileDetails?.serviceName,
        subValue: {
          module: module?.name,
          subModule: subModule?.name
        }
      },
      {
        label: t('date'),
        // value: serverTimeToLocalTime(fileDetails?.createdAt)
        value: serverTimeToLocalTime(fileDetails?.createdAt)
      }
      ]);
    }
  }, [fileDetails, service]);

  // eslint-disable-next-line consistent-return
  const formatInwardNumberTitle = (InwardNumberTitle) => {
    let response = '';
    if (InwardNumberTitle?.length > 0) {
      // eslint-disable-next-line array-callback-return
      InwardNumberTitle?.map((item) => {
        if (item?.legacyFileNo) {
          response = t('legacyFileNo');
        } else {
          response = t('inwardNumber');
        }
      });

      return response;
    }
  };

  // eslint-disable-next-line consistent-return
  const formatInwardNumberValue = (InwardNumberValue) => {
    if (InwardNumberValue?.length > 0) {
      return InwardNumberValue?.map((item) => {
        if (item?.legacyFileNo) {
          return item?.legacyFileNo;
        }
        return item?.inwardNo;
      }).join(',');
    }
  };

  const formatTitleDescription = (data) => {
    const dataSave = [];
    if (data?.inwardDetails?.length > 0) {
      dataSave.push(
        {
          title: formatInwardNumberTitle(data?.inwardDetails),
          value: formatInwardNumberValue(data?.inwardDetails)
        },
        {
          title: t('title'),
          value: data?.title
        },
        {
          title: t('description'),
          value: data?.description ? data?.description : (data?.inwardDetails?.length > 0 && data?.inwardDetails[0]?.generalDetailsResponses?.description)
        }
      );
    } else if (data?.efileDetails?.length > 0) {
      dataSave.push(
        {
          title: formatInwardNumberTitle(data?.efileDetails),
          value: formatInwardNumberValue(data?.efileDetails)
        },
        {
          title: t('title'),
          value: data?.title
        },
        {
          title: t('description'),
          value: data?.description ? data?.description : (data?.efileDetails?.length > 0 && data?.efileDetails[0]?.generalDetailsResponses?.description)
        }
      );
    } else {
      dataSave.push(
        {
          title: t('title'),
          value: data?.title
        },
        {
          title: t('description'),
          value: data?.description ? data?.description : (data?.inwardDetails?.length > 0 && data?.inwardDetails[0]?.generalDetailsResponses?.description)
        }
      );
    }

    return dataSave;
  };

  const handleReopenFile = () => {
    if (fileDetails?.merged && fileDetails?.mainFileNumber?.length > 0) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `Unmerge from ${fileDetails?.mainFileNumber[0]} for processing`,
        title: t('unableToReOpen'),
        backwardActionText: t('ok'),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else {
      setReOpenModal(!reOpenModal);
    }
  };

  const handleCloseUnHoldFile = () => {
    setOpenUnHoldFile(false);
  };

  const handleUnHoldFile = () => {
    setOpenUnHoldFile(true);
  };

  const paramsValues = new URLSearchParams(window.location.search);

  return (
    <div>
      <div className="bg-white px-10 pt-10 rounded-lg mb-5">
        <div className="pl-40 pr-40">
          <StatusDetails fileDetails={fileDetails} />
          <CardContentWithHeader
            title={t('inwardDetails')}
            content={isCollapsed ? <ContentDetails data={formatTitleDescription(fileDetails)} column="3" warningMessage={t('noInwardDeatailsToDisplay')} /> : ''}
            collapseMenuFlag
            handleToggle={handleToggle}
            isCollapsed={isCollapsed}
          />
          {isCollapsed && (
            <div className="mt-5">
              <RoutingInfomation applicantDetails={fileDetails} />
            </div>
          )}
          {isCollapsed && (
            <div className="mt-5">
              <ReceiptDetails applicantDetails={fileDetails} userInfo={userInfo} />
            </div>
          )}
          {isCollapsed && (
            <div className="mt-5">
              <ApplicantDetails applicantDetails={fileDetails} isBeneficiary={false} />
            </div>
          )}
          {isCollapsed && (
            <div className="mt-5">
              {/* <ApplicantDetails applicantDetails={fileDetails} isBeneficiary /> */}
              <CardContentWithHeader
                title={`${t('beneficiary')} ${t('details')}`}
                content={(
                  <div className="mb-3 text-center">
                    <BeneficiaryUpdate from="summary" />
                  </div>
                )}
              />

            </div>
          )}
          <div className="mt-2">
            <NoteLayout
              // title={t('previousNotes')}
              content={
                <StatusAndNotes fileDetails={fileDetails} />
              }
              moreMenu
              fromValue={paramsValues}
            />
          </div>
          {fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.CLOSED && fileDetails?.status !== FILE_STATUS_FOR_API_PARAMS.HOLD && postIdByPenNoDetails?.includes(fileDetails?.postId)
            ? (
              <div className="bg-white rounded-lg mb-10">
                <WorkFlow
                  status={fileDetails?.status}
                  code={fileDetails?.serviceCode}
                  activeHoldFileId={fileDetails?.fileHoldId}
                  fileDetails={fileDetails}
                  from="summary"
                />

              </div>
            ) : (
              <div className="col-span-12 text-right p-4">

                {/* <BackButton /> */}

                {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.CLOSED
                  && (
                    <Button
                      variant="secondary_outline"
                      className="mx-2"
                      onClick={handleReopenFile}
                    >
                      {t('reOpen')}
                    </Button>
                  )}

                {(fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD || fileDetails?.stage === FILE_STATUS_FOR_API_PARAMS.PARKED) && postIdByPenNoDetails?.includes(fileDetails?.postId)
                  && (
                    <Button
                      variant="secondary_outline"
                      className="mx-2"
                      onClick={handleUnHoldFile}
                    >
                      {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage !== FILE_STATUS_FOR_API_PARAMS.PARKED && t('moveToCustodian')}
                      {fileDetails?.status === FILE_STATUS_FOR_API_PARAMS.HOLD && fileDetails?.stage === FILE_STATUS_FOR_API_PARAMS.PARKED && t('revoke')}
                    </Button>
                  )}

                <UnHoldFile
                  openUnHoldFile={openUnHoldFile}
                  handleCloseUnHoldFile={handleCloseUnHoldFile}
                  params={params}
                  saveUnHoldFile={saveUnHoldFile}
                  activeHoldFileId={activeHoldFileId}
                  fileDetails={fileDetails}
                />
              </div>
            )}
        </div>

        <ReOpenReasonModal open={reOpenModal} setReOpenModal={setReOpenModal} />
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  service: getService,
  subModule: getSubmodule,
  module: getModule,
  files: getFiles,
  userInfo: getUserInfo,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  summaryCollapseMenuFlag: getSummaryCollapseMenuFlag
});

const mapDispatchToProps = (dispatch) => ({
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  fetchServicesById: (data) => dispatch(actions.fetchServicesById(data)),
  saveNote: (data) => dispatch(actions.saveNote(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  saveUnHoldFile: (data) => dispatch(actions.saveUnHoldFile(data)),
  fetchAllNotes: (data) => dispatch(actions.fetchAllNotes(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(SummaryDetails);
