import { useState, useEffect } from 'react';
import { CustomTable, t } from 'common/components';
import ArrowView from 'assets/ArrowView';
import CardContentWithHeader from 'common/components/CardContentWithHeader';
import { formatAddressDetails, formatApplicantNameTable } from '../helper';
import ViewDetails from './ViewDetails';

const ApplicantDetails = ({ applicantDetails, isBeneficiary = false }) => {
  const [inwardTableData, setInwardTableData] = useState([]);
  const [openViewDetails, setOpenViewDetails] = useState(false);
  const [viewData, setViewData] = useState();

  const viewActions = (val) => {
    setOpenViewDetails(!openViewDetails);

    const inwardGeneral = applicantDetails?.inwardDetails?.map((item) => item?.generalDetailsResponses);
    const efileGeneral = applicantDetails?.efileDetails?.map((item) => item?.generalDetailsResponses);
    const inwardGeneralDetials = inwardGeneral?.map((item) => item?.details);
    const efileGeneralDetials = efileGeneral?.map((item) => item?.details);
    const formatInward = inwardGeneralDetials ? inwardGeneralDetials?.flat() : [];
    const formatEfile = efileGeneralDetials ? efileGeneralDetials?.flat() : [];
    const merged = [...formatInward, ...formatEfile];
    const applicantId = val?.id;

    const index = merged.findIndex((obj) => Object.values(obj).includes(applicantId));
    if (index > -1) {
      const mergedObj = { ...val, ...merged[index] };
      setViewData(mergedObj);
    } else {
      setViewData(val);
    }
  };

  const handleViewClose = () => {
    setOpenViewDetails(!openViewDetails);
  };

  const headers = [
    // {
    //   header: t('slNo'),
    //   alignment: 'left',
    //   field: 'slNo',
    //   cell: ({ rowIndex }) => rowIndex + 1
    // },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => formatApplicantNameTable(field)
    },
    {
      header: t('address'),
      alignment: 'left',
      field: 'applicantAddress',
      cell: (field) => formatAddressDetails(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          onClick: (row) => {
            viewActions(row);
          },
          icon: <ArrowView />
        }
      ]
    }
  ];

  useEffect(() => {
    const inwardApplicants = applicantDetails?.inwardDetails?.map((item) => item?.applicantDetailsAddress);
    const efileApplicants = applicantDetails?.efileDetails?.map((item) => item?.applicantDetailsResponses);
    const efileInstitutions = applicantDetails?.efileDetails?.map((item) => item?.institutionDetailsResponses);
    const formatInward = (inwardApplicants && JSON.stringify(inwardApplicants) !== '[null]') ? inwardApplicants?.flat() : [];
    const formatEfile = (efileApplicants && JSON.stringify(efileApplicants) !== '[null]') ? efileApplicants?.flat() : [];
    const formatEfileInstitution = (efileInstitutions && JSON.stringify(efileInstitutions) !== '[null]') ? efileInstitutions?.flat() : [];
    if (inwardApplicants?.length > 0 || efileApplicants?.length > 0 || efileInstitutions?.length > 0) {
      setInwardTableData([...formatInward, ...formatEfile, ...formatEfileInstitution]);
    }
  }, [JSON.stringify(applicantDetails)]);

  const checkLength = () => {
    if (isBeneficiary) {
      return inwardTableData.filter((item) => item?.beneficiaryFlag === true).length > 0;
    }
    return inwardTableData.filter((item) => item?.beneficiaryFlag === false || !item?.beneficiaryFlag).length > 0;
  };

  return (
    <CardContentWithHeader
      title={isBeneficiary ? `${t('beneficiary')} ${t('details')}` : t('applicantDetails')}
      content={(
        <>
          <div className="mb-3 text-center">
            {checkLength() ? (
              <CustomTable tableData={isBeneficiary ? inwardTableData.filter((item) => item?.beneficiaryFlag === true) : inwardTableData.filter((item) => item?.beneficiaryFlag === false || !item?.beneficiaryFlag)} columns={headers} paginationEnabled paginationPosition="end" itemsPerPage={5} />
            ) : (
              <span className="px-5">{isBeneficiary ? t('noBeneficiaryDataToDisplay') : t('noApplicantDataToDisplay')}</span>
            )}
          </div>
          <ViewDetails openViewDetails={openViewDetails} viewData={viewData} handleViewClose={handleViewClose} />
        </>
      )}
    />
  );
};

export default ApplicantDetails;
