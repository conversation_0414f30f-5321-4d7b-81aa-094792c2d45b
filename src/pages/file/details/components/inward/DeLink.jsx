import { createStructuredSelector } from 'reselect';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered } from 'pages/common/selectors';
import { Button, IconButton, t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import BackButton from 'common/components/BackButton/BackButton';
import NoNotesIcon from 'assets/NoNotesIcon';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import { getFileDetails } from '../../selector';
import * as actions from '../../actions';

const InwardDeLink = (props) => {
  const {
    fetchFileDetails,
    setFormTitle,
    setFileHeader,
    fileDetails,
    setAlertAction,
    deLinkInward
  } = props;

  const params = useParams();
  const [inwards, setInwards] = useState([]);
  const [selectedInwards, setSelectedInwards] = useState([]);
  const [selectedInwardNos, setSelectedInwardNos] = useState([]);

  useEffect(() => {
    fetchFileDetails(params?.fileNo);
  }, []);

  useEffect(() => {
    const counterInwards = fileDetails?.inwardDetails || [];
    const efileInwards = fileDetails?.efileDetails || [];
    if (counterInwards?.length > 0 || efileInwards?.length > 0) {
      setInwards([...efileInwards, ...counterInwards]);
    }
  }, [JSON.stringify(fileDetails)]);

  useEffect(() => {
    setFormTitle({ title: t('inwardDeLink'), variant: 'normal' });
    setFileHeader([{
      label: t('fileNumber'),
      value: params?.fileNo
    }, {
      label: t('role'),
      value: fileDetails?.role
    }, {
      label: t('service'),
      value: fileDetails?.serviceName
    }
    ]);
  }, []);

  const handleDeLink = () => {
    const sendData = {
      inwardIds: selectedInwards,
      fileNo: params?.fileNo,
      inwardNumbers: selectedInwardNos
    };
    deLinkInward(sendData);
    setSelectedInwards([]);
    setSelectedInwardNos([]);
  };

  const handleName = (val) => {
    if (val?.row?.applicantDetailsResponses?.length > 0) {
      if (val?.row?.applicantDetailsResponses[0].firstName) {
        return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row?.applicantDetailsResponses[0].firstName}</div>;
      } if (val?.row?.applicantDetailsResponses[0].institutionName) {
        return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row?.applicantDetailsResponses[0].institutionName}</div>;
      }
    }
    if (val?.row?.applicantDetailsAddress?.length > 0) {
      if (val?.row?.applicantDetailsAddress[0].firstName) {
        return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row?.applicantDetailsAddress[0].firstName}</div>;
      } if (val?.row?.applicantDetailsAddress[0].institutionName) {
        return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row?.applicantDetailsAddress[0].institutionName}</div>;
      }
    }
    if (val?.row?.institutionDetailsResponses?.length > 0) {
      return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row?.institutionDetailsResponses[0].institutionName}</div>;
    }
    return '';
  };

  const service = (val) => {
    if (val?.row.serviceCode) {
      return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row.serviceCode}</div>;
    }
    if (val?.row.serviceName) {
      return <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{val?.row.serviceName}</div>;
    } return '';
  };

  const handleSelectInward = (data) => {
    const { row } = data;
    const inwardSelected = selectedInwards?.length > 0 ? JSON.parse(JSON.stringify(selectedInwards)) : [];
    const inwardNosSelected = selectedInwardNos?.length > 0 ? JSON.parse(JSON.stringify(selectedInwardNos)) : [];

    const findInward = inwardSelected.findIndex((item) => item === row.inwardId);
    const findInwardNos = inwardNosSelected.findIndex((item) => item === row.inwardNo);

    if (findInward > -1) {
      inwardSelected.splice(findInward, 1);
    } else {
      inwardSelected.push(row?.inwardId);
    }

    if (findInwardNos > -1) {
      inwardNosSelected.splice(findInwardNos, 1);
    } else {
      inwardNosSelected.push(row?.inwardNo);
    }

    setSelectedInwards(inwardSelected);
    setSelectedInwardNos(inwardNosSelected);
  };

  function checkBoxCheck(data) {
    const find = selectedInwards?.findIndex((item) => item === data);
    if (find > -1) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  const selectedFileRow = (data) => {
    return (
      data?.rowIndex !== 0 && (
        <IconButton
          variant="unstyled"
          style={{ textDecoration: 'none' }}
          icon={checkBoxCheck(data?.row?.inwardId)}
          onClick={() => handleSelectInward(data)}
        />
      )
    );
  };

  const handleInwardNo = (fileData) => {
    let inwardNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      inwardNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[400px] break-keep">{cellData.inwardNo}</div>;
    }
    return <div className="block">{inwardNo}</div>;
  };

  const headers = [
    {
      cell: (field) => selectedFileRow(field)
    },
    {
      header: t('inwardNo'),
      alignment: 'left',
      field: 'inwardNo',
      cell: (field) => handleInwardNo(field)
    },
    {
      header: t('applicantName'),
      field: 'firstName',
      alignment: 'left',
      cell: (field) => handleName(field)
    },
    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => service(field)
    }
    // {
    //   header: t('actions'),
    //   alignment: 'left',
    //   cell: tableActions
    // }

  ];

  return (
    <div className="col-span-12 pb-10">
      {inwards?.length > 0 ? (
        <>
          <div className="col-span-12 text-right">
            {selectedInwards?.length > 0 && (
              <Button
                variant="secondary"
                onClick={() => {
                  setAlertAction({
                    open: true,
                    variant: 'alert',
                    message: t('areYouSureWanttoDeLinkInward'),
                    title: t('confirmation'),
                    backwardActionText: t('no'),
                    forwardActionText: t('yes'),
                    forwardAction: () => handleDeLink()
                  });
                }}
              >
                {t('deLink')}
              </Button>
            )}
          </div>
          <div className="col-span-12">

            <CommonTable
              variant="dashboard"
              columns={headers}
              tableData={inwards}
            />
          </div>
          <div className="col-span-12 flex justify-end items-center space-x-4 pt-4">
            <BackButton />
          </div>
        </>
      ) : (
        <>
          <div className="p-10 text-center bg-white p-5 rounded-lg">
            <NoNotesIcon width="100px" height="100px" className="mx-auto" />
            <h4>{t('noInwardsToDisplay')}</h4>
          </div>
          <span className="flex justify-end mt-3"><BackButton /></span>
        </>
      )}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  actionTriggered: getActionTriggered,
  fileDetails: getFileDetails
});

const mapDispatchToProps = (dispatch) => ({
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  deLinkInward: (data) => dispatch(actions.deLinkInward(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(InwardDeLink);
