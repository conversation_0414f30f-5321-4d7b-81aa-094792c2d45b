import { t } from 'common/components';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import RoutingKeys from 'pages/common/components/GeneralDetails/RoutingKeys';
import { numberNull } from 'utils/numberNull';
import { FILE_ROLE } from 'pages/common/constants';
import _ from 'lodash';
import { getDoorKey, getUserInfo } from 'pages/common/selectors';
import { generalRoutingKeys, generalSaveData } from 'pages/common/components/GeneralDetails/helper';
import * as actions from '../../actions';
import { getBpRouteKeys, getCustodianUserDetails, getFileDetails } from '../../selector';
import { applicantName } from '../helper';
import { CUSTODIAN_CHANGE } from '../../constants';

const CustodianChange = ({
  fetchFileDetails,
  fileDetails,
  setFormTitle,
  setFileHeader,
  doorkey, setActionTriggered,
  updateCustodianChangeDetails, setAlertAction,
  fetchUserDetails, custodianUserDetails
}) => {
  const [existingGeneral, setExistingGeneral] = useState({});
  const [serviceInfo, setServiceInfo] = useState({});
  const [post, setPost] = useState([]);
  const [routeKeyObj, setRouteKeyObj] = useState({});

  const params = useParams();

  useEffect(() => {
    fetchFileDetails(params.fileNo);
  }, [params.fileNo]);

  function wardKeySet(ward) {
    if (ward) {
      return 1;
    }
    return null;
  }

  function wardSet(ward, genward) {
    if (ward) {
      return ward;
    } if (numberNull(Number(genward))) {
      return 1;
    }
    return null;
  }

  const generalRouting = (data) => ([
    { routeKey2: 1, routeKeyValue: numberNull(Number(data?.establishmentType)) },
    { routeKey2: 2, routeKeyValue: numberNull(Number(data?.lsgiType)) },
    { routeKey2: 3, routeKeyValue: numberNull(Number(data?.regionType)) },
    { routeKey2: 4, routeKeyValue: numberNull(Number(data?.buildingProjectType)) },
    { routeKey2: 5, routeKeyValue: numberNull(Number(data?.fund)) },
    { routeKey2: 6, routeKeyValue: numberNull(Number(data?.functionalGroup)) },
    { routeKey2: 7, routeKeyValue: numberNull(Number(data?.amountFromClaim)) },
    { routeKey2: 8, routeKeyValue: numberNull(Number(data?.districts)) }
  ]);

  const handleFetchUser = () => {
    if (existingGeneral) {
      const routeKeysArray = _.filter(_.omitBy(generalRouting(routeKeyObj), _.isNil), (el) => !_.isNull(el?.routeKeyValue));
      setActionTriggered({ loading: true, id: 'fetchUserDetails' });
      fetchUserDetails({
        role: FILE_ROLE.OPERATOR,
        officeId: fileDetails?.officeId,
        serviceCode: fileDetails?.serviceCode,
        routeKey1: wardKeySet(routeKeyObj?.ward),
        routeKe1Value: wardSet(routeKeyObj?.ward, null),
        routeKeysArray
      });
    }
  };

  useEffect(() => {
    if (custodianUserDetails) {
      setPost(
        custodianUserDetails?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName} - ${ps.penNo}`
        }))
      );
    }
  }, [custodianUserDetails]);

  const findKeyIndex = (key) => {
    const routeKey2 = fileDetails?.routeKey2;
    const findIndex = routeKey2?.findIndex((item) => item[key]);
    if (findIndex > -1) {
      return routeKey2[findIndex]?.[key];
    } return null;
  };

  useEffect(() => {
    if (fileDetails?.serviceCode) {
      const data = {
        establishmentType: findKeyIndex('1'),
        lsgiType: findKeyIndex('2'),
        regionType: findKeyIndex('3'),
        buildingProjectType: findKeyIndex('4'),
        fund: findKeyIndex('5'),
        functionalGroupId: findKeyIndex('6'),
        amountFromClaim: findKeyIndex('7'),
        districts: findKeyIndex('8')
      };
      setExistingGeneral(data);
    }
    setServiceInfo({ code: fileDetails?.serviceCode, name: fileDetails?.serviceName });
  }, [fileDetails]);

  useEffect(() => {
    setFormTitle({ title: t('custodian_change'), variant: 'normal' });
    if (fileDetails?.fileNo === params?.fileNo) {
      setFileHeader([{
        label: t('fileNumber'),
        value: params.fileNo
      }, {
        label: t('role'),
        value: fileDetails?.role
      }, {
        label: t('service'),
        value: fileDetails?.serviceName
      },
      {
        label: t('applicantName'),
        value: fileDetails?.inwardDetails ? applicantName(fileDetails?.inwardDetails) : ''
      }
      ]);
    }
  }, [JSON.stringify(fileDetails)]);

  const routingSave = (data) => {
    setActionTriggered({ loading: true, id: 'efileGeneralDetailsCreate' });
    const keyData = generalRoutingKeys(data, doorkey);
    const rounteKeyData = _.omitBy(keyData, _.isNil);
    const saveData = generalSaveData(
      rounteKeyData,
      null,
      null,
      data,
      null,
      'custodian',
      params.fileNo,
      { fileNo: params.fileNo, noteText: data.remarks },
      data?.user
    );
    updateCustodianChangeDetails(_.omitBy(saveData, _.isNil));
  };

  const getConfirmation = (data) => {
    setAlertAction({
      open: true,
      variant: 'warning',
      message: t('areYouSureWantToChange'),
      title: t('warning'),
      backwardActionText: t('cancel'),
      forwardActionText: t('confirm'),
      forwardAction: () => routingSave(data)
    });
  };

  const getOfficeCode = () => {
    if (fileDetails?.officeId) {
      return fileDetails?.officeId;
    }
    return null;
  };

  return (
    <div className="bg-white p-5 rounded-lg">

      <RoutingKeys
        routingSave={getConfirmation}
        activeIndex
        existingData={existingGeneral}
        serviceInfo={serviceInfo}
        officeCode={getOfficeCode()}
        from={CUSTODIAN_CHANGE}
        post={post}
        setRouteKeyObj={setRouteKeyObj}
        handleFetchUser={handleFetchUser}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  doorkey: getDoorKey,
  userInfo: getUserInfo,
  custodianUserDetails: getCustodianUserDetails,
  bpRouteKeys: getBpRouteKeys

});

const mapDispatchToProps = (dispatch) => ({
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  updateCustodianChangeDetails: (data) => dispatch(actions.updateCustodianChangeDetails(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchUserDetails: (data) => dispatch(actions.fetchUserDetails(data))
  // fetchBPRouteKey: (data) => dispatch(actions.fetchBPRouteKey(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(CustodianChange);
