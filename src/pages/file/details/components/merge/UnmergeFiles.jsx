import { createStructuredSelector } from 'reselect';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import {
  Button,
  t
} from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from 'pages/file/details/slice';
import { convertToLocalDate } from 'utils/date';
import { useParams } from 'react-router-dom';
import { getActionTriggered, getFiles, getTableLoader } from 'pages/common/selectors';
import { CommonTable } from 'common/components/Table';
import { DATE_FORMAT } from 'pages/common/constants';
import BackButton from 'common/components/BackButton/BackButton';
import NoNotesIcon from 'assets/NoNotesIcon';
import * as actions from '../../actions';
import MergeConfirmation from './MergeConfirmation.jsx';
import {
  getFileDetails, getMergeActionConfirmation, getMergedFiles, getUnMergeFilesParams
} from '../../selector';

const UnmergeFiles = ({
  mergedFiles,
  fetchMergedFiles,
  setFormTitle,
  setFileHeader,
  fetchFileDetails,
  fileDetails,
  actionTriggered,
  setMergeActionConfirmation,
  mergeActionConfirmation,
  setTableLoader,
  tableLoader,
  setUnMergeFilesParams,
  unMergeFilesParams
}) => {
  const [selectedSubFiles, setSelectedSubFiles] = useState([]);
  const [page, setPage] = useState(0);
  const sort = 10;
  const [totalItems, setTotalItems] = useState(0);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [tableData, setTableData] = useState([]);
  const params = useParams();

  const handleFileNo = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[400px] break-keep">{cellData.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const handleTitle = (fileData) => {
    let title;
    if (fileData?.row) {
      const cellData = fileData?.row;
      title = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.title}</div>;
    }
    return <div className="block">{title}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.applicantName}</div>;
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const headers = [
    {
      type: 'multi-select'
    },
    {
      header: t('fileNo'),
      alignment: 'left',
      field: 'fileNo',
      cell: (field) => handleFileNo(field)
    },
    {
      header: t('subject'),
      alignment: 'left',
      field: 'title',
      cell: (field) => handleTitle(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    }
  ];

  useEffect(() => {
    fetchFileDetails(params.fileNo);
    setUnMergeFilesParams({
      ...unMergeFilesParams,
      mainFileNo: params.fileNo,
      search: true
    });
  }, [params, mergeActionConfirmation]);

  useEffect(() => {
    if (unMergeFilesParams?.search) {
      fetchMergedFiles();
    }
  }, [unMergeFilesParams]);

  useEffect(() => {
    if (mergedFiles?.content?.length > 0) {
      setTableLoader({ loading: false, id: 'un-merge-file-search' });
      setTableData(mergedFiles?.content);
      setTotalItems(mergedFiles?.totalElements);
      setNumberOfElements(Number(mergedFiles.numberOfElements));
    } else {
      setTableLoader({ loading: false, id: 'un-merge-file-search' });
      setTableData([]);
      setTotalItems(0);
    }
  }, [mergedFiles]);

  const onPageClick = (data) => {
    setTableLoader({ loading: true, id: 'un-merge-file-search' });
    setPage(data);
    setUnMergeFilesParams({
      ...unMergeFilesParams,
      page: data
    });
  };

  useEffect(() => {
    setFormTitle({ title: t('unmerge'), variant: 'normal' });
    setFileHeader([{
      label: t('fileNumber'),
      value: params.fileNo
    }, {
      label: t('role'),
      value: fileDetails?.role
    }, {
      label: t('service'),
      value: fileDetails?.serviceName
    }, {
      label: t('date'),
      value: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL)
    }
    ]);
  }, []);

  const onRowCheck = (data) => {
    const filteredData = Array.from(data.selectedRows).map((row) => row.fileNo);
    setSelectedSubFiles(filteredData);
  };
  const unMergeFiles = () => {
    setMergeActionConfirmation(true);
  };
  return (
    <>
      <div className="col-span-12 pb-10">
        {tableData?.length > 0 ? (
          <>
            <div className="lg:col-span-12 md:col-span-12 col-span-12">
              <CommonTable
                variant="dashboard"
                columns={headers}
                tableData={tableData}
                onRowCheck={onRowCheck}
                onPageClick={onPageClick}
                itemsPerPage={sort}
                totalItems={totalItems}
                numberOfElements={numberOfElements}
                currentPage={page}
                paginationEnabled
                tableLoader={tableLoader?.loading && tableLoader?.id === 'un-merge-file-search'}
              />
            </div>
            <div className="col-span-12 flex justify-end items-center space-x-4 pt-4">
              <BackButton />
              <Button variant="secondary" onClick={unMergeFiles} isDisabled={selectedSubFiles.length === 0} isLoading={actionTriggered?.id === 'merge-action' && actionTriggered?.loading}>
                {t('unMerge')}
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="p-10 text-center bg-white p-5 rounded-lg">
              <NoNotesIcon width="100px" height="100px" className="mx-auto" />
              <h4>{t('noMergedFiles')}</h4>
            </div>
            <span className="flex justify-end mt-3"><BackButton /></span>
          </>
        )}
      </div>
      <MergeConfirmation
        open={mergeActionConfirmation}
        setMergeConfirmation={setMergeActionConfirmation}
        title={t('unmerge')}
        selectedSubFiles={selectedSubFiles}
        setSelectedSubFiles={setSelectedSubFiles}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  mergedFiles: getMergedFiles,
  files: getFiles,
  fileDetails: getFileDetails,
  actionTriggered: getActionTriggered,
  mergeActionConfirmation: getMergeActionConfirmation,
  tableLoader: getTableLoader,
  unMergeFilesParams: getUnMergeFilesParams
});

const mapDispatchToProps = (dispatch) => ({
  fetchMergedFiles: (data) => dispatch(actions.fetchMergedFiles(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setMergeActionConfirmation: (data) => dispatch(sliceActions.setMergeActionConfirmation(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setUnMergeFilesParams: (data) => dispatch(sliceActions.setUnMergeFilesParams(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(UnmergeFiles);
