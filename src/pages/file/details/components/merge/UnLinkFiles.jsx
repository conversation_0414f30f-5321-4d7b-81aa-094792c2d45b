import { createStructuredSelector } from 'reselect';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import {
  Button,
  IconButton,
  t
} from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from 'pages/file/details/slice';
import { convertToLocalDate } from 'utils/date';
import { useParams } from 'react-router-dom';
import { getActionTriggered, getFiles, getTableLoader } from 'pages/common/selectors';
import { CommonTable } from 'common/components/Table';
import { MERGE } from 'pages/file/details/constants.js';
import { DATE_FORMAT } from 'pages/common/constants';
import BackButton from 'common/components/BackButton/BackButton';
import NoNotesIcon from 'assets/NoNotesIcon';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import * as actions from '../../actions';
import {
  getFileDetails, getLinkActionConfirmation, getMergedFiles, getUnMergeFilesParams
} from '../../selector';
import LinkConfirmation from './LinkConfirmation';

const UnLinkFiles = ({
  linkedFiles,
  fetchLinkedFiles,
  setFormTitle,
  setFileHeader,
  fetchFileDetails,
  fileDetails,
  actionTriggered,
  setLinkActionConfirmation,
  linkActionConfirmation,
  tableLoader,
  unMergeFilesParams,
  setTableLoader,
  setUnMergeFilesParams
}) => {
  const [selectedSubFiles, setSelectedSubFiles] = useState([]);
  const [page, setPage] = useState(0);
  const sort = 10;
  const [totalItems, setTotalItems] = useState(0);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [tableData, setTableData] = useState([]);
  const params = useParams();

  const handleSelect = (data) => {
    const { row } = data;
    const selectedFile = selectedSubFiles?.length > 0 ? JSON.parse(JSON.stringify(selectedSubFiles)) : [];

    const find = selectedFile.findIndex((item) => item === row.fileNo);

    if (find > -1) {
      selectedFile.splice(find, 1);
    } else {
      selectedFile.push(row?.fileNo);
    }

    setSelectedSubFiles(selectedFile);
  };

  function checkBoxCheck(data) {
    const find = selectedSubFiles?.findIndex((item) => item === data);
    if (find > -1) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  const selectedFileRow = (data) => {
    return (
      data?.row?.fileNo !== params?.fileNo && (
        <IconButton
          variant="unstyled"
          style={{ textDecoration: 'none' }}
          icon={checkBoxCheck(data?.row?.fileNo)}
          onClick={() => handleSelect(data)}
        />
      )
    );
  };

  const handleFileNo = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[400px] break-keep">{cellData.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const handleTitle = (fileData) => {
    let title;
    if (fileData?.row) {
      const cellData = fileData?.row;
      title = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.title}</div>;
    }
    return <div className="block">{title}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.applicantName}</div>;
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const headers = [
    {
      cell: (field) => selectedFileRow(field)
    },
    {
      header: t('fileNo'),
      alignment: 'left',
      field: 'fileNo',
      cell: (field) => handleFileNo(field)
    },
    {
      header: t('subject'),
      alignment: 'left',
      field: 'title',
      cell: (field) => handleTitle(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    }
  ];

  useEffect(() => {
    if (linkedFiles?.content?.length > 0) {
      setTableLoader({ loading: false, id: 'un-merge-file-search' });
      setTableData(linkedFiles?.content);
      setTotalItems(linkedFiles?.totalElements);
      setNumberOfElements(Number(linkedFiles.numberOfElements));
    } else {
      setTableLoader({ loading: false, id: 'un-merge-file-search' });
      setTableData([]);
      setTotalItems(0);
    }
  }, [linkedFiles]);

  const onPageClick = (data) => {
    setTableLoader({ loading: true, id: 'un-link-file-search' });
    setPage(data);
    setUnMergeFilesParams({
      ...unMergeFilesParams,
      page: data
    });
  };

  useEffect(() => {
    fetchFileDetails(params.fileNo);
    setUnMergeFilesParams({
      ...unMergeFilesParams,
      fileType: MERGE.LINKED,
      mainFileNo: params.fileNo,
      search: true
    });
  }, [params, linkActionConfirmation]);

  useEffect(() => {
    if (unMergeFilesParams?.search) {
      fetchLinkedFiles();
    }
  }, [unMergeFilesParams]);

  useEffect(() => {
    setFormTitle({ title: t('unLink'), variant: 'normal' });
    setFileHeader([{
      label: t('fileNumber'),
      value: params.fileNo
    }, {
      label: t('role'),
      value: fileDetails?.role
    }, {
      label: t('service'),
      value: fileDetails?.serviceName
    }, {
      label: t('date'),
      value: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL)
    }
    ]);
  }, []);

  const cancel = () => {
    setSelectedSubFiles([]);
  };
  const onRowCheck = (data) => {
    const filteredData = Array.from(data.selectedRows).map((row) => row.fileNo);
    setSelectedSubFiles(filteredData);
  };
  const unLinkFiles = () => {
    setLinkActionConfirmation(true);
  };

  return (
    <>
      <div className="col-span-12 pb-10">
        {tableData?.length > 0 ? (
          <>
            <div className="lg:col-span-12 md:col-span-12 col-span-12">
              <CommonTable
                variant="dashboard"
                columns={headers}
                tableData={tableData}
                onRowCheck={onRowCheck}
                onPageClick={onPageClick}
                itemsPerPage={sort}
                totalItems={totalItems}
                numberOfElements={numberOfElements}
                currentPage={page}
                paginationEnabled
                tableLoader={tableLoader?.loading && tableLoader?.id === 'un-link-file-search'}
              />
            </div>
            <div className="col-span-12 flex justify-end items-center space-x-4 pt-4">
              <BackButton />
              <Button
                variant="secondary_outline"
                size="sm"
                mr={3}
                onClick={cancel}
              >
                {t('cancel')}
              </Button>
              <Button variant="secondary" onClick={unLinkFiles} isDisabled={selectedSubFiles.length === 0} isLoading={actionTriggered?.id === 'unlink-action' && actionTriggered?.loading}>
                {t('unLink')}
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="p-10 text-center bg-white p-5 rounded-lg">
              <NoNotesIcon width="100px" height="100px" className="mx-auto" />
              <h4>{t('noLinkedFiles')}</h4>
            </div>
            <span className="flex justify-end mt-3"><BackButton /></span>
          </>
        )}
      </div>
      <LinkConfirmation
        open={linkActionConfirmation}
        setLinkConfirmation={setLinkActionConfirmation}
        title={t('unLink')}
        selectedSubFiles={selectedSubFiles}
        setSelectedSubFiles={setSelectedSubFiles}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  linkedFiles: getMergedFiles,
  files: getFiles,
  fileDetails: getFileDetails,
  actionTriggered: getActionTriggered,
  linkActionConfirmation: getLinkActionConfirmation,
  tableLoader: getTableLoader,
  unMergeFilesParams: getUnMergeFilesParams
});

const mapDispatchToProps = (dispatch) => ({
  fetchLinkedFiles: (data) => dispatch(actions.fetchMergedFiles(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setLinkActionConfirmation: (data) => dispatch(sliceActions.setLinkActionConfirmation(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setUnMergeFilesParams: (data) => dispatch(sliceActions.setUnMergeFilesParams(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(UnLinkFiles);
