import { createStructuredSelector } from 'reselect';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { convertToLocalDate } from 'utils/date';
import { useParams, useLocation } from 'react-router-dom';
import _ from 'lodash';
import {
  t,
  FormController,
  Button,
  FormWrapper,
  Toast,
  IconButton
} from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  getFiles, getActionTriggered, getUserInfo, getTableLoader
} from 'pages/common/selectors';
import {
  DATE_FORMAT, FILE_STATUS, FILE_STATUS_FOR_API_PARAMS, NOTE_STATUS
} from 'pages/common/constants';
import BackButton from 'common/components/BackButton/BackButton';
import { CommonTable } from 'common/components/Table';
import { STATUS } from 'common/regex';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import * as actions from '../../actions';
import { actions as sliceActions } from '../../slice';
import { MergeSchema } from '../../validate.js';
import MergeConfirmation from './MergeConfirmation.jsx';
import { getFileDetails, getPullNotesDetails, getPullSearchParams } from '../../selector';

const SameSeatMergeFile = ({
  searchMergeFile,
  files,
  fetchFileDetails,
  fileDetails,
  setFormTitle,
  setFileHeader,
  actionTriggered,
  userInfo: { officeId = '', postIds = [], assigner = '' } = {},
  fetchPartialNotes,
  setPullSearchParams,
  pullSearchParams,
  pullNotesDetails,
  setTableLoader,
  tableLoader
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
      search: '',
      fileNumber: '',
      applicantName: '',
      year: '',
      fromDate: '',
      toDate: ''
    },
    resolver: yupResolver(MergeSchema)
  });
  const { errorTost } = Toast;
  const params = useParams();
  const location = useLocation();
  const [mergeConfirmation, setMergeConfirmation] = useState(false);
  const [selectedSubFiles, setSelectedSubFiles] = useState([]);
  const [title, setTitle] = useState('');
  const [isSearch, setIsSearch] = useState(false);
  const todate = watch('toDate');
  const search = watch('search');
  const fileNumber = watch('fileNumber');
  const year = watch('year');
  const fromDate = watch('fromDate');
  const applicantName = watch('applicantName');
  const [searchedFiles, setSearchedFiles] = useState([]);
  const [page, setPage] = useState(0);
  const sort = 10;
  const [totalItems, setTotalItems] = useState(0);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const statusChange = (stage) => {
    switch (stage) {
      case stage?.currentStage === FILE_STATUS.RUNNING:
        return 'bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center';
      case stage?.currentStage === FILE_STATUS.VERY_URGENT:
        return 'bg-[#E83A7A] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#FFFFFF] text-center';
      case stage?.currentStage === FILE_STATUS.URGENT:
        return 'bg-[#FED0D0] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#E33A7A] text-center';
      case stage?.currentStage === FILE_STATUS.PENDING:
        return 'bg-[#F0F3DE] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#8F914B] text-center';
      case stage?.currentStage === FILE_STATUS.DELAYED:
        return 'bg-[#BAEAFE] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#00B2EB] text-center';
      case stage?.currentStage === FILE_STATUS.NORMAL:
        return 'bg-[#C6E5D3] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#53C684] text-center';
      default:
        return 'bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center';
    }
  };
  const handleFileStatus = (val) => {
    let stat;
    if (val?.row) {
      const cellData = val?.row;
      stat = (
        <div className={statusChange(cellData)}>
          {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
        </div>
      );
    }
    return <div className="inline-block">{stat}</div>;
  };

  const handleSelect = (data) => {
    const { row } = data;
    const selectedFile = selectedSubFiles?.length > 0 ? JSON.parse(JSON.stringify(selectedSubFiles)) : [];

    const find = selectedFile.findIndex((item) => item === row.fileNo);

    if (find > -1) {
      selectedFile.splice(find, 1);
    } else {
      selectedFile.push(row?.fileNo);
    }

    setSelectedSubFiles(selectedFile);
  };

  function checkBoxCheck(data) {
    const find = selectedSubFiles?.findIndex((item) => item === data);
    if (find > -1) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  const selectedFileRow = (data) => {
    return (
      data?.row?.fileNo !== params?.fileNo && (
        <IconButton
          variant="unstyled"
          style={{ textDecoration: 'none' }}
          icon={checkBoxCheck(data?.row?.fileNo)}
          onClick={() => handleSelect(data)}
        />
      )
    );
  };

  const handleFileNo = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[400px] break-keep">{cellData.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const handleTitle = (fileData) => {
    let titleDetails;
    if (fileData?.row) {
      const cellData = fileData?.row;
      titleDetails = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.title}</div>;
    }
    return <div className="block">{titleDetails}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantNameDetails;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantNameDetails = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.applicantName}</div>;
    }
    return <div className="block">{applicantNameDetails}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const headers = [
    {
      cell: (field) => selectedFileRow(field)
    },
    {
      header: t('fileNo'),
      alignment: 'left',
      field: 'fileNo',
      cell: (field) => handleFileNo(field)
    },
    {
      header: t('title'),
      alignment: 'left',
      field: 'title',
      cell: (field) => handleTitle(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('status'),
      alignment: 'left',
      field: 'currentStage',
      cell: (field) => handleFileStatus(field)
    }
  ];

  useEffect(() => {
    setSearchedFiles([]);
    setIsSearch(false);
  }, [params]);

  const cancel = () => {
    setSelectedSubFiles([]);
    setIsSearch(false);
    reset();
    setSearchedFiles([]);
  };
  const linkFiles = () => {
    setMergeConfirmation(true);
  };
  const checkObjectValues = (obj) => {
    if (
      obj.search.trim() === ''
      && obj.fileNumber.trim() === ''
      && ((obj.fromDate !== null && obj.fromDate === '') || obj.fromDate === null)
      && ((obj.toDate !== null && obj.toDate === '') || obj.toDate === null)
      && ((obj.year !== null && obj.year.trim() === '') || obj.year === null)
      && obj.applicantName.trim() === ''
    ) {
      return true;
    }
    return false;
  };
  const onSearchFile = (data) => {
    if (checkObjectValues(data)) {
      errorTost({
        title: t('missingSections'),
        description: `${t('pleaseAddAtleastOne')}}`
      });
    } else {
      setTableLoader({ loading: true, id: 'merge-file-search' });
      setPullSearchParams({
        ...pullSearchParams,
        fileNo: data.fileNumber ? data.fileNumber : null,
        keyword: data.search ? data.search : null,
        fromDate: data.fromDate ? convertToLocalDate(data.fromDate, DATE_FORMAT.DATE_LOCAL) : null,
        toDate: data.toDate ? convertToLocalDate(data.toDate, DATE_FORMAT.DATE_LOCAL) : null,
        applicantName: data.applicantName ? data.applicantName : null,
        year: data.year ? new Date(data.year).getFullYear() : null,
        fileStatus: title === 'Merge' ? FILE_STATUS_FOR_API_PARAMS.RUNNING : null,
        size: 10,
        page: 0,
        officeId,
        postId: title === 'Merge' && postIds?.length > 0 ? postIds[0] : null,
        serviceCode: title === 'Merge' ? fileDetails?.serviceCode : null
      });
      setSearchedFiles([]);
      searchMergeFile();
      setIsSearch(true);
    }
  };
  useEffect(() => {
    if (pullNotesDetails?.content?.length > 0) {
      setTableLoader({ loading: false, id: 'merge-file-search' });
      setSearchedFiles(pullNotesDetails?.content);
      setTotalItems(pullNotesDetails?.totalElements);
      setNumberOfElements(Number(pullNotesDetails.numberOfElements));
    } else {
      setTableLoader({ loading: false, id: 'merge-file-search' });
      setSearchedFiles([]);
      setTotalItems(0);
    }
  }, [pullNotesDetails]);
  useEffect(() => {
    if (location) {
      if ((location.pathname).includes('/merge')) {
        setTitle('Merge');
      } else {
        setTitle('Link');
      }
    }
    fetchFileDetails(params.fileNo);
  }, [location]);

  useEffect(() => {
    if (files.length && isSearch) {
      let filteredFiles = files;

      if (title === 'Merge') {
        filteredFiles = files.filter((file) => file.serviceCode === fileDetails.serviceCode);
        if (filteredFiles.length === 0) {
          errorTost({
            title: t('merge'),
            description: t('searchFileError')
          });
        }
      }
    }
  }, [JSON.stringify(files), isSearch]);

  useEffect(() => {
    if (todate === null) {
      setValue('toDate', '');
    }
  }, [todate]);

  useEffect(() => {
    setFormTitle({ title: t(`${title.toLowerCase()}`), variant: 'normal' });
    setFileHeader([{
      label: t('fileNumber'),
      value: params.fileNo
    }, {
      label: t('role'),
      value: fileDetails?.role
    }, {
      label: t('service'),
      value: fileDetails?.serviceName
    }, {
      label: t('date'),
      value: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL)
    }
    ]);
  }, [title]);

  useEffect(() => {
    const notesSearchRequest = {
      fileNo: params?.fileNo,
      assigner,
      noteStatus: NOTE_STATUS.PARTIAL
    };
    if (assigner) {
      fetchPartialNotes(notesSearchRequest);
    }
  }, [JSON.stringify(assigner)]);

  const onPageClick = (data) => {
    setTableLoader({ loading: true, id: 'merge-file-search' });
    setPage(data);
    setPullSearchParams({
      ...pullSearchParams,
      page: data
    });
    searchMergeFile();
  };

  return (
    <>
      <div className="bg-white p-5 rounded-lg">
        <form
          onSubmit={handleSubmit(onSearchFile)}
          id="search-file"
        >
          <FormWrapper>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="fileNumber"
                type="text"
                label={t('concatLabel', { label: t('file'), type: t('number') })}
                control={control}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="applicantName"
                type="text"
                label={t('concatLabel', { label: t('applicant'), type: t('name') })}
                control={control}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="year"
                variant="outlined"
                type="select"
                label={t('year')}
                optionKey="name"
                control={control}
                errors={errors}
                isClearable
                options={_.range(1900, new Date().getFullYear() + 1).reverse().map((yr) => ({ name: yr.toString(), value: yr.toString() }))}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                type="date"
                name="fromDate"
                label={t('concatLabel', { label: t('from'), type: t('date') })}
                control={control}
                dateFormat="dd-MM-yyyy"
                maxDate={new Date()}
                errors={errors}
              />
            </div>
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                type="date"
                name="toDate"
                label={t('concatLabel', { label: t('to'), type: t('date') })}
                control={control}
                dateFormat="dd-MM-yyyy"
                maxDate={new Date()}
                errors={errors}
              />
            </div>

            <div className="col-span-12 text-right">
              <BackButton />
              <Button
                variant="secondary_outline"
                size="sm"
                mr={3}
                onClick={cancel}
              >
                {t('clear')}
              </Button>
              <Button
                variant="secondary"
                type="submit"
                form="search-file"
                isLoading={actionTriggered?.id === 'merge-file-search' && actionTriggered?.loading}
                isDisabled={search === '' && fileNumber === '' && applicantName === '' && year === ''
                  && fromDate === ''
                  && todate === ''}
              >
                {t('search')}
              </Button>
            </div>
          </FormWrapper>
        </form>

      </div>
      <div className="col-span-12 pb-10">
        {isSearch && (
          <CommonTable
            variant="dashboard"
            columns={headers}
            tableData={searchedFiles || []}
            onPageClick={onPageClick}
            itemsPerPage={sort}
            totalItems={totalItems}
            numberOfElements={numberOfElements}
            currentPage={page}
            paginationEnabled
            tableLoader={tableLoader?.loading && tableLoader?.id === 'merge-file-search'}
          />
        )}
        {searchedFiles?.length > 0 && isSearch && (
          <div className="col-span-12 flex justify-end items-center space-x-4 pt-3">
            <Button variant="secondary" onClick={linkFiles} isDisabled={selectedSubFiles.length === 0}>
              {title}
            </Button>
          </div>
        )}

      </div>

      <MergeConfirmation
        open={mergeConfirmation}
        setMergeConfirmation={setMergeConfirmation}
        setSelectedSubFiles={setSelectedSubFiles}
        selectedSubFiles={selectedSubFiles}
        title={title}
        reset={reset}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  files: getFiles,
  fileDetails: getFileDetails,
  actionTriggered: getActionTriggered,
  userInfo: getUserInfo,
  pullSearchParams: getPullSearchParams,
  pullNotesDetails: getPullNotesDetails,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  searchMergeFile: () => dispatch(actions.fetchPullNotesDetails()),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data)),
  setPullSearchParams: (data) => dispatch(sliceActions.setPullSearchParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(SameSeatMergeFile);
