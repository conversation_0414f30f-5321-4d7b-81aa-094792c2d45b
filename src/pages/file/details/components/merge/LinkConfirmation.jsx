import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  t,
  ModalOverlay
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { dark, light } from 'utils/color.js';
import { actions as commonSliceActions } from 'pages/common/slice';

import * as actions from '../../actions.js';

const LinkConfirmation = ({
  open,
  setLinkConfirmation,
  selectedSubFiles,
  title,
  setSelectedSubFiles,
  setActionTriggered,
  unLinkFiles
}) => {
  const params = useParams();
  const unLinkFile = () => {
    setActionTriggered({ loading: true, id: 'unlink-action' });
    const data = {
      mainFileNo: params.fileNo,
      linkFileNos: selectedSubFiles
    };
    unLinkFiles(data);
    setLinkConfirmation(false);
    setSelectedSubFiles([]);
  };
  return (
    <Modal isOpen={open} size="xl" onClose={() => setLinkConfirmation(false)}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader p={0} className="text-center">
          <h4 size="md" className="p-5 rounded-t-lg" style={{ background: light, color: dark }}>
            {title} {t('file')}
          </h4>
        </ModalHeader>
        <ModalBody>
          <div className="px-10 py-5">
            <div className="text-center text-[24px] font-[600] leading-[40px]">
              {t('areYouSureWantToUnLinkTheseFilesFromTheMainFile')}-{params.fileNo}
            </div>
          </div>
        </ModalBody>
        <ModalFooter style={{ background: light, color: dark }}>
          <div className="w-full text-center space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              onClick={() => setLinkConfirmation(false)}
            >
              {t('no')}
            </Button>
            <Button variant="secondary" onClick={unLinkFile}>
              {t('yes')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
});

const mapDispatchToProps = (dispatch) => ({
  unLinkFiles: (data) => dispatch(actions.unLinkFiles(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(LinkConfirmation);
