import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  t,
  ModalOverlay
} from 'common/components';
import { createStructuredSelector } from 'reselect';
import { useParams } from 'react-router-dom';
import { MERGE } from 'pages/file/details/constants.js';
import { connect } from 'react-redux';
import { dark, light } from 'utils/color.js';
import { actions as commonSliceActions } from 'pages/common/slice';

import * as actions from '../../actions.js';

const MergeConfirmation = ({
  open,
  setMergeConfirmation,
  selectedSubFiles,
  mergeFiles,
  title,
  linkFiles,
  setSelectedSubFiles,
  reset,
  setActionTriggered,
  unMergeFiles
}) => {
  const params = useParams();
  const mergeFile = () => {
    setActionTriggered({ loading: true, id: 'merge-action' });
    const data = {
      mainFileNo: params.fileNo
    };
    if (title === MERGE.LINK) {
      data.linkFileNos = selectedSubFiles;
      linkFiles(data);
      reset();
    } else if (title === MERGE.UNMERGE) {
      data.subFileNos = selectedSubFiles;
      unMergeFiles(data);
    } else {
      data.subFileNos = selectedSubFiles;
      mergeFiles(data);
      reset();
    }
    setMergeConfirmation(false);
    setSelectedSubFiles([]);
  };
  return (
    <Modal isOpen={open} size="xl" onClose={() => setMergeConfirmation(false)}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader p={0} className="text-center">
          <h4 size="md" className="p-5 rounded-t-lg" style={{ background: light, color: dark }}>
            {title} {t('file')}
          </h4>
        </ModalHeader>
        <ModalBody>
          <div className="px-10 py-5">
            <div className="text-center text-[24px] font-[600] leading-[40px]">
              {title === MERGE.UNMERGE ? t('areYouSureWantToUnMergeTheseFilesFromTheMainFile')
                : t('concatLabel', { label: t('areYouSureWantTo'), type: t(`${title.toLowerCase()}Desc`) })}-{params.fileNo}
            </div>
          </div>
        </ModalBody>
        <ModalFooter style={{ background: light, color: dark }}>
          <div className="w-full text-center space-x-4">
            <Button
              variant="secondary_outline"
              size="sm"
              onClick={() => setMergeConfirmation(false)}
            >
              {t('no')}
            </Button>
            <Button variant="secondary" onClick={mergeFile}>
              {t('yes')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
});

const mapDispatchToProps = (dispatch) => ({
  mergeFiles: (data) => dispatch(actions.mergeFiles(data)),
  linkFiles: (data) => dispatch(actions.linkFiles(data)),
  unMergeFiles: (data) => dispatch(actions.unMergeFiles(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(MergeConfirmation);
