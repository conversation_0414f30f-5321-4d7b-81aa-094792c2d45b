import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  Button, IconButton, Modal, ModalBody, ModalContent, ModalFooter, ModalHeader, ModalOverlay, Tooltip
} from '@ksmartikm/ui-components';
import { dark, light } from 'utils/color';
import CloseNew from 'assets/CloseNew';
import DocumentPreview from 'common/components/DocumentPreview';
import { getNoteCardDetails } from 'pages/common/selectors';
import { t } from 'common/components';
import { actions as sliceActions } from '../../../../slice';
import ListNotes from '../../../notes/ListNotes';

const Note = ({ fileDetails, setNoteExpand, noteCardDetails }) => {
  const [isOpenDocumentModal, setIsOpenDocumentModal] = useState(false);

  useEffect(() => {
    setNoteExpand(true);
  }, []);

  return (
    <>

      <ListNotes fileDetails={fileDetails} from="create-draft" isOpenDocumentModal={isOpenDocumentModal} setIsOpenDocumentModal={setIsOpenDocumentModal} />
      {
        isOpenDocumentModal && (
          <Modal isOpen={isOpenDocumentModal} size="3xl" onClose={() => setIsOpenDocumentModal(!isOpenDocumentModal)} closeOnOverlayClick={false} closeOnEsc={false}>
            <ModalOverlay />
            <ModalContent>
              <ModalHeader p={0} className="text-center" style={{ background: light, color: dark }}>
                <div className="flex">
                  <div className="flex-none">
                    <h4 size="md" className="p-5 rounded-t-lg">
                      {t('document')}
                    </h4>
                  </div>
                  <div className="flex-grow" />
                  <div className="flex-none justify-end pt-[15px] pr-[15px]">
                    <Tooltip label={t('close')}>
                      <IconButton variant="unstyled" onClick={() => { setIsOpenDocumentModal(!isOpenDocumentModal); }} leftIcon={<CloseNew />} />
                    </Tooltip>
                  </div>
                </div>

              </ModalHeader>
              <ModalBody>
                <DocumentPreview preview={noteCardDetails} from="summary" />
              </ModalBody>
              <ModalFooter style={{ background: light, color: dark }}>
                <div className="w-full text-right space-x-4">
                  <Button
                    variant="secondary_outline"
                    size="sm"
                    onClick={() => setIsOpenDocumentModal(!isOpenDocumentModal)}
                  >
                    {t('close')}
                  </Button>

                </div>
              </ModalFooter>
            </ModalContent>
          </Modal>
        )
      }
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  noteCardDetails: getNoteCardDetails
});

const mapDispatchToProps = (dispatch) => ({
  setNoteExpand: (data) => dispatch(sliceActions.setNoteExpand(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Note);
