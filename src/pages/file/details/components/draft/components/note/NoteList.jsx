import React, { useState, useEffect } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  t
} from 'common/components';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { NOTE_STATUS } from 'pages/common/constants';
import NoteCard from 'common/components/NotesCard/NoteCard';
import { useGenerateDocs } from 'hooks/useGenerateDocs';
import { baseApiURL } from 'utils/http';
import PreviewThumb from 'common/components/DocumentPreview/PreviewThump';
import NoNotesIcon from 'assets/NoNotesIcon';
import * as actions from '../../../../actions';
import {
  getNotes
} from '../../../../selector';

const ListNotes = (props) => {
  const {
    fetchNotes,
    notes
    // fetchFileDocuments
  } = props;

  const params = useParams();
  const [notesData, setNotesData] = useState([]);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const [flag, setFlag] = useState(false);
  const [url, setUrl] = useState('');
  const [contentType, setContentType] = useState('');
  const [content, setContent] = useState({});

  const { loading, previewData } = useGenerateDocs({
    url, flag, contentType, content
  });

  useEffect(() => {
    if (notes) {
      const updated = notes?.content || [];
      if (page === 0) {
        setNotesData(updated);
      } else {
        setNotesData([...notesData, ...updated]);
      }
      if (notes?.last) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      // fetchFileDocuments(params.fileNo);
    } else {
      setNotesData([]);
    }
  }, [notes]);

  useEffect(() => {
    setNotesData([]);
    setPage(0);
    fetchNotes({
      fileNo: params.fileNo, size: 3, page: 0, sortDirection: 'asc', noteStatus: NOTE_STATUS.COMPLETED
    });
  }, [params.fileNo]);

  const fetchScrollNotes = () => {
    fetchNotes({
      fileNo: params.fileNo, size: 3, page: page + 1, sortDirection: 'asc', noteStatus: NOTE_STATUS.COMPLETED
    });
    setPage(page + 1);
  };

  const handlePreview = (doc) => {
    setUrl(`${baseApiURL}${doc.link}`);
    setContentType(doc.contentType);
    setContent(doc.content);
    setFlag(!flag);
  };

  const noteAttachments = (item) => {
    return item?.notesDocsDetails?.map((doc) => (
      <PreviewThumb
        key={doc.id}
        handlePreview={(data) => handlePreview(data)}
        item={doc}
        preview={previewData}
        fileType={doc?.contentType}
        loading={loading}
        from="note-card"
        index={doc?.documentName}
      />
    ));
  };

  return (
    <div>
      <div className="flex gap-5 mb-3" />

      <div className="col-span-12">
        {
          notesData?.length === 0
            ? (
              <div className="text-center min-h-[200px]">
                <div className="w-[150px] mx-auto m-10 py-10">
                  <NoNotesIcon width="150px" />
                </div>
                {t('noNotesFound')}
              </div>
            )
            : (
              <InfiniteScroll
                dataLength={notesData?.length}
                next={fetchScrollNotes}
                hasMore={hasMore}
                loader={<h4>Loading...</h4>}
                height={500}
                className="pr-6"
              >
                <div style={{ height: 510 }}>
                  {
                    notesData?.length > 0 && notesData.map((item) => {
                      return (
                        <NoteCard
                          key={item.id}
                          item={item}
                          attachments={noteAttachments(item)}
                        />
                      );
                    })

                  }

                </div>
              </InfiniteScroll>
            )
        }
      </div>

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  notes: getNotes
});

const mapDispatchToProps = (dispatch) => ({
  fetchNotes: (data) => dispatch(actions.fetchNotes(data)),
  fetchFileDocuments: (data) => dispatch(actions.fetchFileDocuments(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ListNotes);
