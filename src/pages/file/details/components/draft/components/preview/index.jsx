import React from 'react';
import { PdfViewer, t } from 'common/components';
import { Spinner } from '@ksmartikm/ui-components';
import NoDraftsIcon from 'assets/NoDraftsIcon';

const DraftPreview = (props) => {
  const {
    draftId, previewData, loading
  } = props;

  return (
    <div className="overflow-y-auto h-[750px]">
      <div>
        {draftId ? (
          <div className="text-center max-w-[620px] m-auto">
            {loading ? (
              <div className="min-h-[700px] pt-[320px] text-center ">
                <Spinner />
              </div>
            ) : (
              <PdfViewer
                data={previewData}
                type="scroll"
                variant="normal"
              />
            )}
          </div>
        ) : (
          <div className="text-center min-h-[700px]">
            <div className="w-[300px] mx-auto m-10 py-10">
              <NoDraftsIcon width="300px" />
            </div>
            {t('noDraftPreview')}
          </div>
        )}
      </div>
    </div>
  );
};

export default DraftPreview;
