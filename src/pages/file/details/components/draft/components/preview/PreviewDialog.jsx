import { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Modal,
  ModalBody,
  ModalContent,
  ModalOverlay,
  t,
  Tooltip,
  IconButton,
  <PERSON>dalFooter,
  <PERSON><PERSON>
} from 'common/components';

import FullScreenIcon from 'assets/FullScreen';
import CloseOutlineIcon from 'assets/CloseOutline';
import { dark } from 'utils/color';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { EMPLOYEE_ROLES } from 'common/constants';
import { CONFIRMATION_TYPE } from 'pages/file/details/constants';
import DraftPreview from '../DraftPreview';

const DraftPreviewDialog = (props) => {
  const {
    open,
    close,
    draftPreview,
    selectedDraft,
    draftButtons = () => { },
    returnDraft,
    triggerReject,
    approveDraft,
    triggerDraftComplete,
    checkRoleByFileRole,
    actionTriggered,
    fileDetails
  } = props;

  const [full, setFull] = useState(false);

  const handleFull = () => {
    setFull(!full);
  };

  const handlePrint = () => {
    printBlob(draftPreview);
  };

  const handleDownload = () => {
    downloadBlob({ blob: draftPreview, fileName: `KSMART-DRAFT-${selectedDraft?.correspondenceType}.pdf` });
  };

  return (
    <Modal size={full && 'full'} isOpen={open}>
      <ModalOverlay />
      <ModalContent borderRadius={16} style={{ background: '#d4d4d4', maxWidth: full ? '100%' : '520px' }}>
        <ModalBody py={6}>
          <div className="flex items-center bg-white rounded-lg">
            <div className="flex-grow text-left font-bold pl-3" style={{ color: dark }}>
              {t('draftPreview')}
            </div>
            <Tooltip label={t('fullScreen')}>
              <IconButton variant="unstyled" onClick={() => handleFull()} leftIcon={<FullScreenIcon width="21px" height="21px" />} />
            </Tooltip>
            {(selectedDraft?.status === 'APPROVED' || selectedDraft?.draftStage === 3)
              && (
                <>
                  <Tooltip label={t('print')}>
                    <IconButton variant="unstyled" onClick={() => handlePrint()} leftIcon={<PrintIcon width="21px" height="21px" />} />
                  </Tooltip>
                  <Tooltip label={t('download')}>
                    <IconButton variant="unstyled" onClick={() => handleDownload()} leftIcon={<DownloadIcon width="24px" height="24px" color="#000" />} />
                  </Tooltip>
                </>
              )}
            <Tooltip label={t('close')}>
              <IconButton variant="unstyled" onClick={() => close()} leftIcon={<CloseOutlineIcon width="24px" height="24px" color="#000" />} />
            </Tooltip>
          </div>
          <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 220px)' }} className="overflow-y-auto mt-5 rounded-lg">
            <DraftPreview draftPreview={draftPreview} full={full} />
          </div>
        </ModalBody>
        <ModalFooter>
          {draftButtons() && (
            <>
              {(fileDetails?.role === EMPLOYEE_ROLES.VERIFIER || fileDetails?.role === EMPLOYEE_ROLES.APPROVER || fileDetails?.role === EMPLOYEE_ROLES.RECOMMEND) && <Button variant="secondary_outline" type="submit" form="create-draft" className="mx-2" onClick={returnDraft} isLoading={actionTriggered?.id === CONFIRMATION_TYPE.RETURN && actionTriggered?.loading}>{t('needReview')}</Button>}
              {fileDetails?.role === EMPLOYEE_ROLES.APPROVER && <Button variant="secondary_outline" type="submit" form="create-draft" className="mx-2" onClick={triggerReject} isLoading={actionTriggered?.id === CONFIRMATION_TYPE.REJECT && actionTriggered?.loading}>{t('reject')}</Button>}

              {(fileDetails?.role === EMPLOYEE_ROLES.VERIFIER || fileDetails?.role === EMPLOYEE_ROLES.RECOMMEND) && <Button variant="secondary_outline" type="submit" form="create-draft" className="mx-2" onClick={approveDraft} isLoading={actionTriggered?.id === 'verifier-recommending-officer' && actionTriggered?.loading}>{t('approve')}</Button>}

              <Button variant="secondary" className="mx-2" border="none" type="submit" form="create-draft" onClick={triggerDraftComplete} isLoading={actionTriggered?.id === checkRoleByFileRole(fileDetails?.role) && actionTriggered?.loading}>
                {checkRoleByFileRole(fileDetails?.role)}
              </Button>
            </>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
});

const mapDispatchToProps = () => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftPreviewDialog);
