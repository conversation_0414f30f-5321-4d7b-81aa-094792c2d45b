import { PdfViewer, Spinner } from '@ksmartikm/ui-components';
import NoDraftsIcon from 'assets/NoDraftsIcon';
import { t } from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

const DraftPreview = (props) => {
  const {
    draftPreview,
    loading,
    full
  } = props;

  return (
    <div>
      <div>
        <div>
          {draftPreview ? (
            <div className="text-center w-full my-auto">
              {loading ? (
                <div className="pt-[320px] text-center" style={{ height: full ? 'calc(100vh - 150px)' : 'calc(100vh - 50px)' }}>
                  <Spinner />
                </div>
              ) : (
                <PdfViewer
                  data={draftPreview}
                  type="scroll"
                  variant="normal"
                />
              )}
            </div>
          ) : (
            <div className="text-center mt-20">
              <div className="w-[300px] mx-auto py-10">
                <NoDraftsIcon width="300px" />
              </div>
              <span>{t('noDraftPreview')}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
});

const mapDispatchToProps = () => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftPreview);
