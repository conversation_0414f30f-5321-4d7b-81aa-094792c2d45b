import {
  I<PERSON><PERSON><PERSON>on, Tooltip
} from '@ksmartikm/ui-components';
import { t } from 'common/components';
import { getTriggerPrint, getUserInfo } from 'pages/common/selectors';
import { DRAFT_PDF_URL } from 'pages/file/details/constants';
import { getDraftDataById, getSelectedCorrespondence } from 'pages/file/details/selector';
import { useGeneratePdf } from 'hooks/useGeneratePdf';
import { actions as sliceCommonActions } from 'pages/common/slice';
import * as commonActions from 'pages/common/actions';
import * as actions from 'pages/file/details/actions.js';

import React, { useState, useEffect } from 'react';

import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { printBlob } from 'utils/printBlob';
import PrintIcon from 'assets/Printer';
import { secondary } from 'utils/color';
import { downloadBlob } from 'utils/downloadBlob';
import DownloadIcon from 'assets/Download';

const DraftPrint = (props) => {
  const {
    draftId,
    selectedCorrespondence,
    userInfo,
    fetchCorrespondTypeDetails,
    fetchDraftById
  } = props;

  const urlDraftPdf = (draftId && userInfo?.id && selectedCorrespondence[0]?.code.toLowerCase()) ? `${DRAFT_PDF_URL}?draftId=${draftId}&officeId=${userInfo?.id}&template=${selectedCorrespondence[0]?.code.toLowerCase()}` : null;
  const [flag, setFlag] = useState(false);
  const [activeAction, setActiveAction] = useState('print');

  const { previewData: pdfPreviewData } = useGeneratePdf({ url: urlDraftPdf, flag });

  useEffect(() => {
    if (draftId) {
      fetchCorrespondTypeDetails();
      fetchDraftById(draftId);
    }
  }, [draftId]);

  const printDraft = () => {
    setFlag(true);
    setActiveAction('print');
  };

  const downloadDraft = () => {
    setActiveAction('download');
    setFlag(true);
  };

  useEffect(() => {
    if (pdfPreviewData && flag && draftId) {
      if (activeAction === 'print') {
        printBlob(pdfPreviewData);
      } else {
        downloadBlob({ blob: pdfPreviewData, fileName: `KSMART-DRAFT-${selectedCorrespondence[0]?.code}.pdf` });
      }
      setFlag(false);
    }
  }, [pdfPreviewData]);

  return (
    <div>
      <Tooltip label={t('print')}>
        <IconButton variant="ghost" className="bg-none" onClick={printDraft} icon={<PrintIcon width="30px" height="30px" color={secondary} />} />
      </Tooltip>

      <Tooltip label={t('download')}>
        <IconButton variant="ghost" className="bg-none" onClick={downloadDraft} icon={<DownloadIcon width="30px" height="30px" color={secondary} />} />
      </Tooltip>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  draftDataById: getDraftDataById,
  selectedCorrespondence: getSelectedCorrespondence,
  userInfo: getUserInfo,
  triggerPrint: getTriggerPrint
});

const mapDispatchToProps = (dispatch) => ({
  setActiveBlob: (data) => dispatch(sliceCommonActions.setActiveBlob(data)),
  setTriggerPrint: (data) => dispatch(sliceCommonActions.setTriggerPrint(data)),
  fetchCorrespondTypeDetails: () => dispatch(commonActions.fetchCorrespondTypeDetails()),
  fetchDraftById: (data) => dispatch(actions.fetchDraftById(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(DraftPrint);
