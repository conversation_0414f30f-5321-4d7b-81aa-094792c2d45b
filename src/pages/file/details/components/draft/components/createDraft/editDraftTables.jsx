import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import Delete from 'assets/delete';
import Edit from 'assets/Edit';
import { CommonTable } from 'common/components/Table';
import TableView from 'assets/TableView';
import { t } from 'common/components';
import * as actions from '../../../../actions';
import { getApplicantDetails } from '../../../../selector';

const EditDraftTables = ({
  draftAddress,
  enclosureDocuments,
  setEnclosureData,
  setOpen,
  setAddressForEditing
}) => {
  const editActions = (row, field) => {
    const data = row;
    data.addressType = draftAddress.addressType;
    if (field === 'address') {
      setOpen(true);
      setAddressForEditing(data);
    }
  };
  const deleteActions = (row, field) => {
    if (field === 'enclosure') {
      setEnclosureData(enclosureDocuments.filter((obj) => obj.id !== row.id));
    }
  };

  const addressHeaders = [
    {
      header: t('name'),
      alignment: 'left',
      field: 'name'
    },
    {
      header: t('address'),
      alignment: 'left',
      field: 'address'
    },
    {
      header: t('officerName'),
      alignment: 'left',
      field: 'officerName'
    },
    {
      header: t('officeName'),
      alignment: 'left',
      field: 'officeName'
    },
    {
      header: t('edit'),
      alignment: 'left',
      type: 'actions',
      actions: [{
        onClick: (row) => {
          editActions(row, 'address');
        },
        icon: <Edit />,
        label: t('edit')
      }]
    },
    {
      header: t('delete'),
      alignment: 'left',
      type: 'actions',
      actions: [{
        onClick: (row) => {
          deleteActions(row, 'address');
        },
        icon: <Delete />,
        label: t('delete')
      }]
    }
  ];
  const enclosureHeaders = [
    {
      header: t('enclosureName'),
      alignment: 'left',
      field: 'enclosureName'
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [{
        icon: <TableView />,
        label: t('view')
      }]
    },
    {
      header: t('delete'),
      alignment: 'left',
      type: 'actions',
      actions: [{
        onClick: (row) => {
          deleteActions(row, 'enclosure');
        },
        icon: <Delete />,
        label: t('delete')
      }]
    }
  ];

  return (
    <div className="lg:col-span-12 md:col-span-12 col-span-12">
      <h1 className="mt-5"><strong>{t('address')}</strong></h1>
      <div className="mt-5">
        <CommonTable
          variant="draft"
          tableData={draftAddress && draftAddress.applicantAddresses.length ? draftAddress.applicantAddresses : []}
          columns={addressHeaders}
        />
      </div>
      <h1 className="mt-5"><strong>{t('enclosure')}</strong></h1>
      <div className="mt-5">
        <CommonTable
          variant="draft"
          tableData={enclosureDocuments}
          columns={enclosureHeaders}
        />
      </div>
      <h1 className="mt-5"><strong>{t('copyTo')}</strong></h1>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  applicantDetails: getApplicantDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchApplicantDetails: (data) => dispatch(actions.fetchApplicantDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(EditDraftTables);
