import { memo, useState, useEffect } from 'react';
import {
  <PERSON><PERSON>, FormC<PERSON>roller, t, Collapse
} from 'common/components';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getCounterOperator,
  getDispatchClerkDetails,
  getModeOfDispatch,
  getOfficeType,
  getUserInfo
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';

const DispatchFields = ({
  control,
  userInfo,
  onChange,
  dispatchFieldsOpen,
  modeOfDispatchDetails,
  fetchOfficeType,
  officeType,
  dispatchClerkDetails,
  fetchModeOfDispatch,
  fetchCounterOperator,
  counterOperatorDropdown,
  fetchDispatchClerkDetails
}) => {
  const [post, setPost] = useState([]);
  const [isDispatchFieldsOpen, setIsDispatchFieldsOpen] = useState(dispatchFieldsOpen);

  const toggleDispatchFields = () => setIsDispatchFieldsOpen(!isDispatchFieldsOpen);

  useEffect(() => {
    if (dispatchClerkDetails?.length > 0) {
      setPost(
        dispatchClerkDetails?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName ? ps.employeeName : ''} - ${
            ps.penNo ? ps.penNo : ''
          }- ${ps.postName ? ps.postName : ps.postNameInEng}`
        }))
      );
    }
  }, [dispatchClerkDetails]);

  useEffect(() => {
    setIsDispatchFieldsOpen(dispatchFieldsOpen);
  }, [dispatchFieldsOpen]);

  useEffect(() => {
    fetchCounterOperator();
    fetchModeOfDispatch();
  }, []);

  useEffect(() => {
    if (Object.keys(userInfo).length > 0) {
      fetchOfficeType({ officeLbCode: userInfo?.id });
      fetchDispatchClerkDetails({ officeId: userInfo?.id });
    }
  }, [JSON.stringify(userInfo)]);

  return (
    <>
      <div className="col-span-12 flex items-center gap-3">
        <Button
          variant="link"
          style={{
            textDecoration: 'none',
            color: '#456C86',
            fontWeight: 500
          }}
          leftIcon={isDispatchFieldsOpen ? <CheckedBox /> : <UnCheckedBox />}
          onClick={() => { toggleDispatchFields(); onChange(!isDispatchFieldsOpen); }}
        >
          <span className="ml-1 text-[14px]">{t('dispatch')}</span>
        </Button>
      </div>

      <Collapse open={isDispatchFieldsOpen} className="col-span-12 flex">
        <div className="grid grid-cols-12 gap-x-5 gap-y-[30px] pt-2 mb-2">
          <div className="sm:col-span-6 col-span-12">
            <FormController
              name="modeOfDispatch"
              type="select"
              label={t('modeOfDispatch')}
              control={control}
              options={modeOfDispatchDetails || []}
              optionKey="id"
              isClearable
              menuPosition="fixed"
              menuPlacement="top"
            />
          </div>
          <div className="sm:col-span-6 col-span-12">
            <FormController
              name="officeType"
              type="select"
              label={t('officeType')}
              placeholder={t('select')}
              control={control}
              optionKey="id"
              options={officeType || []}
              isClearable
              menuPosition="fixed"
              menuPlacement="top"
            />
          </div>
          <div className="sm:col-span-6 col-span-12">
            <FormController
              name="dispatchSection"
              type="select"
              label={t('dispatchSection')}
              placeholder={t('select')}
              control={control}
              optionKey="id"
              isClearable
              options={counterOperatorDropdown || []}
              menuPosition="fixed"
              menuPlacement="top"
            />
          </div>
          <div className="sm:col-span-6 col-span-12">
            <FormController
              name="user"
              type="select"
              label={t('user')}
              placeholder={t('select')}
              control={control}
              optionKey="postId"
              options={post?.filter((item) => item?.penNo !== null)}
              isClearable
              menuPosition="fixed"
              menuPlacement="top"
            />
          </div>
        </div>
      </Collapse>
    </>
  );
};

const MemoizedDispatchFields = memo(DispatchFields);

const mapStateToProps = createStructuredSelector({
  modeOfDispatchDetails: getModeOfDispatch,
  officeType: getOfficeType,
  userInfo: getUserInfo,
  counterOperatorDropdown: getCounterOperator,
  dispatchClerkDetails: getDispatchClerkDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchModeOfDispatch: (data) => dispatch(commonActions.fetchModeOfDispatch(data)),
  fetchOfficeType: (data) => dispatch(commonActions.fetchOfficeType(data)),
  fetchCounterOperator: (data) => dispatch(commonActions.fetchCounterOperator(data)),
  fetchDispatchClerkDetails: (data) => dispatch(commonActions.fetchDispatchClerkDetails(data))
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MemoizedDispatchFields);
