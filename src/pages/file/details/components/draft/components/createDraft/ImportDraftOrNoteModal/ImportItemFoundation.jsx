import {
  Button, Checkbox, TextInput, t
} from 'common/components';
import Pagination from 'common/components/Table/Pagination';
import { useCallback, useState, memo } from 'react';

const ImportItemFoundation = ({
  item,
  params,
  totalCount = 0,
  pageSize = 10,
  numberOfElements,
  onSearch = () => {},
  onPageChange = () => {}
}) => {
  const [innerSearch, setInnerSearch] = useState('');
  const [isSameFile, setIsSameFile] = useState(false);
  const [page, setPage] = useState(1);

  const handleSearchChange = useCallback((e) => {
    setInnerSearch(e?.target?.value);
  }, []);

  const handlePageChange = useCallback((p) => {
    setPage(p);
    onPageChange(p);
  }, [onPageChange]);

  const handleSearchSubmit = useCallback(() => {
    onSearch(innerSearch);
  }, [innerSearch]);

  const handleIsSameFileChange = useCallback((e) => {
    setIsSameFile(e?.target?.checked);
    if (e?.target?.checked) {
      setInnerSearch(params?.fileNo);
      onSearch(params?.fileNo);
    } else {
      setInnerSearch('');
      onSearch('');
    }
  }, []);

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center gap-3">
        <span className="text-[#232F50] font-bold">{t('fileNo')}.</span>
        <Checkbox onChange={handleIsSameFileChange}>
          <span className="text-[#456C86] font-medium">{t('sameFile')}</span>
        </Checkbox>
      </div>
      <TextInput
        size="sm"
        placeholder={t('search')}
        disabled={isSameFile}
        value={innerSearch}
        onChange={handleSearchChange}
        rightContent={(
          <Button
            py={2}
            px={6}
            type="submit"
            fontSize="sm"
            variant="secondary"
            disabled={isSameFile}
            onClick={handleSearchSubmit}
          >
            {t('search')}
          </Button>
        )}
      />
      <div className="bg-[#E7EFF5] rounded-[8px] p-4 mt-1">{item}</div>
      <Pagination
        currentPage={page}
        totalCount={totalCount}
        pageSize={pageSize}
        numberOfElements={numberOfElements}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default memo(ImportItemFoundation);
