import { t, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON> } from 'common/components';
// import withActionModal from 'pages/common/components/withActionModal';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { CopyToSchema } from 'pages/file/details/validate';
import { useEffect } from 'react';
import OutlineTag from 'pages/common/components/OutlineTag';
// import PlusIconTiny from 'assets/PlusIconTiny';
import DispatchFields from './DispatchFields';

const defaultValues = {
  name: '',
  address: '',
  modeOfDispatch: null,
  user: null,
  isDispatchFieldOpen: false
};

const DraftCopyToModal = ({
  copyToReceiversEditIndex,
  selectedReceivers,
  setSelectedReceivers,
  handleEditCopyToReceivers,
  setCopyToReceiversEditIndex,
  copyToData = [],
  onSave,
  onClose
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch
  } = useForm({
    defaultValues,
    resolver: yupResolver(CopyToSchema),
    mode: 'onChange'
  });

  const handleDispatchCheckboxChange = (isOpen) => {
    setValue('isDispatchFieldOpen', isOpen);

    if (!isOpen) {
      setValue('modeOfDispatch', null);
      setValue('user', null);
    }
  };

  const handleOnSubmit = (values, type) => {
    const isSave = type === 'save';
    const isNewEntry = copyToReceiversEditIndex === null;

    if (isSave && !selectedReceivers?.length) {
      const updated = [...copyToData];

      if (isNewEntry) {
        updated.push({ ...values });
      } else {
        updated[copyToReceiversEditIndex] = { ...values };
      }

      onSave(updated);
    } else if (!isSave) {
      setSelectedReceivers((prevReceivers) => {
        const updatedReceivers = [...prevReceivers];

        if (isNewEntry) {
          updatedReceivers.push({ ...values });
        } else {
          updatedReceivers[copyToReceiversEditIndex] = { ...values };
        }

        return updatedReceivers;
      });
    }

    setCopyToReceiversEditIndex(null);
    reset(defaultValues);
  };

  const handleRemoveSelectedCopyToReceivers = (i) => {
    setCopyToReceiversEditIndex(null);
    setSelectedReceivers((prv) => prv?.filter((p, idx) => i !== idx));
  };

  const handleSave = () => {
    const shouldSubmit = !selectedReceivers?.length;

    if (shouldSubmit) {
      handleSubmit((values) => handleOnSubmit(values, 'save'))();
    } else {
      const name = watch('name');

      if (name) {
        const updatedReceivers = [...selectedReceivers];
        const values = getValues();
        updatedReceivers.push(values);

        onSave([...copyToData, ...updatedReceivers]);
      } else {
        onSave([...copyToData, ...selectedReceivers]);
      }
    }
  };

  const handleDispatchUserChange = (user) => {
    setValue('user', user?.postId);
    setValue('dispatchClerkName', user?.employeeName);
    setValue('dispatchClerkPenNo', user?.penNo);
  };

  useEffect(() => {
    if (copyToData?.length) {
      const isEditing = copyToReceiversEditIndex !== null;

      const currentData = isEditing
        ? copyToData[copyToReceiversEditIndex]
        : defaultValues;

      reset({
        ...currentData,
        modeOfDispatch: currentData?.modeOfDispatch
          ? Number(currentData?.modeOfDispatch)
          : null
      });
    }
  }, [copyToReceiversEditIndex, copyToData, reset, defaultValues]);

  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-7">
          <FormController
            required
            name="name"
            type="text"
            label={`${t('name')}`}
            errors={errors}
            control={control}
          />
          <FormController
            name="address"
            type="textarea"
            label={`${t('address')}`}
            errors={errors}
            control={control}
          />
        </div>
        <div className="mt-4 mb-2">
          <DispatchFields
            control={control}
            errors={errors}
            dispatchFieldsOpen={watch('isDispatchFieldOpen')}
            onChange={handleDispatchCheckboxChange}
            onUserChange={handleDispatchUserChange}
          />
        </div>
        {/* {copyToReceiversEditIndex === null && (
          <div className="flex justify-end mt-2">
            <Button
              variant="primary_outline"
              py={2.5}
              px={4}
              fontSize="xs"
              onClick={handleSubmit(handleOnSubmit)}
            >
              <PlusIconTiny stroke="#00B2EB" strokeWidth="2" />
            </Button>
          </div>
        )} */}
        <div className="flex flex-wrap gap-4">
          {selectedReceivers?.map((item, i) => (
            <OutlineTag
              key={item?.name}
              text={`${item?.name} - ${item?.address}`}
              onTagClick={() => handleEditCopyToReceivers(i)}
              onTagRemove={() => handleRemoveSelectedCopyToReceivers(i)}
            />
          ))}
        </div>
      </div>
      <div className="mb-2 mt-4 flex justify-end items-center gap-2">
        <Button
          type="button"
          variant="secondary_outline"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={onClose}
        >
          {t('cancel')}
        </Button>
        <Button
          variant="secondary"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={handleSave}
        >
          {t('add')}
        </Button>
      </div>
    </>
  );
};

export default DraftCopyToModal;
