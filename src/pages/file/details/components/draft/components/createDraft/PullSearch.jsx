import {
  <PERSON><PERSON>,
  Checkbox, FormController, FormLabel, t
} from 'common/components';
import { DATE_FORMAT } from 'pages/common/constants';
import React from 'react';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { convertToLocalDate } from 'utils/date';
import { getPullSearchParams } from 'pages/file/details/selector';
import { PULL_TYPES } from 'pages/file/details/constants';
import { getActionTriggered, getUserInfo } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from '../../../../slice';

const PullSearch = ({
  params,
  setPullSearchParams,
  pullSearchParams,
  submit,
  setIsChecked,
  isChecked,
  type,
  actionTriggered,
  setActionTriggered,
  setTableLoader,
  clear,
  userInfo,
  fileDetails
}) => {
  const {
    control,
    handleSubmit, setValue, watch
  } = useForm({
    mode: 'all',
    defaultValues: {
      fileNumber: null,
      keywordSearch: null,
      fromDate: null,
      toDate: null,
      size: 10,
      page: 0
    }
  });

  const handleCheck = (e) => {
    if (e.target.checked) {
      setIsChecked(true);
      setValue('fileNumber', params.fileNo);
    } else {
      setIsChecked(false);
      setValue('fileNumber', null);
    }
  };

  const clearSearch = () => {
    clear();
    setValue('fileNumber', null);
    setValue('keywordSearch', null);
    setValue('fromDate', null);
    setValue('toDate', null);
  };

  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'pull-search' });
    setTableLoader({ loading: true, id: 'pull-search' });
    if (type === PULL_TYPES.PULL_DRAFT) {
      setPullSearchParams({
        ...pullSearchParams,
        pullFileNo: data.fileNumber,
        keyword: data.keywordSearch,
        fromDate: convertToLocalDate(data.fromDate, DATE_FORMAT.DATE_LOCAL),
        toDate: convertToLocalDate(data.toDate, DATE_FORMAT.DATE_LOCAL),
        size: 10,
        page: 0,
        officeId: userInfo?.id,
        fileNo: params.fileNo,
        postId: !data.fileNumber ? fileDetails?.postId : null
      });
      submit();
    } else if (type === PULL_TYPES.PULL_NOTES) {
      setPullSearchParams({
        ...pullSearchParams,
        keyword: data.keywordSearch,
        fromDate: convertToLocalDate(data.fromDate, DATE_FORMAT.DATE_LOCAL),
        toDate: convertToLocalDate(data.toDate, DATE_FORMAT.DATE_LOCAL),
        size: 10,
        page: 0,
        officeId: userInfo?.id,
        fileNo: data.fileNumber,
        pullFileNo: null,
        postId: fileDetails?.postId
      });
      submit();
    }
  };
  const fileNumber = watch('fileNumber');
  const keywordSearch = watch('keywordSearch');
  const fromDate = watch('fromDate');
  const toDate = watch('toDate');

  return (
    <form
      id="pull-search-form"
      action="enter"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      <div className="grid grid-cols-12 gap-4 mt-6">
        <div className="col-span-4">
          <FormController
            name="fileNumber"
            type="text"
            label={t('fileNumber')}
            placeholder={t('number')}
            control={control}
            disabled={(isChecked && (type === PULL_TYPES.PULL_NOTES || type === PULL_TYPES.PULL_DRAFT)) || (watch('fromDate') || watch('toDate'))}
          />
        </div>
        <div className="col-span-2 flex">
          <Checkbox
            onChange={(e) => handleCheck(e)}
            isChecked={isChecked}
            isDisabled={watch('fileNumber') || (watch('fromDate') || watch('toDate'))}
          >
            <FormLabel label={t('sameFile')} />
          </Checkbox>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4 mt-6">
        <FormController
          name="keywordSearch"
          type="text"
          label={t('keywordSearch')}
          placeholder={t('keywordSearch')}
          control={control}
          disabled={isChecked && (type === PULL_TYPES.PULL_NOTES || type === PULL_TYPES.PULL_DRAFT)}
        />
        <FormController
          type="date"
          name="fromDate"
          label={t('fromDate')}
          placeholder={t('placeholder')}
          control={control}
          dateFormat="dd-MM-yyyy"
          maxDate={new Date()}
          disabled={(isChecked && (type === PULL_TYPES.PULL_NOTES || type === PULL_TYPES.PULL_DRAFT)) || watch('fileNumber')}
        />
        <FormController
          type="date"
          name="toDate"
          label={t('toDate')}
          placeholder={t('placeholder')}
          control={control}
          dateFormat="dd-MM-yyyy"
          minDate={watch('fromDate')}
          maxDate={new Date()}
          disabled={(isChecked && (type === PULL_TYPES.PULL_NOTES || type === PULL_TYPES.PULL_DRAFT)) || watch('fileNumber')}
        />
      </div>
      <div className="col-span-12 flex justify-end items-center space-x-4 mt-6 mb-6">
        <Button
          variant="secondary_outline"
          size="sm"
          onClick={clearSearch}
        >
          {t('clear')}
        </Button>
        <Button
          variant="secondary_outline"
          size="sm"
          type="submit"
          form="pull-search-form"
          isLoading={actionTriggered?.id === 'pull-search' && actionTriggered?.loading}
          isDisabled={(type === PULL_TYPES.PULL_NOTES || type === PULL_TYPES.PULL_DRAFT) && (isChecked || (!fileNumber && !keywordSearch && !fromDate && !toDate))}
        >
          {t('search')}
        </Button>
      </div>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  pullSearchParams: getPullSearchParams,
  actionTriggered: getActionTriggered,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  setPullSearchParams: (data) => dispatch(sliceActions.setPullSearchParams(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(PullSearch);
