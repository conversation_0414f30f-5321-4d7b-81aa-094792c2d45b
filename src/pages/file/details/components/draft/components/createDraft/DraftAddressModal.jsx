import { connect } from 'react-redux';
import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  t,
  FormController,
  Button,
  // Modal,
  // ModalOverlay,
  // ModalContent,
  // ModalHeader,
  // ModalFooter,
  // ModalBody,
  FormWrapper
} from 'common/components';
import _ from 'lodash';
import * as commonActions from 'pages/common/actions';
import { AddAddressSchema } from 'pages/file/details/validate';
import {
  getDistricts,
  getInstitutions,
  getWard,
  getInstitutionTypes,
  getLocalBody,
  getUserLocalBody,
  getUserInfo,
  getOfficeType,
  getCounterOperator
} from 'pages/common/selectors';
import {
  getSendersDetails,
  getApplicantAddress,
  getReceiversDetails,
  getWardDetails,
  getFileDetails,
  getBpModuleCitizenAddress
} from 'pages/file/details/selector.js';
import { DEFAULT_OFFICE_TYPE, DEFAULT_STATE } from 'common/constants';
import { useParams } from 'react-router-dom';
import { checkExistingApplicantType } from 'pages/common/helper.js';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon.jsx';
import { FILTER_TYPE } from 'pages/common/constants.js';
// import CloseNew from 'assets/CloseNew.jsx';
import generateUUID from 'utils/generateUUID.js';
import * as actions from '../../../../actions.js';
import ApplicantList from './ApplicantList.jsx';
import DispatchFields from './DispatchFields.jsx';

const DraftAddressModal = ({
  open,
  setOpen,
  fetchSenderDetails,
  sendersDetails,
  fetchReceiverDetails,
  receiversDetails,
  fetchDistricts,
  districtDropdown,
  fetchWard,
  wardDropdown,
  fetchLocalBody,
  localBodies,
  fetchInstitutionTypes,
  setAddressData,
  addressData,
  userLocalBody,
  fileDetails,
  userInfo,
  fetchCounterOperator,
  institutionTypes,
  fetchBpModuleCitizenAddress,
  bpModuleCitizenAddress,
  itemToEdit,
  setItemToEdit
}) => {
  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
    setValue,
    setError,
    clearErrors,
    getValues
  } = useForm({
    mode: 'all',
    defaultValues: {
      sender: '',
      addressType: 1,
      name: null,
      address: null,
      sameAsApplicant: true,
      district: null,
      districtName: '',
      officeName: '',
      officerName: '',
      wardName: '',
      wardId: null,
      localBody: null,
      localBodyName: '',
      institutionType: '',
      institution: '',
      sameAsLocalBody: false,
      modeOfDispatch: null,
      officeType: null,
      dispatchSection: null,
      isDispatchFieldOpen: true
    },
    resolver: yupResolver(AddAddressSchema)
  });

  const params = useParams();
  const [isEdit, setIsEdit] = useState(false);
  const [sameAsApplicantArray, setSameAsApplicantArray] = useState([]);
  const [trigger, setTrigger] = useState(false);
  const [localBodyType, setLocalBodyType] = useState([]);
  const [selectedApplicants, setSelectedApplicants] = useState([]);

  useEffect(() => {
    setValue('sameAsLocalBody', false);
  }, []);

  const toggleClose = () => {
    reset();
    setOpen(false);
    setItemToEdit(null);
    setIsEdit(false);
    setSelectedApplicants([]);
  };

  const handleSetName = (row) => {
    return `${row?.firstName || ''} ${row?.middleName || ''} ${
      row?.lastName || ''
    }`.trim();
  };

  useEffect(() => {
    fetchReceiverDetails();
    fetchCounterOperator();
  }, []);

  useEffect(() => {
    if (Object.keys(userInfo).length > 0) {
      const lbTypeIds = userInfo?.userDetails?.offices?.map(
        (ofc) => ofc?.lbTypeId
      );
      fetchSenderDetails({ lbType: lbTypeIds });
    }
  }, [JSON.stringify(userInfo)]);

  const setCommonValues = (address) => {
    setValue('modeOfDispatch', address?.modeOfDispatch);
    setValue('officeType', address?.officeType);
    setValue('dispatchSection', address?.functionalGroupId);
    setValue('user', address?.dispatchClerkPostId);
    setValue('dispatchClerkName', address?.dispatchClerkName);
    setValue('dispatchClerkPenNo', address?.dispatchClerkPenNo);
  };

  useEffect(() => {
    if (addressData?.sender && !itemToEdit) {
      setValue('sender', addressData?.sender);
    }
  }, [addressData?.sender, open]);

  useEffect(() => {
    if (itemToEdit) {
      setIsEdit(true);

      reset();

      const { addressType, address, sender } = itemToEdit;
      const isSameAsApplicant = address?.isApplicant || false;

      setValue('sender', sender);
      setValue('addressType', addressType);
      setValue('sameAsApplicant', isSameAsApplicant);

      if (isSameAsApplicant) {
        setSelectedApplicants([address]);
        setCommonValues(address);
      } else {
        setValue('address', address?.address);

        switch (addressType) {
          case 1:
            setValue('name', address?.name);
            break;
          case 2:
            setValue('officeName', address?.officeName);
            setValue('officerName', address?.officerName);
            break;
          case 3:
            setValue('name', address?.name);
            setValue('wardId', address?.wardId);
            setValue('localBody', address?.localBody);
            setValue('district', address?.district);
            break;
          default:
            break;
        }

        setCommonValues(address);
      }

      if (address?.modeOfDispatch || address?.dispatchClerkPenNo) {
        setValue('isDispatchFieldOpen', true);
      } else {
        setValue('isDispatchFieldOpen', false);
      }
    } else {
      reset();
      setIsEdit(false);
    }
  }, [itemToEdit]);

  const handleSameLocalBody = () => {
    if (watch('sameAsLocalBody') === false) {
      fetchWard(userLocalBody.id);
      setValue('district', userLocalBody?.districtId);
      setValue('localBody', userLocalBody?.officeCode);
      fetchLocalBody({
        districtId: userLocalBody.districtId,
        officeTypeId: DEFAULT_OFFICE_TYPE
      });
    } else {
      setValue('district', null);
      setValue('localBody', null);
    }
    setValue('sameAsLocalBody', !watch('sameAsLocalBody'));
  };

  const handleSameAsApplicant = () => {
    setValue('sameAsApplicant', !watch('sameAsApplicant'));
    if (watch('sameAsApplicant') === false) {
      setValue('address', '');
      setValue('name', '');
      setValue('modeOfDispatch', '');
      setValue('officeType', '');
      setValue('dispatchSection', '');
      setValue('user', '');
      setValue('officeName', '');
      setValue('officerName', '');
    }
  };

  const handleDispatchCheckboxChange = (value) => {
    setValue('isDispatchFieldOpen', value);

    if (!value) {
      setValue('modeOfDispatch', null);
      setValue('user', null);
      setValue('dispatchClerkPenNo', null);
      setValue('dispatchClerkName', null);
    }
  };

  useEffect(() => {
    if (fileDetails) {
      if (fileDetails?.moduleCode === 'BP' && fileDetails?.source === 1) {
        fetchBpModuleCitizenAddress({ fileNo: fileDetails?.fileNo });
      }
      const inwardApplicants = fileDetails?.inwardDetails?.map(
        (item) => item?.applicantDetailsAddress
      );
      const efileApplicants = fileDetails?.efileDetails?.map(
        (item) => item?.applicantDetailsResponses
      );
      const formatInward = inwardApplicants ? inwardApplicants?.flat() : [];
      const formatEfile = efileApplicants ? efileApplicants?.flat() : [];
      if (formatInward?.length > 0 || formatEfile?.length > 0) {
        setSameAsApplicantArray([
          ...formatInward.filter((n) => n),
          ...formatEfile.filter((n) => n)
        ]);
      }
    }
  }, [JSON.stringify(fileDetails)]);

  useEffect(() => {
    if (bpModuleCitizenAddress) {
      const formatEfile = bpModuleCitizenAddress
        ? bpModuleCitizenAddress?.flat()
        : [];
      if (formatEfile?.length > 0) {
        setSameAsApplicantArray([...formatEfile.filter((n) => n)]);
      }
    }
  }, [JSON.stringify(bpModuleCitizenAddress)]);

  const mergeAddresses = (updated, existing) => _.unionBy(updated, existing?.applicantAddresses || [], 'id');

  const updateAddressFinalData = (
    groupedApplicants,
    temp,
    currentTrigger,
    currentParams
  ) => {
    const addressTypes = [
      { key: 'individual', type: 1 },
      { key: 'institutional', type: 2 },
      { key: 'electorialRepAddress', type: 3 }
    ];

    setAddressData({
      sender: getValues('sender'),
      receiver: getValues('addressType'),
      addresses: addressTypes
        ?.map(({ key, type }) => {
          const updated = groupedApplicants[key]?.map((item) => ({
            ...item,
            ...temp
          })) || [];

          const existing = addressData?.addresses?.find(
            (a) => a.addressType === type
          );
          const merged = mergeAddresses(updated, existing);

          return merged.length > 0
            ? {
              applicantAddresses: merged,
              addressType: type,
              fileNo: currentParams?.fileNo
            }
            : null;
        })
        .filter(Boolean)
    });
  };

  const onSubmitAddress = () => {
    const temp = {
      modeOfDispatch: getValues('modeOfDispatch'),
      functionalGroupId: getValues('dispatchSection'),
      dispatchClerkPostId: getValues('user'),
      dispatchClerkName: getValues('dispatchClerkName'),
      dispatchClerkPenNo: getValues('dispatchClerkPenNo')
    };

    if (getValues('sameAsApplicant')) {
      if (!selectedApplicants?.length) {
        setError('applicantReceiver', {
          type: 'applicant-receiver-empty',
          message: t('pleaseSelectAtLeastOneReceiver')
        });
      } else {
        const groupedApplicants = _.groupBy(selectedApplicants, 'groupType');
        updateAddressFinalData(groupedApplicants, temp, trigger, params);
        setTrigger(false);
        toggleClose();
      }

      return;
    }

    if (!isEdit) {
      const newEntry = {
        id: generateUUID(),
        addressType: getValues('addressType'),
        name: getValues('name'),
        address: getValues('address'),
        modeOfDispatch: getValues('modeOfDispatch'),
        officeType: getValues('officeType'),
        functionalGroupId: getValues('dispatchSection'),
        dispatchClerkPostId: getValues('user'),
        dispatchClerkName: getValues('dispatchClerkName'),
        dispatchClerkPenNo: getValues('dispatchClerkPenNo'),
        ...(getValues('addressType') === 2 && {
          officerName: getValues('officerName'),
          officeName: getValues('officeName')
        }),
        ...(getValues('addressType') === 3 && {
          wardName: getValues('wardName'),
          wardId: getValues('wardId'),
          district: getValues('district'),
          districtName: getValues('districtName'),
          localBody: getValues('localBody'),
          localBodyName: getValues('localBodyName')
        })
      };

      const existingIndividualAddress = addressData?.addresses?.find((a) => a?.addressType === 1) || {};
      const existingInstitutionalAddress = addressData?.addresses?.find((a) => a?.addressType === 2) || {};
      const existingElectorialRepAddress = addressData?.addresses?.find((a) => a?.addressType === 3) || {};

      const newAddresses = {
        1: [...(existingIndividualAddress?.applicantAddresses || [])],
        2: [...(existingInstitutionalAddress?.applicantAddresses || [])],
        3: [...(existingElectorialRepAddress?.applicantAddresses || [])]
      };

      newAddresses[getValues('addressType')].push(newEntry);

      setAddressData({
        sender: getValues('sender'),
        receiver: getValues('addressType'),
        addresses: [
          newAddresses[1].length > 0
            ? {
              applicantAddresses: newAddresses[1],
              addressType: 1,
              fileNo: params?.fileNo
            }
            : null,
          newAddresses[2].length > 0
            ? {
              applicantAddresses: newAddresses[2],
              addressType: 2,
              fileNo: params?.fileNo
            }
            : null,
          newAddresses[3].length > 0
            ? {
              applicantAddresses: newAddresses[3],
              addressType: 3,
              fileNo: params?.fileNo
            }
            : null
        ].filter(Boolean)
      });

      toggleClose();
    } else {
      const addressType = getValues('addressType');
      const updatedData = {
        addressType,
        name: getValues('name'),
        address: getValues('address'),
        ...temp,
        ...(addressType === 2 && {
          officerName: getValues('officerName'),
          officeName: getValues('officeName')
        }),
        ...(addressType === 3 && {
          wardId: getValues('wardId'),
          district: getValues('district'),
          localBody: getValues('localBody')
        })
      };

      const updated = [...addressData.addresses];
      updated[itemToEdit.addressDataIndex].applicantAddresses[
        itemToEdit.addressIndex
      ] = updatedData;
      setAddressData({ ...addressData, addresses: updated });
      toggleClose();
    }
  };

  const checkIsApplicantChecked = (applicantId) => {
    return selectedApplicants?.some((item) => item?.id === applicantId);
  };

  const checkIsApplicantDisabled = () => {
    return isEdit;
  };

  const createApplicant = (applicant) => {
    if (!applicant) return null;

    const updatedAddressType = checkExistingApplicantType({ row: applicant }) === 'individual' ? 1 : 2;

    const commonFields = {
      id: applicant.id,
      isApplicant: true,
      addressType: updatedAddressType
    };

    const formatAddress = (fields) => _.compact(fields).join(', ');

    switch (updatedAddressType) {
      case 1:
        return {
          ...commonFields,
          groupType: 'individual',
          firstName: applicant.firstName,
          name: handleSetName(applicant),
          address: formatAddress([
            applicant.houseName,
            applicant.mainPlace,
            applicant.postOfficeName,
            applicant.pincode
          ])
        };
      case 2:
        return {
          ...commonFields,
          groupType: 'institutional',
          officeName: applicant.institutionName,
          officerName: applicant.officerName,
          address: formatAddress([
            applicant.mainPlace,
            applicant.postOfficeName,
            applicant.pincode
          ])
        };
      case 3:
        return {
          ...commonFields,
          groupType: 'electorialRepAddress',
          name: handleSetName(applicant),
          wardId: applicant.wardId,
          localBody: applicant.localBody,
          district: applicant.district,
          address: formatAddress([
            applicant.mainPlace,
            applicant.postOfficeName,
            applicant.pincode
          ])
        };
      default:
        return null;
    }
  };

  const handleApplicantSelect = (applicant) => {
    clearErrors('applicantReceiver');
    const newApplicant = createApplicant(applicant);
    if (!newApplicant) return;

    setSelectedApplicants((prevApplicants) => {
      const isSelected = prevApplicants.some((ap) => ap.id === applicant.id);
      return isSelected
        ? prevApplicants.filter((ap) => ap.id !== applicant.id)
        : [...prevApplicants, newApplicant];
    });
  };

  const handleFieldChange = (field, data) => {
    switch (field) {
      case 'district':
        setValue('district', data?.id);
        setValue('districtName', data?.name);
        fetchInstitutionTypes();
        break;
      case 'localBodyType':
        setValue('localBodyType', data?.id);
        setValue('localBodyTypeName', data?.name);
        fetchLocalBody({
          districtId: watch('district'),
          officeTypeId: data?.id
        });
        break;
      case 'localBody':
        setValue('localBody', data?.id);
        setValue('localBodyName', data?.name);
        fetchWard(data.id);
        break;
      case 'ward':
        setValue('wardId', data?.id);
        setValue('wardName', data?.name);
        break;
      case 'addressType':
        setValue('sameAsApplicant', false);
        setValue('addressType', data?.id);
        if (data?.id) {
          fetchDistricts(DEFAULT_STATE.id);
        }
        break;
      case 'modeOfDispatch':
        setValue('modeOfDispatch', data?.id);
        break;
      case FILTER_TYPE.OFFICE_TYPE:
        setValue('officeType', data?.id);
        break;
      case FILTER_TYPE.DEPARTMENT:
        setValue('dispatchSection', data?.id);
        break;
      case FILTER_TYPE.USER:
        setValue('user', data?.postId);
        setValue('dispatchClerkName', data?.employeeName);
        setValue('dispatchClerkPenNo', data?.penNo);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (institutionTypes) {
      setLocalBodyType(
        _.get(institutionTypes, 'data', [])?.filter(
          (item) => item?.id >= 1 && item?.id <= 5
        )
      );
    }
  }, [institutionTypes]);

  useEffect(() => {
    if (
      fileDetails?.source === 3
      || fileDetails?.source === 5
      || watch('addressType') === 3
    ) {
      setValue('sameAsApplicant', false);
    }
  }, [open, fileDetails?.source, watch('addressType')]);

  return (
  // <Modal
  //   isOpen={open}
  //   size="3xl"
  //   onClose={toggleClose}
  //   className="custom-form-modal"
  // >
  //   <ModalOverlay />
  //   <ModalContent>
  //     <ModalHeader className="flex items-center justify-between">
  //       <h4 className="text-[#09327B] text-[16px] font-extrabold">
  //         {t('addAddress')}
  //       </h4>
  //       <button onClick={toggleClose} aria-label="Close">
  //         <CloseNew fill="#fff" w="40" h="40" />
  //       </button>
  //     </ModalHeader>

    //     <ModalBody m={0} p={0} overflow="auto" maxH={440}>
    <div className="">
      {/* <form
      onSubmit={handleSubmit(onSubmitAddress)} id="add-address-form"
      > */}
      <FormWrapper px={false} py={false}>
        <div className="sm:col-span-6 col-span-12 ">
          <FormController
            name="sender"
            type="select"
            label={t('sender')}
            placeholder={t('sender')}
            control={control}
            options={_.get(sendersDetails, 'data', [])}
            optionKey="id"
            required
            errors={errors}
            menuPosition="fixed"
          />
        </div>
        <div className="sm:col-span-6 col-span-12">
          <FormController
            name="addressType"
            type="select"
            label={t('receiver')}
            placeholder={t('receiver')}
            control={control}
            optionKey="id"
            options={_.get(receiversDetails, 'data', [])}
            handleChange={(data) => handleFieldChange('addressType', data)}
            isDisabled={isEdit}
          />
        </div>

        {fileDetails?.source !== 3
          && fileDetails?.source !== 5
          && watch('addressType') !== 3 && (
            <div className="col-span-12">
              <div className="flex">
                <h4 className="text-[#09327B] font-bold text-[14px] mr-3">
                  Receiver Details
                </h4>
                <Button
                  variant="link"
                  disabled={isEdit}
                  style={{
                    textDecoration: 'none',
                    color: '#456C86',
                    fontWeight: 500
                  }}
                  leftIcon={
                    !watch('sameAsApplicant') ? (
                      <CheckedBox />
                    ) : (
                      <UnCheckedBox />
                    )
                  }
                  onClick={handleSameAsApplicant}
                >
                  <span className="ml-1 text-[14px]">
                    {t('notTheSameAsApplicant')}
                  </span>
                </Button>
              </div>

              {sameAsApplicantArray?.length > 0 && watch('sameAsApplicant') && (
                <ApplicantList
                  applicantList={sameAsApplicantArray}
                  isApplicantDisabled={checkIsApplicantDisabled}
                  checkIsApplicantChecked={checkIsApplicantChecked}
                  onApplicantSelect={handleApplicantSelect}
                />
              )}
            </div>
        )}

        {!watch('sameAsApplicant') && (
          <>
            {watch('addressType') === 3 && (
              <>
                <div className="col-span-12">
                  <Button
                    variant="link"
                    style={{ textDecoration: 'none' }}
                    leftIcon={
                      watch('sameAsLocalBody') ? (
                        <CheckedBox />
                      ) : (
                        <UnCheckedBox />
                      )
                    }
                    onClick={handleSameLocalBody}
                  >
                    {t('sameLocalBody')}
                  </Button>
                </div>

                <div className="sm:col-span-6 col-span-12 ">
                  <FormController
                    name="district"
                    type="select"
                    label={t('district')}
                    placeholder={t('district')}
                    control={control}
                    errors={errors}
                    options={_.get(districtDropdown, 'data', [])}
                    optionKey="id"
                    handleChange={(data) => handleFieldChange('district', data)}
                    required
                  />
                </div>

                <div className="sm:col-span-6 col-span-12 ">
                  <FormController
                    name="localBodyType"
                    type="select"
                    label={t('localBodyType')}
                    placeholder={t('localBodyType')}
                    control={control}
                    errors={errors}
                    options={localBodyType}
                    optionKey="id"
                    handleChange={(data) => handleFieldChange('localBodyType', data)}
                    required
                  />
                </div>
              </>
            )}
            {watch('addressType') === 2 ? (
              <>
                <div className="sm:col-span-6 col-span-12">
                  <FormController
                    name="officerName"
                    type="text"
                    label={t('concatLabel', {
                      label: t('officer'),
                      type: t('name')
                    })}
                    control={control}
                    required
                    errors={errors}
                  />
                </div>
                <div className="sm:col-span-6 col-span-12 ">
                  <FormController
                    name="officeName"
                    type="text"
                    label={t('concatLabel', {
                      label: t('office'),
                      type: t('name')
                    })}
                    control={control}
                    required
                    errors={errors}
                  />
                </div>
              </>
            ) : null}
            {watch('addressType') === 3 ? (
              <>
                <div className="sm:col-span-6 col-span-12 ">
                  <FormController
                    name="localBody"
                    type="select"
                    label={t('localBody')}
                    placeholder={t('localBody')}
                    control={control}
                    errors={errors}
                    options={_.get(localBodies, 'data', [])}
                    optionKey="id"
                    handleChange={(data) => handleFieldChange('localBody', data)}
                    required
                  />
                </div>
                <div className="sm:col-span-6 col-span-12">
                  <FormController
                    name="wardId"
                    type="select"
                    label={t('concatLabel', {
                      label: t('ward'),
                      type: t('name')
                    })}
                    placeholder={t('concatLabel', {
                      label: t('ward'),
                      type: t('name')
                    })}
                    control={control}
                    options={_.get(wardDropdown, 'data', [])}
                    handleChange={(data) => handleFieldChange('ward', data)}
                    errors={errors}
                    required
                    optionKey="id"
                  />
                </div>
              </>
            ) : null}
            {(watch('addressType') === 1 || watch('addressType') === 3) && (
              <div className="col-span-12">
                <FormController
                  name="name"
                  type="text"
                  label={t('name')}
                  control={control}
                  required
                  errors={errors}
                />
              </div>
            )}

            <div className="col-span-12">
              <FormController
                name="address"
                type="textarea"
                label={t('address')}
                control={control}
                required
                errors={errors}
              />
            </div>
          </>
        )}
      </FormWrapper>
      {errors?.applicantReceiver?.message && (
        <span className="text-red-500 text-xs text-right block mt-1">
          {errors?.applicantReceiver?.message}
        </span>
      )}
      <div className="pt-7 pb-6 flex flex-col gap-4">
        <DispatchFields
          control={control}
          errors={errors}
          dispatchFieldsOpen={getValues('isDispatchFieldOpen')}
          onChange={handleDispatchCheckboxChange}
          onUserChange={(data) => handleFieldChange(FILTER_TYPE.USER, data)}
        />
      </div>
      {/* </form> */}
      <div className="flex justify-end gap-2 pt-2 pb-2">
        <Button
          variant="secondary_outline"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={toggleClose}
        >
          {t('cancel')}
        </Button>
        <Button
          // type="submit"
          variant="secondary"
          size="sm"
          // height={10}
          // form="add-address-form"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={() => {
            setTrigger(true);
            handleSubmit(onSubmitAddress)();
          }}
        >
          {t(isEdit ? 'update' : 'add')}
        </Button>
      </div>
    </div>
    //     </ModalBody>
    //     <ModalFooter pt={3} pb={5} px={0} gap={2} justifyContent="center">
    //       <Button
    //         variant="secondary_outline"
    //         size="sm"
    //         height={10}
    //         onClick={toggleClose}
    //       >
    //         {t('cancel')}
    //       </Button>
    //       <Button
    //         type="submit"
    //         variant="secondary"
    //         size="sm"
    //         height={10}
    //         form="add-address-form"
    //         onClick={() => setTrigger(true)}
    //       >
    //         {t(isEdit ? 'update' : 'add')}
    //       </Button>
    //     </ModalFooter>
    //   </ModalContent>
    // </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
  sendersDetails: getSendersDetails,
  receiversDetails: getReceiversDetails,
  districtDropdown: getDistricts,
  wardDropdown: getWard,
  applicantAddress: getApplicantAddress,
  institutionTypes: getInstitutionTypes,
  localBodies: getLocalBody,
  institutions: getInstitutions,
  wardDetails: getWardDetails,
  userLocalBody: getUserLocalBody,
  fileDetails: getFileDetails,
  userInfo: getUserInfo,
  officeType: getOfficeType,
  counterOperatorDropdown: getCounterOperator,
  bpModuleCitizenAddress: getBpModuleCitizenAddress
});

const mapDispatchToProps = (dispatch) => ({
  fetchSenderDetails: (data) => dispatch(actions.fetchSenderDetails(data)),
  fetchReceiverDetails: () => dispatch(actions.fetchReceiverDetails()),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchWard: (data) => dispatch(commonActions.fetchWardsByLocalBodyId(data)),
  fetchInstitutionTypes: () => dispatch(commonActions.fetchInstitutionTypes()),
  fetchLocalBody: (data) => dispatch(commonActions.fetchLocalBody(data)),
  fetchInstitutions: (data) => dispatch(commonActions.fetchInstitutions(data)),
  fetchCounterOperator: (data) => dispatch(commonActions.fetchCounterOperator(data)),
  fetchBpModuleCitizenAddress: (data) => dispatch(actions.fetchBpModuleCitizenAddress(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftAddressModal);
