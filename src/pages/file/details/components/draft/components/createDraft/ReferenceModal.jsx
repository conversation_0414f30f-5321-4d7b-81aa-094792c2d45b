import { useEffect, useId, useState } from 'react';
import { Button, TextInput, t } from 'common/components';
import OutlineTag from 'pages/common/components/OutlineTag';
// import withActionModal from 'pages/common/components/withActionModal';
// import PlusIconTiny from 'assets/PlusIconTiny';

const ReferenceModal = ({
  references,
  selectedReferences,
  setSelectedReferences,
  selectedReferenceIndex,
  setSelectedReferenceIndex,
  onClose,
  onSave
}) => {
  const id = useId();

  const inputRef = window?.document?.getElementById('reference_field');

  const [reference, setReference] = useState('');
  const [isError, setIsError] = useState(false);

  const resetInput = () => {
    setReference('');
    setSelectedReferenceIndex(null);
    inputRef?.focus?.();
  };

  useEffect(() => {
    if (selectedReferenceIndex !== null) {
      setReference(references[selectedReferenceIndex]?.reference || '');
      inputRef?.focus?.();
    }
  }, [selectedReferenceIndex, selectedReferences]);

  const handleValueChange = (e) => {
    if (isError) setIsError(false);
    setReference(e.target.value);
  };

  const addOrUpdateReference = (e, type) => {
    e?.preventDefault();

    const trimmedRef = reference.trim();
    if (!trimmedRef) {
      setIsError(true);
      return;
    }

    const isSave = type === 'save';
    const isNewEntry = selectedReferenceIndex === null;

    if (isSave) {
      const updated = [...references];

      if (isNewEntry) {
        updated.push({ reference: trimmedRef, lNo: id });
      } else {
        updated[selectedReferenceIndex].reference = trimmedRef;
      }

      onSave(updated);
    } else {
      setSelectedReferences((prev) => {
        const updated = [...prev];

        if (isNewEntry) {
          return [...updated, { reference: trimmedRef, slNo: id }];
        }
        updated[selectedReferenceIndex] = {
          ...updated[selectedReferenceIndex],
          reference: trimmedRef
        };
        return updated;
      });
    }

    resetInput();
  };

  const handleSave = (e) => {
    const shouldSubmit = !selectedReferences?.length;

    if (shouldSubmit) {
      addOrUpdateReference(e, 'save');
    } else if (reference.trim()) {
      const updatedSelectedReferences = [...selectedReferences];
      updatedSelectedReferences.push({
        reference,
        slNo: id
      });

      onSave([...references, ...updatedSelectedReferences]);
    } else {
      onSave([...references, ...selectedReferences]);
    }
  };

  const removeReference = (index) => {
    setSelectedReferences((prev) => prev.filter((_, i) => i !== index));
    resetInput();
  };

  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="flex items-center border rounded-xl p-1">
          <div className="flex-grow rounded-xl max-h-[50px]">
            <TextInput
              autoFocus
              id="reference_field"
              type="text"
              error={isError ? t('pleaseEnterAReferenceValue') : undefined}
              value={reference}
              onChange={handleValueChange}
              sx={{
                border: 'none !important',
                maxHeight: '50px !important'
              }}
            />
          </div>
          {/* {selectedReferenceIndex === null && (
            <Button
              py={4}
              px={4}
              mx={2}
              type="submit"
              fontSize="sm"
              variant="primary_outline"
              onClick={addOrUpdateReference}
            >
              <PlusIconTiny stroke="#00B2EB" strokeWidth="2" />
            </Button>
          )} */}
        </div>
        <div className="flex flex-wrap gap-4">
          {selectedReferences.map((item, index) => (
            <OutlineTag
              key={item.slNo}
              text={item.reference}
              onTagRemove={() => removeReference(index)}
            />
          ))}
        </div>
      </div>
      <div className="mb-2 mt-2 flex justify-end items-center gap-2">
        <Button
          type="button"
          variant="secondary_outline"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={onClose}
        >
          {t('cancel')}
        </Button>
        <Button
          variant="secondary"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={handleSave}
        >
          {t('add')}
        </Button>
      </div>
    </>
  );
};

export default ReferenceModal;
