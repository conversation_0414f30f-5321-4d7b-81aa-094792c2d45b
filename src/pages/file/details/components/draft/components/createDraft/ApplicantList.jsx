import { Avatar, Checkbox } from '@ksmartikm/ui-components';
import { InstitutionIcon } from 'assets/InstitutionIcon';
import ProfilePic from 'assets/ProfilePic';
import TruncatedText from 'common/components/TruncatedText';
import {
  applicantName,
  checkExistingApplicantTypeDisplay
} from 'pages/common/helper';
import { memo } from 'react';

const formatAddress = (applicant) => {
  if (!applicant) return '';

  const addressParts = [
    applicant.houseName,
    applicant.postOfficeName ? `${applicant.postOfficeName} P.O` : null,
    applicant.street,
    applicant.mainPlace,
    applicant.pincode || null
  ].filter(Boolean);

  return addressParts.join(', ');
};

const ApplicantBadge = memo(({ applicantType, bg, color }) => {
  return (
    <span
      className="text-[11px] font-bold px-1.5 rounded-[2px]"
      style={{ color, backgroundColor: bg }}
    >
      {applicantType}
    </span>
  );
});

const ApplicantTile = memo(
  ({
    applicant, isDisabled, isApplicantChecked, onCheckboxChange
  }) => {
    const applicantType = checkExistingApplicantTypeDisplay({ row: applicant });
    const isIndividual = applicantType === 'Individual';
    const bg = isIndividual ? '#DDF0FA' : '#FAECDD';
    const color = isIndividual ? '#00B2EB' : '#C46500';

    const AvatarIcon = isIndividual ? ProfilePic : InstitutionIcon;

    return (
      <div className={`p-2 my-2 flex gap-4 opacity-${isDisabled ? 60 : 100}`}>
        <Checkbox
          isDisabled={isDisabled}
          isChecked={isApplicantChecked}
          onChange={onCheckboxChange}
        />
        <div className="flex gap-3 items-center">
          <Avatar
            w="40px"
            h="40px"
            icon={<AvatarIcon color={color} />}
            backgroundColor={bg}
          />
          <div>
            <div className="flex gap-3 items-center">
              <span className="font-[600] text-[14px]">
                <TruncatedText text={applicantName({ row: applicant })} />
              </span>
              <ApplicantBadge
                bg={bg}
                color={color}
                applicantType={applicantType}
              />
            </div>
            <p className="text-[12px] text-[#5C6E93] font-[600]">
              {formatAddress(applicant)}
            </p>
          </div>
        </div>
      </div>
    );
  }
);

const ApplicantList = ({
  applicantList,
  checkIsApplicantChecked,
  onApplicantSelect,
  isApplicantDisabled
}) => {
  return (
    <div className="border border-[#EBEBEB] rounded-[8px] mt-4 px-3 max-h-60 overflow-auto">
      {applicantList?.map((applicant) => (
        <ApplicantTile
          key={applicant?.id}
          applicant={applicant}
          isDisabled={isApplicantDisabled(applicant)}
          isApplicantChecked={checkIsApplicantChecked(applicant?.id)}
          onCheckboxChange={() => onApplicantSelect(applicant)}
        />
      ))}
    </div>
  );
};

export default ApplicantList;
