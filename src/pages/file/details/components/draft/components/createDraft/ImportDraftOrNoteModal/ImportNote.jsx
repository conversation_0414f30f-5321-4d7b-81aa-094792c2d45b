import { memo, useCallback, useEffect } from 'react';
import { createStructuredSelector } from 'reselect';
import {
  getPullSearchNotesParams,
  getSearchedPullNotesDetails
} from 'pages/file/details/selector';
import { connect } from 'react-redux';
import { convertToLocalDate } from 'utils/date';
import NoNotesIcon from 'assets/NoNotesIcon';
import { t, Checkbox, Flex } from 'common/components';
import { Avatar } from '@ksmartikm/ui-components';
import * as actions from '../../../../../actions';
import { actions as sliceActions } from '../../../../../slice';
import ImportItemFoundation from './ImportItemFoundation';

const ImportNote = ({
  pullSearchNotesParams,
  searchedPullNotesDetails,
  searchPullNotesDetails,
  setPullSearchNotesParams,
  selectedNoteToImport,
  params,
  open,
  onNoteSelect = () => {}
}) => {
  const updateSearchNotesParams = useCallback(
    (key, value) => {
      setPullSearchNotesParams({
        ...pullSearchNotesParams,
        [key]: value
      });
    },
    [pullSearchNotesParams]
  );

  const handlePageChange = useCallback(
    (p) => {
      updateSearchNotesParams('page', p - 1);
    },
    [updateSearchNotesParams]
  );

  const handleSearch = useCallback(
    (query) => {
      updateSearchNotesParams('fileNo', query);
    },
    [updateSearchNotesParams]
  );

  const fetchPullNotes = useCallback(() => {
    searchPullNotesDetails();
  }, [pullSearchNotesParams]);

  useEffect(() => {
    fetchPullNotes();
  }, [pullSearchNotesParams, open]);

  const noteCard = ({ note }) => {
    return (
      <Flex
        bg="white"
        cursor="pointer"
        borderRadius="8px"
        p={3}
        gap={4}
        onClick={() => onNoteSelect(note)}
      >
        <div className="flex items-center">
          <Checkbox
            isChecked={selectedNoteToImport?.notesId === note?.notesId}
            onChange={() => onNoteSelect(note)}
          />
        </div>
        <div className="flex-grow">
          <div className="flex items-center gap-4">
            <Avatar w={10} h={10} fontWeight={500} name={note?.assignerName} />
            <div className="flex-1">
              <span className="font-semibold text-[14px] text-[#3C4449]">
                {note?.assignerName}
              </span>
              <p className="text-[#5C6E93] text-[12px] font-semibold -mt-1">
                {`${note?.designation}, ${note?.seatName} PEN:${note?.penNo}`}
              </p>
            </div>
            <div className="text-[#5C6E93] text-[13px] font-semibold">
              {convertToLocalDate(note?.createdAt, 'ddd, MMM DD YYYY, hh:mm A')}
            </div>
          </div>
          <div className="mt-3 ml-14">
            <div
              className="import-note-card-rich-content"
              /* eslint-disable-next-line react/no-danger */
              dangerouslySetInnerHTML={{ __html: note?.notes }}
            />
          </div>
        </div>
      </Flex>
    );
  };

  const noteCards = () => {
    return (
      <div className="flex flex-col gap-3">
        {searchedPullNotesDetails?.content?.length > 0 ? (
          searchedPullNotesDetails?.content?.map((note, i) => noteCard({ index: i, note }))
        ) : (
          <div className="p-10 text-center bg-white rounded-lg">
            <NoNotesIcon width="100px" height="100px" className="mx-auto" />
            <h4>{t('noNotesFound')}</h4>
          </div>
        )}
      </div>
    );
  };

  return (
    <ImportItemFoundation
      params={params}
      totalCount={searchedPullNotesDetails?.totalElements}
      numberOfElements={searchedPullNotesDetails?.numberOfElements}
      pageSize={3}
      item={noteCards()}
      onSearch={handleSearch}
      onPageChange={handlePageChange}
    />
  );
};

const MemoizedImportNote = memo(ImportNote);

const mapStateToProps = createStructuredSelector({
  searchedPullNotesDetails: getSearchedPullNotesDetails,
  pullSearchNotesParams: getPullSearchNotesParams
});

const mapDispatchToProps = (dispatch) => ({
  setPullSearchNotesParams: (data) => dispatch(sliceActions.setPullSearchNotesParams(data)),
  searchPullNotesDetails: (data) => dispatch(actions.searchPullNotesDetails(data)),
  savePullNotes: (data) => dispatch(sliceActions.setPulledNote(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(MemoizedImportNote);
