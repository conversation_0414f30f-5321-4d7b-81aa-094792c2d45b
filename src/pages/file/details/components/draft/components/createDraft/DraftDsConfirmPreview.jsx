import React, { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Modal,
  ModalBody,
  ModalContent,
  ModalOverlay,
  t,
  Tooltip,
  IconButton,
  PdfViewer,
  Button
} from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered, getDigitalSignConfirmation } from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import FullScreenIcon from 'assets/FullScreen';
import CloseOutlineIcon from 'assets/CloseOutline';
import { dark } from 'utils/color';
import { BASE_PATH } from 'common/constants';
import { useParams, useNavigate } from 'react-router-dom';

const DraftDsConfirmPreview = (props) => {
  const {
    digitalSignConfirmation,
    setDigitalSignConfirmation,
    saveSignedDraft,
    setActionTriggered,
    actionTriggered,
    setAlertAction
  } = props;

  const [full, setFull] = useState(false);
  const params = useParams();
  const navigate = useNavigate();

  const handleFull = () => {
    setFull(!full);
  };

  const handleClose = () => {
    setDigitalSignConfirmation({ open: false });
  };

  const navigateToActiveDraft = () => {
    setAlertAction({ open: false });
    setTimeout(() => {
      navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
      digitalSignConfirmation.close();
    }, 300);
  };

  const handleConfirm = () => {
    setActionTriggered({ loading: true, id: 'confirm-ds-save' });
    saveSignedDraft({ blob: digitalSignConfirmation?.payload?.blob, params: digitalSignConfirmation?.payload?.params, navigateToActiveDraft });
  };

  return (
    <Modal size={full && 'full'} isOpen={digitalSignConfirmation?.open}>
      <ModalOverlay />
      <ModalContent borderRadius={16} style={{ background: '#d4d4d4', maxWidth: full ? '100%' : '520px' }}>
        <ModalBody py={6}>
          <div className="flex items-center bg-white rounded-lg">
            <div className="flex-grow text-left font-bold pl-5" style={{ color: dark }}>
              {t('previewWithDs')}
            </div>
            <Tooltip label={t('fullScreen')}>
              <IconButton variant="unstyled" onClick={() => handleFull()} leftIcon={<FullScreenIcon width="21px" height="21px" />} />
            </Tooltip>
            <Tooltip label={t('close')}>
              <IconButton variant="unstyled" onClick={() => handleClose()} leftIcon={<CloseOutlineIcon width="24px" height="24px" color="#000" />} />
            </Tooltip>
          </div>
          <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 220px)' }} className="overflow-y-auto mt-5 rounded-lg">
            <PdfViewer data={digitalSignConfirmation?.payload?.blob} />
          </div>
          <div className="flex items-center rounded-lg gap-3">
            <div className="flex-grow text-center font-bold" />
            <Button onClick={() => handleClose()}>
              {t('cancel')}
            </Button>
            <Button variant="secondary" isLoading={actionTriggered?.loading && actionTriggered?.id === 'confirm-ds-save'} onClick={() => handleConfirm()}>
              {t('confirmDs')}
            </Button>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
  digitalSignConfirmation: getDigitalSignConfirmation,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  setDigitalSignConfirmation: () => dispatch(commonSliceActions.setDigitalSignConfirmation()),
  saveSignedDraft: (data) => dispatch(commonActions.saveSignedDraft(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftDsConfirmPreview);
