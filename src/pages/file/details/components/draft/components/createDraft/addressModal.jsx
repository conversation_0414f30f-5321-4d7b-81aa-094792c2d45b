import { connect } from 'react-redux';
import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  t,
  FormController,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  FormWrapper
} from 'common/components';
import _ from 'lodash';
import * as commonActions from 'pages/common/actions';
import { AddAddressSchema } from 'pages/file/details/validate';
import {
  getDistricts,
  getInstitutions,
  getWard,
  getInstitutionTypes,
  getLocalBody,
  getUserLocalBody,
  getUserInfo,
  getModeOfDispatch,
  getOfficeType,
  getCounterOperator,
  getDispatchClerkDetails
} from 'pages/common/selectors';
import {
  getSendersDetails,
  getApplicantAddress,
  getReceiversDetails,
  getWardDetails,
  getFileDetails
} from 'pages/file/details/selector.js';
import { DEFAULT_OFFICE_TYPE, DEFAULT_STATE } from 'common/constants';
import { useParams } from 'react-router-dom';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon.jsx';
import { FILTER_TYPE } from 'pages/common/constants.js';
import CloseNew from 'assets/CloseNew.jsx';
import Collapse from 'common/components/Collapse.jsx';
import * as actions from '../../../../actions.js';
import ApplicantList from './ApplicantList.jsx';

const AddressModal = ({
  open,
  setOpen,
  fetchSenderDetails,
  sendersDetails,
  fetchReceiverDetails,
  receiversDetails,
  fetchDistricts,
  districtDropdown,
  fetchWard,
  wardDropdown,
  fetchLocalBody,
  localBodies,
  fetchInstitutionTypes,
  setAddressData,
  addressData,
  userLocalBody,
  fileDetails,
  fetchModeOfDispatch,
  modeOfDispatchDetails,
  fetchOfficeType,
  officeType,
  userInfo,
  counterOperatorDropdown,
  fetchCounterOperator,
  fetchDispatchClerkDetails,
  dispatchClerkDetails,
  itemToEdit,
  setItemToEdit
}) => {
  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
    setValue,
    getValues
  } = useForm({
    mode: 'all',
    defaultValues: {
      sender: '',
      addressType: 1,
      name: null,
      address: null,
      sameAsApplicant: true,
      district: null,
      districtName: '',
      officeName: '',
      officerName: '',
      wardName: '',
      wardId: null,
      localBody: null,
      localBodyName: '',
      institutionType: '',
      institution: '',
      sameAsLocalBody: false,
      modeOfDispatch: null,
      officeType: null,
      dispatchSection: null
    },
    resolver: yupResolver(AddAddressSchema)
  });

  const params = useParams();
  const [isEdit, setIsEdit] = useState(false);
  const [sameAsApplicantArray, setSameAsApplicantArray] = useState([]);
  const [trigger, setTrigger] = useState(false);
  const [post, setPost] = useState([]);
  const [selectedApplicants, setSelectedApplicants] = useState([]);
  const [isDispatchFieldOpen, setIsDispatchFieldOpen] = useState(false);

  const toggleClose = () => {
    reset();
    setOpen(false);
    setItemToEdit(null);
    setIsEdit(false);
    setSelectedApplicants([]);
    setIsDispatchFieldOpen(false);
  };

  useEffect(() => {
    setValue('sameAsLocalBody', false);
  }, []);

  useEffect(() => {
    if (dispatchClerkDetails?.length > 0) {
      setPost(
        dispatchClerkDetails?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName ? ps.employeeName : ''} - ${
            ps.penNo ? ps.penNo : ''
          }- ${ps.postName ? ps.postName : ps.postNameInEng}`
        }))
      );
    }
  }, [dispatchClerkDetails]);

  const setCommonValues = (address) => {
    setValue('modeOfDispatch', address?.modeOfDispatch);
    setValue('officeType', address?.officeType);
    setValue('dispatchSection', address?.functionalGroupId);
    setValue('user', address?.dispatchClerkPostId);
  };

  useEffect(() => {
    if (itemToEdit) {
      setIsEdit(true);
      setIsDispatchFieldOpen(true);

      reset();

      const { addressType, address, sender } = itemToEdit;
      const isSameAsApplicant = address?.isApplicant || false;

      setValue('sender', sender);
      setValue('addressType', addressType);
      setValue('sameAsApplicant', isSameAsApplicant);

      if (isSameAsApplicant) {
        setSelectedApplicants([address]);
        setCommonValues(address);
      } else {
        setValue('address', address?.address);

        switch (addressType) {
          case 1:
            setValue('name', address?.name);
            break;
          case 2:
            setValue('officeName', address?.officeName);
            setValue('officerName', address?.officerName);
            break;
          case 3:
            setValue('name', address?.name);
            setValue('wardId', address?.wardId);
            setValue('localBody', address?.localBody);
            setValue('district', address?.district);
            break;
          default:
            break;
        }

        setCommonValues(address);
      }
    } else {
      reset();
      setIsEdit(false);
      setIsDispatchFieldOpen(false);
    }
  }, [itemToEdit]);

  const handleSetName = (row) => {
    const { firstName, middleName, lastName } = row || {};
    const nameParts = [firstName, middleName, lastName].filter(Boolean); // Filters out any undefined or null values
    return nameParts?.join(' ');
  };

  const createApplicant = (applicant) => {
    if (!applicant) return null;

    const commonFields = {
      id: applicant.id,
      isApplicant: true,
      addressType: applicant.addressType
    };

    const formatAddress = (fields) => _.compact(fields).join(', ');

    switch (applicant.addressType) {
      case 1:
        return {
          ...commonFields,
          groupType: 'individual',
          firstName: applicant.firstName,
          name: handleSetName(applicant),
          address: formatAddress([
            applicant.houseName,
            applicant.mainPlace,
            applicant.postOfficeName,
            applicant.pincode
          ])
        };
      case 2:
        return {
          ...commonFields,
          groupType: 'institutional',
          officerName: applicant.officerName,
          officeName: applicant.institutionName,
          address: formatAddress([
            applicant.mainPlace,
            applicant.postOfficeName,
            applicant.pincode
          ])
        };
      case 3:
        return {
          groupType: 'electorialRepAddress',
          name: handleSetName(applicant),
          wardId: applicant.wardId,
          localBody: applicant.localBody,
          district: applicant.district,
          address: formatAddress([
            applicant.mainPlace,
            applicant.postOfficeName,
            applicant.pincode
          ])
        };
      default:
        return null;
    }
  };

  const handleApplicantSelect = (applicant) => {
    const newApplicant = createApplicant(applicant);
    if (!newApplicant) return;

    setSelectedApplicants((prevApplicants) => {
      const isSelected = prevApplicants.some((ap) => ap.id === applicant.id);
      return isSelected
        ? prevApplicants.filter((ap) => ap.id !== applicant.id)
        : [...prevApplicants, newApplicant];
    });
  };

  const checkIsApplicantChecked = (applicantId) => {
    return selectedApplicants?.some((item) => item?.id === applicantId);
  };

  const checkIsApplicantDisabled = () => {
    return isEdit;
  };

  useEffect(() => {
    if (!_.isEmpty(addressData)) {
      setValue('sender', addressData.sender);
    }
  }, [addressData, open]);

  useEffect(() => {
    fetchSenderDetails();
    fetchReceiverDetails();
    fetchModeOfDispatch();
    fetchCounterOperator();
  }, []);

  useEffect(() => {
    if (Object.keys(userInfo).length > 0) {
      fetchOfficeType({ officeLbCode: userInfo?.id });
      fetchDispatchClerkDetails({ officeId: userInfo?.id });
    }
  }, [JSON.stringify(userInfo)]);

  const handleSameLocalBody = () => {
    if (watch('sameAsLocalBody') === false) {
      fetchWard(userLocalBody.id);
      setValue('district', userLocalBody?.districtId);
      setValue('localBody', userLocalBody?.officeCode);
      fetchLocalBody({
        districtId: userLocalBody.districtId,
        officeTypeId: DEFAULT_OFFICE_TYPE
      });
    } else {
      setValue('district', null);
      setValue('localBody', null);
    }
    setValue('sameAsLocalBody', !watch('sameAsLocalBody'));
  };

  const handleSameAsApplicant = () => {
    setValue('sameAsApplicant', !watch('sameAsApplicant'));
  };

  useEffect(() => {
    if (fileDetails) {
      const inwardApplicants = fileDetails?.inwardDetails?.map(
        (item) => item?.applicantDetailsAddress
      );
      const efileApplicants = fileDetails?.efileDetails?.map(
        (item) => item?.applicantDetailsResponses
      );
      const formatInward = inwardApplicants ? inwardApplicants?.flat() : [];
      const formatEfile = efileApplicants ? efileApplicants?.flat() : [];
      if (formatInward?.length > 0 || formatEfile?.length > 0) {
        setSameAsApplicantArray([
          ...formatInward.filter((n) => n),
          ...formatEfile.filter((n) => n)
        ]);
      }
    }
  }, [JSON.stringify(fileDetails)]);

  const mergeAddresses = (updated, existing) => _.unionBy(updated, existing?.applicantAddresses || [], 'id');

  const updateAddressData = (
    groupedApplicants,
    temp,
    currentTrigger,
    currentParams
  ) => {
    const addressTypes = [
      { key: 'individual', type: 1 },
      { key: 'institutional', type: 2 },
      { key: 'electorialRepAddress', type: 3 }
    ];

    setAddressData({
      sender: getValues('sender'),
      receiver: getValues('addressType'),
      addresses: addressTypes
        .map(({ key, type }) => {
          const updated = groupedApplicants[key]?.map((item) => ({
            ...item,
            ...temp
          })) || [];

          const existing = addressData?.addresses?.find(
            (a) => a.addressType === type
          );
          const merged = mergeAddresses(updated, existing);

          return currentTrigger && merged.length > 0
            ? {
              applicantAddresses: merged,
              addressType: type,
              fileNo: currentParams?.fileNo
            }
            : null;
        })
        .filter(Boolean)
    });
  };

  const onSubmitAddress = () => {
    const temp = {
      modeOfDispatch: getValues('modeOfDispatch'),
      officeType: getValues('officeType'),
      functionalGroupId: getValues('dispatchSection'),
      dispatchClerkPostId: getValues('user'),
      dispatchClerkName: getValues('dispatchClerkName'),
      dispatchClerkPenNo: getValues('dispatchClerkPenNo')
    };

    if (getValues('sameAsApplicant')) {
      const groupedApplicants = _.groupBy(selectedApplicants, 'groupType');
      updateAddressData(groupedApplicants, temp, trigger, params);
      setTrigger(false);
      toggleClose();
      return;
    }

    if (isEdit) {
      const addressType = getValues('addressType');
      const updatedData = {
        addressType,
        name: getValues('name'),
        address: getValues('address'),
        ...temp,
        ...(addressType === 2 && {
          officerName: getValues('officerName'),
          officeName: getValues('officeName')
        }),
        ...(addressType === 3 && {
          wardId: getValues('wardId'),
          district: getValues('district'),
          localBody: getValues('localBody')
        })
      };

      const updated = [...addressData.addresses];
      updated[itemToEdit.addressDataIndex].applicantAddresses[
        itemToEdit.addressIndex
      ] = updatedData;
      setAddressData({ ...addressData, addresses: updated });
      toggleClose();
    } else {
      const newEntry = {
        addressType: getValues('addressType'),
        name: getValues('name'),
        address: getValues('address'),
        modeOfDispatch: getValues('modeOfDispatch'),
        officeType: getValues('officeType'),
        functionalGroupId: getValues('dispatchSection'),
        dispatchClerkPostId: getValues('user'),
        dispatchClerkName: getValues('dispatchClerkName'),
        dispatchClerkPenNo: getValues('dispatchClerkPenNo'),
        ...(getValues('addressType') === 2 && {
          officerName: getValues('officerName'),
          officeName: getValues('officeName')
        }),
        ...(getValues('addressType') === 3 && {
          wardName: getValues('wardName'),
          wardId: getValues('wardId'),
          district: getValues('district'),
          districtName: getValues('districtName'),
          localBody: getValues('localBody'),
          localBodyName: getValues('localBodyName')
        })
      };

      const existingIndividualAddress = addressData?.addresses?.find((a) => a?.addressType === 1) || {};
      const existingInstitutionalAddress = addressData?.addresses?.find((a) => a?.addressType === 2) || {};
      const existingElectorialRepAddress = addressData?.addresses?.find((a) => a?.addressType === 3) || {};

      const newAddresses = {
        1: [...(existingIndividualAddress?.applicantAddresses || [])],
        2: [...(existingInstitutionalAddress?.applicantAddresses || [])],
        3: [...(existingElectorialRepAddress?.applicantAddresses || [])]
      };

      newAddresses[getValues('addressType')].push(newEntry);

      setAddressData({
        sender: getValues('sender'),
        receiver: getValues('addressType'),
        addresses: [
          newAddresses[1].length > 0
            ? {
              applicantAddresses: newAddresses[1],
              addressType: 1,
              fileNo: params?.fileNo
            }
            : null,
          newAddresses[2].length > 0
            ? {
              applicantAddresses: newAddresses[2],
              addressType: 2,
              fileNo: params?.fileNo
            }
            : null,
          newAddresses[3].length > 0
            ? {
              applicantAddresses: newAddresses[3],
              addressType: 3,
              fileNo: params?.fileNo
            }
            : null
        ].filter(Boolean)
      });

      toggleClose();
    }
  };

  const handleFieldChange = (field, data) => {
    switch (field) {
      case 'district':
        setValue('district', data?.id);
        setValue('districtName', data?.name);
        fetchLocalBody({
          districtId: data.id,
          officeTypeId: DEFAULT_OFFICE_TYPE
        });
        fetchInstitutionTypes();
        break;
      case 'localBody':
        setValue('localBody', data?.id);
        setValue('localBodyName', data?.name);
        fetchWard(data.id);
        break;
      case 'ward':
        setValue('wardId', data?.id);
        setValue('wardName', data?.name);
        break;
      case 'addressType':
        setValue('sameAsApplicant', false);
        setValue('addressType', data?.id);
        if (data?.id) {
          fetchDistricts(DEFAULT_STATE.id);
        }
        break;
      case 'modeOfDispatch':
        setValue('modeOfDispatch', data?.id);
        break;
      case FILTER_TYPE.OFFICE_TYPE:
        setValue('officeType', data?.id);
        break;
      case FILTER_TYPE.DEPARTMENT:
        setValue('dispatchSection', data?.id);
        break;
      case FILTER_TYPE.USER:
        setValue('user', data?.postId);
        setValue('dispatchClerkName', data?.employeeName);
        setValue('dispatchClerkPenNo', data?.penNo);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (
      fileDetails?.source === 3
      || fileDetails?.source === 5
      || watch('addressType') === 3
    ) {
      setValue('sameAsApplicant', false);
    }
  }, [open, fileDetails?.source, watch('addressType')]);

  return (
    <Modal isOpen={open} size="3xl" onClose={toggleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader className="flex items-center justify-between">
          <h4 className="text-[#09327B] text-[16px] font-extrabold">
            {t('addAddress')}
          </h4>
          <button onClick={toggleClose} aria-label="Close">
            <CloseNew fill="#fff" w="40" h="40" />
          </button>
        </ModalHeader>

        <ModalBody m={0} p={0} overflow="auto" maxH={440}>
          <div className="px-5 pt-2 pb-5">
            <form
              onSubmit={handleSubmit(onSubmitAddress)}
              id="add-address-form"
            >
              <FormWrapper px={false} py={false}>
                <div className="sm:col-span-6 col-span-12 ">
                  <FormController
                    name="sender"
                    type="select"
                    label={t('sender')}
                    placeholder={t('sender')}
                    control={control}
                    options={_.get(sendersDetails, 'data', [])}
                    optionKey="id"
                    required
                    errors={errors}
                  />
                </div>
                <div className="sm:col-span-6 col-span-12">
                  <FormController
                    name="addressType"
                    type="select"
                    label={t('receiver')}
                    placeholder={t('receiver')}
                    control={control}
                    optionKey="id"
                    options={_.get(receiversDetails, 'data', [])}
                    handleChange={(data) => handleFieldChange('addressType', data)}
                    isDisabled={isEdit}
                  />
                </div>
                {fileDetails?.source !== 3
                  && fileDetails?.source !== 5
                  && watch('addressType') !== 3 && (
                    <div className="col-span-12">
                      <div className="flex">
                        <h4 className="text-[#09327B] font-bold text-[14px] mr-3">
                          Receiver Details
                        </h4>
                        <Button
                          variant="link"
                          disabled={isEdit}
                          style={{
                            textDecoration: 'none',
                            color: '#456C86',
                            fontWeight: 500
                          }}
                          leftIcon={
                            !watch('sameAsApplicant') ? (
                              <CheckedBox />
                            ) : (
                              <UnCheckedBox />
                            )
                          }
                          onClick={handleSameAsApplicant}
                        >
                          <span className="ml-1 text-[14px]">
                            {t('notTheSameAsApplicant')}
                          </span>
                        </Button>
                      </div>

                      {sameAsApplicantArray?.length > 0
                        && watch('sameAsApplicant') && (
                          <ApplicantList
                            applicantList={sameAsApplicantArray}
                            isApplicantDisabled={checkIsApplicantDisabled}
                            checkIsApplicantChecked={checkIsApplicantChecked}
                            onApplicantSelect={handleApplicantSelect}
                          />
                      )}
                    </div>
                )}

                {!watch('sameAsApplicant') && (
                  <>
                    {watch('addressType') === 3 && (
                      <>
                        <div className="col-span-12">
                          <Button
                            variant="link"
                            style={{ textDecoration: 'none' }}
                            leftIcon={
                              watch('sameAsLocalBody') ? (
                                <CheckedBox />
                              ) : (
                                <UnCheckedBox />
                              )
                            }
                            onClick={handleSameLocalBody}
                          >
                            {t('sameLocalBody')}
                          </Button>
                        </div>

                        <div className="sm:col-span-6 col-span-12 ">
                          <FormController
                            name="district"
                            type="select"
                            label={t('district')}
                            placeholder={t('district')}
                            control={control}
                            errors={errors}
                            options={_.get(districtDropdown, 'data', [])}
                            optionKey="id"
                            handleChange={(data) => handleFieldChange('district', data)}
                            required
                          />
                        </div>

                        <div className="sm:col-span-6 col-span-12 ">
                          <FormController
                            name="localBody"
                            type="select"
                            label={t('localBody')}
                            placeholder={t('localBody')}
                            control={control}
                            errors={errors}
                            options={_.get(localBodies, 'data', [])}
                            optionKey="id"
                            handleChange={(data) => handleFieldChange('localBody', data)}
                            required
                          />
                        </div>
                      </>
                    )}
                    {watch('addressType') === 2 ? (
                      <>
                        <div className="sm:col-span-6 col-span-12">
                          <FormController
                            name="officerName"
                            type="text"
                            label={t('concatLabel', {
                              label: t('officer'),
                              type: t('name')
                            })}
                            control={control}
                            required
                            errors={errors}
                          />
                        </div>
                        <div className="sm:col-span-6 col-span-12 ">
                          <FormController
                            name="officeName"
                            type="text"
                            label={t('concatLabel', {
                              label: t('office'),
                              type: t('name')
                            })}
                            control={control}
                            required
                            errors={errors}
                          />
                        </div>
                      </>
                    ) : null}
                    {watch('addressType') === 3 ? (
                      <div className="col-span-12">
                        <FormController
                          name="wardId"
                          type="select"
                          label={t('concatLabel', {
                            label: t('ward'),
                            type: t('name')
                          })}
                          placeholder={t('concatLabel', {
                            label: t('ward'),
                            type: t('name')
                          })}
                          control={control}
                          options={_.get(wardDropdown, 'data', [])}
                          handleChange={(data) => handleFieldChange('ward', data)}
                          errors={errors}
                          required
                          optionKey="id"
                        />
                      </div>
                    ) : null}
                    {(watch('addressType') === 1
                      || watch('addressType') === 3) && (
                      <div className="col-span-12">
                        <FormController
                          name="name"
                          type="text"
                          label={t('name')}
                          control={control}
                          required
                          errors={errors}
                        />
                      </div>
                    )}
                    <div className="col-span-12">
                      <FormController
                        name="address"
                        type="textarea"
                        label={t('address')}
                        control={control}
                        required
                        errors={errors}
                      />
                    </div>
                  </>
                )}
                <div className="col-span-12 flex items-center gap-3">
                  <Button
                    variant="link"
                    style={{
                      textDecoration: 'none',
                      color: '#456C86',
                      fontWeight: 500
                    }}
                    leftIcon={
                      isDispatchFieldOpen ? <CheckedBox /> : <UnCheckedBox />
                    }
                    onClick={() => setIsDispatchFieldOpen(!isDispatchFieldOpen)}
                  >
                    <span className="ml-1 text-[14px]">{t('dispatch')}</span>
                  </Button>
                </div>

                <Collapse
                  open={isDispatchFieldOpen}
                  className="col-span-12 flex"
                >
                  <div className="grid grid-cols-12 gap-x-5 gap-y-[30px] pt-2 mb-2">
                    <div className="sm:col-span-6 col-span-12">
                      <FormController
                        name="modeOfDispatch"
                        type="select"
                        label={t('modeOfDispatch')}
                        control={control}
                        options={modeOfDispatchDetails || []}
                        handleChange={(data) => handleFieldChange('modeOfDispatch', data)}
                        optionKey="id"
                        isClearable
                        menuPosition="fixed"
                        menuPlacement="top"
                      />
                    </div>
                    <div className="sm:col-span-6 col-span-12">
                      <FormController
                        name="officeType"
                        type="select"
                        label={t('officeType')}
                        placeholder={t('select')}
                        control={control}
                        optionKey="id"
                        handleChange={(data) => handleFieldChange(FILTER_TYPE.OFFICE_TYPE, data)}
                        options={officeType || []}
                        isClearable
                        menuPosition="fixed"
                        menuPlacement="top"
                      />
                    </div>
                    <div className="sm:col-span-6 col-span-12">
                      <FormController
                        name="dispatchSection"
                        type="select"
                        label={t('dispatchSection')}
                        placeholder={t('select')}
                        control={control}
                        optionKey="id"
                        isClearable
                        handleChange={(data) => handleFieldChange(FILTER_TYPE.DEPARTMENT, data)}
                        options={counterOperatorDropdown || []}
                        menuPosition="fixed"
                        menuPlacement="top"
                      />
                    </div>
                    <div className="sm:col-span-6 col-span-12">
                      <FormController
                        name="user"
                        type="select"
                        label={t('user')}
                        placeholder={t('select')}
                        control={control}
                        optionKey="postId"
                        handleChange={(data) => handleFieldChange(FILTER_TYPE.USER, data)}
                        options={post?.filter((item) => item?.penNo !== null)}
                        isClearable
                        menuPosition="fixed"
                        menuPlacement="top"
                      />
                    </div>
                  </div>
                </Collapse>
              </FormWrapper>
            </form>
          </div>
        </ModalBody>
        <ModalFooter pt={3} pb={5} px={0} gap={2} justifyContent="center">
          <Button
            variant="secondary_outline"
            size="sm"
            height={10}
            onClick={toggleClose}
          >
            {t('cancel')}
          </Button>
          <Button
            type="submit"
            variant="secondary"
            size="sm"
            height={10}
            form="add-address-form"
            onClick={() => setTrigger(true)}
          >
            {t(isEdit ? 'update' : 'add')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({
  sendersDetails: getSendersDetails,
  receiversDetails: getReceiversDetails,
  districtDropdown: getDistricts,
  wardDropdown: getWard,
  applicantAddress: getApplicantAddress,
  institutionTypes: getInstitutionTypes,
  localBodies: getLocalBody,
  institutions: getInstitutions,
  wardDetails: getWardDetails,
  userLocalBody: getUserLocalBody,
  fileDetails: getFileDetails,
  userInfo: getUserInfo,
  modeOfDispatchDetails: getModeOfDispatch,
  officeType: getOfficeType,
  counterOperatorDropdown: getCounterOperator,
  dispatchClerkDetails: getDispatchClerkDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchSenderDetails: () => dispatch(actions.fetchSenderDetails()),
  fetchReceiverDetails: () => dispatch(actions.fetchReceiverDetails()),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchWard: (data) => dispatch(commonActions.fetchWardsByLocalBodyId(data)),
  fetchInstitutionTypes: () => dispatch(commonActions.fetchInstitutionTypes()),
  fetchLocalBody: (data) => dispatch(commonActions.fetchLocalBody(data)),
  fetchInstitutions: (data) => dispatch(commonActions.fetchInstitutions(data)),
  fetchModeOfDispatch: (data) => dispatch(commonActions.fetchModeOfDispatch(data)),
  fetchOfficeType: (data) => dispatch(commonActions.fetchOfficeType(data)),
  fetchCounterOperator: (data) => dispatch(commonActions.fetchCounterOperator(data)),
  fetchDispatchClerkDetails: (data) => dispatch(commonActions.fetchDispatchClerkDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(AddressModal);
