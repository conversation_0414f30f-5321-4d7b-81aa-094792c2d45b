import { memo, useCallback, useEffect } from 'react';
import NoNotesIcon from 'assets/NoNotesIcon';
import { t } from 'common/components';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { getCorrespondTypeDropdown, getUserInfo } from 'pages/common/selectors';
import {
  getFileDetails,
  getPullDraftDetails,
  getPullSearchDraftParams
} from 'pages/file/details/selector';
import { actions as sliceActions } from '../../../../../slice';
import { Collapsible } from './Collapsible';
import ImportItemFoundation from './ImportItemFoundation';
import * as actions from '../../../../../actions';

const ImportDraft = ({
  correspondTypeDropdown,
  selectedDraftToImport,
  onDraftSelect,
  params,
  setPullSearchDraftParams,
  pullSearchDraftParams,
  pullDraftList,
  fetchPullDraftDetails,
  fileDetails,
  userInfo
}) => {
  const updateSearchDraftParams = useCallback(
    (key, value) => {
      setPullSearchDraftParams({
        ...pullSearchDraftParams,
        [key]: value,
        postId: fileDetails?.postId,
        officeId: userInfo?.id
      });
    },
    [fileDetails, userInfo, pullSearchDraftParams]
  );

  const handlePageChange = useCallback(
    (p) => {
      updateSearchDraftParams('page', p - 1);
    },
    [updateSearchDraftParams]
  );

  const handleSearch = useCallback(
    (query) => {
      updateSearchDraftParams('fileNo', query);
    },
    [updateSearchDraftParams]
  );

  const fetchPullDrafts = useCallback(() => {
    if (pullSearchDraftParams?.postId && pullSearchDraftParams?.officeId) {
      fetchPullDraftDetails();
    }
  }, [pullSearchDraftParams, userInfo, fileDetails]);

  useEffect(() => {
    fetchPullDrafts();
  }, [pullSearchDraftParams, userInfo, fileDetails]);

  const checkCorresdence = useCallback(
    (data) => {
      if (data) {
        const getName = correspondTypeDropdown?.data?.map((item) => {
          if (item.id === data) {
            return item.name;
          }
          return '';
        });
        return getName;
      }

      return '';
    },
    [correspondTypeDropdown]
  );

  const draftCard = ({ draft }) => {
    return (
      <Collapsible
        title={checkCorresdence(draft?.draftType)}
        subject={draft?.subject}
        date={draft?.fileCreatedDate}
        checked={draft?.draftId === selectedDraftToImport?.draftId}
        onCheckChange={() => onDraftSelect(draft)}
      >
        {/* eslint-disable-next-line react/no-danger */}
        <div dangerouslySetInnerHTML={{ __html: draft?.draftText }} />
      </Collapsible>
    );
  };

  const draftCards = () => {
    return (
      <div className="flex flex-col gap-3">
        {pullDraftList?.content?.length > 0 ? (
          pullDraftList?.content?.map((draft, i) => draftCard({ index: i, draft }))
        ) : (
          <div className="p-10 text-center bg-white rounded-lg">
            <NoNotesIcon width="100px" height="100px" className="mx-auto" />
            <h4>{t('noNotesFound')}</h4>
          </div>
        )}
      </div>
    );
  };

  return (
    <ImportItemFoundation
      params={params}
      totalCount={pullDraftList?.totalElements}
      numberOfElements={pullDraftList?.numberOfElements}
      pageSize={10}
      item={draftCards()}
      onSearch={handleSearch}
      onPageChange={handlePageChange}
    />
  );
};

const MemoizedImportDraft = memo(ImportDraft);

const mapStateToProps = createStructuredSelector({
  correspondTypeDropdown: getCorrespondTypeDropdown,
  pullSearchDraftParams: getPullSearchDraftParams,
  pullDraftList: getPullDraftDetails,
  fileDetails: getFileDetails,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchPullDraftDetails: (data) => dispatch(actions.fetchPullDraftDetails(data)),
  setPullSearchDraftParams: (data) => dispatch(sliceActions.setPullSearchDraftParams(data))
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MemoizedImportDraft);
