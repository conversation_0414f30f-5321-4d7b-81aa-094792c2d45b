import { Checkbox } from '@ksmartikm/ui-components';
import KeyboardDownArrow from 'assets/KeyboardDownArrow';
import React, { useState, useRef, useEffect } from 'react';

export function Collapsible({
  title,
  subject,
  date,
  checked,
  children,
  onCheckChange
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [height, setHeight] = useState(0);
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current) {
      const { scrollHeight } = contentRef.current;
      setHeight(isExpanded ? scrollHeight : 0);
    }
  }, [isExpanded]);

  return (
    <div
      aria-hidden
      onClick={onCheckChange}
      className="bg-white rounded-[12px] shadow-md border border-gray-100 overflow-hidden transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:shadow-md"
      style={{ transform: `translateY(${isExpanded ? '0' : '-2px'})` }}
    >
      <div className="p-4 cursor-pointer flex items-center justify-between select-none">
        <div className="flex-1 flex w-[80%]">
          <Checkbox isChecked={checked} />
          <div className="ml-4 w-[80%]">
            <p className="text-[16px] font-semibold text-[#232F50] transition-colors duration-500">
              {title}
            </p>
            {subject && (
              <div className="flex w-[100%] items-center">
                <p className="w-[13%] text-[#323232] text-[14px] transition-colors duration-500">
                  Subject :
                </p>
                <div
                  className="w-[87%] ml-1 whitespace-nowrap truncate overflow-hidden text-ellipsis inline-block align-middle"
                  /* eslint-disable-next-line react/no-danger */
                  dangerouslySetInnerHTML={{ __html: subject }}
                />
                ...
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <span className="text-[#5C6E93] font-semibold text-[14px]">
            {date}
          </span>
          <button
            onClick={(e) => {
              e?.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
            className="p-2 transition-all duration-500 ease-[cubic-bezier(0.34,1.56,0.64,1)]"
            style={{
              transform: `rotate(${isExpanded ? '180deg' : '0deg'}) scale(${
                isExpanded ? '1.1' : '1'
              })`
            }}
          >
            <KeyboardDownArrow stroke="#323232" />
          </button>
        </div>
      </div>

      <div
        ref={contentRef}
        className="overflow-hidden transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]"
        style={{
          height,
          opacity: height === 0 ? 0 : 1,
          transform: `translateY(${height === 0 ? '-8px' : '0'})`
        }}
      >
        <div className="px-4 pb-4 pt-2 border-t border-gray-100">
          {children}
        </div>
      </div>
    </div>
  );
}
