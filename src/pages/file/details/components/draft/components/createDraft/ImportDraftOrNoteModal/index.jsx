import {
  Button,
  Checkbox,
  Modal,
  ModalBody,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay
} from '@ksmartikm/ui-components';
import BgPlainCloseIcon from 'assets/BgPlainCloseIcon';
import { t } from 'i18next';
import {
  useCallback, useState, memo, useMemo,
  useEffect
} from 'react';
import _ from 'lodash';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { Toast } from 'common/components';
import {
  getPullSearchDraftParams,
  getPullSearchNotesParams
} from 'pages/file/details/selector';
import { getUserInfo } from 'pages/common/selectors';
import Tabs from 'common/components/Tabs';
import ImportDraft from './ImportDraft';
import ImportNote from './ImportNote';
import { actions as sliceActions } from '../../../../../slice';

const { errorTost } = Toast;

const ImportDraftOrNoteModal = ({
  open,
  savePullNotes,
  params,
  onClose = () => {},
  pullSearchNotesParams,
  pullSearchDraftParams,
  setPullSearchNotesParams,
  setPullSearchDraftParams,
  setPulledDraft,
  userInfo
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedNoteToImport, setSelectedNoteToImport] = useState(null);
  const [selectedDraftToImport, setSelectedDraftToImport] = useState(null);
  const [selectedNoteText, setSelectedNoteText] = useState('');
  const [isSameFile, setIsSameFile] = useState(false);
  const [innerSearch, setInnerSearch] = useState('');

  useEffect(() => {
    if (open) {
      savePullNotes(null);
      setPulledDraft(null);
    }
  }, [open]);

  const resetSearchParams = () => {
    setInnerSearch('');
    setIsSameFile(false);
    setSelectedNoteToImport(null);
    setSelectedDraftToImport(null);
    setPullSearchNotesParams({
      page: 0,
      size: 3,
      fileNo: '',
      sortDirection: 'desc',
      search: false,
      noteStatus: 'COMPLETED'
    });
    setPullSearchDraftParams({
      page: 0,
      size: 10,
      postId: '',
      sortDirection: 'desc',
      officeId: '',
      fileNo: ''
    });
  };

  const toggleClose = () => {
    onClose();
    setActiveTab(0);
    resetSearchParams();
  };

  const handleTabsChange = useCallback(({ index }) => {
    setActiveTab(index);
    resetSearchParams();
  }, []);

  const handleSelectNote = useCallback((note) => {
    setSelectedNoteToImport((prev) => (prev?.notesId === note?.notesId ? null : note));
  }, [setSelectedNoteToImport]);

  const handleSelectDraft = useCallback((draft) => {
    setSelectedDraftToImport((prev) => (prev?.draftId === draft?.draftId ? null : draft));
  }, []);

  const handleImport = (e) => {
    e?.preventDefault();

    if (activeTab === 0) {
      if (_.isEmpty(selectedNoteToImport)) {
        errorTost({
          description: t('selectNote')
        });
        return;
      }

      savePullNotes(selectedNoteText || selectedNoteToImport?.notes);
    } else {
      if (_.isEmpty(selectedDraftToImport)) {
        errorTost({
          description: t('selectDraft')
        });
        return;
      }

      setPulledDraft(selectedNoteText || selectedDraftToImport?.draftText);
    }

    toggleClose();
  };

  const handleIsSameFileChange = useCallback(
    (e) => {
      setIsSameFile(e?.target?.checked);

      if (activeTab === 0) {
        if (e?.target?.checked) {
          setPullSearchNotesParams({
            ...pullSearchNotesParams,
            fileNo: params?.fileNo
          });
        }
      } else if (e?.target?.checked) {
        setPullSearchDraftParams({
          ...pullSearchDraftParams,
          officeId: userInfo?.id,
          pullFileNo: params?.fileNo,
          fileNo: params?.fileNo
        });
      }

      if (e?.target?.checked) {
        setInnerSearch(params?.fileNo);
      } else {
        setInnerSearch('');
      }
    },
    [
      activeTab,
      params,
      setPullSearchDraftParams,
      setPullSearchNotesParams,
      setInnerSearch
    ]
  );

  const tabs = useMemo(
    () => [
      {
        title: t('note'),
        content: (
          <ImportNote
            open={open}
            params={params}
            selectedNoteToImport={selectedNoteToImport}
            onNoteSelect={handleSelectNote}
            setSelectedNoteText={setSelectedNoteText}
            setIsSameFile={setIsSameFile}
            isSameFile={isSameFile}
            innerSearch={innerSearch}
            setInnerSearch={setInnerSearch}
          />
        )
      },
      {
        title: t('draft'),
        content: (
          <ImportDraft
            open={open}
            params={params}
            selectedDraftToImport={selectedDraftToImport}
            onDraftSelect={handleSelectDraft}
            setSelectedNoteText={setSelectedNoteText}
            setIsSameFile={setIsSameFile}
            isSameFile={isSameFile}
            innerSearch={innerSearch}
            setInnerSearch={setInnerSearch}
          />
        )
      }
    ],
    [
      selectedNoteToImport,
      selectedDraftToImport,
      isSameFile,
      innerSearch,
      setInnerSearch,
      setSelectedNoteText,
      setIsSameFile
    ]
  );

  return (
    <Modal
      isCentered
      scrollBehavior="inside"
      isOpen={open}
      size="100%"
      onClose={toggleClose}
    >
      <ModalOverlay />
      <ModalContent m={0} w="745px" h="90%" maxH="none">
        <ModalHeader
          py={3}
          px={3}
          display="flex"
          justifyContent="space-between"
          alignItems="center"
        >
          <div className="flex items-center justify-center">
            <span className="text-[#09327B] text-[18px] font-bold">
              {t('importContent')}
            </span>
          </div>
          <div className="flex items-center">
            <div className="flex items-center gap-3 mr-3">
              <Checkbox
                isChecked={isSameFile}
                onChange={handleIsSameFileChange}
              >
                <span className="text-[#456C86] font-[500] text-[14px]">
                  {t('fromThisFile')}
                </span>
              </Checkbox>
            </div>
            <button type="button" onClick={toggleClose} aria-label="Close">
              <BgPlainCloseIcon stroke="#5C6E93" />
            </button>
          </div>
        </ModalHeader>
        <ModalBody py={0} px={1} className="import-draft-note-modal-body">
          <Tabs
            tabs={tabs}
            defaultTab={0}
            onTabChange={(i) => handleTabsChange({ index: i })}
          />
        </ModalBody>
        <ModalFooter p={0} mb={3} mt={0} gap={2} justifyContent="center">
          <Button
            type="button"
            variant="secondary_outline"
            py={2}
            minW={140}
            fontSize="sm"
            fontWeight={700}
            onClick={toggleClose}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="secondary"
            py={2}
            minW={140}
            fontSize="sm"
            fontWeight={700}
            onClick={handleImport}
          >
            {t('import')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

const MemoizedImportDraftOrNoteModal = memo(ImportDraftOrNoteModal);

const mapStateToProps = createStructuredSelector({
  pullSearchNotesParams: getPullSearchNotesParams,
  pullSearchDraftParams: getPullSearchDraftParams,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  savePullNotes: (data) => dispatch(sliceActions.setPulledNote(data)),
  setPullSearchNotesParams: (data) => dispatch(sliceActions.setPullSearchNotesParams(data)),
  setPullSearchDraftParams: (data) => dispatch(sliceActions.setPullSearchDraftParams(data))
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MemoizedImportDraftOrNoteModal);
