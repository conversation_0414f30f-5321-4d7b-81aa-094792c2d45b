import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  t,
  FormController,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody
} from 'common/components';
import { EnclosureSchema } from 'pages/file/details/validate';
import { dark, light } from 'utils/color';

const EnclosureModal = ({
  open,
  setOpenEnclosure,
  setEnclosureData,
  setEnclosureFiles,
  isDraftSaved,
  enclosureData
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm({
    defaultValues: {
      enclosureName: '',
      enclosureFile: ''
    },
    resolver: yupResolver(EnclosureSchema),
    mode: 'onChange'
  });
  const [enclosureArray, setEnclosureArray] = useState([]);
  const enclosureFile = watch('enclosureFile');
  const enclosureName = watch('enclosureName');
  const [isClick, setIsClick] = useState(false);

  useEffect(() => {
    setEnclosureArray([]);
    if (enclosureData.length) {
      setEnclosureArray(enclosureData);
    }
  }, [isDraftSaved, enclosureData]);

  useEffect(() => {
    if (enclosureData !== undefined && Object.keys(enclosureData)?.length === 0) {
      reset();
      setEnclosureArray([]);
    }
  }, [enclosureData]);

  const onSubmitEnclosure = async (data) => {
    const enclosuredata = {
      enclosureName: data.enclosureName,
      documentName: enclosureFile.name,
      file: data.enclosureFile
    };
    setEnclosureArray((prevState) => [...prevState, enclosuredata]);
    setEnclosureFiles((prevState) => [...prevState, enclosureFile]);
    reset();
    setIsClick(false);
  };
  const addEnclosure = () => {
    if (enclosureArray.length || (enclosureName !== '' && enclosureFile !== '')) {
      if (enclosureName !== '' && enclosureFile !== '') {
        const enclosuredata = {
          enclosureName,
          documentName: enclosureFile.name,
          file: enclosureFile
        };
        const enclArr = JSON.parse(JSON.stringify(enclosureArray));
        enclArr.push(enclosuredata);
        setEnclosureData(enclArr);
        setEnclosureFiles((prevState) => [...prevState, enclosureFile]);
      } else {
        setEnclosureData(enclosureArray);
      }
      setOpenEnclosure(false);
      setIsClick(false);
      setValue('enclosureName', '');
      setValue('enclosureFile', '');
    } else {
      setIsClick(true);
    }
  };
  return (
    <Modal isOpen={open} size="2xl" onClose={() => setOpenEnclosure(false)} className="custom-form-modal">
      <ModalOverlay />
      <ModalContent p={0}>
        <ModalHeader className="rounded-t-lg text-center" style={{ background: light, color: dark }}>
          <h4 size="md">
            {t('addEnclosure')}
          </h4>
        </ModalHeader>
        <ModalBody>
          <div className="px-5 pt-10 pb-10">
            <form
              onSubmit={handleSubmit(onSubmitEnclosure)}
              id="add-enclosure-form"
            >
              <div className="grid grid-rows gap-4">
                <div className="flex-grow">
                  <FormController
                    name="enclosureName"
                    type="text"
                    label={t('concatLabel', { label: t('enclosure'), type: t('name') })}
                    control={control}
                    required
                    errors={errors}
                  />
                </div>
                <div className="flex-grow pt-10">
                  <FormController
                    name="enclosureFile"
                    type="file"
                    fileSize={2}
                    label={t('chooseFile')}
                    fileTypes="png"
                    control={control}
                    errors={errors}
                    required
                  />
                </div>
              </div>
            </form>
          </div>
        </ModalBody>
        <ModalFooter style={{ background: light, color: dark }}>
          <div className="col-span-12 flex justify-end items-center space-x-2">
            <Button
              variant="secondary_outline"
              size="sm"
              mr={3}
              height={10}
              onClick={() => {
                reset();
                setOpenEnclosure(false);
              }}
            >
              {t('cancel')}
            </Button>
            {
              isClick
                ? (
                  <Button
                    variant="secondary"
                    size="sm"
                    height={10}
                    type="submit"
                    form="add-enclosure-form"
                  >
                    {t('addEnclosure')}
                  </Button>
                )
                : (
                  <Button
                    variant="secondary"
                    size="sm"
                    height={10}
                    onClick={addEnclosure}
                  >
                    {t('addEnclosure')}
                  </Button>
                )
            }
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default EnclosureModal;
