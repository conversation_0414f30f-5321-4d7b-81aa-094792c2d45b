import { createStructuredSelector } from 'reselect';
import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Edit } from 'assets/Svg';
import {
  CustomTable, t
} from 'common/components';
import { useParams } from 'react-router-dom';
import * as actions from '../../../../actions.js';
import { getPendingDraftDetails } from '../../../../selector.js';

const PendingDraftDetails = (props) => {
  const {
    pendingDraftDetails,
    fetchPendingDraftDetails,
    setActiveIndex,
    setEditDraft,
    activeIndex
  } = props;
  const params = useParams();
  const editPendingDraftData = (data) => {
    setActiveIndex(0);
    setEditDraft(data);
  };
  const headers = [
    {
      header: t('slNo'),
      alignment: 'left',
      field: 'slNo'
    },
    {
      header: t('draftType'),
      alignment: 'left',
      field: 'draftType'
    },
    {
      header: t('draftStatus'),
      alignment: 'left',
      field: 'draftStage'
    },
    {
      header: t('createdBy'),
      alignment: 'left',
      field: 'createdBy'
    },
    {
      header: t('seat'),
      alignment: 'left',
      field: 'postId'
    },
    {
      header: t('createdDate'),
      alignment: 'left',
      field: 'createdAt'
    },
    {
      header: t('action'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          label: t('edit'),
          icon: <Edit />,
          onClick: (data) => editPendingDraftData(data)
        }
      ]
    }
  ];

  const [tableData, setTableData] = useState([]);
  useEffect(() => {
    if (params.fileNo && activeIndex === 1) {
      fetchPendingDraftDetails(params.fileNo);
    }
  }, [params, activeIndex]);
  useEffect(() => {
    if (pendingDraftDetails?.length > 0) {
      setTableData(pendingDraftDetails);
    }
  }, [pendingDraftDetails]);

  return (
    <div className="-mt-10">
      <CustomTable
        columns={headers}
        tableData={tableData}
        paginationEnabled
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  pendingDraftDetails: getPendingDraftDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchPendingDraftDetails: (data) => dispatch(actions.fetchPendingDraftDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PendingDraftDetails);
