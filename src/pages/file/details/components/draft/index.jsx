import React, { useState, useEffect } from 'react';
import { t, But<PERSON>, CustomTab } from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useParams, useSearchParams } from 'react-router-dom';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from 'pages/file/details/slice';
import {
  getActionTriggered,
  getActiveBlobDetails,
  getNoteCardDetails,
  getUserInfo
} from 'pages/common/selectors';
import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';
import { NOTE_STATUS } from 'pages/common/constants';
import DocumentPreview from 'common/components/DocumentPreview';
import { blobUrlToBase64 } from 'utils/common';
import * as commonActions from 'pages/common/actions';
import * as dsActions from 'pages/profile/ds/actions';
import { getAllEnrolls, getDsLocalEnrollment } from 'pages/profile/ds/selector';
import { generatePdf } from 'hooks/generatePdf';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import dayjs from 'dayjs';
import DocumentNewExpandComponents from 'common/components/DocumentPreview/DocumentNewExpandComponents';
import {
  getDraftDataById,
  getDraftPreview,
  getDraftPreviewFlag,
  getFileDetails,
  getFileDocuments,
  getMergeLinkActive,
  getMergeLinkActiveId,
  getSelectedCorrespondence
} from '../../selector';
import * as actions from '../../actions';
import { applicantName } from '../helper';
import Note from './components/note';
import { checkRoleByFileRole } from '../../helper';
import DraftPreview from './components/DraftPreview';
import { DRAFT_PDF_URL } from '../../constants';
import DraftDsConfirmPreview from './components/createDraft/DraftDsConfirmPreview';
import DraftNewPreview from '../notes/draft/DraftNewPreview';
import ShowAllDocuments from '../notes/ShowAllDocuments';
import DraftCreateOrUpdate from './components/createDraft/DraftCreateOrUpdate';

const Draft = (props) => {
  const {
    setFormTitle,
    fetchFileDetails,
    setFileHeader,
    fileDetails,
    userInfo,
    setAlertAction,
    actionTriggered,
    fetchPartialNotes,
    setDraftPreviewFlag,
    draftPreviewFlag,
    fetchFileDocuments,
    // fileDocuments,
    setDraftDataById,
    mergeLinkActive,
    mergeLinkActiveId,
    draftDataById,
    setActionTriggered,
    fetchAllEnroll,
    signPdf,
    selectedCorrespondence,
    noteCardDetails,
    setIsDigitalSignAction
  } = props;

  const params = useParams();
  const [editDraft, setEditDraft] = useState({});
  const [activeIndex, setActiveIndex] = useState(0);
  const [roles, setRoles] = useState(null);
  const [draftSaveStatus, setDraftSaveStatus] = useState('save');
  const [draftAction, setDraftAction] = useState(null);
  const [open, setOpen] = useState(false);
  const [collapsePreview, setCollapsePreview] = useState(false);
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const [loading, setLoading] = useState({ loading: false });
  const [draftPreview, setDraftPreview] = useState({});
  // const [selectedDraft, setSelectedDraft] = useState({});
  const [draftItems, setDraftItems] = useState();
  const [searchParams] = useSearchParams();

  const [openNewExpand, setOpenNewExpand] = useState(false);
  // const [flowAction, setFlowAction] = useState(false);
  const [full, setFull] = useState(false);

  const handleTogglePreview = () => {
    setCollapsePreview(!collapsePreview);
  };

  const close = () => {
    setOpen(false);
    setIsDigitalSignAction(false);
  };

  useEffect(() => {
    if (fileDetails?.serviceCode) {
      const serviceDetails = userInfo?.userRoles;
      const findService = serviceDetails?.find(
        (item) => item.code === fileDetails?.serviceCode
      );
      if (_.keys(findService).length > 0) {
        const findRole = findService?.roles;
        setRoles(findRole);
      }
    }
  }, [fileDetails]);

  useEffect(() => {
    if (params) {
      if (params.fileNo) {
        fetchFileDetails(params.fileNo);
        const notesSearchRequest = {
          fileNo: params?.fileNo,
          assigner: userInfo?.assigner,
          noteStatus: NOTE_STATUS.PARTIAL
        };
        if (userInfo?.assigner) {
          fetchPartialNotes(notesSearchRequest);
        }
      }
    }
  }, [params, JSON.stringify(userInfo?.assigner)]);

  useEffect(() => {
    setFormTitle({ title: t('draft'), variant: 'normal' });
    if (fileDetails?.fileNo === params?.fileNo) {
      setFileHeader([
        {
          label: t('fileNumber'),
          value: params.fileNo
        },
        {
          label: t('role'),
          value: fileDetails?.role
        },
        {
          label: t('service'),
          value: fileDetails?.serviceName
        },
        {
          label: t('applicantName'),
          value: fileDetails?.inwardDetails
            ? applicantName(fileDetails?.inwardDetails)
            : ''
        }
      ]);
    }
  }, [JSON.stringify(fileDetails)]);

  const handlePreview = async (item) => {
    if (params?.draftid !== null && params?.draftid !== undefined) {
      // setSelectedDraft(draftDataById);
      setLoading({ loading: true, id: params?.draftid });
      const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${
        params?.draftid
      }&officeId=${
        userInfo?.officeId
      }&template=${selectedCorrespondence[0]?.code.toLowerCase()}`;
      const generate = generatePdf({ url: urlDraftPdf });
      const { data, status } = await generate.then((result) => result);
      if (status === 'success') {
        setLoading({ loading: false });
        if (item === 'click') {
          setOpen(true);
        }
        setLoading(false);
        setDraftPreview(data);
      } else {
        setLoading({ loading: false });
      }
    }
  };

  useEffect(() => {
    if (
      params?.draftid
      && userInfo?.officeId
      && selectedCorrespondence?.length > 0
    ) {
      handlePreview();
    }
  }, [
    params?.draftid,
    userInfo?.officeId,
    JSON.stringify(selectedCorrespondence),
    draftPreviewFlag
  ]);

  useEffect(() => {
    if (params) {
      if (params?.fileNo) {
        setDraftDataById();
        fetchFileDocuments(
          mergeLinkActive ? mergeLinkActiveId : params?.fileNo
        );
      }
    }
  }, [params, userInfo, mergeLinkActiveId, mergeLinkActive]);

  const getDocumentHead = () => {
    if (noteCardDetails?.length > 0) {
      return t('allDocuments');
    }
    return t('documents');
  };

  const getDocumentContents = () => {
    if (noteCardDetails?.length > 0) {
      return (
        <DocumentPreview
          expandEnable
          setOpenNewExpand={setOpenNewExpand}
          openNewExpand={openNewExpand}
          setFull={setFull}
          full={full}
        />
      );
    }

    return (
      <ShowAllDocuments
        mergeLinkActive={mergeLinkActive}
        mergeLinkActiveId={mergeLinkActiveId}
        fileNo={params?.fileNo}
      />
    );
  };

  const tabs = [
    {
      title: t('draftPreview'),
      content: (
        <DraftPreview
          draftPreview={params?.draftid ? draftPreview : null}
          loading={loading?.loading && loading?.id === params?.draftid}
        />
      )
    },
    { title: t('notes'), content: <Note fileDetails={fileDetails} /> },
    { title: getDocumentHead(), content: getDocumentContents() }
  ];

  const handleDraftPreview = (data) => {
    if (data === 0) {
      setDraftPreviewFlag(!draftPreviewFlag);
    }
  };

  const handleTabsChange = (data) => {
    setActiveIndex(data?.index);
    handleDraftPreview(data?.index);
  };

  // const triggerReject = () => {
  //   setDraftSaveStatus(CONFIRMATION_TYPE.REJECT);
  //   setDraftAction('reject');
  // };

  const triggerDraftComplete = (val) => {
    setDraftSaveStatus(val === 'approved' ? 'approved' : 'proceed');
    setDraftAction(checkRoleByFileRole(fileDetails?.role));
  };

  // const returnDraft = () => {
  //   setDraftSaveStatus('return');
  //   setDraftAction('return');
  // };

  // const approveDraft = () => {
  //   setDraftSaveStatus('approved');
  //   setDraftAction('approved');
  // };

  const handleEsign = async (data) => {
    // console.log('>>>4:', data);
    const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${
      data.draftId || params?.draftid
    }&officeId=${
      userInfo?.id
    }&template=${selectedCorrespondence[0]?.code.toLowerCase()}`;

    const draft = await fetch(urlDraftPdf, {
      method: 'GET',
      headers: {
        Accept: DOCUMENT_TYPES.PDF,
        Authorization: `Bearer ${token}`
      }
    })
      .then((response) => response.arrayBuffer())
      .then((response) => {
        const arr = new Uint8Array(response);
        const blob = new Blob([arr], {
          type: DOCUMENT_TYPES.PDF
        });
        const blobUrl = window.URL.createObjectURL(blob);
        blobUrlToBase64(blobUrl).then((base64Data) => {
          if (base64Data) {
            const newPayload = {
              base64: base64Data,
              xtop: 200,
              ytop: 300,
              xbottom: 380,
              ybottom: 350,
              pageno: 0,
              reason: 'KSMART'
            };
            signPdf({
              pdfRequest: newPayload,
              draftRequest: {
                fileNo: params?.fileNo,
                draftNo: data?.draftNo,
                draftId: data.draftId || params?.draftid
              }
            });
          }
        });
      })
      .catch(() => {
        setAlertAction({ open: false });
      });
    if (draft) {
      setAlertAction({ open: false });
    }
  };

  // const handleCloseDs = () => {
  //   setActionTriggered({ id: 'digital-sign', loading: false }); setAlertAction({ open: false });
  // };

  const handleConfirm = async (data) => {
    fetchAllEnroll({
      from: 'draft-save',
      handleEsign: () => handleEsign(data)
    });
    setActionTriggered({ id: 'digital-sign', loading: true });
  };

  const handleCheckDateTime = async (data) => {
    // console.log('>>>2:', data);
    const response = await fetch(
      `${baseApiURL}/${API_URL.E_SIGN.DATE_CHECK}?datetime=${dayjs().format(
        'DD/MM/YYYY'
      )}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    const res = await response.json();
    if (res?.payload?.isSame) {
      handleConfirm({
        draftId: data?.draftid || params?.draftid,
        draftNo: data?.draftNo
      });
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t(
          'systemDateNoteUpdated'
        )} <br/><div style="font-size: 14px"> ${t('pleaseCheck')} </div>`,
        title: t('warning'),
        backwardActionText: t('close')
      });
    }
  };

  // useEffect(() => {
  //   if (activeBlob) {
  //     blobUrlToBase64(activeBlob)
  //       .then((base64Data) => {
  //         setFileBase64(base64Data);
  //       });
  //   }
  // }, [activeBlob]);

  // const draftButtons = () => {
  //   if (draftDataById?.esigned === false && draftDataById?.isEditable === false) {
  //     return false;
  //   }
  //   if (draftDataById?.esigned === false && draftDataById?.status === 'APPROVED') {
  //     return true;
  //   }
  //   if (draftDataById?.esigned === false && draftDataById?.status !== 'APPROVED') {
  //     return true;
  //   }
  //   if (draftDataById?.esigned === true && draftDataById?.status === 'APPROVED') {
  //     return false;
  //   }
  //   if (!params?.draftid) {
  //     return true;
  //   }
  //   return false;
  // };

  const handleNewPopup = (data) => {
    setDraftItems(data);
    setOpen(true);
  };

  // const buttonsBasedOnApprove = () => {
  //   return ((draftDataById?.esigned === false && draftDataById?.draftStage !== 3) || !draftDataById) ? (
  //     <Button
  //       variant="secondary"
  //       className="mx-2"
  //       type="submit"
  //       form="create-draft"
  //       onClick={triggerDraftComplete}
  //       isLoading={actionTriggered?.id === checkRoleByFileRole(fileDetails?.role) && actionTriggered?.loading}
  //     >
  //       {`${t('proceed')} & ${t('preview')}`}
  //     </Button>
  //   ) : (
  //     <Button
  //       variant="secondary"
  //       className="mx-2"
  //       type="submit"
  //       form="create-draft"
  //       onClick={() => triggerDraftComplete('approved')}
  //       isLoading={actionTriggered?.id === checkRoleByFileRole(fileDetails?.role) && actionTriggered?.loading}
  //     >
  //       Approve
  //     </Button>
  //   );
  // };

  return (
    <div className="overflow-hidden" style={{ height: 'calc(100vh - 188px)' }}>
      <div
        className={
          collapsePreview
            ? 'grid grid-cols-1 gap-3 h-full'
            : 'grid grid-cols-2 gap-3 h-full'
        }
      >
        <div className="rounded-lg overflow-y-auto overflow-x-hidden pr-1">
          <DraftCreateOrUpdate
            editDraft={editDraft}
            setEditDraft={setEditDraft}
            setFlag={setDraftPreviewFlag}
            flag={draftPreviewFlag}
            roles={roles}
            draftSaveStatus={draftSaveStatus}
            setDraftSaveStatus={setDraftSaveStatus}
            draftAction={draftAction}
            handleTogglePreview={handleTogglePreview}
            collapsePreview={collapsePreview}
            handleEsign={(data) => handleCheckDateTime(data)}
            handlePreview={() => handlePreview('click')}
            handleNewPopup={(data) => handleNewPopup(data)}
            searchParamsData={searchParams?.get('from') || 'draft-page'}
          />
        </div>
        {!collapsePreview && (
          <div className="h-full overflow-hidden pr-1">
            <div className="bg-white px-10 rounded-lg py-5 h-[90%] overflow-auto">
              <CustomTab
                data={tabs}
                handleChange={handleTabsChange}
                currentIndex={activeIndex}
              />
            </div>
            <div className="h-[10%] flex items-end justify-end">
              {params?.draftid
                ? (draftDataById?.esigned === false || !draftDataById) && (
                <Button
                  variant="secondary"
                  className="mx-2"
                  type="submit"
                  form="create-draft"
                  onClick={triggerDraftComplete}
                  isLoading={
                        actionTriggered?.id
                          === checkRoleByFileRole(fileDetails?.role)
                        && actionTriggered?.loading
                      }
                >
                  {`${t('proceed')} & ${t('preview')}`}
                </Button>
                )
                : (draftDataById?.esigned === false || !draftDataById) && (
                <Button
                  variant="secondary"
                  border="none"
                  type="submit"
                  form="create-draft"
                  onClick={triggerDraftComplete}
                  isLoading={
                        actionTriggered?.id
                          === checkRoleByFileRole(fileDetails?.role)
                        && actionTriggered?.loading
                      }
                >
                  {/* {checkRoleByFileRole(fileDetails?.role)} */}
                  {`${t('proceed')} & ${t('preview')}`}
                </Button>
                )}
            </div>
          </div>
        )}
      </div>

      <div className="flex pt-4">
        <div className="flex-grow text-right">
          {/* {draftDataById?.draftStage === 3 && draftDataById?.esigned === false && (
            <Button variant="primary_outline" className="mx-2" onClick={() => handleCheckDateTime()} isLoading={actionTriggered?.id === 'digital-sign' && actionTriggered?.loading}>
              {t('digitalSign')}
            </Button>
          )} */}

          <DraftNewPreview
            open={open}
            close={close}
            draftItems={draftItems}
            fileDetails={fileDetails}
            handleCheckDateTime={handleCheckDateTime}
          />

          <DraftDsConfirmPreview />

          {openNewExpand && full && (
            <DocumentNewExpandComponents
              noteCardDetails={noteCardDetails}
              full={full}
              openNewExpand={openNewExpand}
              setOpenNewExpand={setOpenNewExpand}
              setFull={setFull}
            />
          )}
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  draftPreview: getDraftPreview,
  fileDocuments: getFileDocuments,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  draftPreviewFlag: getDraftPreviewFlag,
  mergeLinkActive: getMergeLinkActive,
  mergeLinkActiveId: getMergeLinkActiveId,
  activeBlob: getActiveBlobDetails,
  draftDataById: getDraftDataById,
  dsLocalEnrollment: getDsLocalEnrollment,
  allEnrolls: getAllEnrolls,
  selectedCorrespondence: getSelectedCorrespondence,
  noteCardDetails: getNoteCardDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchFileDocuments: (data) => dispatch(actions.fetchFileDocuments(data)),
  saveDraftReturn: (data) => dispatch(actions.saveDraftReturn(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data)),
  setDraftPreviewFlag: (data) => dispatch(sliceActions.setDraftPreviewFlag(data)),
  setDraftDataById: (data) => dispatch(sliceActions.setDraftDataById(data)),
  signPdf: (data) => dispatch(commonActions.signPdf(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchAllEnroll: (data) => dispatch(dsActions.fetchAllEnroll(data)),
  fetchDsEnroll: (data) => dispatch(dsActions.fetchDsEnroll(data)),
  setIsDigitalSignAction: (data) => dispatch(sliceActions.setIsDigitalSignAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Draft);
