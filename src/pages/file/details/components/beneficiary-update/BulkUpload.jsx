import {
  Button, CustomT<PERSON>, <PERSON>u, <PERSON>u<PERSON>utton, <PERSON>u<PERSON><PERSON>, <PERSON>u<PERSON>ist,
  <PERSON>dal,
  Modal<PERSON>ody,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  <PERSON>dalHeader,
  ModalOverlay
} from '@ksmartikm/ui-components';
import { FormController, t } from 'common/components';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as XLSX from 'xlsx';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered } from 'pages/common/selectors';
import * as actions from 'pages/file/details/actions';
import DownArrow from 'assets/DownArrow';
import { dark, light } from 'utils/color';
import { DEFAULT_COUNTRY, DEFAULT_STATE } from 'common/constants';
import { getFileDetails } from '../../selector';

const Content = ({
  VITE_BASE_URL, control, onFileSelected, columns, keyValueResponse
}) => {
  return (
    <>
      <div className="flex gap-4 justify-end">
        <Menu>
          <MenuButton as={Button} rightIcon={<DownArrow />} colorScheme="pink" variant="outline">
            {t('template')} {t('downloads')}
          </MenuButton>
          <MenuList>
            <MenuItem as="a" href={`${VITE_BASE_URL}/individual.xlsx`} download="individual.xlsx">{t('individual')}</MenuItem>
            <MenuItem as="a" href={`${VITE_BASE_URL}/institution.xlsx`} download="institution.xlsx">{t('institution')}</MenuItem>
          </MenuList>
        </Menu>
      </div>
      <div className="col-span-6 mt-10">
        <FormController
          name="beneficiaryDocs"
          type="file"
          label={t('attachFileNotes')}
          placeholder={t('dropOrChooseFilesToUpload')}
          control={control}
          handleChange={(data) => onFileSelected(data)}
          accept="excel"
        />
      </div>

      <div className="mb-3 mt-5">
        <CustomTable tableData={keyValueResponse?.filter((item) => item?.beneficiaryName || item?.institutionName)} columns={columns} paginationEnabled paginationPosition="end" itemsPerPage={5} />
      </div>
    </>
  );
};

const BulkUpload = ({
  open, handleCloseBen, setActionTriggered, saveBeneficiary, setAlertAction, fileDetails, actionTriggered
}) => {
  const [jsonData, setJsonData] = useState(null);
  const params = useParams();
  const { VITE_BASE_URL } = import.meta.env;

  const {
    control
  } = useForm({
    mode: 'all',
    defaultValues: {
      beneficiaryDocs: ''
    }
  });

  const onFileSelected = (e) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const binaryString = event.target.result;
      const workbook = XLSX.read(binaryString, { type: 'binary' });
      const worksheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[worksheetName];
      let data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      data = data.filter((row) => row.some((cell) => cell !== null && cell !== ''));
      setJsonData(data);
    };
    reader.readAsBinaryString(e);
  };

  const headerArray = jsonData?.length > 0 && jsonData[0];
  const nonZero = jsonData?.filter((element, index) => element && index !== 0);
  const keyValueResponse = nonZero?.map((item) => {
    const obj = {};
    for (let i = 0; i < headerArray.length; i += 1) {
      obj[headerArray[i]] = item[i];
    }
    return obj;
  });

  const handleName = (val) => {
    if (val?.row?.institutionName) {
      return val?.row?.institutionName;
    }
    if (val?.row?.beneficiaryName) {
      return val?.row?.beneficiaryName;
    } return '';
  };

  const columns = [
    {
      header: t('name'),
      alignment: 'left',
      field: 'beneficiaryName',
      cell: (field) => handleName(field)
    },
    {
      header: t('phoneNo'),
      alignment: 'left',
      field: 'mobileNo'
    }
  ];

  const setBeneficiaryType = (data) => {
    const beneficiaryDetailsRequest = data.map((item) => ({
      ...item,
      beneficiaryType: item.beneficiaryName ? 'INDIVIDUAL' : 'INSTITUTION',
      countryId: DEFAULT_COUNTRY.id,
      stateId: DEFAULT_STATE.id
    }));
    return beneficiaryDetailsRequest;
  };

  const submitBulkUpload = () => {
    if (keyValueResponse) {
      if (keyValueResponse?.filter((item) => item?.beneficiaryName || item?.institutionName).length > 0) {
        const saveDataIndividual = {
          fileNo: params?.fileNo,
          beneficiaryDetailsRequest: setBeneficiaryType(keyValueResponse)
        };
        const fetchRequest = {
          fileNo: params?.fileNo, officeId: fileDetails?.officeId, page: 0, size: 10, sortDirection: 'desc'
        };
        setActionTriggered({ loading: true, id: 'save-beneficiary' });
        saveBeneficiary({
          data: saveDataIndividual, from: 'save', handleCloseBen, fetchRequest
        });
      } else {
        setAlertAction({
          open: true,
          variant: 'warning',
          message: `${t('noDataFound')} <br/><div style="font-size: 14px"> ${t('pleaseUseTemplatesForUpload')} </div>`,
          title: t('warning'),
          backwardActionText: t('close')
        });
      }
    }
  };

  return (
    <Modal isOpen={open} size="xl" onClose={() => handleCloseBen(false)}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader p={0} className="text-center">
          <h4 size="md" className="p-5 rounded-t-lg" style={{ background: light, color: dark }}>
            {t('beneficiaryBulkUpload')}
          </h4>
        </ModalHeader>
        <ModalBody style={{ maxHeight: 'calc(100vh - 150px)', overflowY: 'auto' }}>
          <Content VITE_BASE_URL={VITE_BASE_URL} columns={columns} onFileSelected={onFileSelected} control={control} keyValueResponse={keyValueResponse} />
        </ModalBody>
        <ModalFooter style={{ background: light, color: dark }}>
          <div className="w-full text-right space-x-4">

            <Button
              variant="secondary_outline"
              className="shadow-md"
              onClick={() => handleCloseBen(false)}
            >
              {t('cancel')}
            </Button>

            <Button
              variant="secondary"
              className="shadow-md"
              isLoading={actionTriggered?.id === 'save-beneficiary' && actionTriggered?.loading}
              onClick={submitBulkUpload}
            >
              {t('upload')}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  // <FormModal
  //   modalTitle={t('beneficiaryBulkUpload')}
  //   open={open}
  //   close={() => {
  //     handleCloseBen();
  //   }}
  //   modalSize="4xl"
  //   content={}
  //   formId="bulk-upload-form"
  //   type="button"
  //   closeButtonText={t('close')}
  //   closeOnOverlayClick={false}
  //   closeOnEsc={false}
  //   actionButtonText={t('upload')}
  //   handleSelect={submitBulkUpload}
  // />
  );
};

const mapStateToProps = createStructuredSelector({
  actionTriggered: getActionTriggered,
  fileDetails: getFileDetails
});

const mapDispatchToProps = (dispatch) => ({
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  saveBeneficiary: (data) => dispatch(actions.saveBeneficiary(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(BulkUpload);
