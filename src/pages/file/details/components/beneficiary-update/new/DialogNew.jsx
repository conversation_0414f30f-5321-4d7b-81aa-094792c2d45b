import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import _ from 'lodash';
import {
  FormWrapper, FormController, t, Button,
  ModalFooter,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody
} from 'common/components';
import WhatsappIcon from 'assets/Whatsapp';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as commonActions from 'pages/common/actions';
import {
  getCountry,
  getState,
  getDistricts,
  getPostOffice,
  getSidebarData,
  getUserInfo,
  getLocalBodyType,
  getUserLocalBody,
  getGenerateAadharOtp,
  getVerifyAadharOtp,
  getOtpStatus,
  getSmartDetails,
  getActionTriggered,
  getLocalBodyTypeByDBylbCodeApplicant,
  getWard,
  getBanks,
  getBranches,
  getEducation,
  getGender,
  getCategory,
  getFinancialStatus,
  getLocalBodyTypeDetails,
  getAccountType,
  getTreasuryType,
  getAccountId
} from 'pages/common/selectors';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DEFAULT_COUNTRY, DEFAULT_STATE } from 'common/constants';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import { numberNull } from 'utils/numberNull';
import { docLength, docNumber } from 'pages/counter/new/components/helper';
import {
  EMAIL_ONLY, ENG_ONLY, ML_ONLY, MOBILE_ONLY
} from 'common/regex';
import Registration from 'common/components/Registration';
import { actions as sliceActions } from 'pages/citizen/e-file/slice';
import * as actions from 'pages/file/details/actions';
import { getPostOfficePreview } from 'pages/citizen/e-file/components/Preview/helper';
import { nameValidation } from 'utils/validateFile';
import { beneficiarySchema } from 'pages/file/details/validate';
import { beneficiaryDefaultValues, beneFiciarySaveFormat } from 'pages/file/details/helper';
import { getBeneficiaryById, getFileDetails } from 'pages/file/details/selector';
import { dark, light } from 'utils/color';
import { applicantName } from '../../helper';

const NewBeneficiary = (props) => {
  const {
    fetchCountry,
    countryDropdown,
    fetchStates,
    stateDropdown,
    fetchDistricts,
    districtDropdown,
    postOfficeDropdown,
    fetchPostOffice,
    userLocalBody,
    setActionTriggered,
    // fetchSmartProfile,
    setAlertAction,
    actionTriggered,
    userInfo,
    // setRegisterOpen,
    fetchPostOfficeByPin,
    banksOptions,
    fetchBanks,
    branchesOptions,
    fetchBranchByBank,
    fetchEducation,
    fetchCategory,
    category,
    fetchFinancialStatus,
    financialStatus,
    accountType,
    treasuryType,
    fetchAccountType,
    fetchTreasuryType,
    saveBeneficiary,
    smartDetails,
    fetchFileDetails,
    setFileHeader,
    setFormTitle,
    fileDetails,
    fetchBeneficiaryById,
    beneficiaryById,
    fetchLocalBodyByOfficeCode,
    fetchAccountId,
    accountIdDetails,
    // direct props
    benOpen,
    setBenOpen,
    beneficiaryId,
    setBeneficiaryId
  } = props;

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    getValues,
    formState: { errors },
    reset
  } = useForm({
    mode: 'all',
    defaultValues: beneficiaryDefaultValues,
    resolver: yupResolver(beneficiarySchema)
  });

  const params = useParams();
  const countrySelected = watch('countryId');
  const documentType = watch('documentType');
  const isWhatsappSame = watch('isWhatsappSame');
  const mobileNo = watch('mobileNo');
  const whatsappNo = watch('whatsappNo');

  const [ifscList, setIfscList] = useState([]);

  const userType = [
    {
      id: 'INDIVIDUAL',
      name: t('individual')
    },
    {
      id: 'INSTITUTION',
      name: t('institution')
    }
  ];

  useEffect(() => {
    if (params.fileNo) {
      fetchFileDetails(params.fileNo);
    }
  }, [params]);

  useEffect(() => {
    if (beneficiaryId) {
      fetchBeneficiaryById(beneficiaryId);
    }
  }, [beneficiaryId]);

  useEffect(() => {
    setFormTitle({ title: t('newBeneficiary'), variant: 'normal' });
    setFileHeader([
      {
        label: t('fileNumber'),
        value: params.fileNo
      },
      {
        label: t('role'),
        value: fileDetails?.role
      },
      {
        label: t('service'),
        value: fileDetails?.serviceName
      },
      {
        label: t('applicantName'),
        value: fileDetails?.inwardDetails ? applicantName(fileDetails?.inwardDetails) : ''
      }
    ]);
  }, [JSON.stringify(fileDetails)]);

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  useEffect(() => {
    if (userInfo?.id) {
      fetchLocalBodyByOfficeCode(userInfo?.id);
    }
  }, [userInfo?.id]);

  const setDocument = (data) => {
    if (data.documentType) {
      return data.documentNo;
    }
    if (data.aadharNo) {
      return data.aadharNo;
    }
    if (data.udid) {
      return data.udid;
    }
    if (data.passport) {
      return data.passport;
    }
    return null;
  };

  useEffect(() => {
    if (userLocalBody) {
      setValue('countryId', DEFAULT_COUNTRY.id);
      setValue('stateId', userLocalBody?.stateCode);
      setValue('districtId', userLocalBody?.districtId);
      if (userLocalBody?.stateCode) {
        fetchDistricts(userLocalBody?.stateCode);
      }
      if (userLocalBody?.districtId) {
        fetchPostOffice({ districtId: userLocalBody?.districtId });
      }
    }
  }, [userLocalBody, benOpen]);

  // const handleRegister = () => {
  //   setAlertAction(false);
  //   setRegisterOpen(true);
  // };

  // const fetchKsmartId = () => {
  //   setActionTriggered({ loading: true, id: 'fetchKsmartId' });
  //   const sendData = {
  //     phoneNumber: getValues('mobileNo'),
  //     project: 'KSMART',
  //     module: 'PENSION',
  //     userType: USER_TYPE.CITIZEN
  //   };
  //   fetchSmartProfile({ sendData, handleRegister });
  // };

  const handleFieldChange = (field, data) => {
    const malayalamOnly = data?.target?.value?.replace(ML_ONLY, '');
    const nameValidate = nameValidation(data?.target?.value);
    const englishOnly = data?.target?.value?.replace(ENG_ONLY, '');
    const emailOnly = data?.target?.value?.replace(EMAIL_ONLY, '');
    const mobileValidation = data?.target?.value?.replace(MOBILE_ONLY, '');

    if (data) {
      switch (field) {
        case 'beneficiaryType':
          setValue('beneficiaryType', data);
          break;
        case 'countryId':
          setValue('countryCode', data.countryCode);
          if (data.id !== DEFAULT_COUNTRY.id) {
            setValue('documentType', 3);
          } else {
            setValue('documentType', 1);
          }
          setValue('documentNo', null);
          setValue('stateId', null);
          setValue('districtId', null);
          setValue('postoffice', null);
          setValue('wardName', null);
          setValue('officeId', null);
          setValue('postofficename', null);
          setValue('pincode', null);
          break;
        case 'stateId':
          setActionTriggered({ loading: true, id: 'counter-applicant-district' });
          fetchDistricts(data.id);
          setValue('districtId', null);
          setValue('postoffice', null);
          setValue('wardName', null);
          setValue('officeId', null);
          setValue('postofficename', null);
          setValue('pincode', null);
          break;
        case 'districtId':
          setActionTriggered({ loading: true, id: 'counter-applicant-postoffice' });
          fetchPostOffice({ districtId: data.id });
          setValue('postoffice', null);
          setValue('wardName', null);
          setValue('officeId', null);
          setValue('pincode', null);
          setValue('postofficename', null);
          break;
        case 'documentType':
          setValue('documentType', Number(data));
          break;
        case 'mobileNo':
          if (mobileValidation) {
            setValue('mobileNo', mobileValidation);
            // if (mobileValidation?.toString().length === 10) {
            //   fetchKsmartId();
            // }
            if (isWhatsappSame) {
              setValue('whatsapp', mobileValidation);
            } else {
              setValue('whatsapp', '');
            }
          } else {
            setValue('mobileNo', null);
          }
          break;
        case 'whatsappCheck':
          setValue('isWhatsappSame', data);
          setValue('whatsappNo', data ? mobileNo : '');
          break;
        case 'postoffice':
          setValue('postoffice', data.id);
          setValue('pincode', data.pinCode);
          setValue('postofficename', data.name);
          break;
        case 'pincode':
          if (data.length >= 6) {
            fetchPostOfficeByPin(data);
          }
          break;
        case 'isWhatsappSame':
          setValue('isWhatsappSame', data);
          setValue('whatsappNo', data ? mobileValidation : '');
          break;
        case 'whatsappNo':
          setValue('whatsappNo', mobileValidation);
          if (mobileValidation !== whatsappNo) {
            setValue('isWhatsappSame', false);
          }
          break;
        case 'localBeneficiaryName':
          setValue('localBeneficiaryName', malayalamOnly);
          break;
        case 'localHouseName':
          setValue('localHouseName', malayalamOnly);
          break;
        case 'localStreet':
          setValue('localStreet', malayalamOnly);
          break;
        case 'localLocalPlace':
          setValue('localLocalPlace', malayalamOnly);
          break;
        case 'localMainPlace':
          setValue('localMainPlace', malayalamOnly);
          break;
        case 'localInstitutionName':
          setValue('localInstitutionName', malayalamOnly);
          break;
        case 'localOfficeName':
          setValue('localOfficeName', malayalamOnly);
          break;
        case 'localDesignation':
          setValue('localDesignation', malayalamOnly);
          break;
        case 'beneficiaryName':
          setValue('beneficiaryName', nameValidate);
          break;
        case 'houseName':
          setValue('houseName', englishOnly);
          break;
        case 'street':
          setValue('street', englishOnly);
          break;
        case 'localPlace':
          setValue('localPlace', englishOnly);
          break;
        case 'mainPlace':
          setValue('mainPlace', englishOnly);
          break;
        case 'emailId':
          setValue('emailId', emailOnly);
          break;
        case 'categoryId':
          setValue('categoryId', Number(data.id));
          break;
        case 'financialStatusId':
          setValue('financialStatusId', Number(data.id));
          break;
        case 'ownership':
          setValue('ownership', Number(data));
          break;
        case 'accountTypeId':
          setValue('accountTypeId', Number(data?.id));
          if (data?.id === 1) {
            fetchBanks();
            fetchAccountId({ lookupType: 'account_type' });
          } else if (data?.id === 2) {
            fetchTreasuryType();
            fetchAccountId({ lookupType: 'account_type' });
          }
          break;
        case 'bankNameId':
          fetchBranchByBank({ bankId: Number(data.id) });
          setValue('bankNameId', data.id);
          break;
        case 'bankBranchId':
          setValue('bankBranchId', data.id);
          setValue('ifsc', data.ifscCode);
          break;
        case 'ifsc':
          setValue('ifsc', data.ifscCode);
          setValue('bankBranchId', data.id);
          break;

        case 'accountId':
          setValue('accountId', data.id);
          break;
        case 'gstNo':
          setValue('gstNo', data?.target?.value);
          break;
        case 'panNo':
          setValue('panNo', data?.target?.value);
          break;
        default:
          break;
      }
    } else {
      switch (field) {
        case 'countryId':
          setValue('countryCode', null);
          setValue('documentType', 1);
          setValue('documentNo', null);
          setValue('pincode', null);
          setValue('stateId', null);
          break;
        case 'stateId':
          setValue('districtId', null);
          setValue('postoffice', null);
          setValue('wardName', null);
          setValue('officeId', null);
          setValue('pincode', null);
          setValue('postofficename', null);
          break;
        case 'districtId':
          setValue('postoffice', null);
          setValue('wardName', null);
          setValue('officeId', null);
          setValue('pincode', null);
          setValue('postofficename', null);
          break;
        case 'postoffice':
          setValue('postoffice', null);
          setValue('pincode', null);
          setValue('postofficename', null);
          break;
        default:
          break;
      }
    }
  };

  useEffect(() => {
    if (smartDetails?.length > 0) {
      const smartData = smartDetails[0];
      if (watch('mobileNo') === smartData?.phoneNumber || !watch('mobileNo')) {
        setValue('beneficiaryName', smartData.name);
        setValue('emailId', smartData.email);
        setValue('mobileNo', smartData.phoneNumber);
        setValue('whatsappNo', smartData.whatsappNumber);
        setValue('documentNo', smartData.aadhaarNo);
        if (smartData.aadhaarId) {
          if (Object.keys(smartData.aadhaarId).length > 0) {
            setValue('pincode', smartData.aadhaarId.pincode);
            if (smartData.aadhaarId.pincode) {
              fetchPostOfficeByPin(smartData.aadhaarId.pincode);
            }
            setValue('mainPlace', smartData.aadhaarId.vtc);
            setValue('houseName', smartData.aadhaarId.houseEng);
          }
        }
      }
    }
  }, [JSON.stringify(smartDetails)]);

  useEffect(() => {
    if (Object.keys(userInfo).length > 0) {
      setActionTriggered({ loading: false, id: 'counter-applicant-ward' });
    }
  }, [JSON.stringify(userInfo)]);

  const handleCloseBen = () => {
    setBenOpen(false);
    setActionTriggered({ loading: false, id: 'counter-applicant-ward' });
    setAlertAction({ open: false });
  };

  const onSubmitForm = (data) => {
    const saveDataIndividual = {
      fileNo: params?.fileNo,
      beneficiaryId: beneficiaryId || null,
      beneficiaryDetailsRequest: [beneFiciarySaveFormat(data)]
    };

    const fetchRequest = {
      fileNo: params?.fileNo, officeId: fileDetails?.officeId, page: 0, size: 10, sortDirection: 'desc'
    };

    setActionTriggered({ loading: true, id: 'save-beneficiary' });
    saveBeneficiary({
      data: saveDataIndividual, from: beneficiaryId ? 'update' : 'save', handleCloseBen, fetchRequest
    });
    // setBenOpen(false);
  };

  useEffect(() => {
    fetchCountry();
    setActionTriggered({ loading: false, id: 'counter-applicant-country' });
    fetchStates();
    fetchDistricts(DEFAULT_STATE.id);
    fetchEducation();
    fetchCategory();
    fetchFinancialStatus();
    fetchAccountType();
  }, []);

  useEffect(() => {
    if (isWhatsappSame) {
      setValue('whatsappNo', mobileNo);
    }
  }, [isWhatsappSame, mobileNo]);

  useEffect(() => {
    if (mobileNo !== whatsappNo) {
      setValue('isWhatsappSame', false);
    }
  }, [whatsappNo]);

  useEffect(() => {
    if (beneficiaryId && beneficiaryById) {
      if (Object.keys(beneficiaryById).length > 0) {
        const data = beneficiaryById;
        setValue('localbodyType', numberNull(Number(data.addressType)));
        setValue('countryId', numberNull(Number(data.countryId)));
        if (Number(data.countryId) !== DEFAULT_COUNTRY.id) {
          setValue('documentType', 3);
        } else {
          setValue('documentType', 1);
        }
        if (numberNull(Number(data.stateId))) {
          fetchDistricts(data.stateId);
        }
        if (numberNull(Number(data.districtId))) {
          fetchPostOffice({ districtId: data.districtId });
        }
        setValue('stateId', numberNull(Number(data.stateId)));
        setValue('districtId', numberNull(Number(data.districtId)));

        if (data.beneficiaryType === 'INDIVIDUAL' || Number(data.beneficiaryType) === 1) {
          setValue('beneficiaryType', 'INDIVIDUAL');
          setValue('beneficiaryName', data.beneficiaryName);
          setValue('localBeneficiaryName', data.localBeneficiaryName);
          setValue('documentNo', setDocument(data));
          setValue('institutionName', null);
          setValue('institutionDate', null);
          setValue('officerName', null);
          setValue('designation', null);
        } else {
          setValue('beneficiaryType', 'INSTITUTION');
          setValue('institutionName', data.institutionName);
          setValue('documentNo', null);
          setValue('beneficiaryName', null);
          setValue('localBeneficiaryName', null);
          setValue('institutionDate', data.institutionDate);
          setValue('officerName', data.officerName);
          setValue('designation', data.designation);

          setValue('panNo', data.panNo);
          setValue('gstNo', data.gstNo);
        }

        setValue('houseName', data.houseName);
        setValue('localHouseName', data.localHouseName);
        setValue('postoffice', Number(data.postoffice));
        setValue('pincode', data.pincode);
        setValue('street', data.street);
        setValue('localPlace', data.localPlace);
        setValue('mainPlace', Number(data.countryId) !== DEFAULT_COUNTRY.id ? data.city : data.mainPlace);
        setValue('emailId', data.emailId);
        setValue('mobileNo', data.mobileNo);
        setValue('isWhatsappSame', data.isWhatsappSame);
        setValue('whatsappNo', data.whatsappNo);
        setValue('landLine', data.landLine);
        setValue('referenceNo', data.referenceNo);
        setValue('postofficename', getPostOfficePreview(Number(data.postoffice), data.postOfficeList));
        setValue('financialStatusId', data.financialStatusId);
        setValue('categoryId', data.categoryId);
        setValue('localStreet', data.localStreet);
        setValue('localLocalPlace', data.localLocalPlace);
        setValue('localMainPlace', data.localMainPlace);
        setValue('accountNo', data.accountNo);
        setValue('bankNameId', data.bankNameId);
        setValue('accountTypeId', data.accountTypeId);
        setValue('treasuryTypeId', data.treasuryTypeId);
        if (data.treasuryTypeId) {
          fetchTreasuryType();
        }
        setValue('treasuryAccountNo', data.treasuryAccountNo);
        setValue('headOfAccount', data.headOfAccount);
        if (Number(data.bankNameId)) {
          fetchBanks();
          fetchBranchByBank({ bankId: Number(data.bankNameId) });
        }
        if (Number(data.treasuryType)) {
          fetchTreasuryType();
        }
        setValue('bankBranchId', data.bankBranchId);
        setValue('ifsc', data.ifsc);
        setValue('localBodyTypeId', data.localBodyTypeId);
        setValue('accountId', data?.accountId);
        if (data?.accountId) {
          fetchAccountId({ lookupType: 'account_type' });
        }
      }
    }
  }, [JSON.stringify(beneficiaryById)]);

  useEffect(() => {
    if (branchesOptions && branchesOptions?.data?.length > 0) {
      setIfscList(
        branchesOptions?.data?.map((bo) => ({
          ...bo,
          name: `${bo.ifscCode ? bo.ifscCode : ''} - ${bo?.name ? bo?.name : ''}`
        }))
      );
    }
  }, [branchesOptions]);

  return (
    <>
      <Button onClick={() => { setBenOpen(true); reset(beneficiaryDefaultValues); setBeneficiaryId(null); }} variant="secondary_outline">
        {t('addBeneficiary')}
      </Button>
      <Modal isOpen={benOpen} size="full" onClose={() => setBenOpen(false)}>
        <form onSubmit={handleSubmit(onSubmitForm)} id="applicant-form">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader p={0} className="text-center">
              <h4 size="md" className="p-5 rounded-t-lg" style={{ background: light, color: dark }}>
                {t('beneficiary')}
              </h4>
            </ModalHeader>
            <ModalBody style={{ maxHeight: 'calc(100vh - 150px)', overflowY: 'auto' }}>

              <div id="individual_details" />
              <FormWrapper>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="beneficiaryType"
                    type="radio"
                    control={control}
                    errors={errors}
                    options={userType}
                    optionKey="id"
                    handleChange={(data) => handleFieldChange('beneficiaryType', data)}
                  />
                </div>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="countryId"
                    type="select"
                    label={t('country')}
                    control={control}
                    errors={errors}
                    options={actionTriggered?.id === 'counter-applicant-country' && actionTriggered?.loading ? [] : _.get(countryDropdown, 'data', [])}
                    handleChange={(data) => handleFieldChange('countryId', data)}
                    isLoading={actionTriggered?.id === 'counter-applicant-country' && actionTriggered?.loading}
                    optionKey="id"
                    required
                    isClearable

                  />
                </div>
                {Number(countrySelected) === DEFAULT_COUNTRY.id && (
                  <>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="stateId"
                        type="select"
                        label={t('state')}
                        control={control}
                        errors={errors}
                        optionKey="id"
                        options={actionTriggered?.id === 'counter-applicant-state' && actionTriggered?.loading ? [] : _.get(stateDropdown, 'data', [])}
                        handleChange={(data) => handleFieldChange('stateId', data)}
                        isLoading={actionTriggered?.id === 'counter-applicant-state' && actionTriggered?.loading}
                        required
                        isClearable
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="districtId"
                        type="select"
                        label={t('district')}
                        control={control}
                        errors={errors}
                        optionKey="id"
                        options={actionTriggered?.id === 'counter-applicant-district' && actionTriggered?.loading ? [] : _.get(districtDropdown, 'data', [])}
                        handleChange={(data) => handleFieldChange('districtId', data)}
                        isLoading={actionTriggered?.id === 'counter-applicant-district' && actionTriggered?.loading}
                        required
                        isClearable
                      />
                    </div>
                  </>
                )}

                {Number(countrySelected) === DEFAULT_COUNTRY.id ? (
                  <div className="lg:col-span-4 md:col-span-6 col-span-12">
                    <FormController
                      name="mobileNo"
                      type="text"
                      label={t('concatLabel', { label: t('mobile'), type: t('number') })}
                      control={control}
                      errors={errors}
                      handleChange={(data) => handleFieldChange('mobileNo', data)}
                      maxLength={10}
                      rightContent={(
                        <>
                          <div className="w-[2px] mx-2 bg-gray-100 h-[54px]" />
                          <FormController
                            type="check"
                            control={control}
                            errors={errors}
                            name="isWhatsappSame"
                            label={<WhatsappIcon />}
                          />
                        </>
                      )}
                      required={watch('beneficiaryType') === 'INDIVIDUAL'}
                    />
                  </div>
                ) : (
                  <div className="lg:col-span-4 md:col-span-6 col-span-12 flex">
                    <div className="border rounded-l-lg mr-[-5px] p-[15px]">
                      +{getValues('countryCode')}
                    </div>
                    <FormController
                      name="internationalMobileNo"
                      type="text"
                      label={t('concatLabel', { label: t('mobile'), type: t('number') })}
                      control={control}
                      errors={errors}
                      handleChange={(data) => handleFieldChange('internationalMobileNo', data)}
                      required
                    />
                  </div>
                )}

                {Number(countrySelected) === DEFAULT_COUNTRY.id && (
                  <div className="lg:col-span-4 md:col-span-6 col-span-12">
                    <FormController
                      name="whatsappNo"
                      type="text"
                      label={t('concatLabel', { label: t('whatsapp'), type: t('number') })}
                      control={control}
                      errors={errors}
                      handleChange={(data) => handleFieldChange('whatsappNo', data)}
                      maxLength={10}
                    />
                  </div>
                )}

                {watch('beneficiaryType') === 'INDIVIDUAL'
                  && (
                    <>
                      <div className="lg:col-span-4 md:col-span-6 col-span-12">
                        <FormController
                          name="documentNo"
                          type="text"
                          label={docNumber(documentType)}
                          control={control}
                          errors={errors}
                          handleChange={(data) => handleFieldChange('documentNo', data)}
                          required={Number(countrySelected) !== DEFAULT_COUNTRY.id}
                          maxlength={docLength(documentType)}
                        />
                      </div>

                      <>
                        <div className="lg:col-span-4 md:col-span-6 col-span-12">
                          <FormController
                            name="beneficiaryName"
                            type="text"
                            label={t('concatLabel', { label: t('beneficiary'), type: t('name') })}
                            control={control}
                            errors={errors}
                            handleChange={(data) => handleFieldChange('beneficiaryName', data)}
                            required
                          />
                        </div>
                        <div className="lg:col-span-4 md:col-span-6 col-span-12">
                          <FormController
                            name="localBeneficiaryName"
                            type="text"
                            label={t('concatLabel', { label: t('beneficiary'), type: t('local') })}
                            control={control}
                            errors={errors}
                            handleChange={(data) => handleFieldChange('localBeneficiaryName', data)}
                          />
                        </div>

                        {Number(countrySelected) === DEFAULT_COUNTRY.id && (
                          <>
                            <div className="lg:col-span-4 md:col-span-6 col-span-12">
                              <FormController
                                name="houseName"
                                type="text"
                                label={t('concatLabel', { label: t('house'), type: t('name') })}
                                control={control}
                                errors={errors}
                                handleChange={(data) => handleFieldChange('houseName', data)}
                              />
                            </div>
                            <div className="lg:col-span-4 md:col-span-6 col-span-12">
                              <FormController
                                name="localHouseName"
                                type="text"
                                label={t('concatLabel', { label: t('houseName'), type: t('local') })}
                                control={control}
                                errors={errors}
                                handleChange={(data) => handleFieldChange('localHouseName', data)}
                              />
                            </div>
                          </>
                        )}
                      </>
                    </>
                  )}

                {watch('beneficiaryType') === 'INSTITUTION' && (
                  <>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="institutionName"
                        type="text"
                        label={t('concatLabel', { label: t('institution'), type: t('name') })}
                        control={control}
                        errors={errors}
                        // handleChange={(data) => handleFieldChange('institutionName', data)}
                        required
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="officerName"
                        type="text"
                        label={t('concatLabel', { label: t('officer'), type: t('name') })}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('officerName', data)}
                      // required
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="designation"
                        type="text"
                        label={t('designation')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('designation', data)}
                        required
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="localInstitutionName"
                        type="text"
                        label={t('concatLabel', { label: t('institution'), type: t('local') })}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('localInstitutionName', data)}
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="localOfficeName"
                        type="text"
                        label={t('concatLabel', { label: t('officer'), type: t('local') })}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('localOfficeName', data)}
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="localDesignation"
                        type="text"
                        label={t('concatLabel', { label: t('designation'), type: t('local') })}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('localDesignation', data)}
                      />
                    </div>

                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="landLine"
                        type="text"
                        label={t('concatLabel', { label: t('landLine'), type: t('number') })}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('landLine', data)}
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="panNo"
                        type="text"
                        label={t('panNo')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('panNo', data)}
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="gstNo"
                        type="text"
                        label={t('gstNo')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('gstNo', data)}
                      />
                    </div>

                  </>
                )}

                {Number(countrySelected) === DEFAULT_COUNTRY.id && (
                  <>
                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="postoffice"
                        type="select"
                        label={t('postOffice')}
                        control={control}
                        errors={errors}
                        optionKey="id"
                        options={actionTriggered?.id === 'counter-applicant-postoffice' && actionTriggered?.loading ? [] : _.get(postOfficeDropdown, 'data', [])}
                        handleChange={(data) => handleFieldChange('postoffice', data)}
                        isLoading={actionTriggered?.id === 'counter-applicant-postoffice' && actionTriggered?.loading}
                        isClearable
                      />
                    </div>

                    <div className="lg:col-span-4 md:col-span-6 col-span-12">
                      <FormController
                        name="pincode"
                        type="text"
                        label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pinCode' : 'postZipCode')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('pincode', data)}
                      />
                    </div>
                  </>
                )}

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="street"
                    type="text"
                    label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'street' : 'streetNoName')}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('street', data)}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="localStreet"
                    type="text"
                    label={
                      Number(countrySelected) === DEFAULT_COUNTRY.id
                        ? t('concatLabel', { label: t('street'), type: t('local') })
                        : t('concatLabel', { label: t('streetNoName'), type: t('local') })
                    }
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('localStreet', data)}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="localPlace"
                    type="text"
                    label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'localPlace' : 'locality')}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('localPlace', data)}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="localLocalPlace"
                    type="text"
                    label={
                      Number(countrySelected) === DEFAULT_COUNTRY.id
                        ? t('concatLabel', { label: t('localPlace'), type: t('local') })
                        : t('concatLabel', { label: t('locality'), type: t('local') })
                    }
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('localLocalPlace', data)}
                  />
                </div>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="mainPlace"
                    type="text"
                    label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'mainPlace' : 'cityTown')}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('mainPlace', data)}
                  />
                </div>

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="localMainPlace"
                    type="text"
                    label={
                      Number(countrySelected) === DEFAULT_COUNTRY.id
                        ? t('concatLabel', { label: t('mainPlace'), type: t('local') })
                        : t('concatLabel', { label: t('cityTown'), type: t('local') })
                    }
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('localMainPlace', data)}
                  />
                </div>
                {Number(countrySelected) !== DEFAULT_COUNTRY.id && (
                  <div className="lg:col-span-4 md:col-span-6 col-span-12">
                    <FormController
                      name="pincode"
                      type="text"
                      label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pincode' : 'postZipCode')}
                      control={control}
                      errors={errors}
                      disabled={Number(countrySelected) === DEFAULT_COUNTRY.id}
                      handleChange={(data) => handleFieldChange('pincode', data)}
                      required={Number(countrySelected) === DEFAULT_COUNTRY.id}
                    />
                  </div>
                )}

                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="emailId"
                    type="text"
                    label={t('emailId')}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('emailId', data)}
                  />
                </div>

                {watch('beneficiaryType') === 'INDIVIDUAL'
                  && (
                    <>

                      <div className="lg:col-span-4 md:col-span-12 col-span-12">
                        <FormController
                          name="categoryId"
                          variant="outlined"
                          type="select"
                          label={t('category')}
                          optionKey="id"
                          control={control}
                          errors={errors}
                          options={_.get(category, 'data', [])}
                          isClearable
                          handleChange={(data) => handleFieldChange('categoryId', data)}
                          isLoading={actionTriggered?.id === 'counter-applicant-category' && actionTriggered?.loading}
                        />
                      </div>
                      <div className="lg:col-span-4 md:col-span-12 col-span-12">

                        <FormController
                          name="financialStatusId"
                          variant="outlined"
                          type="select"
                          label={t('financialStatus')}
                          optionKey="id"
                          control={control}
                          errors={errors}
                          isClearable
                          options={_.get(financialStatus, 'data', [])}
                          handleChange={(data) => handleFieldChange('financialStatusId', data)}
                          isLoading={actionTriggered?.id === 'counter-applicant-financialstatus' && actionTriggered?.loading}
                        />
                      </div>

                    </>
                  )}

                <div className="lg:col-span-4 md:col-span-12 col-span-12">
                  <FormController
                    name="accountTypeId"
                    variant="outlined"
                    type="select"
                    label={t('accountType')}
                    optionKey="id"
                    control={control}
                    errors={errors}
                    isClearable
                    options={accountType?.filter((item) => item.id !== 3) || []}
                    handleChange={(data) => handleFieldChange('accountTypeId', data)}
                    isLoading={actionTriggered?.id === 'accountType' && actionTriggered?.loading}
                  />
                </div>

                {
                  (watch('accountTypeId') === 1 || watch('accountTypeId') === 2)
                  && (
                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="accountId"
                        variant="outlined"
                        type="select"
                        label={t('accountId')}
                        optionKey="id"
                        control={control}
                        errors={errors}
                        isClearable
                        options={watch('accountTypeId') === 1 ? accountIdDetails?.filter((item) => item?.id === 1 || item?.id === 2) : accountIdDetails?.filter((item) => item?.id !== 1 && item?.id !== 2)}
                        handleChange={(data) => handleFieldChange('accountId', data)}
                        isLoading={actionTriggered?.id === 'accountId' && actionTriggered?.loading}
                      />
                    </div>
                  )
                }

                {watch('accountTypeId') === 2 && (
                  <>
                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="treasuryTypeId"
                        variant="outlined"
                        type="select"
                        label={t('treasuryType')}
                        optionKey="id"
                        control={control}
                        errors={errors}
                        isClearable
                        options={treasuryType || []}
                        handleChange={(data) => handleFieldChange('treasuryType', data)}
                        isLoading={actionTriggered?.id === 'treasuryType' && actionTriggered?.loading}
                      />
                    </div>
                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="treasuryAccountNo"
                        variant="outlined"
                        type="text"
                        label={t('treasuryAccountNo')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('treasuryAccountNo', data)}
                        maxLength={18}
                      />
                    </div>
                  </>
                )}
                {watch('accountTypeId') === 4 && (
                  <div className="lg:col-span-4 md:col-span-12 col-span-12">
                    <FormController
                      name="headOfAccount"
                      variant="outlined"
                      type="text"
                      label={t('headOfAccount')}
                      control={control}
                      errors={errors}
                      handleChange={(data) => handleFieldChange('headOfAccount', data)}
                      maxLength={18}
                    />
                  </div>
                )}

                {watch('accountTypeId') === 1 && (
                  <>
                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="bankNameId"
                        variant="outlined"
                        type="select"
                        label={t('bank')}
                        optionKey="id"
                        control={control}
                        errors={errors}
                        isClearable
                        options={_.get(banksOptions, 'data', [])}
                        handleChange={(data) => handleFieldChange('bankNameId', data)}
                        isLoading={actionTriggered?.id === 'counter-applicant-bank' && actionTriggered?.loading}
                      />
                    </div>

                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="ifsc"
                        variant="outlined"
                        // type="text"
                        type="select"
                        label={t('ifsc')}
                        control={control}
                        errors={errors}
                        isClearable
                        optionKey="ifscCode"
                        options={ifscList}
                        // readOnly
                        handleChange={(data) => handleFieldChange('ifsc', data)}
                        isLoading={actionTriggered?.id === 'counter-applicant-ifcs' && actionTriggered?.loading}
                      />
                    </div>

                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="bankBranchId"
                        variant="outlined"
                        type="select"
                        label={t('branch')}
                        optionKey="id"
                        control={control}
                        errors={errors}
                        isClearable
                        options={_.get(branchesOptions, 'data', [])}
                        handleChange={(data) => handleFieldChange('bankBranchId', data)}
                        isLoading={actionTriggered?.id === 'counter-applicant-branch' && actionTriggered?.loading}
                      />
                    </div>

                    <div className="lg:col-span-4 md:col-span-12 col-span-12">
                      <FormController
                        name="accountNo"
                        variant="outlined"
                        type="text"
                        label={t('accountNo')}
                        control={control}
                        errors={errors}
                        handleChange={(data) => handleFieldChange('accountNo', data)}
                        // minLength={9}
                        maxLength={18}
                      />
                    </div>
                  </>
                )}

              </FormWrapper>

            </ModalBody>
            <ModalFooter style={{ background: light, color: dark }}>
              <div className="w-full text-right space-x-4">

                <Button
                  variant="secondary_outline"
                  className="shadow-md"
                  onClick={() => setBenOpen(false)}
                >
                  {t('cancel')}
                </Button>

                <Button
                  variant="secondary"
                  className="shadow-md"
                  type="submit"
                  isLoading={actionTriggered?.id === 'save-beneficiary' && actionTriggered?.loading}
                >
                  {beneficiaryId ? t('updateBeneficiary') : t('saveBeneficiary')}
                </Button>
              </div>
            </ModalFooter>
          </ModalContent>
        </form>
      </Modal>
      <Registration />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  countryDropdown: getCountry,
  stateDropdown: getState,
  districtDropdown: getDistricts,
  wardDropdown: getWard,
  postOfficeDropdown: getPostOffice,
  formComponentData: getSidebarData,
  localBodyType: getLocalBodyType,
  localBodyTypeByDBylbCode: getLocalBodyTypeByDBylbCodeApplicant,
  userLocalBody: getUserLocalBody,
  generateAadharOtpRes: getGenerateAadharOtp,
  validateAadharOtpRes: getVerifyAadharOtp,
  otpStatus: getOtpStatus,
  smartDetails: getSmartDetails,
  actionTriggered: getActionTriggered,
  userInfo: getUserInfo,
  banksOptions: getBanks,
  branchesOptions: getBranches,
  educationOptions: getEducation,
  genderOptions: getGender,
  category: getCategory,
  financialStatus: getFinancialStatus,
  localBodyTypeDetails: getLocalBodyTypeDetails,
  accountType: getAccountType,
  treasuryType: getTreasuryType,
  beneficiaryById: getBeneficiaryById,
  accountIdDetails: getAccountId
});

const mapDispatchToProps = (dispatch) => ({
  fetchCountry: () => dispatch(commonActions.fetchCountry()),
  fetchStates: () => dispatch(commonActions.fetchState()),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchWard: (data) => dispatch(commonActions.fetchWardsByLocalBodyId(data)),
  fetchPostOffice: (data) => dispatch(commonActions.fetchPostOffice(data)),
  setActiveAccordian: (data) => dispatch(sliceActions.setActiveAccordian(data)),
  setApplicationSidebarStatus: (data) => dispatch(commonSliceActions.setSidebarStatus(data)),
  setJointApplicant: (data) => dispatch(sliceActions.setJointApplicant(data)),
  deleteApplicant: (data) => dispatch(actions.deleteApplicant(data)),
  fetchLocalBodyByDistrictByTypeApplicant: (data) => dispatch(commonActions.fetchLocalBodyByDistrictByTypeApplicant(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  verifyAadharOtp: (data) => dispatch(commonActions.verifyAadharOtp(data)),
  fetchSmartProfile: (data) => dispatch(commonActions.fetchSmartProfile(data)),
  setRegisterOpen: (data) => dispatch(commonSliceActions.setRegisterOpen(data)),
  fetchPostOfficeByPin: (data) => dispatch(commonActions.fetchPostOfficeByPin(data)),
  fetchLocalBodyType: (data) => dispatch(commonActions.fetchLocalBodyType(data)),
  fetchBanks: () => dispatch(commonActions.fetchBanks()),
  fetchBranchByBank: (data) => dispatch(commonActions.fetchBranchByBank(data)),
  fetchEducation: () => dispatch(commonActions.fetchEducation()),
  fetchGender: () => dispatch(commonActions.fetchGender()),
  fetchCategory: () => dispatch(commonActions.fetchCategory()),
  fetchFinancialStatus: () => dispatch(commonActions.fetchFinancialStatus()),
  fetchAccountType: (data) => dispatch(commonActions.fetchAccountType(data)),
  fetchTreasuryType: (data) => dispatch(commonActions.fetchTreasuryType(data)),
  saveBeneficiary: (data) => dispatch(actions.saveBeneficiary(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  fetchBeneficiaryById: (data) => dispatch(actions.fetchBeneficiaryById(data)),
  fetchLocalBodyByOfficeCode: (data) => dispatch(commonActions.fetchLocalBodyByOfficeCode(data)),
  fetchAccountId: (data) => dispatch(commonActions.fetchAccountId(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(NewBeneficiary);
