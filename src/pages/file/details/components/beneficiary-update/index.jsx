import { createStructuredSelector } from 'reselect';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { actions as commonSliceActions } from 'pages/common/slice';
import { connect } from 'react-redux';
import {
  Button, IconButton, Input, InputGroup, InputRightElement, t
} from 'common/components';
import SearchIcon from 'assets/SearchIcon';
import { CommonTable } from 'common/components/Table';
import { handleSlNo } from 'utils/serialNumber';
import { getTableLoader } from 'pages/common/selectors';
import Edit from 'assets/Edit';
import Delete from 'assets/delete';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import { secondary } from 'utils/color';
// import { ExcelSvg } from 'common/components/DocumentPreview/Icons';
import ArrowView from 'assets/ArrowView';
import * as actions from '../../actions';
import { getBeneficiaryList, getFileDetails } from '../../selector';
import NewBeneficiary from './new/DialogNew';
import { applicantName } from '../helper';
import ViewDetails from './ViewDetails';
// import BulkUpload from './BulkUpload';

const styles = {
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  }
};

const Beneficiary = (props) => {
  const {
    fetchFileDetails,
    setFormTitle,
    setFileHeader,
    fileDetails,
    fetchBeneficiary,
    beneficiary,
    tableLoader,
    deleteBeneficiary,
    setAlertAction,
    from = null
  } = props;

  const params = useParams();
  const [search, setSearch] = useState('');
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [selectedIds, setSelectedIds] = useState([]);
  const [phone, setPhone] = useState('');
  const [name, setName] = useState('');
  const [selectedId, setSelectedId] = useState(null);
  const [benOpen, setBenOpen] = useState(false);
  const [openViewDetails, setOpenViewDetails] = useState(false);
  const [benViewData, setBenViewData] = useState(null);
  // const [bulkUpload, setBulkUpload] = useState(false);

  useEffect(() => {
    if (params.fileNo) {
      fetchFileDetails(params.fileNo);
    }
  }, [params]);

  useEffect(() => {
    if (fileDetails?.officeId && params?.fileNo) {
      fetchBeneficiary({
        fileNo: params?.fileNo, officeId: fileDetails?.officeId, page, size: 10, sortDirection: 'desc'
      });
    }
  }, [params?.fileNo, fileDetails, page]);

  useEffect(() => {
    setFormTitle({ title: t('beneficiary'), variant: 'normal' });
    setFileHeader([
      {
        label: t('fileNumber'),
        value: params.fileNo
      },
      {
        label: t('role'),
        value: fileDetails?.role
      },
      {
        label: t('service'),
        value: fileDetails?.serviceName
      },
      {
        label: t('applicantName'),
        value: fileDetails?.inwardDetails ? applicantName(fileDetails?.inwardDetails) : ''
      }
    ]);
  }, [JSON.stringify(fileDetails)]);

  // const handleAdd = () => {
  //   navigate(`${BASE_PATH}/file/${params?.fileNo}/beneficiary/new`);
  // };

  useEffect(() => {
    if (beneficiary?.content) {
      setTableData(beneficiary?.content);
      setTotalItems(beneficiary?.totalElements);
      setNumberOfElements(Number(beneficiary.numberOfElements));
    }
  }, [beneficiary]);

  const handleName = (val) => {
    if (val?.row?.institutionName) {
      return val?.row?.institutionName;
    }
    if (val?.row?.beneficiaryName) {
      return val?.row?.beneficiaryName;
    } return '';
  };

  const handleSelect = (data) => {
    const { row } = data;
    const selected = selectedIds?.length > 0 ? JSON.parse(JSON.stringify(selectedIds)) : [];
    const find = selected.findIndex((item) => item === row.beneficiaryId);
    if (find > -1) {
      selected.splice(find, 1);
    } else {
      selected.push(row?.beneficiaryId);
    }
    setSelectedIds(selected);
  };

  function checkBoxCheck(data) {
    const find = selectedIds?.findIndex((item) => item === data);
    if (find > -1) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  const selectedFileRow = (data) => {
    return (
      <IconButton
        variant="unstyled"
        style={{ textDecoration: 'none' }}
        icon={checkBoxCheck(data?.row?.beneficiaryId)}
        onClick={() => handleSelect(data)}
      />
    );
  };

  const handleOpen = (id) => {
    setSelectedId(id);
    setBenOpen(true);
  };

  const handleOpenBen = (row) => {
    setOpenViewDetails(true);
    setBenViewData(row?.row);
  };

  const tableActions = (row) => {
    return (
      <>
        {from !== 'summary' && (
        <IconButton
          variant="unstyled"
          onClick={() => {
            handleOpen(row?.row?.beneficiaryId);
          }}
          icon={<Edit />}
        />
        )}
        <IconButton
          variant="unstyled"
          onClick={() => {
            handleOpenBen(row);
          }}
          icon={<ArrowView />}
        />
      </>
    );
  };

  const handleViewClose = () => {
    setOpenViewDetails(!openViewDetails);
  };

  const columns = [
    {
      cell: (field) => (from === 'summary' ? null : selectedFileRow(field))
    },
    {
      header: t('slNo'),
      field: 'slNo',
      alignment: 'left',
      cell: (field) => handleSlNo(field, page)
    },
    {
      header: t('nameOfApplicant'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleName(field)
    },
    {
      header: t('mobileNo'),
      field: 'mobileNo',
      alignment: 'left'
    },
    {
      header: t('mainPlace'),
      field: 'mainPlace',
      alignment: 'left'
    },
    {
      header: t('action'),
      alignment: 'left',
      cell: tableActions
    }

  ];

  const onPageClick = (data) => {
    setPage(data);
  };

  const pageSize = () => {
    const idLength = selectedIds?.length || 0;
    return (totalItems - idLength) <= 10 ? 0 : page;
  };

  const handleDelete = () => {
    deleteBeneficiary({
      fileNo: params?.fileNo,
      data: { beneficiaryIds: selectedIds },
      params: {
        fileNo: params?.fileNo, officeId: fileDetails?.officeId, page: pageSize(), size: 10, sortDirection: 'desc'
      }
    });
    setSelectedIds([]);
  };

  const handleConfirmation = () => {
    if (selectedIds?.length === 0) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('pleaseSelectBeneficiaries'),
        title: t('confirmation'),
        backwardActionText: t('ok'),
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else {
      setAlertAction({
        open: true,
        variant: 'alert',
        message: `Are you sure want to delete ${selectedIds?.length} ${selectedIds?.length === 1 ? 'beneficiary' : 'beneficiaries'}`,
        title: t('confirmation'),
        backwardActionText: t('no'),
        forwardActionText: t('yes'),
        closeOnOverlayClick: false,
        closeOnEsc: false,
        forwardAction: () => handleDelete()
      });
    }
  };

  const handleSearch = () => {
    fetchBeneficiary({
      fileNo: params?.fileNo, officeId: fileDetails?.officeId, page, size: 10, sortDirection: 'desc', mobileNo: phone === '' ? null : phone, searchKeyword: name === '' ? null : name?.toLowerCase()
    });
  };

  useEffect(() => {
    // eslint-disable-next-line no-restricted-globals
    if (isNaN(search)) {
      setName(search);
      setPhone(null);
    } else {
      setPhone(search);
      setName(null);
    }
  }, [search]);

  // const handleBulkUpload = () => {
  //   setBulkUpload(true);
  // };

  // const handleBulkUploadClose = () => {
  //   setBulkUpload(false);
  // };

  return (
    <>
      {from !== 'summary' && (
      <div className="flex items-center gap-5">
        <div className="flex-grow" />

        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('Enter Name or Phone')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => handleSearch()} icon={<SearchIcon />} variant="ghost" />
            </InputRightElement>
          </InputGroup>
        </div>
        <div className="flex-none">
          <NewBeneficiary beneficiaryId={selectedId} setBeneficiaryId={setSelectedId} setBenOpen={setBenOpen} benOpen={benOpen} />
        </div>
        {/* <div className="flex-none">
          <Button onClick={handleAdd} variant="secondary_outline">
            {t('addBeneficiary')}
          </Button>
        </div> */}
        {/* <div>
          <Button
            variant="primary_outline"
            leftIcon={<ExcelSvg color={primary} />}
            onClick={handleBulkUpload}

          >
            {t('bulkUpload')}
          </Button>
        </div> */}
        <div className="flex-none">
          <Button
            variant="secondary_outline"
            leftIcon={<Delete color={secondary} />}
            onClick={() => handleConfirmation()}
          >
            {t('delete')}
          </Button>
        </div>
      </div>
      )}
      <div className="mb-10">
        <CommonTable
          variant={from === 'summary' ? 'normal' : 'dashboard'}
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          currentPage={page}
          itemsPerPage={10}
          totalItems={totalItems}
          onPageClick={onPageClick}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'file-table'}
          numberOfElements={numberOfElements}
        />
      </div>
      {/* {bulkUpload && <BulkUpload open={bulkUpload} handleCloseBen={handleBulkUploadClose} />} */}
      <ViewDetails openViewDetails={openViewDetails} viewData={benViewData} handleViewClose={handleViewClose} />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  beneficiary: getBeneficiaryList,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  fetchBeneficiary: (data) => dispatch(actions.fetchBeneficiary(data)),
  deleteBeneficiary: (data) => dispatch(actions.deleteBeneficiary(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Beneficiary);
