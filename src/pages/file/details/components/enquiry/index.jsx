import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>, FormWrapper, RichText, t
} from 'common/components';
import _ from 'lodash';
import { useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { dayjs } from 'utils/date';
import NotesCard from 'common/components/NotesCard';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getActionTriggered, getUserInfo } from 'pages/common/selectors';
import { DATE_FORMAT } from 'pages/common/constants';
import BackButton from 'common/components/BackButton/BackButton';
import * as actions from '../../actions';
import { getEnquiryDetails, getEnquiryDocuments, getFileDetails } from '../../selector';
import Location from './components/LocationMap';
import AddDocuments from './components/AddDocuments';
import { fileColors } from '../../constants';
import { applicantName } from '../helper';

const Enquiry = (props) => {
  const {
    saveEnquiry,
    saveEnquiryDoc,
    setFormTitle, setFileHeader, enquiryDetails, fetchEnquiryDetails, updateEnquiryText,
    fetchEnquiryDoc, enquiryDocuments, updateEnquiryDoc, fetchFileDetails,
    fileDetailsData,
    userInfo,
    actionTriggered,
    setActionTriggered
  } = props;
  const params = useParams();
  const [textEditorData, setTextEditorData] = useState('');
  const [location, setLocation] = useState({ lat: 8.38873173003715, lng: 76.98696464727485 });
  const [documents, setDocuments] = useState([]);
  const [enquiryTextError, setEnquiryTextError] = useState('');
  const [fileError, setFileError] = useState('');
  const [enquiryId, setEnquiryId] = useState('');
  const [existingData, setExistingData] = useState('');
  const [enquiryReport, setEnquiryReport] = useState(
    {
      enquiryText: '', createdBy: '', createdAt: ''
    }
  );

  const handleEditorChange = (data) => {
    setTextEditorData(data);
    if (data && !'<p></p>') {
      setEnquiryTextError('');
    }
  };

  useEffect(() => {
    if (params) {
      if (params.fileNo) {
        fetchFileDetails(params.fileNo);
      }
    }
  }, [params]);

  useEffect(() => {
    if (fileDetailsData) {
      if (fileDetailsData?.enquiryIds) {
        const enquiryData = fileDetailsData?.enquiryIds;
        if (enquiryData?.length > 0) {
          setEnquiryId(enquiryData[0]);
          fetchEnquiryDetails(params.fileNo);
          fetchEnquiryDoc(params.fileNo);
        } else {
          setEnquiryId('');
        }
      }
    } else {
      setEnquiryId(null);
    }
  }, [JSON.stringify(fileDetailsData)]);

  useEffect(() => {
    if (enquiryDetails) {
      if (_.size(enquiryDetails) > 0) {
        setEnquiryId(enquiryDetails.enquiryId);
        setEnquiryReport(enquiryDetails);
      }
    }
  }, [enquiryDetails]);

  useEffect(() => {
    setFormTitle({ title: t('fileEnquiry'), variant: 'normal' });
    setFileHeader([{
      label: t('fileNumber'),
      value: params.fileNo
    }, {
      label: t('role'),
      value: fileDetailsData?.role
    }, {
      label: t('servicee'),
      value: fileDetailsData?.serviceName
    },
    {
      label: t('applicantName'),
      value: fileDetailsData?.inwardDetails ? applicantName(fileDetailsData?.inwardDetails) : ''
    }
    ]);
  }, [JSON.stringify(fileDetailsData)]);

  useEffect(() => {
    if (enquiryDocuments && enquiryDocuments?.length > 0) {
      const existingDocs = enquiryDocuments?.map((item) => ({
        documentName: item.documentName,
        id: item.id,
        uploadedEnquiryDocsInfo: item.uploadedEnquiryDocsInfo
      }));
      setDocuments(existingDocs);
      // Do something with existingDocs if needed
    }
  }, [enquiryDocuments]);
  const handleSaveDoc = () => {
    setActionTriggered({ loading: true, id: 'createDraft' });
    const docPayload = documents.map((item, index) => ({
      officeId: userInfo.id,
      fileNo: params.fileNo,
      documentName: item.documentName,
      fileStoreId: index + 1
    }));
    const docs = [];
    documents.map((item) => {
      docs.push(item.file);
      return docs;
    });
    const payload = {
      request: {
        officeId: userInfo.id,
        serviceCode: fileDetailsData?.serviceCode,
        fileNo: params.fileNo,
        latitude: (location.lat).toString(),
        longitude: (location.lng).toString(),
        enquiryText: textEditorData,
        assigner: '774e7a3f-c1a6-4e6d-a6c9-bc81c93d6535',
        enquiryDocs: docPayload
      },
      enquiryDocs: docs
    };

    const payloadText = {
      officeId: userInfo.id,
      serviceCode: fileDetailsData?.serviceCode,
      fileNo: params.fileNo,
      latitude: (location.lat).toString(),
      longitude: (location.lng).toString(),
      enquiryText: textEditorData,
      assigner: '774e7a3f-c1a6-4e6d-a6c9-bc81c93d6535',
      enquiryDocs: docPayload
    };

    const payloadTextUpdate = {
      fileNo: params.fileNo,
      data: {
        enquiryText: textEditorData
      }
    };

    if (!enquiryId) {
      if (textEditorData && documents.length === 0 && textEditorData !== '<p></p>\n') {
        saveEnquiry(payloadText);
        setEnquiryTextError('');
      } else if (textEditorData && documents.length > 0 && textEditorData !== '<p></p>\n') {
        saveEnquiryDoc(payload);
      } else if (textEditorData === '' || textEditorData === '<p></p>\n') {
        setEnquiryTextError(t('isRequired', { type: t('enquiryText') }));
      } else {
        setEnquiryTextError('');
      }
    } else {
      updateEnquiryText(payloadTextUpdate);
    }
  };

  const handleUpdateDoc = (documentName, file) => {
    const payload = {
      request: {
        officeId: userInfo.id,
        serviceCode: fileDetailsData?.serviceCode,
        documentName,
        fileNo: params.fileNo,
        fileStoreId: documents.length + 1,
        enquiryDocs: []
      },
      enquiryDocs: file
    };
    updateEnquiryDoc(payload);
  };

  const handleEdit = (data) => {
    setExistingData(data.enquiryText);
    setEnquiryId(data.enquiryId);
  };

  return (
    <div className="bg-white p-5 rounded-lg">
      <FormWrapper>
        <div className="col-span-6">
          <div className="grid grid-cols-1 gap-4">
            <div className="col-span-12">
              <Location setLocation={setLocation} location={location} />
            </div>
            <div className="col-span-12">
              <AddDocuments setDocuments={setDocuments} documents={documents} fileError={fileError} setFileError={setFileError} enquiryId={enquiryId} handleUpdateDoc={handleUpdateDoc} />
            </div>
            {enquiryReport?.enquiryText
              && (
                <div className="col-span-12">
                  <NotesCard
                    key={enquiryReport.enquiryText}
                    item={enquiryReport}
                    title={enquiryReport.createdBy || 'Reporter'}
                    description={enquiryReport.enquiryText}
                    caption={dayjs(enquiryReport.createdAt).format(DATE_FORMAT.DATE_LOCAL)}
                    actions={[t('edit')]}
                    handleEdit={handleEdit}
                    colors={fileColors[0]}
                  />
                </div>
              )}
            <div className="col-span-12">

              <RichText
                label={t('enquiryReport')}
                value={existingData}
                placeHolder="enterEnquiryReports"
                error={enquiryTextError}
                onChange={handleEditorChange}
                height="400"
                required
              />
            </div>
            <div className="col-span-12 text-right">
              <BackButton />
              <Button
                variant="solid"
                colorScheme="secondary"
                onClick={handleSaveDoc}
                isLoading={actionTriggered?.id === 'createEnquiry' && actionTriggered?.loading}
              >
                {t('saveEnquiry')}
              </Button>
            </div>
          </div>
        </div>
      </FormWrapper>

    </div>

  );
};

const mapStateToProps = createStructuredSelector({
  enquiryDetails: getEnquiryDetails,
  enquiryDocuments: getEnquiryDocuments,
  fileDetailsData: getFileDetails,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  saveEnquiry: (data) => dispatch(actions.saveEnquiry(data)),
  saveEnquiryDoc: (data) => dispatch(actions.saveEnquiryDoc(data)),
  fetchEnquiryDetails: (data) => dispatch(actions.fetchEnquiryDetails(data)),
  updateEnquiryText: (data) => dispatch(actions.updateEnquiryText(data)),
  fetchEnquiryDoc: (data) => dispatch(actions.fetchEnquiryDoc(data)),
  updateEnquiryDoc: (data) => dispatch(actions.updateEnquiryDoc(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Enquiry);
