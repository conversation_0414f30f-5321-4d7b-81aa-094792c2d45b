import React, { useState } from 'react';
import VideoIcon from 'assets/Video';
import {
  AudioRecorder,
  VideoRecorder,
  IconButton,
  ImageCapture,
  TextInput,
  FileUpload,
  t
} from 'common/components';
import AudioIcon from 'assets/Audio';
import ImageIcon from 'assets/Image';
import { CommonTable } from 'common/components/Table';
import View from 'assets/View';
import Delete from 'assets/delete';

const styles = {
  fileCover: {
    width: '100%',
    border: '1px solid #E2E8F0',
    borderRadius: '6px',
    padding: '15px 20px 20px 20px'
  },
  fileInput: {
    width: '100%',
    height: '64px',
    border: '1px solid #E2E8F0',
    borderRadius: '6px',
    padding: '0 1rem'
  },
  icon: {
    background: 'none'
  }
};

const AddDocuments = ({
  setDocuments = [], documents, fileError, setFileError, enquiryId, handleUpdateDoc = () => { }
}) => {
  const [openAudio, setOpenAudio] = useState(false);
  const [openVideo, setOpenVideo] = useState(false);
  const [openImage, setOpenImage] = useState(false);
  const [documentName, setDocumentName] = useState('');
  const [nameError, setNameError] = useState('');
  const [file, setFile] = useState('');

  const handleAddDoc = (data) => {
    if (data) {
      setFileError('');
      setNameError('');
      if ((enquiryId && documents.length >= 1) || (enquiryId && documents.length === 0)) {
        handleUpdateDoc(documentName || data.name, data);
      } else {
        const newArray = [{ documentName: documentName || data.name, file: data }];
        const existArray = documents || [];
        setDocuments([...existArray, ...newArray]);
      }
      setDocumentName('');
      setFile('');
    }
  };

  const closeAudio = () => {
    setOpenAudio(false);
  };

  const closeVideo = () => {
    setOpenVideo(false);
  };

  const closeImage = () => {
    setOpenImage(false);
  };

  const audioOpen = async () => {
    setOpenAudio(true);
  };

  const videoOpen = async () => {
    setOpenVideo(true);
  };

  const imageOpen = async () => {
    setOpenImage(true);
  };

  const handleRemove = (data) => {
    const copyArray = JSON.parse(JSON.stringify(documents));
    copyArray.splice(data.rowIndex, 1);
    setDocuments(copyArray);
  };

  const columns = [
    {
      header: t('slNo'),
      cell: (({ rowIndex }) => (rowIndex + 1)),
      alignment: 'left'
    },
    {
      header: t('documentName'),
      field: 'documentName',
      alignment: 'left'
    },
    {
      header: t('action'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          label: t('edit'),
          icon: <View />
        },
        {
          label: t('delete'),
          icon: <Delete />,
          onClick: (data) => handleRemove(data)
        }
      ]
    }
  ];

  return (
    <>
      <h4 className="text-blue-800 font-[700] mt-5">{t('addDocuments')}</h4>
      <fieldset className="mt-2" style={styles.fileCover}>
        <div className="flex gap-4 items-center">
          <div className="flex-grow">
            <TextInput
              label={t('documentName')}
              type="text"
              size="xs"
              error={nameError}
              value={documentName}
              onChange={(event) => setDocumentName(event.target.value)}
            />
          </div>
          <div>
            <IconButton style={styles.icon} onClick={videoOpen} icon={<VideoIcon />} />
            <VideoRecorder
              open={openVideo}
              close={closeVideo}
              title={t('videoRecorder')}
              file={(data) => {
                handleAddDoc(data);
              }}
            />
          </div>
          <div>
            <IconButton style={styles.icon} onClick={audioOpen} icon={<AudioIcon />} />
            <AudioRecorder
              title={t('audioRecorder')}
              open={openAudio}
              close={closeAudio}
              file={(data) => {
                handleAddDoc(data);
              }}
            />
          </div>
          <div>
            <IconButton style={styles.icon} onClick={imageOpen} icon={<ImageIcon />} />
            <ImageCapture
              title={t('imageCapture')}
              open={openImage}
              close={closeImage}
              file={(data) => {
                handleAddDoc(data);
              }}
            />
          </div>
        </div>
        <div className="flex mt-5 gap-4">

          <div className="flex-grow">
            <FileUpload
              label={t('supportingDocuments')}
              error={fileError}
              value={file}
              onChange={(data) => {
                handleAddDoc(data);
              }}
            />
          </div>
        </div>
      </fieldset>

      <div>
        {documents.length > 0
          && (
            <CommonTable
              columns={columns}
              tableData={documents}
            />
          )}
      </div>
    </>
  );
};

export default AddDocuments;
