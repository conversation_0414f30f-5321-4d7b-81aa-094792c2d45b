import React from 'react';

import { LocationMap } from 'common/components';

const Location = ({ setLocation, location }) => {
  const mapStyle = {
    mapSize: {
      width: '100%',
      height: '500px'
    }
  };
  const onChange = (data) => {
    const formateData = {
      lat: data.latitude,
      lng: data.longitude
    };
    setLocation(formateData);
  };

  return (
    <div>
      <LocationMap
        handleChange={onChange}
        defaultPosition={location}
        mapStyle={mapStyle}
        isMapPlaceRequired
        mapKey="AIzaSyB_2cieu90RKipfkKS8I59nHLw48nvUiA4"
      />

    </div>
  );
};

export default Location;
