import {
  FormC<PERSON>roller,
  FormModal,
  FormWrapper, t
} from 'common/components';
import { getModulesDropdown, getServicesSearchList } from 'pages/common/selectors';
import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import * as commonActions from 'pages/common/actions';
import { useForm } from 'react-hook-form';
import _ from 'lodash';
import { getSubModulesSearchList } from 'pages/counter/new/selectors';
import { FILTER_TYPE } from 'pages/common/constants';
import * as actions from '../../../counter/new/actions';

const ChildFileAdvancedSearch = ({
  open, setOpen,
  fetchModulesOptions,
  subModulesDropdown,
  modulesDropdown,
  fetchSubModuleByModuleId,
  fetchServicesById,
  setModuleId,
  setModuleCode, setSubModuleCode, setServiceCode,
  setDataSearch, dataSearch, setConfirmServiceName,
  setServiceName, serviceName,
  servicesSearchList
}) => {
  const {
    control,
    formState: { errors }, handleSubmit, setValue, getValues
  } = useForm({
    mode: 'all',
    defaultValues: {
      services: null,
      modules: null,
      subModule: null
    }
  });

  useEffect(() => {
    fetchModulesOptions();
  }, []);

  const handleClose = () => {
    setOpen(false);
  };

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.MODULES:
        fetchSubModuleByModuleId(data.id);
        setValue('modules', data?.id);
        setModuleId(data.id);
        setModuleCode(data?.code);
        break;
      case FILTER_TYPE.SUB_MODULES:
        fetchServicesById({ module: getValues('modules'), subModule: data.id });
        setSubModuleCode(data?.code);
        break;
      case FILTER_TYPE.SERVICES:
        setDataSearch(data);
        setServiceCode(data?.code);
        setServiceName(data?.code);
        break;
      default:
        break;
    }
  };

  const handleSearch = () => {
    if (dataSearch !== '') {
      setOpen(false);
      setConfirmServiceName(serviceName);
      setValue('services', null);
      setValue('modules', null);
      setValue('subModule', null);
    }
  };

  return (

    <FormModal
      modalTitle={t('advanceSearch')}
      open={open}
      close={handleClose}
      actionButtonText={t('go')}
      content={(
        <form
          id="child-file-advance-search-form"
          onSubmit={handleSubmit(handleSearch)}
        >
          <FormWrapper>
            <div className="col-span-12">
              <FormController
                name="modules"
                type="select"
                label={t('module')}
                placeholder={t('module')}
                control={control}
                errors={errors}
                optionKey="id"
                options={_.get(modulesDropdown, 'data', [])}
                handleChange={(data) => handleFieldChange(FILTER_TYPE.MODULES, data)}
                required
              />
            </div>
            <div className="col-span-12">
              <FormController
                name="subModule"
                type="select"
                label={t('concatLabel', { label: t('sub'), type: t('module') })}
                placeholder={t('concatLabel', { label: t('sub'), type: t('module') })}
                control={control}
                errors={errors}
                options={subModulesDropdown}
                handleChange={(data) => handleFieldChange(FILTER_TYPE.SUB_MODULES, data)}
                required
              />
            </div>
            <div className="col-span-12">
              <FormController
                name="services"
                type="select"
                label={t('services')}
                placeholder={t('searchHere')}
                control={control}
                errors={errors}
                options={servicesSearchList}
                handleChange={(data) => {
                  handleFieldChange(FILTER_TYPE.SERVICES, data);
                }}
                required
              />
            </div>
          </FormWrapper>
        </form>
      )}
      formId="child-file-advance-search-form"
      type="submit"
      modalSize="xl"
      closeButtonText={t('cancel')}
      closeOnOverlayClick={false}
      closeOnEsc={false}
    />
  );
};

const mapStateToProps = createStructuredSelector({
  modulesDropdown: getModulesDropdown,
  subModulesDropdown: getSubModulesSearchList,
  servicesSearchList: getServicesSearchList
});

const mapDispatchToProps = (dispatch) => ({
  fetchModulesOptions: () => dispatch(commonActions.fetchModuleDetails()),
  fetchSubModuleByModuleId: (data) => dispatch(actions.fetchSubModuleByModuleId(data)),
  fetchServicesById: (data) => dispatch(commonActions.fetchServicesById(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ChildFileAdvancedSearch);
