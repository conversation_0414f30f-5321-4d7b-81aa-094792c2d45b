import CloseOutlineIcon from 'assets/CloseOutline';
import SearchIcon from 'assets/SearchIcon';
import { Button, IconButton } from 'common/components';
import { t } from 'i18next';
import React, { useState } from 'react';
import FileSearch from './FileSearch';

const FloatingSearch = () => {
  const [open, setOpen] = useState(false);
  const [openFileSearch, setOpenFileSearch] = useState(false);
  return (
    <>
      <div className="fixed bottom-[82px] right-[40px]"><IconButton variant="primary" style={{ height: '40px' }} onClick={() => setOpen(!open)} icon={open ? <CloseOutlineIcon height="24px" width="24px" color="#fff" /> : <SearchIcon height="30px" width="30px" color="#fff" />} /></div>
      {open
      && (
      <div className="p-3 rounded-lg bg-white border-2 border-blue-300 fixed bottom-[122px] right-[80px]">
        <h4 className="text-[16px] font-bold">{t('floatingSearch')}</h4>
        <div className="flex flex-col gap-2 mt-3">
          <Button variant="primary_outline" justifyContent="start" onClick={() => setOpenFileSearch(true)}>File Search</Button>
          <Button variant="primary_outline" justifyContent="start">Service Search</Button>
        </div>
      </div>
      )}
      <FileSearch open={openFileSearch} close={() => setOpenFileSearch(false)} />
    </>
  );
};

export default FloatingSearch;
