import React, { useState, useEffect } from 'react';
import {
  Modal,
  ModalBody,
  ModalContent,
  ModalOverlay,
  t,
  Tooltip,
  IconButton,
  InputGroup,
  Input,
  InputRightElement,
  Button
} from 'common/components';
import { dark } from 'utils/color';
import CloseOutlineIcon from 'assets/CloseOutline';
import { DATE_FORMAT, FILTER_TYPE, X_STATE_CODE } from 'pages/common/constants';
import SearchIcon from 'assets/SearchIcon';
import { CommonTable } from 'common/components/Table';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as actions from 'pages/searchFile/listing/actions';
import { actions as sliceActions } from 'pages/searchFile/listing/slice';
import * as commonActions from 'pages/common/actions';
import {
  handleApplicantName, handleCurrentUser, handleCustodian, handleFileNumber, handleFileStage, handleFileStatus, handleInwardNumber, handleServiceName, handleTitle
} from 'pages/searchFile/listing/components/Helper';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getSearchList, getSearchListParams } from 'pages/searchFile/listing/selectors';
import { getStatus, getTableLoader, getUserInfo } from 'pages/common/selectors';
import { convertToLocalDate } from 'utils/date';
import { capitalizeFirstLetter } from 'utils/capitalize';

const styles = {
  head: {
    fontSize: '18px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '5px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none',
      display: 'flex',
      justifyContent: 'center'
    }
  },
  date: {
    width: '200px',
    input: {
      borderRadius: '5px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '5px',
    border: '1px solid #CBD5E0',
    background: 'none',
    padding: '8px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const FileSearch = (props) => {
  const {
    setTableLoader,
    tableLoader,
    searchFileData,
    searchFileParams,
    fetchSearchFiles,
    setSearchListParams,
    userInfo,
    statusDropdown,
    fetchStatus,
    // direct props
    open,
    close
  } = props;
  const [openAdvanceSearch, setOpenAdvanceSearch] = useState(false);
  const [search, setSearch] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [date, setDate] = useState();
  const [status, setStatus] = useState('');
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const columns = [

    {
      header: t('fileNumber'),
      alignment: 'left',
      field: 'fileNo',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('inwardNumber'),
      alignment: 'left',
      field: 'inwardNo',
      cell: (field) => handleInwardNumber(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('title'),
      alignment: 'left',
      field: 'title',
      cell: (field) => handleTitle(field)
    },
    {
      header: t('custodian'),
      alignment: 'left',
      field: 'custodian',
      cell: (field) => handleCustodian(field)
    },
    {
      header: t('service'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('currentUser'),
      alignment: 'left',
      field: 'currentUser',
      cell: (field) => handleCurrentUser(field)
    },
    {
      header: t('stage'),
      alignment: 'left',
      field: 'currentStage',
      cell: (field) => handleFileStage(field)
    },
    {
      header: t('status'),
      alignment: 'left',
      field: 'fileStatus',
      cell: (field) => handleFileStatus(field)
    }

  ];

  useEffect(() => {
    // fetchModulesOptions();
    fetchStatus({ code: X_STATE_CODE });
    // fetchFileTypeOptions();
    // if (userInfo?.officeId) {
    //   fetchFunctionalGroups(`officeId=${userInfo?.officeId}`);
    // }
  }, [userInfo]);

  useEffect(() => {
    if (searchFileData) {
      setTableLoader({ loading: false, id: 'search-file-table' });
      if (Object.keys(searchFileData).length > 0) {
        if (searchFileData?.content) {
          setTableData(searchFileData?.content);
          setTotalItems(Number(`${searchFileData.totalPages}0`));
          setNumberOfElements(Number(searchFileData.numberOfElements));
        } else {
          setTableData([searchFileData]);
          setTotalItems(0);
        }
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [searchFileData]);

  const onPageClick = (data) => {
    setPage(data);
    setSearchListParams({
      ...searchFileParams, page: data, search: true, officeId: userInfo?.officeId
    });
  };

  useEffect(() => {
    if (searchFileParams?.search) {
      setTableLoader({ loading: true, id: 'search-file-table' });
      fetchSearchFiles();
    }
  }, [searchFileParams]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setSearchListParams({
          ...searchFileParams,
          keyword: data || null,
          search: true,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          statusList: null,
          officeId: userInfo?.officeId,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setSearchListParams({
          ...searchFileParams,
          createdAt: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          search: true,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          statusList: null,
          officeId: userInfo?.officeId,
          page: 0
        });
        setDate(data);
        break;
      case FILTER_TYPE.STATUS:
        setSearchListParams({
          ...searchFileParams,
          statusList: data || null,
          search: true,
          serviceCode: null,
          moduleCode: null,
          submoduleCode: null,
          fileNo: null,
          applicantName: null,
          department: null,
          seat: null,
          fromDate: null,
          toDate: null,
          officeId: userInfo?.officeId,
          page: 0
        });
        setStatus(data);
        break;
      default:
        break;
    }
  };

  const handleOpen = () => {
    setOpenAdvanceSearch(!openAdvanceSearch);
  };

  return (
    <div>
      <Modal size="7xl" isOpen={open}>
        <ModalOverlay />
        <ModalContent borderRadius={16} style={{ margin: '30px' }}>
          <ModalBody p={5}>
            <div className="flex items-center bg-slate-100 pl-5 pr-0 py-1 gap-2 rounded-lg">
              <div className="flex-grow text-left font-bold" style={{ color: dark }}>
                {t('fileSearch')}
              </div>
              {!openAdvanceSearch && (
              <>
                <div className="flex-none">
                  <InputGroup style={styles.search}>
                    <Input
                      placeholder={t('searchHere')}
                      style={styles.search.input}
                      value={search}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                          triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search);
                        }
                      }}
                      onChange={(event) => {
                        setSearch(event.target.value);
                      }}
                    />
                    <InputRightElement>
                      <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon width="24px" height="24px" color="#000" />} colorScheme="blue" variant="unstyled" isRound />
                    </InputRightElement>
                  </InputGroup>
                </div>
                <div className="flex-none customFileDatePicker">
                  <InputGroup style={styles.date}>
                    <Input
                      value={date}
                      onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
                      type="date"
                      style={styles.date.input}
                    />
                  </InputGroup>
                </div>

                <div style={styles.sort} className="flex-none">
                  <select onChange={(event) => triggerSearch(FILTER_TYPE.STATUS, event.target.value)} value={status} style={styles.select}>
                    <option value="">{t('allStatus')}</option>
                    {statusDropdown?.map((item) => (
                      <option key={item?.id} value={item?.name}>{capitalizeFirstLetter(item?.name.toLowerCase())}</option>
                    ))}
                  </select>
                </div>
              </>
              )}
              <div className="flex-none">
                <Button
                  onClick={handleOpen}
                  size="lg"
                  colorScheme="pink"
                  style={{
                    height: '44px', borderRadius: '5px', width: '153px', fontSize: '14px'
                  }}
                >
                  {openAdvanceSearch ? t('minimalSearch') : t('advanceSearch')}
                </Button>
              </div>
              <div className="flex-none">
                <Tooltip label={t('close')}>
                  <IconButton size="sm" variant="unstyled" onClick={() => close()} leftIcon={<CloseOutlineIcon width="24px" height="24px" color="#000" />} />
                </Tooltip>
              </div>
            </div>
            <div style={{ height: 'calc(100vh - 160px)' }} className="overflow-y-auto mt-3 rounded-lg flex">
              {openAdvanceSearch
              && (
              <div className="flex-none">
                <h4>{t('advanceSearch')}</h4>
                <form />
              </div>
              )}
              <CommonTable
                variant="dashboard"
                tableData={tableData}
                columns={columns}
                itemsPerPage={10}
                onPageClick={onPageClick}
                totalItems={totalItems}
                currentPage={page}
                paginationEnabled
                tableLoader={tableLoader?.loading && tableLoader?.id === 'search-file-table'}
                numberOfElements={numberOfElements}
              />
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  searchFileData: getSearchList,
  tableLoader: getTableLoader,
  searchFileParams: getSearchListParams,
  userInfo: getUserInfo,
  statusDropdown: getStatus
});

const mapDispatchToProps = (dispatch) => ({
  fetchSearchFiles: () => dispatch(actions.fetchSearchFiles()),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setSearchListParams: (data) => dispatch(sliceActions.setSearchListParams(data)),
  fetchStatus: (data) => dispatch(commonActions.fetchStatus(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(FileSearch);
