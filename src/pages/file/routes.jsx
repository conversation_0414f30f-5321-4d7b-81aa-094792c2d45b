import { lazy } from 'react';
import InwardDeLink from './details/components/inward/DeLink';

const FileSummary = lazy(() => import('./details/components/summaryDetails'));
const PullFile = lazy(() => import('./pullFile/components/fileSearch.jsx'));
const FileNotes = lazy(() => import('./details/components/notes'));
const Draft = lazy(() => import('./details/components/draft'));
const Enquiry = lazy(() => import('./details/components/enquiry'));
const ChildFile = lazy(() => import('./details/components/ChildFile'));
const MergeFile = lazy(() => import('./details/components/merge/SameSeatMergeFile.jsx'));
const LinkFile = lazy(() => import('./details/components/merge/SameSeatMergeFile.jsx'));
const UnMergeFile = lazy(() => import('./details/components/merge/UnmergeFiles.jsx'));
const UnLinkFiles = lazy(() => import('./details/components/merge/UnLinkFiles.jsx'));
const BeneficaryUpdate = lazy(() => import('./details/components/beneficiary-update'));
const CustodianChange = lazy(() => import('./details/components/custodian/CustodianChange.jsx'));

const routes = [{
  path: '',
  children: [
    {
      path: ':fileNo',
      element: <FileSummary />
    },
    {
      path: ':fileNo/summary',
      element: <FileSummary />
    },
    {
      path: ':fileNo/notes',
      element: <FileNotes />
    },
    {
      path: ':fileNo/draft',
      element: <Draft />
    },
    {
      path: ':fileNo/draft/:draftid',
      element: <Draft />
    },
    {
      path: 'pull-file',
      element: <PullFile />
    },
    {
      path: ':fileNo/enquiry',
      element: <Enquiry />
    },
    {
      path: ':fileNo/child',
      element: <ChildFile />
    },
    {
      path: ':fileNo/merge-file',
      element: <MergeFile />
    },
    {
      path: ':fileNo/file-link',
      element: <LinkFile />
    },
    {
      path: ':fileNo/link',
      element: <LinkFile />
    },
    {
      path: ':fileNo/unmerge',
      element: <UnMergeFile />
    },
    {
      path: ':fileNo/beneficiary',
      element: <BeneficaryUpdate />
    },
    {
      path: ':fileNo/un-link-file',
      element: <UnLinkFiles />
    },
    {
      path: ':fileNo/unLink',
      element: <UnLinkFiles />
    },
    {
      path: ':fileNo/custodian-change',
      element: <CustodianChange />
    },
    {
      path: ':fileNo/inward-de-link',
      element: <InwardDeLink />
    }
  ]
}];

export { routes };
