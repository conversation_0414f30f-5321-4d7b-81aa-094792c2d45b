import {
  all, takeLatest, fork, put, take
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

export function* fetchInbox({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchInbox, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_INBOX_SUCCESS,
    ACTION_TYPES.FETCH_INBOX_FAILURE]);
  if (type === ACTION_TYPES.FETCH_INBOX_SUCCESS) {
    yield put(sliceActions.setInbox(responsePayLoad.data));
  }
}

export default function* Saga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_INBOX, fetchInbox)
  ]);
}
