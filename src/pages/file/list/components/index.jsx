import SearchIcon from 'assets/SearchIcon';
import {
  InputGroup, InputRightElement, Input, Button, t
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import { convertToLocalDate } from 'utils/date';
import React, { useState, useEffect } from 'react';
import {
  useSearchParams, createSearchParams, useNavigate, useLocation
} from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { BASE_PATH } from 'common/constants';
import { getUserInfo } from 'pages/common/selectors';
import { DATE_FORMAT } from 'pages/common/constants';
import { getInboxData } from '../selector';
import * as actions from '../actions';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    input: {
      borderRadius: '20px',
      border: '1px solid #fff'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '120px'
  },
  sort: {
    display: 'flex'
  }
};

const Inbox = (props) => {
  const {
    inboxData,
    fetchInbox,
    userInfo
  } = props;

  const activeRows = [{}];
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [search, setSearch] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [date, setDate] = useState('');
  const [sort, setSort] = useState(10);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const fileNoCell = (data) => {
    if (data?.row) {
      const cellData = data?.row;
      const navigateDetails = (number, serviceCode) => {
        const paramData = createSearchParams({
          serviceCode
        });
        navigate(`${BASE_PATH}/file/${number}/summary?${paramData}`);
      };
      return (
        <Button variant="link" onClick={() => navigateDetails(cellData?.fileNo, cellData.serviceCode)}>
          <span className="text-black underline  underline-offset-6">
            {cellData?.fileNo}
          </span>

        </Button>
      );
    }
    return '';
  };

  const columns = [
    {
      header: t('fileNumber'),
      field: 'fileNo',
      cell: (field) => fileNoCell(field)
    },
    {
      header: t('fileDate'),
      field: 'fileDate'
    },
    {
      header: t('applicantName'),
      field: 'applicantName'
    },
    {
      header: t('service'),
      field: 'serviceName'
    },
    {
      header: t('forwardedBy'),
      field: 'forwardedBy'
    },
    {
      header: t('custodian'),
      field: 'custodian'
    },
    {
      header: t('deliveryDate'),
      field: 'deliveryDate'
    }
  ];

  const onPageClick = (data) => {
    setPage(data);
    const paramData = createSearchParams({
      page: data, size: (searchParams.get('size') ? searchParams.get('size') : sort), searchKeyword: (searchParams.get('searchKeyword') ? searchParams.get('searchKeyword') : search), fileCreationDate: (searchParams.get('fileCreationDate') ? searchParams.get('fileCreationDate') : date), sortDirection: 'desc'
    });
    navigate(`?${paramData}`);
  };

  const triggerSearch = (field, data) => {
    const updatedParams = {
      page: searchParams.get('page') || page,
      size: searchParams.get('size') || sort,
      searchKeyword: searchParams.get('searchKeyword') || search,
      fileCreationDate: searchParams.get('fileCreationDate') || date,
      postId: userInfo.id,
      sortDirection: 'desc'
    };

    switch (field) {
      case 'searchKeyword':
        updatedParams.searchKeyword = data;
        break;
      case 'date':
        updatedParams.fileCreationDate = convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL);
        setDate(convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL));
        break;
      case 'sort':
        updatedParams.size = data;
        setSort(data);
        break;
      default:
        break;
    }

    const paramData = createSearchParams(updatedParams);
    navigate(`?${paramData}`);
  };

  useEffect(() => {
    if (location) {
      const payload = {
        params: location.search ? location.search : `?fileCreationDate=&page=0&postId=${userInfo.id}&searchKeyword=&size=10&sortDirection=desc`
      };
      fetchInbox(payload);
    }
  }, [location]);

  useEffect(() => {
    if (inboxData) {
      if (Object.keys(inboxData).length > 0) {
        setTableData(inboxData.content);
        setTotalItems(parseInt(`${inboxData.totalPages}0`, 10));
        setNumberOfElements(Number(inboxData.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [inboxData]);

  return (
    <>
      <div className="grid gap-4 grid-cols-5">
        <div>
          <h4 style={styles.head}>
            <strong>
              {t('applications')}
            </strong>
          </h4>
        </div>
        <div>
          <InputGroup style={styles.search}>
            <Input
              placeholder="Search by Name, Number"
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
                triggerSearch('searchKeyword', event.target.value);
              }}
            />
            <InputRightElement>
              <SearchIcon />
            </InputRightElement>
          </InputGroup>
        </div>
        <div>
          <InputGroup style={styles.search}>
            <Input
              value={date}
              onChange={(event) => triggerSearch('date', event.target.value)}
              type="date"
              style={styles.search.input}
            />
          </InputGroup>
        </div>
        <div style={styles.sort}>
          <div style={styles.label}>Sort:</div>
          <select onChange={(event) => triggerSearch('sort', event.target.value)} value={sort} style={styles.select}>
            <option value={5}>5 Items</option>
            <option value={10}>10 Items</option>
            <option value={25}>25 Items</option>
          </select>
        </div>
      </div>
      <div className="col-span-12">
        <CommonTable
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={sort}
          totalItems={totalItems}
          currentPage={page}
          paginationEnabled
          variant="dashboard"
          numberOfElements={numberOfElements}
        />

      </div>

    </>
  );
};

const mapStateToProps = createStructuredSelector({
  inboxData: getInboxData,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  fetchInbox: (data) => dispatch(actions.fetchInbox(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Inbox);
