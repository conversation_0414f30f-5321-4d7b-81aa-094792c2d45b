import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_INBOX: `${STATE_REDUCER_KEY}/FETCH_INBOX`,
  FETCH_INBOX_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INBOX_REQUEST`,
  FETCH_INBOX_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INBOX_SUCCESS`,
  FETCH_INBOX_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INBOX_FAILURE`

};

// fetch application summary details
export const fetchInbox = createAction(ACTION_TYPES.FETCH_INBOX);
