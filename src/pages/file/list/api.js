import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchInbox = (payload) => {
  const { params } = payload;
  return {
    url: API_URL.INBOX.FETCH_INBOX.replace('?query', params),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INBOX_REQUEST,
        ACTION_TYPES.FETCH_INBOX_SUCCESS,
        ACTION_TYPES.FETCH_INBOX_FAILURE
      ]
    }
  };
};
