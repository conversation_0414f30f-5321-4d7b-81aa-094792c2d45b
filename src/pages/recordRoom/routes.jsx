import { lazy } from 'react';

const RecordRoomListing = lazy(() => import('./listing/components'));
const RecordRoomViewInboxDetails = lazy(() => import('./listing/components/ViewInboxDetails'));
const RecordRoomViewOutboxDetails = lazy(() => import('./listing/components/ViewOutboxDetails'));

const routes = [
  {
    path: 'record-room',
    element: <RecordRoomListing />
  },
  {
    path: 'record-room/inbox',
    element: <RecordRoomListing />
  },
  {
    path: 'record-room/outbox',
    element: <RecordRoomListing />
  },
  {
    path: 'record-room/inbox/:documentId',
    element: <RecordRoomViewInboxDetails />
  },
  {
    path: 'record-room/outbox/:documentId',
    element: <RecordRoomViewOutboxDetails />
  },
  {
    path: 'record-room/:documentId',
    element: <RecordRoomViewInboxDetails />
  }

  // {
  //   path: 'record-room/in-hand-records',
  //   element: <RecordRoomList />
  // },
  // {
  //   path: 'record-room/requested',
  //   element: <RecordRoomList />
  // },
  // {
  //   path: 'record-room/sent-records',
  //   element: <RecordRoomList />
  // }
];

export { routes };
