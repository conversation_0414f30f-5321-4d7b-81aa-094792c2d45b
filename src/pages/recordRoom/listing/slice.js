import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { FILE_STATUS_FOR_API_PARAMS, INWARD_STATUS } from 'pages/common/constants';
import { STATE_REDUCER_KEY } from './constants';
import { ACTION_TYPES } from './actions';

const initialState = {
  reassignUserTableListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    statusList: FILE_STATUS_FOR_API_PARAMS.SUSPENDED
  },
  reassignFiles: [],
  otpStatus: false,
  otpSendResponse: '',
  tableListParams: {
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    status: INWARD_STATUS.COMPLETED,
    recordStatus: INWARD_STATUS.RECORD_RECEIVED,
    page: 0,
    size: 10,
    sortDirection: 'desc'
  },
  inwardsDetailsParams: {
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID)
  },
  postByRecieverFunctionalGroup: [],
  postByMessengerFunctionalGroup: []
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setReassignUserTableList: (state, { payload }) => {
      _.set(state, 'reassignUserTableList', payload);
    },
    setReassignUserTableListParams: (state, { payload }) => {
      _.set(state, 'reassignUserTableListParams', payload);
    },
    setReassignFiles: (state, { payload }) => {
      _.set(state, 'reassignFiles', payload);
    },
    setOtpStatus: (state, { payload }) => {
      _.set(state, 'otpStatus', payload);
    },
    setTableListParams: (state, { payload }) => {
      _.set(state, 'tableListParams', payload);
    },
    setInboxTableList: (state, { payload }) => {
      _.set(state, 'inboxTableList', payload);
    },
    setOutboxTableList: (state, { payload }) => {
      _.set(state, 'outboxTableList', payload);
    },
    setInwardsDetailsParams: (state, { payload }) => {
      _.set(state, 'inwardsDetailsParams', payload);
    },
    setInwardsDetailsTableList: (state, { payload }) => {
      _.set(state, 'inwardsDetailsTableList', payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS, (state, { payload }) => {
        _.set(state, 'functionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_RECIEVER_FUNC_GROUP_SUCCESS, (state, { payload }) => {
        _.set(state, 'postByRecieverFunctionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_LOCATION_RECIEVER_SUCCESS, (state, { payload }) => {
        _.set(state, 'postByRecieverFunctionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_MESSENGER_FUNC_GROUP_SUCCESS, (state, { payload }) => {
        _.set(state, 'postByMessengerFunctionalGroup', payload);
      })
      .addCase(ACTION_TYPES.FETCH_POST_BY_LOCATION_MESSENGER_SUCCESS, (state, { payload }) => {
        _.set(state, 'postByMessengerFunctionalGroup', payload);
      })
      .addCase(ACTION_TYPES.SEND_OTP_SUCCESS, (state, { payload }) => {
        _.set(state, 'otpSendResponse', payload.data.otp.UUID);
      })
      .addCase(ACTION_TYPES.VERIFY_OTP_SUCCESS, (state, { payload }) => {
        _.set(state, 'otpVerifyResponse', payload.data);
      });
  }
});

export const { actions, reducer } = slice;
