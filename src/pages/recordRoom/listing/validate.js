import * as yup from 'yup';
import { NUMERIC } from 'common/regex';
import { t } from '../../../common/components';

export const AssignFormSchema = yup.object({
  recieverRouteType: yup.string()
    .required(t('isRequired', { type: `${t('unit')}` })),
  recieverTypeOfOffice: yup.string()
    .required(t('isRequired', { type: `${t('typeOfOffice')}` })),
  recieverFunctionalGroup: yup.string()
    .required(t('isRequired', { type: `${t('functionalGroup')}` })),
  sendTo: yup.string()
    .required(t('isRequired', { type: `${t('send')} ${t('to')}` })),

  messengerRouteType: yup.string()
    .required(t('isRequired', { type: `${t('unit')}` })),
  messengerTypeOfOffice: yup.string()
    .required(t('isRequired', { type: `${t('typeOfOffice')}` })),
  messengerFunctionalGroup: yup.string()
    .required(t('isRequired', { type: `${t('functionalGroup')}` })),
  selectMessenger: yup.string()
    .required(t('isRequired', { type: `${t('select')} ${t('messenger')}` }))
}).required();

export const otpVerificationSchema = yup.object({
  otp: yup
    .string()
    .required(t('isRequired', { type: t('otp') }))
    .min(
      6,
      t('mustBeAtLeast', {
        type: t('otp'),
        count: 6,
        unit: t('digits')
      })
    )
    .max(
      6,
      t('mustBeAtLeast', {
        type: t('otp'),
        count: 6,
        unit: t('digits')
      })
    )
    .matches(NUMERIC, t('invalidType', { type: t('otp') }))
});
