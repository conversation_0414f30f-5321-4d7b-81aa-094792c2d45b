import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_RE_ASSIGN_USER_LIST: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST`,
  FETCH_RE_ASSIGN_USER_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST_REQUEST`,
  FETCH_RE_ASSIGN_USER_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST_SUCCESS`,
  FETCH_RE_ASSIGN_USER_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST_FAILURE`,

  RE_ASSIGN_FILE: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE`,
  RE_ASSIGN_FILE_REQUEST: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE_REQUEST`,
  RE_ASSIGN_FILE_SUCCESS: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE_SUCCESS`,
  RE_ASSIGN_FILE_FAILURE: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE_FAILURE`,

  FETCH_INBOX: `${STATE_REDUCER_KEY}/FETCH_INBOX`,
  FETCH_INBOX_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INBOX_REQUEST`,
  FETCH_INBOX_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INBOX_SUCCESS`,
  FETCH_INBOX_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INBOX_FAILURE`,

  FETCH_OUTBOX: `${STATE_REDUCER_KEY}/FETCH_OUTBOX`,
  FETCH_OUTBOX_REQUEST: `${STATE_REDUCER_KEY}/FETCH_OUTBOX_REQUEST`,
  FETCH_OUTBOX_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_OUTBOX_SUCCESS`,
  FETCH_OUTBOX_FAILURE: `${STATE_REDUCER_KEY}/FETCH_OUTBOX_FAILURE`,

  FETCH_INWARD_DETAILS: `${STATE_REDUCER_KEY}/FETCH_INWARD_DETAILS`,
  FETCH_INWARD_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_INWARD_DETAILS_REQUEST`,
  FETCH_INWARD_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_INWARD_DETAILS_SUCCESS`,
  FETCH_INWARD_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_INWARD_DETAILS_FAILURE`,

  SEND_OTP: `${STATE_REDUCER_KEY}/SEND_OTP_INBOX`,
  SEND_OTP_REQUEST: `${STATE_REDUCER_KEY}/SEND_OTP_REQUEST`,
  SEND_OTP_SUCCESS: `${STATE_REDUCER_KEY}/SEND_OTP_SUCCESS`,
  SEND_OTP_FAILURE: `${STATE_REDUCER_KEY}/SEND_OTP_FAILURE`,

  VERIFY_OTP: `${STATE_REDUCER_KEY}/VERIFY_OTP`,
  VERIFY_OTP_REQUEST: `${STATE_REDUCER_KEY}/VERIFY_OTP_REQUEST`,
  VERIFY_OTP_SUCCESS: `${STATE_REDUCER_KEY}/VERIFY_OTP_SUCCESS`,
  VERIFY_OTP_FAILURE: `${STATE_REDUCER_KEY}/VERIFY_OTP_FAILURE`,

  ASSIGN_RECORD_ROOM_FILE: `${STATE_REDUCER_KEY}/ASSIGN_RECORD_ROOM_FILE`,
  ASSIGN_RECORD_ROOM__REQUEST: `${STATE_REDUCER_KEY}/ASSIGN_RECORD_ROOM__REQUEST`,
  ASSIGN_RECORD_ROOM__SUCCESS: `${STATE_REDUCER_KEY}/ASSIGN_RECORD_ROOM__SUCCESS`,
  ASSIGN_RECORD_ROOM__FAILURE: `${STATE_REDUCER_KEY}/ASSIGN_RECORD_ROOM__FAILURE`,

  FETCH_DEPARTMENTS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS`,
  FETCH_DEPARTMENTS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_REQUEST`,
  FETCH_DEPARTMENTS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_SUCCESS`,
  FETCH_DEPARTMENTS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DEPARTMENTS_FAILURE`,

  FETCH_POST_BY_LOCATION_RECIEVER: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_RECIEVER`,
  FETCH_POST_BY_LOCATION_RECIEVER_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_RECIEVER_REQUEST`,
  FETCH_POST_BY_LOCATION_RECIEVER_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_RECIEVER_SUCCESS`,
  FETCH_POST_BY_LOCATION_RECIEVER_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_RECIEVER_FAILURE`,

  FETCH_POST_BY_RECIEVER_FUNC_GROUP: `${STATE_REDUCER_KEY}/FETCH_POST_BY_RECIEVER_FUNC_GROUP`,
  FETCH_POST_BY_RECIEVER_FUNC_GROUP_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_RECIEVER_FUNC_GROUP_REQUEST`,
  FETCH_POST_BY_RECIEVER_FUNC_GROUP_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_RECIEVER_FUNC_GROUP_SUCCESS`,
  FETCH_POST_BY_RECIEVER_FUNC_GROUP_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_RECIEVER_FUNC_GROUP_FAILURE`,

  FETCH_POST_BY_LOCATION_MESSENGER: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_MESSENGER`,
  FETCH_POST_BY_LOCATION_MESSENGER_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_MESSENGER_REQUEST`,
  FETCH_POST_BY_LOCATION_MESSENGER_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_MESSENGER_SUCCESS`,
  FETCH_POST_BY_LOCATION_MESSENGER_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_LOCATION_MESSENGER_FAILURE`,

  FETCH_POST_BY_MESSENGER_FUNC_GROUP: `${STATE_REDUCER_KEY}/FETCH_POST_BY_MESSENGER_FUNC_GROUP`,
  FETCH_POST_BY_MESSENGER_FUNC_GROUP_REQUEST: `${STATE_REDUCER_KEY}/FETCH_POST_BY_MESSENGER_FUNC_GROUP_REQUEST`,
  FETCH_POST_BY_MESSENGER_FUNC_GROUP_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_POST_BY_MESSENGER_FUNC_GROUP_SUCCESS`,
  FETCH_POST_BY_MESSENGER_FUNC_GROUP_FAILURE: `${STATE_REDUCER_KEY}/FETCH_POST_BY_MESSENGER_FUNC_GROUP_FAILURE`
};

export const fetchReassignUserTableList = createAction(ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST);
export const reassignFileApi = createAction(ACTION_TYPES.RE_ASSIGN_FILE);

export const fetchInboxData = createAction(ACTION_TYPES.FETCH_INBOX);
export const fetchOutboxData = createAction(ACTION_TYPES.FETCH_OUTBOX);

export const fetchInwardDetails = createAction(ACTION_TYPES.FETCH_INWARD_DETAILS);

export const sendOtp = createAction(ACTION_TYPES.SEND_OTP);
export const verifyOtp = createAction(ACTION_TYPES.VERIFY_OTP);

export const assignRecordRoom = createAction(ACTION_TYPES.ASSIGN_RECORD_ROOM_FILE);

export const fetchFunctionalGroupDropdown = createAction(ACTION_TYPES.FETCH_DEPARTMENTS);

export const fetchPostByLocationRecieverList = createAction(ACTION_TYPES.FETCH_POST_BY_LOCATION_RECIEVER);
export const fetchPostByRecieverFunctionalGroup = createAction(ACTION_TYPES.FETCH_POST_BY_RECIEVER_FUNC_GROUP);

export const fetchPostByLocationMessengerList = createAction(ACTION_TYPES.FETCH_POST_BY_LOCATION_MESSENGER);
export const fetchPostByMessengerFunctionalGroup = createAction(ACTION_TYPES.FETCH_POST_BY_MESSENGER_FUNC_GROUP);
