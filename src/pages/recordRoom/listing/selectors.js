import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getRecordRoomDetails = (state) => state[STATE_REDUCER_KEY];

// reassign selector

const reassignUserListData = (state) => state?.reassignUserTableList;
export const getReassignUserListData = flow(getRecordRoomDetails, reassignUserListData);

const reassignUserSearchListParams = (state) => state?.reassignUserTableListParams;
export const getReassignUserSearchListParams = flow(getRecordRoomDetails, reassignUserSearchListParams);

const reassignFiles = (state) => state?.reassignFiles;
export const getReassignFiles = flow(getRecordRoomDetails, reassignFiles);

// params selector
const tableListParams = (state) => state?.tableListParams;
export const getTableListParams = flow(getRecordRoomDetails, tableListParams);

const inwardsDetailsParams = (state) => state?.inwardsDetailsParams;
export const getInwardsDetailsParams = flow(getRecordRoomDetails, inwardsDetailsParams);

// record-room inbox selector

const inboxTableListData = (state) => state?.inboxTableList;
export const getInboxTableListData = flow(getRecordRoomDetails, inboxTableListData);

// record-room outbox selector

const outboxTableListData = (state) => state?.outboxTableList;
export const getOutboxTableListData = flow(getRecordRoomDetails, outboxTableListData);

// record-room inwards details selector

const inwardsDetailsTableListData = (state) => state?.inwardsDetailsTableList;
export const getInwardsDetailsTableListData = flow(getRecordRoomDetails, inwardsDetailsTableListData);

// otp response selectors

const otpSendResponse = (state) => state?.otpSendResponse;
export const getOtpSendResponse = flow(getRecordRoomDetails, otpSendResponse);

const otpVerifyResponse = (state) => state?.otpVerifyResponse;
export const getOtpVerifyResponse = flow(getRecordRoomDetails, otpVerifyResponse);

// functional group selectors

const functionalGroupDropdown = (state) => state.functionalGroup || [];
export const getFunctionalGroupDropdown = flow(getRecordRoomDetails, functionalGroupDropdown);

// assign reciever selectors

const postByRecieverFunctionalGroup = (state) => state.postByRecieverFunctionalGroup || [];
export const getPostByRecieverFunctionalGroup = flow(getRecordRoomDetails, postByRecieverFunctionalGroup);

// assign messenger selectors

const postByMessengerFunctionalGroup = (state) => state.postByMessengerFunctionalGroup || [];
export const getPostByMessengerFunctionalGroup = flow(getRecordRoomDetails, postByMessengerFunctionalGroup);
