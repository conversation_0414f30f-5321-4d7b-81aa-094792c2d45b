import {
  all, takeLatest, fork, put, take, select, call
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'common/components';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import {
  getInwardsDetailsParams,
  getReassignUserSearchListParams,
  getTableListParams
} from './selectors';
import { INWARD_STATUS } from '../../common/constants';

export function* fetchReassignUserTableList() {
  const apiParams = yield select(getReassignUserSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchReassignUserTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_SUCCESS,
    ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_SUCCESS) {
    yield put(sliceActions.setReassignUserTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 're-assign-user-file-table' }));
  }
}

export function* reassignFileApi({ payload }) {
  yield fork(handleAPIRequest, api.reassignFileApi, payload);
  const { type } = yield take([
    ACTION_TYPES.RE_ASSIGN_FILE_SUCCESS,
    ACTION_TYPES.RE_ASSIGN_FILE_FAILURE]);
  if (type === ACTION_TYPES.RE_ASSIGN_FILE_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 're-assign-file' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'success',
        message: t('reassignSuccessfully'),
        title: t('success'),
        backwardActionText: t('ok')
      })
    );
    yield call(fetchReassignUserTableList);
    yield put(sliceActions.setReassignFiles([]));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 're-assign-file' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: t('reassignFailed'),
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
    yield put(sliceActions.setReassignFiles([]));
  }
}

export function* fetchInboxTableList() {
  const apiParams = yield select(getTableListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchInboxTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_INBOX_SUCCESS,
    ACTION_TYPES.FETCH_INBOX_FAILURE]);
  if (type === ACTION_TYPES.FETCH_INBOX_SUCCESS) {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'record-room-inbox-table' }));
    yield put(sliceActions.setInboxTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'record-room-inbox-table' }));
  }
}

export function* fetchOutboxTableList() {
  const apiParams = yield select((state) => ({
    ...getTableListParams(state),
    recordStatus: INWARD_STATUS.HANDED_OVER_TO_MESSENGER
  }));
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchOutboxTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_OUTBOX_SUCCESS,
    ACTION_TYPES.FETCH_OUTBOX_FAILURE]);
  if (type === ACTION_TYPES.FETCH_OUTBOX_SUCCESS) {
    yield put(sliceActions.setOutboxTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'record-room-outbox-table' }));
  }
}

export function* fetchInwardDetails() {
  const apiParams = yield select(getInwardsDetailsParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  yield fork(handleAPIRequest, api.fetchInwardDetails, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_INWARD_DETAILS_SUCCESS,
    ACTION_TYPES.FETCH_INWARD_DETAILS_FAILURE
  ]);
  if (type === ACTION_TYPES.FETCH_INWARD_DETAILS_SUCCESS) {
    yield put(sliceActions.setInwardsDetailsTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'inwards-physical-docs-table' }));
  }
}

export function* sendOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.sendOtp, payload);
  const { mobileNo } = payload;
  const { type } = yield take([
    ACTION_TYPES.SEND_OTP_SUCCESS,
    ACTION_TYPES.SEND_OTP_FAILURE
  ]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'otpsend' }));
  if (type === ACTION_TYPES.SEND_OTP_SUCCESS) {
    yield put(commonSliceActions.setOtpStatus(true));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'success', message: `${t('otpSuccessfullySendto')} ${mobileNo}`, title: t('success'), backwardActionText: t('ok')
    }));
  }
  if (type === ACTION_TYPES.SEND_OTP_FAILURE) {
    yield put(commonSliceActions.setOtpStatus(false));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('otpSendingFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* verifyOtp({ payload = {} }) {
  yield fork(handleAPIRequest, api.verifyOtp, payload);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.VERIFY_OTP_SUCCESS,
    ACTION_TYPES.VERIFY_OTP_FAILURE
  ]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'otpverify' }));
  if (type === ACTION_TYPES.VERIFY_OTP_SUCCESS) {
    if (responsePayLoad?.data) {
      yield put(commonSliceActions.setOtpStatus(false));
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'success', message: t('otpSuccessfullyVerified'), title: t('success'), backwardActionText: t('ok')
      }));
    } else {
      yield put(commonSliceActions.setOtpStatus(true));
      yield put(commonSliceActions.setAlertAction({
        open: true, variant: 'error', message: t('otpVerificationFailed'), title: t('failed'), backwardActionText: t('ok')
      }));
    }
  }
  if (type === ACTION_TYPES.VERIFY_OTP_FAILURE) {
    yield put(commonSliceActions.setOtpStatus(true));
    yield put(commonSliceActions.setAlertAction({
      open: true, variant: 'error', message: t('otpVerificationFailed'), title: t('failed'), backwardActionText: t('ok')
    }));
  }
}

export function* fetchFunctionalGroupDropdown({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchFunctionalGroupDropdown, payload);
}

export function* fetchPostByLocationRecieverList({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostByLocationRecieverList, payload);
}

export function* fetchPostByRecieverFunctionalGroup({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostByRecieverFunctionalGroup, payload);
}

export function* fetchPostByLocationMessengerList({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostByLocationMessengerList, payload);
}

export function* fetchPostByMessengerFunctionalGroup({ payload = {} }) {
  yield call(handleAPIRequest, api.fetchPostByMessengerFunctionalGroup, payload);
}

export function* assignRecordRoom({ payload = {} }) {
  const { inwardId } = payload;
  yield fork(handleAPIRequest, api.assignRecordRoom, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.ASSIGN_RECORD_ROOM__SUCCESS,
    ACTION_TYPES.ASSIGN_RECORD_ROOM__FAILURE
  ]);

  if (type === ACTION_TYPES.ASSIGN_RECORD_ROOM__SUCCESS) {
    if (responsePayLoad?.data && inwardId) {
      // yield put(commonSliceActions.navigateTo({ to: `${BASE_PATH}/citizen/e-file/${serviceCode}/${responsePayLoad.data}?kswiftId=${kswiftId}` }));
    } else {
      // yield put(commonSliceActions.navigateTo({ to: `${BASE_PATH}/citizen/e-file/${serviceCode}/${responsePayLoad.data}` }));
    }
  }
}

export default function* recordRoomSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST, fetchReassignUserTableList),
    takeLatest(ACTION_TYPES.RE_ASSIGN_FILE, reassignFileApi),
    takeLatest(ACTION_TYPES.FETCH_INBOX, fetchInboxTableList),
    takeLatest(ACTION_TYPES.FETCH_OUTBOX, fetchOutboxTableList),
    takeLatest(ACTION_TYPES.FETCH_INWARD_DETAILS, fetchInwardDetails),
    takeLatest(ACTION_TYPES.SEND_OTP, sendOtp),
    takeLatest(ACTION_TYPES.VERIFY_OTP, verifyOtp),
    takeLatest(ACTION_TYPES.ASSIGN_RECORD_ROOM_FILE, assignRecordRoom),

    takeLatest(ACTION_TYPES.FETCH_DEPARTMENTS, fetchFunctionalGroupDropdown),

    takeLatest(ACTION_TYPES.FETCH_POST_BY_LOCATION_RECIEVER, fetchPostByLocationRecieverList),
    takeLatest(ACTION_TYPES.FETCH_POST_BY_RECIEVER_FUNC_GROUP, fetchPostByRecieverFunctionalGroup),

    takeLatest(ACTION_TYPES.FETCH_POST_BY_LOCATION_MESSENGER, fetchPostByLocationMessengerList),
    takeLatest(ACTION_TYPES.FETCH_POST_BY_MESSENGER_FUNC_GROUP, fetchPostByMessengerFunctionalGroup)
  ]);
}
