import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getReassignUserListData, getReassignUserSearchListParams } from '../selectors';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import RecordRoomTab from './RecordRoomTab';
import { RECORD_ROOM_TABS } from '../constants';

import RecordsTable from './RecordsTable';

const List = ({
  fetchReassignUserTableList, reassignUserListParams,
  setTableLoader, tableLoader
}) => {
  const today = new Date().toISOString().split('T')[0];
  const [activeTab, setActiveTab] = useState(RECORD_ROOM_TABS.INBOX);
  const [page, setPage] = useState(0);
  const [search, setSearch] = useState('');
  const [date, setDate] = useState();

  useEffect(() => {
    if (reassignUserListParams) {
      setTableLoader({ loading: true, id: 'inbox-file-table' });
      fetchReassignUserTableList();
    }
  }, [reassignUserListParams]);

  // const triggerSearch = (field, data) => {
  //   setPage(0);
  //   switch (field) {
  //     case FILTER_TYPE.SEARCH_KEY_WORD:
  //       setReassignUserTableListParams({
  //         ...reassignUserListParams,
  //         keyword: data || null,
  //         page: 0
  //       });
  //       break;
  //     case FILTER_TYPE.SERVICES:
  //       setReassignUserTableListParams({
  //         ...reassignUserListParams,
  //         serviceCode: data?.code || null,
  //         page: 0
  //       });
  //       break;
  //     default:
  //       break;
  //   }
  // };

  return (
    <>
      <RecordRoomTab
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        search={search}
        setSearch={setSearch}
        date={date}
        setDate={setDate}
        today={today}
      />
      <RecordsTable
        setPage={setPage}
        page={page}
        tableLoader={tableLoader}
        setTableLoader={setTableLoader}
        activeTab={activeTab}
        filteredDate={date}
        filteredSearchKey={search}
      />

    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  reassignUserTableList: getReassignUserListData,
  reassignUserListParams: getReassignUserSearchListParams,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchReassignUserTableList: (data) => dispatch(actions.fetchReassignUserTableList(data)),
  setReassignUserTableListParams: (data) => dispatch(sliceActions.setReassignUserTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
