import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getTableLoader, getUserInfo } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import TableView from 'assets/TableView';
import { t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { getInboxTableListData, getOutboxTableListData, getTableListParams } from '../selectors';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import RecordRoomTab from './RecordRoomTab';
import { RECORD_ROOM_TABS } from '../constants';
import { FILTER_TYPE } from '../../../common/constants';

const RecordRoom = ({
  tableLoader, setTableLoader, fetchInboxTableList, inboxTableListData, fetchOutboxTableList, outboxTableListData, tableListParams, setTableListParams
}) => {
  const navigate = useNavigate();
  const today = new Date().toISOString().split('T')[0];
  const [activeTab, setActiveTab] = useState(RECORD_ROOM_TABS.INBOX);
  const [page, setPage] = useState(0);
  const [search, setSearch] = useState('');
  const [date, setDate] = useState();
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState();
  const [numberOfElements, setNumberOfElements] = useState(0);

  const applyingData = (data, tableId) => {
    setTableLoader({ loading: true, id: `${tableId}` });
    if (data) {
      setTableLoader({ loading: true, id: `${tableId}` });
      if (Object.keys(data).length > 0) {
        setTableData(data?.content);
        setTotalItems(Number(`${data?.totalPages}0`));
        setNumberOfElements(Number(data?.numberOfElements));
        setTableLoader({ loading: false, id: `${tableId}` });
      } else {
        setTableLoader({ loading: false, id: `${tableId}` });
        setTableData([]);
        setTotalItems(0);
      }
    }
  };

  useEffect(() => {
    switch (activeTab) {
      case 'INBOX':
        fetchInboxTableList();
        break;
      case 'OUTBOX':
        fetchOutboxTableList();
        break;
      default:
        break;
    }
  }, [activeTab, tableListParams]);

  // useEffect(() => {
  //   switch (activeTab) {
  //     case 'INBOX':
  //       setTableLoader({ loading: true, id: 'record-room-inbox-table' });
  //       if (inboxTableListData?.content.length > 0) applyingData(inboxTableListData, 'record-room-inbox-table');
  //       else applyingData([], 'record-room-inbox-table'); // sampleInwardTableData
  //       break;
  //     case 'OUTBOX':
  //       setTableLoader({ loading: true, id: 'record-room-outbox-table' });
  //       if (outboxTableListData?.content.length > 0) applyingData(outboxTableListData, 'record-room-outbox-table');
  //       else applyingData([], 'record-room-outbox-table'); // sampleInwardTableData
  //       break;
  //     default:
  //       break;
  //   }
  // }, [activeTab, inboxTableListData, outboxTableListData]);

  useEffect(() => {
    if (activeTab === 'INBOX') {
      setTableLoader({ loading: true, id: 'record-room-inbox-table' });
      if (inboxTableListData?.content.length > 0) applyingData(inboxTableListData, 'record-room-inbox-table');
      else applyingData([], 'record-room-inbox-table'); // sampleInwardTableData
    }
  }, [activeTab, inboxTableListData]);

  useEffect(() => {
    if (activeTab === 'OUTBOX') {
      setTableLoader({ loading: true, id: 'record-room-outbox-table' });
      if (outboxTableListData?.content.length > 0) applyingData(outboxTableListData, 'record-room-outbox-table');
      else applyingData([], 'record-room-outbox-table'); // sampleInwardTableData
    }
  }, [activeTab, outboxTableListData]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setTableListParams({
          ...tableListParams,
          searchKeyword: data || null,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setTableListParams({
          ...tableListParams,
          inwardDate: data || null,
          page: 0
        });
        break;
      default:
        break;
    }
  };

  const onPageClick = (data) => {
    setPage(data);
    setTableListParams({
      ...tableListParams,
      page: data
    });
  };

  const handleInwardNumber = (inwardData) => {
    let inwardsNumber;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      inwardsNumber = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.inwardNo}</div>
      );
    }
    return <div className="block">{inwardsNumber}</div>;
  };

  const handleInwardDate = (inwardData) => {
    let inwardsDate;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      inwardsDate = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.inwardDate}</div>
      );
    }
    return <div className="block">{inwardsDate}</div>;
  };

  const handleApplicantName = (inwardData) => {
    let applicantName;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData?.applicantNames?.filter((item) => item && item !== 'null')?.join(', ')}</div>;
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleServiceName = (inwardData) => {
    let serviceName;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      serviceName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.serviceName}</div>
      );
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleNoOfPhysicalDocument = (inwardData) => {
    let noOfPhysicalDocument;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      noOfPhysicalDocument = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.numberOfPhysicalDocuments}</div>
      );
    }
    return <div className="block">{noOfPhysicalDocument}</div>;
  };

  const columns = [
    {
      header: t('slNo'),
      cell: ({ rowIndex }) => rowIndex + 1,
      alignment: 'left'
    },
    {
      header: t('inwardNumber'),
      field: 'inwardNumber',
      alignment: 'left',
      cell: (field) => handleInwardNumber(field)
    },
    {
      header: t('inwardDate'),
      field: 'inwardDate',
      alignment: 'left',
      cell: (field) => handleInwardDate(field)
    },
    {
      header: t('applicantName'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('servicee'),
      field: 'servicee',
      cell: (field) => handleServiceName(field)
    },
    {
      header: `${t('noOf')} ${t('physicalDocument')}`,
      field: 'noOfPhysicalDocument',
      cell: (field) => handleNoOfPhysicalDocument(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            navigate(`${row?.documentId}`);
          }
        }
      ]
    }
  ];

  return (
    <div className="mb-10">
      <RecordRoomTab
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        search={search}
        setSearch={setSearch}
        date={date}
        setDate={setDate}
        today={today}
        triggerSearch={triggerSearch}
      />
      <div className="col-span-12">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === `${activeTab === 'INBOX' ? 'record-room-inbox-table' : 'record-room-outbox-table'}`}
        />
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  tableLoader: getTableLoader,
  tableListParams: getTableListParams,
  inboxTableListData: getInboxTableListData,
  outboxTableListData: getOutboxTableListData
});

const mapDispatchToProps = (dispatch) => ({
  setTableListParams: (data) => dispatch(sliceActions.setTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  fetchInboxTableList: (data) => dispatch(actions.fetchInboxData(data)),
  fetchOutboxTableList: (data) => dispatch(actions.fetchOutboxData(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(RecordRoom);
