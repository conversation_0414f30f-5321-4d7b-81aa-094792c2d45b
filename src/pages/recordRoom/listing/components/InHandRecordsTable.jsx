import TableView from 'assets/TableView';
import { IconButton, t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { BASE_UI_PATH } from 'common/constants';
import React, { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

const InHandRecordsTable = ({
  page, setPage, tableLoader, setTableLoader
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const onPageClick = (data) => {
    setPage(data);
    // setReassignUserTableListParams({
    //   ...reassignUserListParams,
    //   page: data,
    //   officeId: userInfo.id
    // });
    setTotalItems();
    setTableData();
    setNumberOfElements();
    setTableLoader();
  };

  // useEffect(() => {
  //   if (reassignUserTableList) {
  //     setTableLoader({ loading: false, id: 're-assign-user-file-table' });
  //     if (Object.keys(reassignUserTableList).length > 0) {
  //       setTableData(reassignUserTableList.content);
  //       setTotalItems(Number(`${reassignUserTableList.totalPages}0`));
  //       setNumberOfElements(Number(reassignUserTableList.numberOfElements));
  //     } else {
  //       setTableData([]);
  //       setTotalItems(0);
  //     }
  //   }
  // }, [reassignUserTableList]);

  const viewActions = (data) => {
    window.location = `${BASE_UI_PATH}${data?.url}`;
  };

  const handleDesignatedSeat = (val) => {
    let designatedSeat;
    if (val?.row) {
      const cellData = val?.row;
      designatedSeat = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.designatedSeat}</div>
      );
    }
    return <div className="inline-block">{designatedSeat}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNumber = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
      );
    }
    return <div className="block">{fileNumber}</div>;
  };

  const handleDocumentName = (fileData) => {
    let documentName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      documentName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData?.documentName}</div>;
    }
    return <div className="block">{documentName}</div>;
  };

  const handleDocumentNo = (fileData) => {
    let documentNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      documentNo = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.documentNo}</div>
      );
    }
    return <div className="block">{documentNo}</div>;
  };

  const handleCurrentLocation = (fileData) => {
    let currentLocation;
    if (fileData?.row) {
      const cellData = fileData?.row;
      currentLocation = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.currentLocation}</div>
      );
    }
    return <div className="block">{currentLocation}</div>;
  };
  const handleArrivalDate = (fileData) => {
    let arrivalDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      arrivalDate = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.arrivalDate}</div>
      );
    }
    return <div className="block">{arrivalDate}</div>;
  };

  const actionsTable = (row) => {
    return (
      <div className="flex gap-3">
        <IconButton variant="ghost" onClick={() => viewActions(row?.row)} icon={<TableView />} />
      </div>
    );
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('documentNo'),
      field: 'documentNo',
      alignment: 'left',
      cell: (field) => handleDocumentNo(field)
    },
    {
      header: t('documentName'),
      field: 'documentName',
      alignment: 'left',
      cell: (field) => handleDocumentName(field)
    },
    {
      header: t('designatedSeat'),
      field: 'designatedSeat',
      cell: (field) => handleDesignatedSeat(field)
    },
    {
      header: t('currentLocation'),
      field: 'currentLocation',
      cell: (field) => handleCurrentLocation(field)
    },
    {
      header: t('arrivalDate'),
      field: 'arrivalDate',
      cell: (field) => handleArrivalDate(field)
    },
    {
      header: t('Actions'),
      alignment: 'left',
      cell: (field) => actionsTable(field)
    }

  ];

  return (
    <div>
      <div className="col-span-12 mt-3">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'in-hand-records-table'}
        />

      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
});

const mapDispatchToProps = () => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(InHandRecordsTable);
