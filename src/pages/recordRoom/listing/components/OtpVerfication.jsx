import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import { actions as commonSliceActions } from 'pages/common/slice';
import { But<PERSON>, FormController } from 'common/components';
import { t } from 'i18next';
import { getActionTriggered, getOtpStatus } from 'pages/common/selectors';
import { otpVerificationSchema } from '../validate';
import { getOtpSendResponse, getOtpVerifyResponse } from '../selectors';
import * as actions from '../actions';

const OtpVerfication = (props) => {
  const {
    formData,
    actionTriggered,
    verifyOtp,
    resendOtp,
    setActionTriggered,
    otpSendResponse,
    assignRecordRoom,
    otpVerifyResponse,
    otpStatus = true
  } = props;

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues
  } = useForm({
    mode: 'all',
    defaultValues: {
      otp: null
    },
    resolver: yupResolver(otpVerificationSchema)
  });

  const onSubmitForm = () => {
  };

  const handleFormSubmition = () => {
    const assignFormParams = formData;
    assignRecordRoom(assignFormParams);
  };

  const handleVerifyOtp = () => {
    setActionTriggered({ loading: true, id: 'otpVerify' });
    const verifyData = {
      inwardId: formData?.inwardId,
      data: {
        recordIds: formData.recordIds,
        id: otpSendResponse,
        otp: getValues('otp'),
        mobileNo: formData.messenger.mobileNo
      }
    };
    verifyOtp(verifyData);
  };

  const handleReSendOtp = () => {
    setActionTriggered({ loading: true, id: 'resendOtp' });
    resendOtp({ inwardId: formData?.inwardNo, mobileNo: formData?.messanger?.mobileNo });
  };

  useEffect(() => {
    if (otpVerifyResponse) {
      handleFormSubmition();
    }
  }, [otpVerifyResponse]);

  return (
    <form
      id="assign-form_otp"
      onSubmit={handleSubmit(onSubmitForm)}
    >
      {otpStatus && (
        <div className="flex justify-end gap-4 flex-col">
          <div id="assign-form_otp" />
          <FormController
            style={{ padding: '0 100px 0 10px' }}
            name="otp"
            type="number"
            label={t('otp')}
            control={control}
            errors={errors}
            required
            rightContent={(
              <Button
                isLoading={actionTriggered?.id === 'otpVerify' && actionTriggered?.loading}
                onClick={() => handleVerifyOtp()}
                variant="secondary"
                size="lg"
                type="submit"
              >
                {t('submit')}
              </Button>
            )}
          />
          <Button
            type="button"
            onClick={() => handleReSendOtp()}
            variant="link"
            className="flex gap-2 items-center"
            isLoading={actionTriggered?.id === 'resendOtp' && actionTriggered?.loading}
          >
            {t('reSendOtp')}
          </Button>
        </div>
      )}
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  otpStatus: getOtpStatus,
  actionTriggered: getActionTriggered,
  otpSendResponse: getOtpSendResponse,
  otpVerifyResponse: getOtpVerifyResponse
});

const mapDispatchToProps = (dispatch) => ({
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  verifyOtp: (data) => dispatch(actions.verifyOtp(data)),
  sendOtp: (data) => dispatch(actions.sendOtp(data)),
  assignRecordRoom: (data) => dispatch(actions.assignRecordRoom(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(OtpVerfication);
