import React, {
  useState, useEffect, useCallback
} from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import View from 'assets/View';
import { t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { dark } from 'utils/color';
import { STORAGE_KEYS } from 'common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  getActionTriggered, getTableLoader, getUserInfo
} from 'pages/common/selectors';
import { documenturl } from '../constants';
import {
  getInwardsDetailsParams,
  getInwardsDetailsTableListData
} from '../selectors';
import PreviewDocs from './PreviewDocs';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';

const ViewOutboxDetails = (props) => {
  const {
    tableLoader,
    setTableLoader,
    userInfo,
    inwardsDetailsParams,
    setInwardsDetailsParams,
    fetchInwardDetails,
    inwardsDetailsTableListData
  } = props;

  const navigate = useNavigate();
  const { documentId } = useParams();
  const activeRows = [{}];
  const active = 'outbox';

  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [docPreview, setDocPreview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [previewItem, setPreviewItem] = useState(null);
  const [inwardNo, setInwardNo] = useState('');
  const [inwardId, setInwardId] = useState('');

  useEffect(() => {
    if (inwardsDetailsParams) {
      fetchInwardDetails();
    }
  }, [inwardsDetailsParams]);
  useEffect(() => {
    if (documentId) {
      setInwardsDetailsParams({
        ...inwardsDetailsParams,
        documentId
      });
    }
  }, [documentId]);
  useEffect(() => {
    if (inwardsDetailsTableListData?.content) {
      setTableLoader({ loading: true, id: 'inwards-physical-docs-table' });
      if (Object.keys(inwardsDetailsTableListData?.content).length > 0) {
        const contentData = inwardsDetailsTableListData?.content[0];
        let madatoryDocuments = [];
        let supportingDocumnets = [];
        if (contentData.documentTypeDocs !== null) madatoryDocuments = [...Object.values(contentData.documentTypeDocs)];
        if (contentData.supportingDocs !== null) supportingDocumnets = contentData.supportingDocs.map((supportDoc) => supportDoc.uploadedDocumentInfo);

        setTableLoader({ loading: false, id: 'inwards-physical-docs-table' });
        setTableData([...madatoryDocuments, ...supportingDocumnets]);
        setInwardNo(contentData?.inwardNo);
        setInwardId(contentData?.inwardId);
        setTotalItems(Number(`${inwardsDetailsTableListData?.totalPages}0`));
        setNumberOfElements(Number(inwardsDetailsTableListData?.numberOfElements));
      } else {
        setTableLoader({ loading: false, id: 'inwards-physical-docs-table' });
        setInwardNo(''); // sampleViewInwardTableData?.inwardNo
        setTableData([]); // const addMandaroryData = addMandatoryKey(sampleViewInwardTableData); [...Object.values(addMandaroryData?.documentTypeDocs), ...addMandaroryData.supportingDocs]
        setNumberOfElements(0); // sampleViewInwardTableData.supportingDocs.length + Object.values(sampleViewInwardTableData.documentTypeDocs).length
        setTotalItems(0);
      }
    }
  }, [inwardsDetailsTableListData]);

  const goToBack = () => navigate(`/ui/file-management/services/record-room/${active}`);
  const onPageClick = (data) => setPage(data);

  const handleDocumentName = (data) => {
    let documentName;
    if (data?.row) {
      const cellData = data?.row;
      documentName = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.documentName }</div>
      );
    }
    return <div className="block">{documentName}</div>;
  };

  const updatePreviewItem = useCallback((item) => setPreviewItem(item), []);
  const getDocument = (url, token, body) => {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  };
  const handlePreview = (data, type, typeId) => {
    const sendData = {
      inwardId,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: data.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };
  const handleView = async (data) => {
    updatePreviewItem(data);
    await handlePreview(data, 2, 0);
  };

  const handleFileLocation = (data) => {
    let fileLocation;
    if (data?.row) {
      const cellData = data?.row;
      fileLocation = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileLocation }</div>
      );
    }
    return <div className="block">{fileLocation}</div>;
  };

  const columns = [
    {
      header: t('slNo'),
      cell: ({ rowIndex }) => rowIndex + 1,
      alignment: 'left'
    },
    {
      header: t('documentName'),
      field: 'documentName',
      alignment: 'left',
      cell: (field) => handleDocumentName(field)
    },
    {
      header: t('view'),
      type: 'actions',
      alignment: 'left',
      actions: [
        {
          icon: <View />,
          onClick: (row) => {
            handleView(row);
          }
        }
      ]
    },
    active === 'outbox' && ({
      header: t('fileLocation'),
      alignment: 'left',
      cell: (field) => handleFileLocation(field)
    })
  ];

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={goToBack} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t(`${active}`)}
        </div>
      </div>
      <div className="bg-white px-10 py-10 rounded-lg mb-5">
        <div className="col-span-12">

          <p className="text-[#153171] font-lato text-md font-bold pb-12">{`Inwards ID: ${inwardNo}`}</p>

          {active === 'outbox' && (
            <div className="pb-6">
              <CommonTable
                variant="normal"
                tableData={tableData}
                columns={columns}
                activeRows={activeRows}
                currentPage={page}
                itemsPerPage={10}
                totalItems={totalItems}
                onPageClick={onPageClick}
                paginationEnabled
                tableLoader={tableLoader?.loading && tableLoader?.id === 'inwards-physical-docs-table'}
                numberOfElements={numberOfElements}
              />
            </div>
          )}
        </div>
      </div>
      {previewItem && (
        <PreviewDocs
          fileType={previewItem?.documentContentType}
          preview={docPreview}
          loading={loading}
          setLoading={setLoading}
        />
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  tableLoader: getTableLoader,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  inwardsDetailsParams: getInwardsDetailsParams,
  inwardsDetailsTableListData: getInwardsDetailsTableListData
});

const mapDispatchToProps = (dispatch) => ({
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),

  setInwardsDetailsParams: (data) => dispatch(sliceActions.setInwardsDetailsParams(data)),
  fetchInwardDetails: (data) => dispatch(actions.fetchInwardDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ViewOutboxDetails);
