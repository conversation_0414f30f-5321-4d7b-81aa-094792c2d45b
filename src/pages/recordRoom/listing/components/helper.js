export const sampleInwardTableData = {
  totalElements: 8,
  totalPages: 1,
  size: 8,
  content: [
    {
      recordId: 'REC-1',
      inwardNo: 'INV-123',
      inwardDate: '2024-07-05',
      applicantNames: ['<PERSON>', '<PERSON>'],
      serviceName: 'Document Processing',
      numberOfPhysicalDocuments: 5,
      fileLocation: '/files/inv123'
    },
    {
      recordId: 'REC-2',
      inwardNo: 'INV-124',
      inwardDate: '2024-07-06',
      applicantNames: ['<PERSON>'],
      serviceName: 'Application Review',
      numberOfPhysicalDocuments: 3,
      fileLocation: '/files/inv124'
    },
    {
      recordId: 'REC-3',
      inwardNo: 'INV-125',
      inwardDate: '2024-07-07',
      applicantNames: ['<PERSON>', '<PERSON>'],
      serviceName: 'Approval Workflow',
      numberOfPhysicalDocuments: 7,
      fileLocation: '/files/inv125'
    },
    {
      recordId: 'REC-4',
      inwardNo: 'INV-126',
      inwardDate: '2024-07-08',
      applicantNames: ['<PERSON>'],
      serviceName: 'Payment Verification',
      numberOfPhysicalDocuments: 2,
      fileLocation: '/files/inv126'
    },
    {
      recordId: 'REC-5',
      inwardNo: 'INV-127',
      inwardDate: '2024-07-09',
      applicantNames: ['Sophia Lee', 'David Adams'],
      serviceName: 'Review Committee',
      numberOfPhysicalDocuments: 4,
      fileLocation: '/files/inv127'
    },
    {
      recordId: 'REC-6',
      inwardNo: 'INV-128',
      inwardDate: '2024-07-10',
      applicantNames: ['Olivia Turner'],
      serviceName: 'Quality Assurance',
      numberOfPhysicalDocuments: 6,
      fileLocation: '/files/inv128'
    },
    {
      recordId: 'REC-7',
      inwardNo: 'INV-129',
      inwardDate: '2024-07-11',
      applicantNames: ['William Harris', 'Ella Martinez'],
      serviceName: 'Final Approval',
      numberOfPhysicalDocuments: 8,
      fileLocation: '/files/inv129'
    },
    {
      recordId: 'REC-8',
      inwardNo: 'INV-130',
      inwardDate: '2024-07-12',
      applicantNames: ['Liam Clark'],
      serviceName: 'Archiving',
      numberOfPhysicalDocuments: 1,
      fileLocation: '/files/inv130'
    }
  ],
  number: 0,
  sort: {
    empty: true,
    sorted: true,
    unsorted: true
  },
  first: true,
  last: true,
  numberOfElements: 8,
  pageable: {
    offset: 0,
    sort: {
      empty: true,
      sorted: true,
      unsorted: true
    },
    pageNumber: 0,
    pageSize: 8,
    paged: true,
    unpaged: true
  },
  empty: false
};

export const sampleViewInwardTableData = {
  inwardNo: 'INV-123',
  supportingDocs: [
    {
      id: 'a1234567-89ab-cdef-1234-56789abcdef0',
      awsS3Key: 'newkey1',
      documentNo: 'doc101',
      documentName: 'New Document 1',
      documentContentType: 'pdf',
      issueDate: '2024-08-10',
      validUpto: '2025-08-10'
    },
    {
      id: 'b1234567-89ab-cdef-1234-56789abcdef1',
      awsS3Key: 'newkey2',
      documentNo: 'doc102',
      documentName: 'New Document 2',
      documentContentType: 'image',
      issueDate: '2024-08-11',
      validUpto: '2025-08-11'
    },
    {
      id: 'c1234567-89ab-cdef-1234-56789abcdef2',
      awsS3Key: 'newkey3',
      documentNo: 'doc103',
      documentName: 'New Document 3',
      documentContentType: 'image',
      issueDate: '2024-08-12',
      validUpto: '2025-08-12'
    },
    {
      id: 'd1234567-89ab-cdef-1234-56789abcdef3',
      awsS3Key: 'newkey4',
      documentNo: 'doc104',
      documentName: 'New Document 4',
      documentContentType: 'pdf',
      issueDate: '2024-08-13',
      validUpto: '2025-08-13'
    }
  ],
  documentTypeDocs: {
    additionalProp1: {
      id: 'e1234567-89ab-cdef-1234-56789abcdef4',
      awsS3Key: 'newkey5',
      documentNo: 'doctype101',
      documentName: 'New Document Type 1',
      documentContentType: 'image',
      issueDate: '2024-08-14',
      validUpto: '2025-08-14'
    }
  }
};
