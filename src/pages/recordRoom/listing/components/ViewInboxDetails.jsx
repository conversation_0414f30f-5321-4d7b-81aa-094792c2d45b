import React, {
  useState, useEffect, useCallback
} from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, IconButton } from '@ksmartikm/ui-components';
import _ from 'lodash';
import BackArrow from 'assets/BackIcon';
import View from 'assets/View';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import { FormController, FormWrapper, t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { dark } from 'utils/color';
import { STORAGE_KEYS } from 'common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  getActionTriggered, getTableLoader, getUserInfo, getOfficeType,
  getOtpStatus
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { RECORD_ROOM_ROUTE_CHANGE_OPTIONS, documenturl } from '../constants';
import {
  getInwardsDetailsParams,
  getInwardsDetailsTableListData,
  getFunctionalGroupDropdown,
  getPostByRecieverFunctionalGroup,
  getPostByMessengerFunctionalGroup
} from '../selectors';
import * as actions from '../actions';
import { actions as sliceActions } from '../slice';
import { AssignFormSchema } from '../validate';
import PreviewDocs from './PreviewDocs';
import OtpVerfication from './OtpVerfication';

const ViewInboxDetails = (props) => {
  const {
    tableLoader,
    setTableLoader,
    userInfo,
    inwardsDetailsParams,
    setInwardsDetailsParams,
    fetchInwardDetails,
    inwardsDetailsTableListData,
    actionTriggered,
    setActionTriggered,
    setAlertAction,
    sendOtp,
    otpStatus,
    fetchOfficeType,
    officeType,
    fetchFunctionalGroupDropdown,
    functionalGroupDropdown,

    fetchPostByLocationRecieverList,
    fetchPostByRecieverFunctionalGroup,
    postByRecieverFunctionalGroup,

    fetchPostByLocationMessengerList,
    fetchPostByMessengerFunctionalGroup,
    postByMessengerFunctionalGroup
  } = props;

  const navigate = useNavigate();
  const { documentId } = useParams();
  const activeRows = [{}];
  const active = 'inbox';
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm({
    mode: 'all',
    defaultValues: {
      recieverRouteType: '',
      recieverTypeOfOffice: '',
      recieverFunctionalGroup: '',
      sendTo: '',
      messengerRouteType: '',
      messengerTypeOfOffice: '',
      messengerFunctionalGroup: '',
      selectMessenger: ''
    },
    resolver: yupResolver(AssignFormSchema)
  });

  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [docPreview, setDocPreview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [previewItem, setPreviewItem] = useState(null);
  const [selectedIds, setSelectedIds] = useState([]);
  const [messengerList, setMessengerList] = useState([]);
  const [sendToList, setSendToList] = useState([]);
  const [inwardNo, setInwardNo] = useState('');
  const [inwardId, setInwardId] = useState('');
  const [formData, setFormData] = useState({
    inwardId,
    recordIds: selectedIds,
    reciever: null,
    messenger: null
  });

  useEffect(() => {
    if (inwardsDetailsParams) {
      fetchInwardDetails();
    }
  }, [inwardsDetailsParams]);
  useEffect(() => {
    if (documentId) {
      setInwardsDetailsParams({
        ...inwardsDetailsParams,
        documentId
      });
    }
  }, [documentId]);
  useEffect(() => {
    if (inwardsDetailsTableListData) {
      setTableLoader({ loading: true, id: 'inwards-physical-docs-table' });
      if (Object.keys(inwardsDetailsTableListData?.content).length > 0) {
        const contentData = inwardsDetailsTableListData?.content[0];
        let madatoryDocuments = [];
        let supportingDocumnets = [];
        if (contentData?.mandatoryDocs) {
          madatoryDocuments = contentData.mandatoryDocs.map((madatoryDoc) => {
            return { ...madatoryDoc.uploadedDocumentInfo, recordId: madatoryDoc.recordId, isMandatory: true };
          });
        }
        if (contentData?.supportingDocs) {
          supportingDocumnets = contentData.supportingDocs.map((supportDoc) => {
            return { ...supportDoc.uploadedDocumentInfo, recordId: supportDoc.recordId, isMandatory: false };
          });
        }

        setTableLoader({ loading: false, id: 'inwards-physical-docs-table' });
        setTableData([...madatoryDocuments, ...supportingDocumnets]);
        setInwardNo(contentData?.inwardNo);
        setInwardId(contentData?.inwardId);
        setFormData({ ...formData, inwardId: contentData?.inwardId });
        setTotalItems(Number(`${inwardsDetailsTableListData?.totalPages}0`));
        setNumberOfElements(Number(inwardsDetailsTableListData?.numberOfElements));
      } else {
        setTableLoader({ loading: false, id: 'inwards-physical-docs-table' });
        setInwardNo(''); // sampleViewInwardTableData?.inwardNo
        setTableData([]); // [...Object.values(sampleViewInwardTableData?.documentTypeDocs), ...sampleViewInwardTableData.supportingDocs]
        setNumberOfElements(0); // sampleViewInwardTableData.supportingDocs.length + Object.values(sampleViewInwardTableData.documentTypeDocs).length
        setTotalItems(0);
      }
    }
  }, [inwardsDetailsTableListData]);

  useEffect(() => {
    if (postByRecieverFunctionalGroup.data?.length > 0) {
      setSendToList(
        postByRecieverFunctionalGroup?.data?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName ? ps.employeeName : ''} - ${ps.designation} - ${ps.penNo ? ps.penNo : ''} - ${ps.mobileNo ? ps.mobileNo : ''} - ${ps.postnameEng ? ps.postnameEng : ps.postNameInEng}`
        }))
      );
    }
  }, [postByRecieverFunctionalGroup]);

  useEffect(() => {
    if (postByMessengerFunctionalGroup.data?.length > 0) {
      setMessengerList(
        postByMessengerFunctionalGroup?.data?.map((ps) => ({
          ...ps,
          name: `${ps.employeeName ? ps.employeeName : ''} - ${ps.designation} - ${ps.penNo ? ps.penNo : ''} - ${ps.mobileNo ? ps.mobileNo : ''} - ${ps.postnameEng ? ps.postnameEng : ps.postNameInEng}`
        }))
      );
    }
  }, [postByMessengerFunctionalGroup]);

  useEffect(() => setFormData({ ...formData, recordIds: selectedIds }), [selectedIds]);

  const goToBack = () => navigate(`/ui/file-management/services/record-room/${active}`);
  const onSubmitForm = () => {};

  const onPageClick = (data) => {
    setPage(data);
    setInwardsDetailsParams({
      ...inwardsDetailsParams,
      page: data
    });
  };

  const handleDocumentName = (data) => {
    let documentName;
    if (data?.row) {
      const cellData = data?.row;
      documentName = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.documentName} {cellData?.isMandatory && '*'}</div>
      );
    }
    return <div className="block">{documentName}</div>;
  };

  const updatePreviewItem = useCallback((item) => setPreviewItem(item), []);
  const getDocument = (url, token, body) => {
    try {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          setDocPreview(response);
        });
    } catch (error) {
      setLoading(false);
    }
  };
  const handlePreview = (data, type, typeId) => {
    const sendData = {
      inwardId,
      fileTypeForPreview: type,
      documentTypeId: typeId,
      fileId: data.id,
      userInfo: {
        officeId: userInfo?.id
      }
    };
    getDocument(documenturl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), sendData);
  };
  const handleView = async (data) => {
    updatePreviewItem(data);
    if (data.isMandatory) await handlePreview(data, 1, data?.documentTypeId); // mandatory docs preview
    else await handlePreview(data, 2, 0); // supporting docs preview
  };

  const handleSelection = (data) => {
    const selected = selectedIds?.length > 0 ? JSON.parse(JSON.stringify(selectedIds)) : [];
    const find = selected.findIndex((item) => item === data);
    if (find > -1) selected.splice(find, 1);
    else selected.push(data);
    setSelectedIds(selected);
  };
  function checkBoxCheck(data) {
    const find = selectedIds.findIndex((item) => item === data);
    if (find > -1) return <CheckedBox />;
    return <UnCheckedBox />;
  }
  const handleSelectRow = (data) => {
    return (
      <IconButton
        variant="unstyled"
        style={{ textDecoration: 'none' }}
        icon={checkBoxCheck(data?.row?.recordId)}
        onClick={() => handleSelection(data?.row?.recordId)}
      />
    );
  };

  const handleFieldChange = (field, data) => {
    switch (field) {
      case 'recieverRouteType':
        setValue('recieverRouteType', data?.id);
        setValue('recieverTypeOfOffice', null);
        setValue('recieverFunctionalGroup', null);
        setValue('sendTo', null);
        if (data?.id === 2) {
          fetchFunctionalGroupDropdown();
        } else {
          fetchOfficeType({ officeLbCode: userInfo?.id });
        }
        break;
      case 'recieverFunctionalGroup':
        setValue('recieverFunctionalGroup', data.functionalGroupId);
        setValue('sendTo', null);
        fetchPostByRecieverFunctionalGroup({ functionalGroupId: data.functionalGroupId, officeId: userInfo?.id });
        break;
      case 'recieverTypeOfOffice':
        if (data) {
          setValue('recieverTypeOfOffice', data.id);
        } else {
          setValue('sendTo', null);
        }
        fetchPostByLocationRecieverList({ location: data.id });
        break;
      case 'sendTo':
        setValue('sendTo', data.id);
        setFormData({ ...formData, reciever: data });
        break;

      case 'messengerRouteType':
        setValue('messengerRouteType', data?.id);
        setValue('messengerTypeOfOffice', null);
        setValue('selectMessanger', null);
        if (data?.id === 2) {
          fetchFunctionalGroupDropdown();
        } else {
          fetchOfficeType({ officeLbCode: userInfo?.id });
        }
        break;
      case 'messengerFunctionalGroup':
        setValue('messengerFunctionalGroup', data.functionalGroupId);
        setValue('selectMessenger', null);
        fetchPostByMessengerFunctionalGroup({ functionalGroupId: data.functionalGroupId, officeId: userInfo?.id });
        break;
      case 'messengerTypeOfOffice':
        if (data) {
          setValue('messengerTypeOfOffice', data.id);
        } else {
          setValue('selectMessenger', null);
        }
        fetchPostByLocationMessengerList({ location: data.id });
        break;
      case 'selectMessenger':
        setValue('selectMessenger', data.id);
        setFormData({ ...formData, messenger: data });
        break;
      default:
        break;
    }
  };

  const handleSendOtp = () => {
    if (formData?.inwardId && formData?.messenger?.penNo && formData?.reciever?.penNo && formData.recordIds.length > 0) {
      setTableData([...tableData.filter((data) => formData.recordIds.includes(data.recordId))]);
      setAlertAction(false);
      setActionTriggered({ loading: true, id: 'otpsend' });
      sendOtp({ inwardId, mobileNo: formData?.messenger?.mobileNo });
    } else if (formData.recordIds.length === 0) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseSelect')} ${t('atleastOne')} ${t('physicalDocument')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
    } else if (!formData?.messenger?.penNo) {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t('pleaseCompleteMissingDetails')}`,
        title: t('missingSections'),
        backwardActionText: t('ok')
      });
    }
  };

  const columns = [
    {
      header: t('slNo'),
      cell: ({ rowIndex }) => rowIndex + 1,
      alignment: 'left'
    },
    {
      header: t('documentName'),
      field: 'documentName',
      alignment: 'left',
      cell: (field) => handleDocumentName(field)
    },
    {
      header: t('view'),
      type: 'actions',
      alignment: 'left',
      actions: [
        {
          icon: <View />,
          onClick: (row) => {
            handleView(row);
          }
        }
      ]
    },
    active === 'inbox' && ({
      header: t('select'),
      alignment: 'left',
      cell: (field) => handleSelectRow(field)
    })
  ];

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={goToBack} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t(`${active}`)}
        </div>
      </div>
      <div className="bg-white px-10 py-10 rounded-lg mb-5">
        <div className="col-span-12">

          <p className="text-[#153171] font-lato text-md font-bold pb-12">{`Inwards ID: ${inwardNo}`}</p>
          {otpStatus && (
            <>
              <p className="text-[#153171] font-lato text-md font-bold pb-12">{`${t('messenger')}: ${formData?.messenger?.employeeName}`}</p>
              <p className="text-[#153171] font-lato text-md font-bold pb-12">{`${t('send')} ${t('to')}: ${formData?.reciever?.employeeName}`}</p>
            </>
          )}
          {active === 'inbox' && (
            <form id="assign-record-room-form" onSubmit={handleSubmit(onSubmitForm)}>
              <div className="pb-6">
                <CommonTable
                  variant="normal"
                  tableData={tableData}
                  columns={columns}
                  activeRows={activeRows}
                  currentPage={page}
                  itemsPerPage={10}
                  totalItems={totalItems}
                  onPageClick={onPageClick}
                  paginationEnabled
                  tableLoader={tableLoader?.loading && tableLoader?.id === 'inwards-physical-docs-table'}
                  numberOfElements={numberOfElements}
                />
              </div>

              {otpStatus ? (
                <div className="pb-5 flex justify-end">
                  <OtpVerfication formData={formData} />
                </div>
              ) : (
                <>
                  <div className="col-span-12">
                    <h2 className="text-[#153171] font-lato text-xl font-bold">{`${t('receiver')} ${t('details')}`}</h2>
                    <FormWrapper>
                      <div className="lg:col-span-4 md:col-span-4 col-span-12">
                        <FormController
                          name="recieverRouteType"
                          type="select"
                          label={t('unit')}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="id"
                          handleChange={(data) => handleFieldChange('recieverRouteType', data)}
                          options={RECORD_ROOM_ROUTE_CHANGE_OPTIONS}
                          required
                        />
                      </div>

                      {Number(watch('recieverRouteType')) === 1 && (
                        <div className="lg:col-span-4 md:col-span-4 col-span-12">
                          <FormController
                            name="recieverTypeOfOffice"
                            type="select"
                            label={t('officeType')}
                            placeholder={t('select')}
                            control={control}
                            errors={errors}
                            optionKey="id"
                            handleChange={(data) => handleFieldChange('recieverTypeOfOffice', data)}
                            options={officeType || []}
                            required
                          />
                        </div>
                      )}

                      {Number(watch('recieverRouteType')) === 2 && (
                      <div className="lg:col-span-4 md:col-span-4 col-span-12">
                        <FormController
                          name="recieverFunctionalGroup"
                          type="select"
                          label={t('functionalGroup')}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="functionalGroupId"
                          handleChange={(data) => handleFieldChange('recieverFunctionalGroup', data)}
                          options={_.get(functionalGroupDropdown, 'data', [])}
                          required
                        />
                      </div>
                      )}

                      {watch('recieverRouteType') && (
                      <div className="lg:col-span-4 md:col-span-4 col-span-12">
                        <FormController
                          name="sendTo"
                          type="select"
                          label={`${t('send')} ${t('to')}`}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="id"
                          handleChange={(data) => handleFieldChange('sendTo', data)}
                          options={sendToList?.filter((item) => item?.penNo !== null)}
                          required
                        />
                      </div>
                      )}
                    </FormWrapper>
                  </div>

                  <div className="col-span-12">
                    <h2 className="text-[#153171] font-lato text-xl font-bold">{`${t('messenger')} ${t('details')}`}</h2>
                    <FormWrapper>
                      <div className="lg:col-span-4 md:col-span-4 col-span-12">
                        <FormController
                          name="messengerRouteType"
                          type="select"
                          label={t('unit')}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="id"
                          handleChange={(data) => handleFieldChange('messengerRouteType', data)}
                          options={RECORD_ROOM_ROUTE_CHANGE_OPTIONS}
                          required
                        />
                      </div>

                      {Number(watch('messengerRouteType')) === 1 && (
                        <div className="lg:col-span-4 md:col-span-4 col-span-12">
                          <FormController
                            name="messengerTypeOfOffice"
                            type="select"
                            label={t('officeType')}
                            placeholder={t('select')}
                            control={control}
                            errors={errors}
                            optionKey="id"
                            handleChange={(data) => handleFieldChange('messengerTypeOfOffice', data)}
                            options={officeType || []}
                            required
                          />
                        </div>
                      )}

                      {Number(watch('messengerRouteType')) === 2 && (
                      <div className="lg:col-span-4 md:col-span-4 col-span-12">
                        <FormController
                          name="messengerFunctionalGroup"
                          type="select"
                          label={t('functionalGroup')}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="functionalGroupId"
                          handleChange={(data) => handleFieldChange('messengerFunctionalGroup', data)}
                          options={_.get(functionalGroupDropdown, 'data', [])}
                          required
                        />
                      </div>
                      )}

                      {watch('messengerRouteType') && (
                      <div className="lg:col-span-4 md:col-span-4 col-span-12">
                        <FormController
                          name="selectMessenger"
                          type="select"
                          label={`${t('select')} ${t('messenger')}`}
                          placeholder={t('select')}
                          control={control}
                          errors={errors}
                          optionKey="id"
                          handleChange={(data) => handleFieldChange('selectMessenger', data)}
                          options={messengerList?.filter((item) => item?.penNo !== null)}
                          required
                        />
                      </div>
                      )}
                    </FormWrapper>
                  </div>

                  <div className="py-5 col-span-12 text-right">
                    <Button
                      onClick={() => handleSendOtp()}
                      type="submit"
                      py={5}
                      variant="secondary"
                      isLoading={actionTriggered?.id === 'otpsend' && actionTriggered?.loading}
                    >
                      {t('sendOtp')}
                    </Button>
                  </div>
                </>
              )}
            </form>
          )}
        </div>
      </div>
      {previewItem && (
        <PreviewDocs
          fileType={previewItem?.documentContentType}
          preview={docPreview}
          loading={loading}
          setLoading={setLoading}
        />
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  tableLoader: getTableLoader,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  otpStatus: getOtpStatus,
  inwardsDetailsParams: getInwardsDetailsParams,
  inwardsDetailsTableListData: getInwardsDetailsTableListData,

  officeType: getOfficeType,
  functionalGroupDropdown: getFunctionalGroupDropdown,

  postByRecieverFunctionalGroup: getPostByRecieverFunctionalGroup,
  postByMessengerFunctionalGroup: getPostByMessengerFunctionalGroup
});

const mapDispatchToProps = (dispatch) => ({
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  sendOtp: (data) => dispatch(actions.sendOtp(data)),

  setInwardsDetailsParams: (data) => dispatch(sliceActions.setInwardsDetailsParams(data)),
  fetchInwardDetails: (data) => dispatch(actions.fetchInwardDetails(data)),

  fetchOfficeType: (data) => dispatch(commonActions.fetchOfficeType(data)),

  fetchFunctionalGroupDropdown: () => dispatch(actions.fetchFunctionalGroupDropdown()),

  fetchPostByLocationRecieverList: (data) => dispatch(actions.fetchPostByLocationRecieverList(data)),
  fetchPostByRecieverFunctionalGroup: (data) => dispatch(actions.fetchPostByRecieverFunctionalGroup(data)),

  fetchPostByLocationMessengerList: (data) => dispatch(actions.fetchPostByLocationMessengerList(data)),
  fetchPostByMessengerFunctionalGroup: (data) => dispatch(actions.fetchPostByMessengerFunctionalGroup(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(ViewInboxDetails);
