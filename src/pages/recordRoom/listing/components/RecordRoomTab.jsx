import BackArrow from 'assets/BackIcon';
import {
  Button, IconButton, Input, InputGroup, InputRightElement, t
} from 'common/components';
import React, { useEffect } from 'react';
import { dark } from 'utils/color';
import { useNavigate, useLocation } from 'react-router-dom';
import { BASE_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import SearchIcon from 'assets/SearchIcon';
import { FILTER_TYPE } from 'pages/common/constants';
import { RECORD_ROOM_TABS } from '../constants';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '200px',
    input: {
      borderRadius: '7px',
      border: '1px solid #BFD4F7'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    input: {
      borderRadius: '7px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '120px'
  },
  sort: {
    display: 'flex'
  }
};

const RecordRoomTab = ({
  activeTab, setActiveTab, setSearch, search, setDate, date, today, triggerSearch
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleActive = (data) => {
    setActiveTab(data);
    setSearch('');
    setDate('');
    if (data === RECORD_ROOM_TABS.INBOX) {
      navigate(`${BASE_PATH}/services/record-room/inbox`);
    } else if (data === RECORD_ROOM_TABS.OUTBOX) {
      navigate(`${BASE_PATH}/services/record-room/outbox`);
    }
  };

  useEffect(() => {
    if (location?.pathname.includes('record-room/inbox')) {
      setActiveTab(RECORD_ROOM_TABS.INBOX);
    } else if (location?.pathname.includes('record-room/outbox')) {
      setActiveTab(RECORD_ROOM_TABS.OUTBOX);
    }
  }, [location.pathname]);

  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  return (
    <div className="flex gap-5 items-center">
      <div className="flex-none">
        <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" style={{ marginTop: '-4px' }} />} />
      </div>
      <div className="text-[#09327B] text-[20px] normal font-semibold leading-[28px] capitalize">
        {t('recordRoom')}
      </div>
      <div className="flex-grow bg-white rounded-lg">
        <div className="flex">
          <div className="flex-grow">

            <Button
              variant="unstyled"
              colorScheme={activeTab === RECORD_ROOM_TABS.INBOX ? 'secondary' : 'white'}
              className={activeTab === RECORD_ROOM_TABS.INBOX ? '!text-[14px] border-b-2 border-rose-600 !rounded-none !mx-6 !text-rose-600 py-5' : '!text-[14px] border-b-2 !border-white !rounded-none !mx-6 !text-[#828282] py-5'}
              onClick={() => handleActive(RECORD_ROOM_TABS.INBOX)}
              style={{ height: '50px' }}
            >
              {t('inbox')}
            </Button>

            <Button
              variant="unstyled"
              colorScheme={activeTab === RECORD_ROOM_TABS.OUTBOX ? 'secondary' : 'white'}
              className={activeTab === RECORD_ROOM_TABS.OUTBOX ? '!text-[14px] border-b-2 border-rose-600 !rounded-none !mx-6 !text-rose-600 py-5' : '!text-[14px] border-b-2 !border-white !rounded-none !mx-6 !text-[#828282] py-5'}
              onClick={() => handleActive(RECORD_ROOM_TABS.OUTBOX)}
              style={{ height: '50px' }}
            >
              {t('outbox')}
            </Button>
          </div>

          <div className="flex-none bg-white rounded-lg p-[5px] customFileDatePicker ">
            <div className="flex gap-3">
              <InputGroup style={styles.search}>
                <Input
                  placeholder={t('searchHere')}
                  style={styles.search.input}
                  value={search}
                  onChange={(event) => {
                    setSearch(event.target.value);
                    triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, event.target.value);
                  }}
                />
                <InputRightElement>
                  <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
                </InputRightElement>
              </InputGroup>

              <InputGroup style={styles.date}>
                <Input
                  value={date}
                  onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
                  type="date"
                  style={styles.date.input}
                  max={today}
                />
              </InputGroup>
            </div>

          </div>
        </div>

      </div>

    </div>
  );
};

export default RecordRoomTab;
