import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  IconButton, Modal, ModalOverlay, ModalContent, ModalBody, Tooltip, t,
  ModalHeader
} from 'common/components';
import { useState, useEffect } from 'react';
import { PdfViewer, Spinner } from '@ksmartikm/ui-components';
import { actions as commonSliceActions } from 'pages/common/slice';
import MiniScreenIcon from 'assets/MiniScreen';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import Rotate from 'assets/Rotate';
import CloseOutlineIcon from 'assets/CloseOutline';
import FullScreenIcon from 'assets/FullScreen';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';

const PreviewDocs = (props) => {
  const {
    preview = {}, fileType, loading, setLoading
  } = props;
  const [open, setOpen] = useState(false);
  const [previewData, setPreviewData] = useState('');
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);

  const [full, setFull] = useState(false);

  useEffect(() => {
    if (preview) {
      if (fileType) {
        const arr = new Uint8Array(preview);
        const blob = new Blob([arr], {
          type: fileType
        });
        const url = window.URL.createObjectURL(blob);
        setLoading(false);
        setPreviewData(url);
        setOpen(true);
      }
    }
  }, [preview]);

  const handleClose = () => {
    setOpen(false);
    setLoading(false);
  };

  const handleFull = () => {
    setFull(!full);
  };

  const printAck = () => {
    printBlob(previewData);
  };

  const downloadAck = () => {
    downloadBlob({ blob: previewData, fileName: 'KSMART-FILE-DOCUMENT' });
  };

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };

  return (
    <Modal isOpen={open} size={full ? 'full' : '4xl'} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <div className="flex items-center justify-end w-full pt-2">
            <Tooltip label={t('fullScreen')}>
              <IconButton variant="unstyled" onClick={() => handleFull()} leftIcon={full ? <MiniScreenIcon width="21px" height="21px" /> : <FullScreenIcon width="21px" height="21px" />} />
            </Tooltip>
            <Tooltip label={t('print')}>
              <IconButton variant="unstyled" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" />} />
            </Tooltip>
            <Tooltip label={t('download')}>
              <IconButton variant="unstyled" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" />} />
            </Tooltip>
            <Tooltip label={t('rotate')}>
              <IconButton variant="unstyled" onClick={rotatePreview} leftIcon={<Rotate style={{ transform: `rotate(${rotatePreviewFlag}deg)` }} width="21px" height="21px" />} />
            </Tooltip>
            <Tooltip label={t('close')}>
              <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
            </Tooltip>
          </div>
        </ModalHeader>
        <ModalBody>

          {loading ? <div className="px-3 py-5 h-[500px] w-[100%]"> <Spinner style={{ marginTop: '230px', marginLeft: 'calc(50% - 30px)' }} /> </div> : (
            <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto">
              <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                {fileType.includes('image') ? <img width="100%" src={previewData} alt="Preview" /> : <PdfViewer title="inward" width="100%" data={previewData} aria-label="loading" />}
              </div>
            </div>
          )}

        </ModalBody>

      </ModalContent>
    </Modal>
  );
};

const mapStateToProps = createStructuredSelector({

});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PreviewDocs);
