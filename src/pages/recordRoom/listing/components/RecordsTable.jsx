import React, { useState, useEffect } from 'react';
import TableView from 'assets/TableView';
import { t } from 'common/components';
import { CommonTable } from 'common/components/Table';
import { actions as commonSliceActions } from 'pages/common/slice';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getTableLoader } from 'pages/common/selectors';
import { useNavigate } from 'react-router-dom';
import { actions as sliceActions } from '../slice';
import { getInboxTableListData, getTableListParams } from '../selectors';
import * as actions from '../actions';

export const sampleTableData = {
  content: [
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125801',
      inwardsDate: '2023-06-01',
      applicantName: '<PERSON>',
      serviceName: 'Passport Renewal',
      noOfPhysicalDocument: 3,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125802',
      inwardsDate: '2023-06-02',
      applicantName: '<PERSON>',
      serviceName: 'Visa Application',
      noOfPhysicalDocument: 5,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125803',
      inwardsDate: '2023-06-03',
      applicantName: 'Alice Johnson',
      serviceName: 'ID Card Issuance',
      noOfPhysicalDocument: 2,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125804',
      inwardsDate: '2023-06-04',
      applicantName: 'Bob Brown',
      serviceName: "Driver's License",
      noOfPhysicalDocument: 4,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125805',
      inwardsDate: '2023-06-05',
      applicantName: 'Charlie Davis',
      serviceName: 'Birth Certificate',
      noOfPhysicalDocument: 1,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125806',
      inwardsDate: '2023-06-06',
      applicantName: 'Emily White',
      serviceName: 'Marriage Certificate',
      noOfPhysicalDocument: 3,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125807',
      inwardsDate: '2023-06-07',
      applicantName: 'Frank Harris',
      serviceName: 'Death Certificate',
      noOfPhysicalDocument: 2,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125808',
      inwardsDate: '2023-06-08',
      applicantName: 'Grace Lee',
      serviceName: 'Property Registration',
      noOfPhysicalDocument: 6,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125809',
      inwardsDate: '2023-06-09',
      applicantName: 'Henry Martin',
      serviceName: 'Business License',
      noOfPhysicalDocument: 4,
      fileLocation: ''
    },
    {
      inwardsNo: '635c45b0-66c3-4ae4-aae5-d80de2125810',
      inwardsDate: '2023-06-10',
      applicantName: 'Isla Nelson',
      serviceName: 'Change of Name',
      noOfPhysicalDocument: 2,
      fileLocation: ''
    }
  ],
  pageable: {
    pageNumber: 0,
    pageSize: 10,
    sort: [
      {
        direction: 'DESC',
        property: 'createdAt',
        ignoreCase: false,
        nullHandling: 'NATIVE',
        ascending: false,
        descending: true
      }
    ],
    offset: 0,
    paged: true,
    unpaged: false
  },
  last: true,
  totalElements: 10,
  totalPages: 1,
  size: 10,
  number: 0,
  sort: [
    {
      direction: 'DESC',
      property: 'createdAt',
      ignoreCase: false,
      nullHandling: 'NATIVE',
      ascending: false,
      descending: true
    }
  ],
  first: true,
  numberOfElements: 10,
  empty: false
};

const RecordsTable = (props) => {
  const {
    activeTab, page, setPage, tableLoader, setTableLoader, filteredDate, filteredSearchKey, fetchInboxTableList, inboxTableListData, tableListParams, setTableListParams
  } = props;

  const navigate = useNavigate();
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState();
  const [numberOfElements, setNumberOfElements] = useState(0);

  const applyingData = (data, tableId) => {
    if (data) {
      if (Object.keys(data).length > 0) {
        setTableData(data?.content);
        setTotalItems(Number(`${data?.totalPages}0`));
        setNumberOfElements(Number(data?.numberOfElements));
        setTableLoader({ loading: false, id: `${tableId}` });
      } else {
        setTableLoader({ loading: false, id: `${tableId}` });
        setTableData([]);
        setTotalItems(0);
      }
    }
  };

  useEffect(() => {
    if (tableListParams) {
      fetchInboxTableList();
    }
  }, [tableListParams]);

  useEffect(() => {
    if (activeTab === 'INBOX' && inboxTableListData) {
      setTableLoader({ loading: true, id: 'record-room-inbox-table' });
      applyingData(inboxTableListData, 'record-room-inbox-table');
    }
  }, [inboxTableListData]);

  useEffect(() => {
    setTableListParams({
      ...tableListParams,
      inwardData: filteredDate || null,
      page: 0
    });
  }, [filteredDate]);

  useEffect(() => {
    setTableListParams({
      ...tableListParams,
      keyword: filteredSearchKey || null,
      page: 0
    });
  }, [filteredSearchKey]);

  const onPageClick = (data) => {
    setPage(data);
    setTotalItems();
    setTableData();
    setNumberOfElements();
    setTableLoader();
  };

  const handleInwardNumber = (inwardData) => {
    let inwardsNumber;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      inwardsNumber = (
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.inwardsNo}</div>
      );
    }
    return <div className="block">{inwardsNumber}</div>;
  };

  const handleInwardDate = (inwardData) => {
    let inwardsDate;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      inwardsDate = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.inwardsDate}</div>
      );
    }
    return <div className="block">{inwardsDate}</div>;
  };

  const handleApplicantName = (inwardData) => {
    let applicantName;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData?.applicantName}</div>;
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleServiceName = (inwardData) => {
    let serviceName;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      serviceName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.serviceName}</div>
      );
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleNoOfPhysicalDocument = (inwardData) => {
    let noOfPhysicalDocument;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      noOfPhysicalDocument = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.noOfPhysicalDocument}</div>
      );
    }
    return <div className="block">{noOfPhysicalDocument}</div>;
  };

  const handleFileLocation = (inwardData) => {
    let fileLocation;
    if (inwardData?.row) {
      const cellData = inwardData?.row;
      fileLocation = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.fileLocation}</div>
      );
    }
    return <div className="block">{fileLocation}</div>;
  };

  const columns = [
    {
      header: t('slNo'),
      cell: ({ rowIndex }) => rowIndex + 1,
      alignment: 'left'
    },
    {
      header: t('inwardNumber'),
      field: 'inwardNumber',
      alignment: 'left',
      cell: (field) => handleInwardNumber(field)
    },
    {
      header: t('inwardDate'),
      field: 'inwardDate',
      alignment: 'left',
      cell: (field) => handleInwardDate(field)
    },
    {
      header: t('applicantName'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('servicee'),
      field: 'servicee',
      cell: (field) => handleServiceName(field)
    },
    {
      header: `${t('noOf')} ${t('physicalDocument')}`,
      field: 'noOfPhysicalDocument',
      cell: (field) => handleNoOfPhysicalDocument(field)
    },
    {
      header: t('fileLocation'),
      field: 'fileLocation',
      cell: (field) => handleFileLocation(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            navigate(`${row?.inwardsNo}`);
          }
        }
      ]
    }
  ];

  return (
    <div>
      <div className="col-span-12">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === `${activeTab === 'INBOX' ? 'record-room-inbox-table' : 'record-room-outbox-table'}`}
        />

      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  inboxTableListData: getInboxTableListData,
  tableListParams: getTableListParams,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchInboxTableList: (data) => dispatch(actions.fetchInboxData(data)),
  setTableListParams: (data) => dispatch(sliceActions.setTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(RecordsTable);
