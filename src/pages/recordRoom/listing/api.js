import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchReassignUserTableList = (params) => {
  return {
    url: API_URL.SEARCH_FILES.FETCH_SEARCH_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_REQUEST,
        ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_SUCCESS,
        ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_FAILURE
      ],
      params
    }
  };
};

export const reassignFileApi = (data) => {
  return {
    url: API_URL.RE_ASSIGN_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.RE_ASSIGN_FILE_REQUEST,
        ACTION_TYPES.RE_ASSIGN_FILE_SUCCESS,
        ACTION_TYPES.RE_ASSIGN_FILE_FAILURE
      ],
      data
    }
  };
};

export const fetchInboxTableList = (params) => {
  return {
    url: API_URL.RECORD_ROOM.FETCH_INBOX,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INBOX_REQUEST,
        ACTION_TYPES.FETCH_INBOX_SUCCESS,
        ACTION_TYPES.FETCH_INBOX_FAILURE
      ],
      params
    }
  };
};

export const fetchOutboxTableList = (params) => {
  return {
    url: API_URL.RECORD_ROOM.FETCH_OUTBOX,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_OUTBOX_REQUEST,
        ACTION_TYPES.FETCH_OUTBOX_SUCCESS,
        ACTION_TYPES.FETCH_OUTBOX_FAILURE
      ],
      params
    }
  };
};

export const fetchInwardDetails = (params) => {
  return {
    url: API_URL.RECORD_ROOM.FETCH_INWARD_DETAILS,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_INWARD_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_INWARD_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_INWARD_DETAILS_FAILURE
      ],
      params
    }
  };
};

export const sendOtp = (params) => {
  return {
    url: API_URL.RECORD_ROOM.SEND_OTP,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.SEND_OTP_REQUEST,
        ACTION_TYPES.SEND_OTP_SUCCESS,
        ACTION_TYPES.SEND_OTP_FAILURE
      ],
      params
    }
  };
};

export const verifyOtp = (payload) => {
  const { inwardId, data } = payload;
  return {
    url: API_URL.RECORD_ROOM.VERIFY_OTP.replace(':inwardId', inwardId),
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.VERIFY_OTP_REQUEST,
        ACTION_TYPES.VERIFY_OTP_SUCCESS,
        ACTION_TYPES.VERIFY_OTP_FAILURE
      ],
      data // data is body
    }
  };
};

export const assignRecordRoom = (params) => {
  return {
    url: API_URL.RECORD_ROOM.ASSIGN_RECORD_ROOM_FILE,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.ASSIGN_RECORD_ROOM__REQUEST,
        ACTION_TYPES.ASSIGN_RECORD_ROOM__SUCCESS,
        ACTION_TYPES.ASSIGN_RECORD_ROOM__FAILURE
      ],
      params
    }
  };
};

export const fetchFunctionalGroupDropdown = () => {
  return {
    url: API_URL.COMMON.FETCH_FUNCTIONAL_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DEPARTMENTS_REQUEST,
        ACTION_TYPES.FETCH_DEPARTMENTS_SUCCESS,
        ACTION_TYPES.FETCH_DEPARTMENTS_FAILURE
      ]
    }
  };
};

export const fetchPostByLocationRecieverList = (data) => {
  return {
    url: API_URL.COMMON.FETCH_POST_BY_LOCATION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_LOCATION_RECIEVER_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_LOCATION_RECIEVER_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_LOCATION_RECIEVER_FAILURE
      ],
      params: data
    }
  };
};

export const fetchPostByRecieverFunctionalGroup = (params) => {
  return {
    url: API_URL.COMMON.POST_BY_FUNC_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_RECIEVER_FUNC_GROUP_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_RECIEVER_FUNC_GROUP_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_RECIEVER_FUNC_GROUP_FAILURE
      ],
      params
    }
  };
};

export const fetchPostByLocationMessengerList = (data) => {
  return {
    url: API_URL.COMMON.FETCH_POST_BY_LOCATION,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_LOCATION_MESSENGER_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_LOCATION_MESSENGER_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_LOCATION_MESSENGER_FAILURE
      ],
      params: data
    }
  };
};

export const fetchPostByMessengerFunctionalGroup = (params) => {
  return {
    url: API_URL.COMMON.POST_BY_FUNC_GROUP,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_POST_BY_MESSENGER_FUNC_GROUP_REQUEST,
        ACTION_TYPES.FETCH_POST_BY_MESSENGER_FUNC_GROUP_SUCCESS,
        ACTION_TYPES.FETCH_POST_BY_MESSENGER_FUNC_GROUP_FAILURE
      ],
      params
    }
  };
};
