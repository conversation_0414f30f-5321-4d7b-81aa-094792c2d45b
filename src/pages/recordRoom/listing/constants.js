import { baseApiURL } from 'utils/http';

export const STATE_REDUCER_KEY = 'record-room';

export const documenturl = `${baseApiURL}/inward-management-services/preview-documents`;

export const RECORD_ROOM_TABS = {
  // IN_HAND_RECORDS: 'IN_HAND_RECORDS',
  // REQUESTED: 'REQUESTED',
  // SENT_RECORDS: 'SENT_RECORDS',
  INBOX: 'INBOX',
  OUTBOX: 'OUTBOX'
};

export const RECORD_ROOM_ROUTE_CHANGE_OPTIONS = [
  { id: 1, name: 'Zonal/Circle' },
  { id: 2, name: 'Functional Groups' }
];
