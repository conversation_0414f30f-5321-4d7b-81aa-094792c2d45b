import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchMyFileTableList = (params) => {
  return {
    url: API_URL.SEARCH_FILES.MY_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_MY_FILE_LIST_REQUEST,
        ACTION_TYPES.FETCH_MY_FILE_LIST_SUCCESS,
        ACTION_TYPES.FETCH_MY_FILE_LIST_FAILURE
      ],
      params
    }
  };
};
