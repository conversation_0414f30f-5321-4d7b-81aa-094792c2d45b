import {
  t,
  Input, InputGroup, InputRightElement, Icon<PERSON>utton, FormController
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getServicesDropdown,
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import TableView from 'assets/TableView';
import { DATE_FORMAT, FILTER_TYPE } from 'pages/common/constants';
import SearchIcon from 'assets/SearchIcon';
import { convertToLocalDate } from 'utils/date';
import { actions as commonSliceActions } from 'pages/common/slice';
import BackArrow from 'assets/BackIcon.jsx';
import { dark } from 'utils/color.js';
import { EMPLOYEE_SERVICE_PATH, STORAGE_KEYS } from 'common/constants';
import _ from 'lodash';
import * as commonActions from 'pages/common/actions';
import { useForm } from 'react-hook-form';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import FileStatusModal from 'pages/reports/fileLogReports/components/FileStatusModal';
import ClearIcon from 'assets/Clear';
import {
  handleCurrentUser, handleFileNumber, handleFileStage, handleForwardedBy, handleServiceName
} from './Helper';
import { getMyFilesListData, getMyFilesSearchListParams } from '../selectors';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';

const styles = {
  head: {
    fontSize: '18px',
    paddingTop: '10px',
    color: '#09327B'
  },
  search: {
    width: '250px',
    background: 'rgba(232, 239, 244, 0.7)',
    input: {
      borderRadius: '7px',
      border: '1px solid #BFD4F7',
      background: 'rgba(232, 239, 244, 0.7)'
    },
    button: {
      background: 'none'
    }
  },
  date: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #BFD4F7',
      textTransform: 'uppercase'
    },
    button: {
      background: 'none'
    }
  },
  label: {
    paddingRight: '5px',
    lineHeight: '40px'
  },
  select: {
    borderRadius: '20px',
    border: '1px solid #fff',
    background: 'none',
    padding: '8px',
    marginLeft: '15px',
    width: '200px'
  },
  sort: {
    display: 'flex'
  }
};

const List = ({
  userInfo, fetchMyFileTableList, setMyFileListParams, myFilesTableList, myFileTableListParams,
  setTableLoader, tableLoader, serviceDropdown, fetchServicesOptions
}) => {
  const {
    control
  } = useForm({
    mode: 'all'
  });

  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [search, setSearch] = useState('');
  // const [date, setDate] = useState();
  // const today = new Date().toISOString().split('T')[0];
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [openFileStatusModal, setOpenFileStatusModal] = useState(false);
  const [modalData, setModalData] = useState([]);
  const [downloadUrl, setDownloadUrl] = useState('');

  useEffect(() => {
    fetchServicesOptions();
  }, []);

  const handleCloseFileStatusModal = () => {
    setOpenFileStatusModal(false);
  };

  const viewActions = async (data) => {
    const response = await (fetch(`${baseApiURL}/${API_URL.REPORTS.FETCH_STATUS_LOG_REPORT}?fileNo=${data.fileNo}&officeId=${userInfo.id}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`
      }
    }));

    const res = await response.json();
    if (res) {
      setOpenFileStatusModal(!openFileStatusModal);
      const downloadUrlDetails = `${baseApiURL}/${API_URL.REPORTS.FILE_LOG_GENERATE_REPORTS}?fileNo=${data.fileNo}&officeId=${userInfo.id}&template=${'filelog'}`;
      setDownloadUrl(downloadUrlDetails);
      setModalData(res);
    }
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('service'),
      field: 'serviceName',
      alignment: 'left',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('title'),
      field: 'title',
      alignment: 'left',
      cell: (field) => handleForwardedBy(field)
    },
    {
      header: t('currentUser'),
      alignment: 'left',
      field: 'currentUser',
      cell: (field) => handleCurrentUser(field)
    },
    {
      header: t('status'),
      alignment: 'left',
      field: 'status',
      cell: (field) => handleFileStage(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            viewActions(row);
          }
        }
      ]
    }

  ];

  useEffect(() => {
    if (myFileTableListParams) {
      setTableLoader({ loading: true, id: 'my-file-table' });
      fetchMyFileTableList();
    }
  }, [myFileTableListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setMyFileListParams({
      ...myFileTableListParams,
      page: data,
      officeId: userInfo.id
    });
  };

  useEffect(() => {
    if (myFilesTableList) {
      setTableLoader({ loading: false, id: 'my-file-table' });
      if (Object.keys(myFilesTableList).length > 0) {
        setTableData(myFilesTableList?.content);
        setTotalItems(Number(`${myFilesTableList.totalPages}0`));
        setNumberOfElements(Number(myFilesTableList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [myFilesTableList]);

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setMyFileListParams({
          ...myFileTableListParams,
          searchKeyword: data || null,
          page: 0
        });
        break;
      case FILTER_TYPE.DATE:
        setMyFileListParams({
          ...myFileTableListParams,
          createdAt: data ? convertToLocalDate(data, DATE_FORMAT.DATE_LOCAL) : null,
          page: 0
        });
        // setDate(data);
        break;
      case FILTER_TYPE.SERVICES:
        setMyFileListParams({
          ...myFileTableListParams,
          serviceCodes: [data?.code] || null,
          page: 0
        });
        break;
      default:
        break;
    }
  };

  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1]">
          {t('myFiles')}
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              {search && (
                <button
                  className="pr-2 cursor-pointer"
                  onClick={() => {
                    setSearch('');
                    triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, null);
                  }}
                  aria-label="Clear search input"
                  style={{ background: 'none', border: 'none' }}
                >
                  <ClearIcon />
                </button>
              )} <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>

        {/* <div className="flex-none customFileDatePicker">
          <InputGroup style={styles.date}>
            <Input
              value={date}
              onChange={(event) => triggerSearch(FILTER_TYPE.DATE, event.target.value)}
              type="date"
              style={styles.date.input}
              max={today}
            />
          </InputGroup>
        </div> */}
        <div style={{ width: '300px' }}>
          <FormController
            name="services"
            type="select"
            placeholder={t('searchHere')}
            control={control}
            options={_.get(serviceDropdown, 'data', [])}
            handleChange={(data) => {
              triggerSearch(FILTER_TYPE.SERVICES, data);
            }}
            optionKey="code"
          />
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'my-file-table'}
          numberOfElements={numberOfElements}
        />
        <FileStatusModal openFileStatusModal={openFileStatusModal} handleCloseFileStatusModal={handleCloseFileStatusModal} modalData={modalData} downloadUrl={downloadUrl} />
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  myFilesTableList: getMyFilesListData,
  myFileTableListParams: getMyFilesSearchListParams,
  tableLoader: getTableLoader,
  serviceDropdown: getServicesDropdown

});

const mapDispatchToProps = (dispatch) => ({
  fetchMyFileTableList: (data) => dispatch(actions.fetchMyFileTableList(data)),
  setMyFileListParams: (data) => dispatch(sliceActions.setMyFileListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  fetchServicesOptions: () => dispatch(commonActions.fetchServicesDetails())

});

export default connect(mapStateToProps, mapDispatchToProps)(List);
