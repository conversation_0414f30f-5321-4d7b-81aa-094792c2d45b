import { t } from 'common/components';
import { FILE_STATUS } from 'pages/common/constants';
import _ from 'lodash';
import { STATUS } from 'common/regex';

const statusChange = (stage) => {
  switch (stage) {
    case stage?.status === FILE_STATUS.RUNNING:
      return 'bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center min-w-[100px]';
    case stage?.status === FILE_STATUS.PENDING:
      return 'bg-[#F0F3DE] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#8F914B] text-center min-w-[100px]';
    default:
      return 'bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center min-w-[100px]';
  }
};
export const handleFileStage = (val) => {
  let stage;
  if (val?.row) {
    const cellData = val?.row;
    stage = (
      <div className={statusChange(cellData)}>
        {_.capitalize(cellData?.fileStatus?.replace(STATUS, ' '))}
      </div>
    );
  }
  return <div className="inline-block">{stage}</div>;
};

const handleUserDetails = (userDetails = {}) => {
  return (
    <div>
      {(userDetails?.employeeName !== ' ' || userDetails?.employeeName !== '' || userDetails?.employeeName !== null || userDetails?.employeeName !== undefined) && <div className="text-[16px]">{userDetails?.employeeName}</div>}
      {(userDetails?.penNo !== ' ' || userDetails?.penNo !== '' || userDetails?.penNo !== null || userDetails?.penNo !== undefined) && <div className="text-[14px]">{t('penNo')} : {userDetails?.penNo}</div>}
      {(userDetails?.postNameInEng !== ' ' || userDetails?.postNameInEng !== '' || userDetails?.postNameInEng !== null || userDetails?.postNameInEng !== undefined) && <div className="text-[14px]">{t('seat')} : {userDetails?.postNameInEng}</div>}
      {(userDetails?.designation !== ' ' || userDetails?.designation !== '' || userDetails?.designation !== null || userDetails?.designation !== undefined) && <div className="text-[14px]">{t('designation')} : {userDetails?.designation}</div>}
      {/* {(userDetails?.location !== ' ' || userDetails?.location !== '' || userDetails?.location !== null || userDetails?.location !== undefined) && <div className="text-[14px]">{t('location')} : {userDetails?.location}</div>} */}

    </div>
  );
};

export const handleCurrentUser = (val) => {
  const { currentUser = {} } = val.row;
  return <div className="inline-block">{handleUserDetails(currentUser)}</div>;
};

export const handleFileNumber = (fileData) => {
  let fileNumber;
  if (fileData?.row) {
    const cellData = fileData?.row;
    fileNumber = (
      <>
        <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
        <div className="text-[14px] font-[700] text-[#B5B5B5]">{cellData?.fileDate}</div>
      </>
    );
  }
  return <div className="block">{fileNumber}</div>;
};

export const handleServiceName = (fileData) => {
  let serviceName;
  if (fileData?.row) {
    const cellData = fileData?.row;
    serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.serviceName}</div>;
  }
  return <div className="block">{serviceName}</div>;
};

export const handleForwardedBy = (fileData) => {
  let title;
  if (fileData?.row) {
    const cellData = fileData?.row;
    title = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px]">{cellData?.title}</div>;
  }
  return <div className="block">{title}</div>;
};
