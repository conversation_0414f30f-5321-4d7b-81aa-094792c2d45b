import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  myFileTableListParams: {
    sortDirection: 'desc',
    page: 0,
    size: 10
  }
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setMyFileTableList: (state, { payload }) => {
      _.set(state, 'myFileTableList', payload);
    },
    setMyFileListParams: (state, { payload }) => {
      _.set(state, 'myFileTableListParams', payload);
    }
  }
});

export const { actions, reducer } = slice;
