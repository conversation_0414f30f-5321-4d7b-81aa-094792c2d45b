import {
  all, takeLatest, fork, put, take, select
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import { getMyFilesSearchListParams } from './selectors';

export function* fetchMyFileTableList() {
  const apiParams = yield select(getMyFilesSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchMyFileTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_MY_FILE_LIST_SUCCESS,
    ACTION_TYPES.FETCH_MY_FILE_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_MY_FILE_LIST_SUCCESS) {
    yield put(sliceActions.setMyFileTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'my-file-table' }));
  }
}

export default function* counterSaga() {
  yield all([

    takeLatest(ACTION_TYPES.FETCH_MY_FILE_LIST, fetchMyFileTableList)
  ]);
}
