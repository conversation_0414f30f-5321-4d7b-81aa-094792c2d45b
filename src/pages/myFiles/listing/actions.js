import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_MY_FILE_LIST: `${STATE_REDUCER_KEY}/FETCH_MY_FILE_LIST`,
  FETCH_MY_FILE_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_MY_FILE_LIST_REQUEST`,
  FETCH_MY_FILE_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_MY_FILE_LIST_SUCCESS`,
  FETCH_MY_FILE_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_MY_FILE_LIST_FAILURE`

};
export const fetchMyFileTableList = createAction(ACTION_TYPES.FETCH_MY_FILE_LIST);
