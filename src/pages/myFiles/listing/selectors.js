import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getMyFileDetails = (state) => state[STATE_REDUCER_KEY];

const myFilesListData = (state) => state?.myFileTableList;
export const getMyFilesListData = flow(getMyFileDetails, myFilesListData);

const myFilesSearchListParams = (state) => state?.myFileTableListParams;
export const getMyFilesSearchListParams = flow(getMyFileDetails, myFilesSearchListParams);
