import {
  all,
  fork,
  takeLatest,
  take,
  call,
  put
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import { STORAGE_KEYS } from 'common/constants';
import { setDataToStorage } from 'utils/encryption';
import { roleFormatter } from 'utils/user';
import { routeRedirect } from 'utils/common';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'i18next';
import * as api from './api';
import { ACTION_TYPES } from './actions';

export function* kswiftLogin({ payload = {} }) {
  const { serviceCode, kswiftId, demandId = null } = payload;
  yield fork(handleAPIRequest, api.kswiftLogin, payload);
  const {
    payload: {
      data: {
        data: {
          token,
          userId,
          userType,
          userName,
          name,
          organizationId,
          ...rest
        } = {}
      } = {},
      error
    } = {},
    type
  } = yield take([
    ACTION_TYPES.KSWIFT_LOGIN_SUCCESS,
    ACTION_TYPES.KSWIFT_LOGIN_FAILURE
  ]);

  if (type === ACTION_TYPES.KSWIFT_LOGIN_SUCCESS) {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);

    setDataToStorage(
      STORAGE_KEYS.USER_ROLES,
      roleFormatter([], userType),
      true
    );
    setDataToStorage(STORAGE_KEYS.USER_DETAILS, {
      userName: userName || name, userId, userType, ...rest
    }, true);
    if (demandId) {
      yield call(routeRedirect(`ui/fin/e-pay/summary/${demandId}?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else if (serviceCode === 'PTTPPT') {
      yield call(routeRedirect(`ui/property-tax/pay-tax-for-others?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else if (serviceCode === 'BPPA01') {
      yield call(routeRedirect(`ui/building-permit/applications/permits-and-approvals/new-building-permit/licensee?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else if (serviceCode === 'BPPP02') {
      yield call(routeRedirect(`ui/building-permit/post-permits-and-approval/plinthLevelInspection/permits?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else if (serviceCode === 'BFIF01') {
      yield call(routeRedirect(`ui/business-facilitation/citizen/ifteos-new?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else if (serviceCode === 'BFIF02') {
      yield call(routeRedirect(`ui/business-facilitation/citizen/ifteos-renewal?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else if (serviceCode === 'PTBSOC') {
      yield call(routeRedirect(`ui/property-tax/building-services/ownership-change/citizen?serviceCode=${serviceCode}&kswiftId=${kswiftId}`));
    } else {
      yield call(routeRedirect(`ui/file-management/citizen/e-file/${serviceCode}?kswiftId=${kswiftId}`));
    }
  } else {
    yield put(
      commonSliceActions.setAlertAction(
        {
          open: true,
          variant: 'error',
          message: error?.response?.data?.message,
          title: t('unAuthorised')
        }
      )
    );
  }
}

export default function* kswiftSaga() {
  yield all([
    takeLatest(ACTION_TYPES.KSWIFT_LOGIN, kswiftLogin)
  ]);
}
