import { Progress } from 'common/components';
import { t } from 'i18next';
import React, { useEffect } from 'react';

import { connect } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import * as actions from '../actions';

const KswiftLogin = (props) => {
  const {
    kswiftLogin
  } = props;

  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (
      searchParams?.get('userId')
      && searchParams?.get('serviceCode')
      && searchParams?.get('kswiftId')
      && searchParams?.get('demandId')
    ) {
      kswiftLogin({
        userId: searchParams.get('userId'),
        serviceCode: searchParams.get('serviceCode'),
        kswiftId: searchParams.get('kswiftId'),
        demandId: searchParams.get('demandId')
      });
    } else if (
      searchParams?.get('userId')
      && searchParams?.get('serviceCode')
      && searchParams?.get('kswiftId')
    ) {
      kswiftLogin({
        userId: searchParams.get('userId'),
        serviceCode: searchParams.get('serviceCode'),
        kswiftId: searchParams.get('kswiftId')
      });
    }
  }, [searchParams]);

  return (
    <div className="w-full flex align-middle bg-white" style={{ height: 'calc( 100vh - 100px)' }}>
      <div className="w-[300px] m-auto text-center">
        <div className="mb-5">
          {t('securelySigninToKsmart')}
        </div>
        <Progress size="xs" isIndeterminate />
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  // allEnrolls: getAllEnrolls
});

const mapDispatchToProps = (dispatch) => ({
  kswiftLogin: (data) => dispatch(actions.kswiftLogin(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(KswiftLogin);
