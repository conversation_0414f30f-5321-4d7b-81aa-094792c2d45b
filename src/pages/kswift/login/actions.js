import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  KSWIFT_LOGIN: `${STATE_REDUCER_KEY}/KSWIFT_LOGIN`,
  KSWIFT_LOGIN_REQUEST: `${STATE_REDUCER_KEY}/KSWIFT_LOGIN_REQUEST`,
  KSWIFT_LOGIN_SUCCESS: `${STATE_REDUCER_KEY}/KSWIFT_LOGIN_SUCCESS`,
  KSWIFT_LOGIN_FAILURE: `${STATE_REDUCER_KEY}/KSWIFT_LOGIN_FAILURE`

};

export const kswiftLogin = createAction(ACTION_TYPES.KSWIFT_LOGIN);
