import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const kswiftLogin = (data) => {
  return {
    url: API_URL.KSWIFT.LOGIN.replace(':userId', data?.userId).replace(':serviceCode', data?.serviceCode),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.KSWIFT_LOGIN_REQUEST,
        ACTION_TYPES.KSWIFT_LOGIN_SUCCESS,
        ACTION_TYPES.KSWIFT_LOGIN_FAILURE
      ]
    }
  };
};
