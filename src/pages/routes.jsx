import { InfoPages } from 'common/components';
import { lazy } from 'react';
import { Navigate } from 'react-router-dom';
import { BASE_PATH } from 'common/constants';
import RouteRedirectComponent from 'common/components/RouteRedirectComponent';
import { routes as counterRoutes } from './counter/routes';
import { routes as arisingRoutes } from './arising/routes';
import { routes as legacyfilesRoutes } from './legacyfiles/routes';
import { routes as fileRoutes } from './file/routes';
import { routes as reportsRoutes } from './reports/routes';
import { routes as searchFileRoutes } from './searchFile/routes';
import { routes as citizenRoutes } from './citizen/routes';
import { routes as archivedFileRoutes } from './archivedFile/routes';
import { routes as processedFileRoutes } from './ProcessesdFile/routes';
import { routes as temporaryDisposedFileRoutes } from './temporaryDisposedFile/routes';
import { routes as inwardSearchRoutes } from './inwardSearch/routes';
import { routes as scpRoutes } from './scpList/routes';
import { routes as publicFileSearchRoutes } from './publicFileTracking/routes';
import { routes as profileRoutes } from './profile/routes';
import { routes as reassignFilesRoutes } from './reassignFiles/routes';
import { routes as kswiftRoutes } from './kswift/routes';
import { routes as recordRoomRoutes } from './recordRoom/routes';
import { routes as dispatchLatestRoutes } from './Dispatch-latest/routes';
import { routes as esignRoutes } from './esign/routes';
import { routes as myFilesRoutes } from './myFiles/routes';
import { routes as eSignedRoutes } from './eSignRequests/routes';

const App = lazy(() => import('../App'));
const Layout = lazy(() => import('../layout'));
const File = lazy(() => import('../layout/File'));
const Services = lazy(() => import('../layout/Services'));
const Dashboard = lazy(() => import('../layout/Dashboard'));
const Profile = lazy(() => import('../layout/Profile'));
const Kswift = lazy(() => import('../layout/Kswift'));
const Esign = lazy(() => import('../layout/Esign'));

export const routes = [
  {
    path: '/',
    element: <RouteRedirectComponent />,
    errorElement: <InfoPages.ErrorBoundary />
  },
  {
    path: '/ui/file-management',
    element: <RouteRedirectComponent />,
    errorElement: <InfoPages.ErrorBoundary />
  },
  {
    path: '/ui/file-management/',
    element: <RouteRedirectComponent />,
    errorElement: <InfoPages.ErrorBoundary />
  },
  {
    path: BASE_PATH,
    element: <Navigate to={`${BASE_PATH}/dashboard`} replace />,
    errorElement: <InfoPages.ErrorBoundary />
  },
  {
    path: BASE_PATH,
    element: <App />,
    errorElement: <InfoPages.ErrorBoundary />,
    children: [
      {
        path: 'dashboard',
        element: <Dashboard />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: []
      },
      {
        path: 'services',
        element: <Services />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...reportsRoutes,
          ...fileRoutes,
          ...searchFileRoutes,
          ...archivedFileRoutes,
          ...processedFileRoutes,
          ...temporaryDisposedFileRoutes,
          ...inwardSearchRoutes,
          ...scpRoutes,
          ...reassignFilesRoutes,
          ...recordRoomRoutes,
          ...dispatchLatestRoutes,
          ...myFilesRoutes,
          ...eSignedRoutes
        ]
      },

      {
        path: 'application',
        element: <Layout />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...counterRoutes,
          ...arisingRoutes,
          ...legacyfilesRoutes
        ]
      },
      {
        path: 'citizen',
        element: <Layout />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...citizenRoutes
        ]
      },
      {
        path: 'file',
        element: <File />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...fileRoutes
        ]
      },
      {
        path: 'public',
        element: <Services />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...publicFileSearchRoutes
        ]
      },
      {
        path: 'profile',
        element: <Profile />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...profileRoutes
        ]
      },
      {
        path: 'kswift',
        element: <Kswift />,
        errorElement: <InfoPages.ErrorBoundary />,
        children: [
          ...kswiftRoutes
        ]
      },
      {
        path: 'esign',
        errorElement: <InfoPages.ErrorBoundary />,
        element: <Esign />,
        children: [
          ...esignRoutes
        ]
      }
    ]
  },
  {
    path: '*',
    element: <InfoPages.NotFound />
  }
];
