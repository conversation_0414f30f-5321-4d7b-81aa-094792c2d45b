import { baseApiURL } from 'utils/http';

export const STATE_REDUCER_KEY = 'counter';

export const inwardAknowledgementUrl = `${baseApiURL}/inward-management-services/generate-acknowledgement/`;
export const documenturl = `${baseApiURL}/inward-management-services/preview-documents`;

export const SIDEBAR_KEYS = {
  APPLICATION: 'APPLICATION',
  REQUIRED_DOCUMENTS: 'REQUIRED_DOCUMENTS',
  GUIDELINES: 'GUIDELINES',
  QUICK_ACCESS: 'QUICK_ACCESS',
  PROCESS: 'PROCESS'
};

export const COUNTER_APPLICATION_KEYS_INDEX = {
  Service: 0,
  Applicant: 1,
  General: 2,
  Document: 3
};

export const COUNTER_APPLICATION_KEYS = {
  Service: 'Service',
  Applicant: 'Applicant',
  General: 'General',
  Document: 'Document'
};

export const localbodyRadioOptions = [{
  id: 1,
  name: 'Applicant'
},
{
  id: 2,
  name: 'Institution'
}];

export const documentRadioOptions = [{
  id: 1,
  name: 'Aadhaar Number'
}, {
  id: 2,
  name: 'UDID Number'
}];

export const countryCode = [{
  id: 1,
  code: 'IND',
  phoneCode: '91'
},
{
  id: 2,
  code: 'USA',
  phoneCode: '1'
}];

export const ownershipOptions = [{
  id: 1,
  name: 'Own'
}, {
  id: 2,
  name: 'Rental'
}];

export const localBodyPropertyType = [{
  id: 1,
  name: 'Market'
}, {
  id: 2,
  name: 'Bus Stand'
}, {
  id: 3,
  name: 'Shopping'
}, {
  id: 2,
  name: 'Complex'
}];

export const buildingUsage = [{
  id: 1,
  name: 'Residence'
}, {
  id: 2,
  name: 'Commercial'
}];

export const functions = [{
  id: 1,
  name: 'Functional Group 1'
}, {
  id: 2,
  name: 'Functional Group 2'
}];

export const functionalGroup = [
  {
    id: 1,
    code: '10',
    name: 'General Administration',
    nameInLocal: 'ജനറൽ അഡ്മിനിസ്ട്രേഷൻ',
    active: true
  },
  {
    id: 2,
    code: '20',
    name: 'Human Resources Management',
    nameInLocal: 'ഹ്യൂമൻ റിസോഴ്സ്സ് മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 3,
    code: '30',
    name: 'House Keeping',
    nameInLocal: 'ഹൗസ് കീപ്പിങ്',
    active: true
  },
  {
    id: 4,
    code: '40',
    name: 'Finance Management',
    nameInLocal: 'ഫിനാൻസ്  മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 5,
    code: '50',
    name: 'Town Planning/ Regulations',
    nameInLocal: 'ടൌൺ  പ്ലാനിംഗ് / റെഗുലേഷൻസ്',
    active: true
  },
  {
    id: 6,
    code: '60',
    name: 'Public Work',
    nameInLocal: 'പബ്ലിക്  വർക്ക് ',
    active: true
  },
  {
    id: 7,
    code: '70',
    name: 'Civic Service',
    nameInLocal: 'സിവിക്  സർവീസ്',
    active: true
  },
  {
    id: 8,
    code: '80',
    name: 'Health & Sanitation',
    nameInLocal: 'ഹെൽത്ത്  & സാനിറ്റേഷൻ',
    active: true
  },
  {
    id: 9,
    code: '90',
    name: 'Property Management',
    nameInLocal: 'പ്രോപ്പർട്ടി  മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 10,
    code: '110',
    name: 'Welfare Schemes',
    nameInLocal: 'വെൽഫെയർ സ്കീംസ് ',
    active: true
  },
  {
    id: 11,
    code: '120',
    name: 'Social Engineering',
    nameInLocal: 'സോഷ്യൽ  എഞ്ചിനീയറിംഗ്',
    active: true
  },
  {
    id: 12,
    code: '130',
    name: 'Social Service',
    nameInLocal: 'സോഷ്യൽ  സർവീസ്',
    active: true
  },
  {
    id: 13,
    code: '140',
    name: 'Allied Institution Management',
    nameInLocal: 'അലൈഡ്  ഇന്സ്ടിട്യൂഷൻ   മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 14,
    code: '150',
    name: 'Revenue Management',
    nameInLocal: 'റെവന്യൂ മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 15,
    code: '160',
    name: 'Disaster management',
    nameInLocal: 'ഡിസാസ്റ്റർ  മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 16,
    code: '170',
    name: 'De-centralised plan management',
    nameInLocal: 'ഡി -സെൻട്രലൈസ്ഡ്  പ്ലാൻ  മാനേജ്‌മന്റ്',
    active: true
  },
  {
    id: 17,
    code: '180',
    name: 'Fairs & Festivals',
    nameInLocal: 'ഫയേഴ്‌സ്   & ഫെസ്ടിവൽസ്',
    active: true
  },
  {
    id: 18,
    code: '190',
    name: 'Conservation',
    nameInLocal: 'കൺസേർവഷൻ',
    active: true
  }
];

export const localBodyCode = 'kl.cochin';

export const ADDRESS_TYPE = {
  APPLICANT_ADDRESS_INSIDE_LOCAL_BODY: 'Applicant Address Inside Local Body',
  APPLICANT_ADDRESS_OUTSIDE_LOCAL_BODY: 'Applicant Address Outside Local Body'
};

export const LOCAL_BODY_TYPE_IDS = {
  APPLICANT_DETAILS_ID: 'applicantDetailsId',
  INSTITION_DETAILS_ID: 'institutionDetailsId',
  EFILE_APPLICANT_DETAILS_ID: 'efileApplicantDetailsId',
  EFILE_INSTITION_DETAILS_ID: 'efileInstitutionDetailsId'
};

export const LOCAL_BODY_TYPE = {
  JOINT_APPLICANT_ADDRESS_INSIDE_LOCAL_BODY: 'Joint Applicant Address Inside Local Body',
  JOINT_APPLICANT_ADDRESS_OUTSIDE_LOCAL_BODY: 'Joint Applicant Address Outside Local Body'
};

export const DOCUMENT_TYPE = {
  AADHAR: 'aadharNo',
  UUID: 'udid',
  PASSPORT: 'passport'
};

export const DOCUMENTS_IGNORE_LIST = [
  'RIRI01', 'RIRI02', 'RIRI03'
];
