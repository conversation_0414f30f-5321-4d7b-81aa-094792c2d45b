import {
  all, takeLatest, call, fork, put, take, select
} from 'redux-saga/effects';
import { Toast, t } from 'common/components';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as fileSliceActions } from 'pages/file/details/slice';
import {
  getSidebarData
} from 'pages/common/selectors';
import { BASE_PATH, FINANCE_BASE_PATH } from 'common/constants';
import { fetchFileDetails } from 'pages/file/details/saga';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';
import { COUNTER_APPLICATION_KEYS } from './constants';

const { successTost, errorTost } = Toast;

export function* fetchApplication({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchApplication, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.FETCH_APPLICATION_SERVICE_FAILURE]);
  if (type === ACTION_TYPES.FETCH_APPLICATION_SERVICE_SUCCESS) {
    yield put(sliceActions.setActiveCounterFormData(responsePayLoad?.data));
    yield put(commonSliceActions.setActionTriggered(false));
  }
}

export function* saveApplication({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveApplicationServiceApi, payload);
  const formComponentData = yield select(getSidebarData);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.SAVE_APPLICATION_SERVICE_FAILURE]);
  if (type === ACTION_TYPES.SAVE_APPLICATION_SERVICE_SUCCESS) {
    yield put(commonSliceActions.setActiveAccordian(COUNTER_APPLICATION_KEYS.Service));
    yield put(commonSliceActions.setSidebarStatus({ activeStep: 1, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    yield put(sliceActions.setActiveInwardId(responsePayLoad?.data));
    yield call(fetchApplication, { payload: responsePayLoad?.data });
    yield call(successTost, { id: 'saved', title: t('success'), description: t('success') });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counterServiceCreate' }));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counterServiceCreate' }));
  }
}

export function* updateApplication({ payload = {} }) {
  const { fileNo, ...rest } = payload;
  yield fork(handleAPIRequest, api.updateApplicationServiceApi, rest);
  const formComponentData = yield select(getSidebarData);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS,
    ACTION_TYPES.UPDATE_APPLICATION_SERVICE_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_APPLICATION_SERVICE_SUCCESS) {
    yield put(commonSliceActions.setActiveAccordian(COUNTER_APPLICATION_KEYS.General));
    yield put(commonSliceActions.setSidebarStatus({ activeStep: payload?.from === 'beneficiary' ? 1 : 2, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    if (fileNo) {
      yield put(fileSliceActions.setActiveBenAccordian(1));
    } else {
      yield put(sliceActions.setActiveInwardId(responsePayLoad?.data?.inwardId));
    }
    yield call(fetchApplication, { payload: responsePayLoad?.data?.inwardId });

    yield call(successTost, { id: 'saved', title: t('success'), description: responsePayLoad.message });
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant' }));
    if (payload?.from === 'beneficiary') {
      if (payload?.applicantId) {
        yield put(
          commonSliceActions.navigateTo({
            to: `${BASE_PATH}/file/${fileNo}/beneficiary/application/${responsePayLoad?.data?.inwardId}/${payload?.applicantId}`
          })
        );
      } else {
        const currentApplicantIds = payload?.existingApplicants;
        const updatedApplicantIds = responsePayLoad?.data?.applicantDetailsId;
        const newid = updatedApplicantIds.filter((item) => !currentApplicantIds?.includes(item));
        yield put(
          commonSliceActions.navigateTo({
            to: `${BASE_PATH}/file/${fileNo}/beneficiary/application/${responsePayLoad?.data?.inwardId}/${newid[0]}`
          })
        );
      }
    }
  } else {
    const errorMessage = responsePayLoad?.error?.response?.data?.errors || {};
    const values = Object.values(errorMessage).toString().replace(',', '<br />');

    yield put(commonSliceActions.setAlertAction({
      open: true,
      variant: 'error',
      message: values || t('applicantSaveFailed'),
      title: t('error'),
      backwardActionText: t('ok')
    }));
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-applicant' }));
  }
}

export function* fetchServicesByServiceCode({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchServiceByServiceCode, payload);
}

export function* fetchSubModulesByModuleId({ payload = {} }) {
  yield fork(handleAPIRequest, api.fetchSubModulesByModuleId, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS,
    ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID_SUCCESS) {
    yield put(sliceActions.setSubModulesSearchList(_.get(responsePayLoad, 'data', {})));
  }
}

export function* saveDocuments({ payload = {} }) {
  const { supportingDocs, request, handleClear = () => { } } = payload;
  const formData = new FormData();
  formData.append('supportingDocs', supportingDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));

  yield fork(handleAPIRequest, api.saveDocuments, formData);
  const { type } = yield take([
    ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_DOCUMENTS_SUCCESS) {
    handleClear();
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-supporting-doc-upload' }));
    yield call(fetchApplication, { payload: request.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-supporting-doc-upload' }));
  }
}

export function* saveMandatoryDocuments({ payload = {} }) {
  const { documentTypeDocs, request } = payload;
  const formData = new FormData();
  formData.append('documentTypeDocs', documentTypeDocs);
  formData.append('request', new Blob([JSON.stringify(request)], {
    type: 'application/json'
  }));

  yield fork(handleAPIRequest, api.saveMandatoryDocuments, formData);
  const { type } = yield take([
    ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS,
    ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS_SUCCESS) {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-mandatory-doc-upload' }));
    yield call(fetchApplication, { payload: request.inwardId });
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counter-mandatory-doc-upload' }));
  }
}

export function* getDocumentTypes({ payload = {} }) {
  yield fork(handleAPIRequest, api.getDocumentTypes, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DOCUMENT_TYPES_SUCCESS,
    ACTION_TYPES.FETCH_DOCUMENT_TYPES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_DOCUMENT_TYPES_SUCCESS) {
    yield put(sliceActions.setDocumentTypes(responsePayLoad.data));
  }
}

export function* saveDocumentsTypeData({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveDocumentsTypeData, payload);
  const { type } = yield take([
    ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_SUCCESS,
    ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_FAILURE]);
  if (type === ACTION_TYPES.SAVE_DOCUMENTS_TYPE_DATA_SUCCESS) {
    yield call(successTost, { id: t('saved'), title: t('success'), description: t('fileUploadSuccess') });
    yield call(fetchApplication, { payload: payload.id });
  } else {
    yield call(errorTost, { id: t('error'), title: t('error'), description: t('fileUploadFailed') });
  }
}

export function* saveGeneralDetails({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveGeneralDetails, payload);
  const formComponentData = yield select(getSidebarData);
  const { payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.SAVE_GENERAL_DETAILS_SUCCESS,
    ACTION_TYPES.SAVE_GENERAL_DETAILS_FAILURE]);
  yield put(commonSliceActions.setActionTriggered({ loading: false, id: 'counterGeneralDetailsCreate' }));
  if (responsePayLoad?.status === 'SUCCESS') {
    if (payload?.from === 'beneficiary') {
      yield call(fetchFileDetails, { payload: payload?.fileNo });
      yield put(commonSliceActions.setSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
      yield put(
        commonSliceActions.navigateTo({
          to: `${BASE_PATH}/file/${payload?.fileNo}/beneficiary`
        })
      );
    } else {
      yield call(fetchApplication, { payload: responsePayLoad?.data?.inwardId });
      yield put(commonSliceActions.setActiveAccordian(COUNTER_APPLICATION_KEYS.General));
      yield put(commonSliceActions.setSidebarStatus({ activeStep: payload?.from === 'counter' ? 3 : 2, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    }
    yield call(successTost, { id: 'saved', title: t('success'), description: t('generalDetailsSavedSucessfully') });
  } else {
    yield call(errorTost, { id: t('failed'), title: t('failed'), description: t('generalDetailsSaveFailed') });
  }
}

export function* saveComplete({ payload = {} }) {
  yield fork(handleAPIRequest, api.saveComplete, payload);
  const formComponentData = yield select(getSidebarData);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.COMPLETE_SAVE_SUCCESS,
    ACTION_TYPES.COMPLETE_SAVE_FAILURE]);
  if (type === ACTION_TYPES.COMPLETE_SAVE_SUCCESS) {
    yield call(successTost, { id: 'saved', title: 'Success', description: 'success' });
    yield put(commonSliceActions.setInwardNextUserModal(false));
    yield put(commonSliceActions.setActiveAccordian(COUNTER_APPLICATION_KEYS.Service));
    yield put(commonSliceActions.setSidebarStatus({ activeStep: 0, completedSteps: [...formComponentData.completedSteps, formComponentData.activeStep] }));
    yield put(commonSliceActions.setAcknowledgeTriggered({ loading: false, id: 'counterCreate' }));
    yield call(fetchApplication, { payload: responsePayLoad?.data });
  }
}

export function* deleteApplicant({ payload = {} }) {
  yield fork(handleAPIRequest, api.deleteApplicant, payload);
  const { fileNo } = payload;
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.DELETE_APPLICANT_SUCCESS,
    ACTION_TYPES.DELETE_APPLICANT_FAILURE]);
  if (type === ACTION_TYPES.DELETE_APPLICANT_SUCCESS) {
    if (fileNo) {
      yield call(fetchFileDetails, { payload: fileNo });
    } else {
      yield call(fetchApplication, { payload: payload.inwardId });
    }
    yield call(successTost, { id: 'saved', title: t('success'), description: responsePayLoad.message });
  } else {
    yield call(successTost, { id: 'failed', title: t('failed'), description: responsePayLoad.message });
  }
}

export function* generateDemand({ payload = {} }) {
  yield fork(handleAPIRequest, api.generateDemand, payload);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.GENERATE_DEMANT_SUCCESS,
    ACTION_TYPES.GENERATE_DEMANT_FAILURE]);
  if (type === ACTION_TYPES.GENERATE_DEMANT_SUCCESS) {
    if (responsePayLoad?.data?.demand?.id) {
      window.location.href = `${FINANCE_BASE_PATH}/file-receipt-generation/${responsePayLoad?.data?.demand?.id}`;
    }
  }
}

export function* updateDocuments({ payload: { requestData = {}, handleClear = () => { } } }) {
  const { inwardId } = requestData;
  yield fork(handleAPIRequest, api.updateDocuments, requestData);
  const { type } = yield take([
    ACTION_TYPES.UPDATE_DOCUMENTS_SUCCESS,
    ACTION_TYPES.UPDATE_DOCUMENTS_FAILURE]);
  if (type === ACTION_TYPES.UPDATE_DOCUMENTS_SUCCESS) {
    handleClear();
    yield call(fetchApplication, { payload: inwardId });
    yield call(successTost, { id: 'saved', title: t('success'), description: t('documentsUpdatedSuccessfully') });
    yield put(commonSliceActions.setActionTriggered({ loading: false }));
  } else {
    yield call(errorTost, { id: 'failed', title: t('failed'), description: t('documentsUpdateFailed') });
    yield put(commonSliceActions.setActionTriggered({ loading: false }));
  }
}

export default function* counterSaga() {
  yield all([
    takeLatest(ACTION_TYPES.SAVE_APPLICATION_SERVICE, saveApplication),
    takeLatest(ACTION_TYPES.UPDATE_APPLICATION_SERVICE, updateApplication),
    takeLatest(ACTION_TYPES.FETCH_APPLICATION_SERVICE, fetchApplication),
    takeLatest(ACTION_TYPES.FETCH_SERVICE_BY_SERVICE_CODE, fetchServicesByServiceCode),
    takeLatest(ACTION_TYPES.FETCH_SUB_MODULES_BY_MODULE_ID, fetchSubModulesByModuleId),
    takeLatest(ACTION_TYPES.SAVE_DOCUMENTS, saveDocuments),
    takeLatest(ACTION_TYPES.SAVE_MANDATORY_DOCUMENTS, saveMandatoryDocuments),
    takeLatest(ACTION_TYPES.FETCH_DOCUMENT_TYPES, getDocumentTypes),
    takeLatest(ACTION_TYPES.SAVE_GENERAL_DETAILS, saveGeneralDetails),
    takeLatest(ACTION_TYPES.COMPLETE_SAVE, saveComplete),
    takeLatest(ACTION_TYPES.DELETE_APPLICANT, deleteApplicant),
    takeLatest(ACTION_TYPES.GENERATE_DEMANT, generateDemand),
    takeLatest(ACTION_TYPES.UPDATE_DOCUMENTS, updateDocuments)
  ]);
}
