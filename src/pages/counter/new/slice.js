import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';
import { ACTION_TYPES } from './actions';

const initialState = {
  activeAccordian: 'Service',
  subModule: {},
  module: {},
  subModulesSearch: {},
  servicesSearchList: {},
  activeCounterFormData: {},
  activeInwardId: '',
  jointApplicant: [],
  documentTypes: [],
  generalFieldValidation: {},
  documentPreview: {},
  requiredDocsCount: 0
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setSubModulesSearchList: (state, { payload }) => {
      _.set(state, 'subModulesSearchList', payload);
    },
    setServicesSearchList: (state, { payload }) => {
      _.set(state, 'servicesSearchList', payload);
    },
    setActiveCounterFormData: (state, { payload }) => {
      _.set(state, 'activeCounterFormData', payload);
    },
    setDocumentDetails: (state, { payload }) => {
      _.set(state, 'documentDetails', payload);
    },
    setDocumentTypes: (state, { payload }) => {
      _.set(state, 'documentTypes', payload);
    },
    setActiveInwardId: (state, { payload }) => {
      _.set(state, 'activeInwardId', payload);
    },
    setJointApplicant: (state, { payload }) => {
      _.set(state, 'jointApplicant', payload);
    },
    setGeneralFieldValidation: (state, { payload }) => {
      _.set(state, 'generalFieldValidation', payload);
    },
    setRequiredDocsCount: (state, { payload }) => {
      _.set(state, 'requiredDocsCount', payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(ACTION_TYPES.FETCH_SERVICE_BY_SERVICE_CODE_SUCCESS, (state, { payload }) => {
        _.set(state, 'serviceByCodeDetails', payload.data);
      });
  }
});

export const { actions, reducer } = slice;
