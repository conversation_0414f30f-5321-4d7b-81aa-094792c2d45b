import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import _ from 'lodash';
import {
  <PERSON><PERSON><PERSON><PERSON>, FormController, t, Button, IconButton
} from 'common/components';
import WhatsappIcon from 'assets/Whatsapp';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as commonActions from 'pages/common/actions';
import {
  getCountry,
  getState,
  getDistricts,
  getWard,
  getPostOffice,
  getActionTriggered,
  getUserLocalBody,
  getUserInfo,
  getOtpStatus,
  getGenerateAadharOtp,
  getSmartDetails
} from 'pages/common/selectors';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DEFAULT_COUNTRY, DEFAULT_STATE } from 'common/constants';
import { CommonTable } from 'common/components/Table';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import Edit from 'assets/Edit';
import Delete from 'assets/delete';
import { scrollToTop } from 'pages/common/components/ScrollEvent';
import {
  EMAIL_ONLY, ENG_ONLY, ML_ONLY, MOBILE_ONLY
} from 'common/regex';
import Registration from 'common/components/Registration';
import { reFormattedDate } from 'utils/date';
import { Spinner } from '@ksmartikm/ui-components';
import { applicantName, checkType } from 'pages/common/helper';
import { nameValidation } from 'utils/validateFile';
import { getJointApplicant } from '../selectors';
import { ApplicatDetailsFormSchema } from '../validate';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';
import { localbodyRadioOptions } from '../constants';
import {
  applicantDefaultValues,
  docLength,
  docNumber,
  formatApplicantDetails,
  formateDocumentType,
  setDocument
} from './helper';

const ApplicantDetails = (props) => {
  const {
    fetchCountry,
    countryDropdown,
    fetchStates,
    stateDropdown,
    fetchDistricts,
    districtDropdown,
    fetchWard,
    postOfficeDropdown,
    fetchPostOffice,
    updateApplication,
    formActiveData,
    jointApplicantData,
    setJointApplicant,
    deleteApplicant,
    actionTriggered,
    setActionTriggered,
    userLocalBody,
    userInfo,
    otpStatus,
    verifyAadharOtp,
    generateAadharOtpRes,
    // fetchSmartProfile,
    smartDetails,
    fetchPostOfficeByPin,
    inwardID,
    fetchApplication,
    from
    // setRegisterOpen,
    // setAlertAction
  } = props;

  const [jointApplications, setJointApplications] = useState([]);
  const [editState, setEditState] = useState(false);
  const [editIndex, setEditIndex] = useState('');
  const [editId, setEditId] = useState(null);

  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    reset,
    formState: { errors }
  } = useForm({
    mode: 'all',
    defaultValues: applicantDefaultValues,
    resolver: yupResolver(ApplicatDetailsFormSchema)
  });

  const params = useParams();
  const localbodyType = watch('localbodyType');
  const countrySelected = watch('countryId');
  const documentType = watch('documentType');
  const isWhatsappSame = watch('isWhatsappSame');
  const jointApplication = watch('jointApplication');
  const mobileNo = watch('mobileNo');
  const whatsapp = watch('whatsapp');
  const stateSelected = watch('stateId');
  const [hideButton, setHideButton] = useState(false);
  const [isPostal, setIsPostal] = useState(false);

  const resetData = () => {
    reset(
      { ...applicantDefaultValues, jointApplication: !!jointApplication, isPostal: !!isPostal },
      { keepValues: false }
    );
    setJointApplications([]);
  };
  useEffect(() => {
    if (jointApplications?.length && from) {
      setHideButton(!!(from === 'beneficiary' && jointApplications.length));
    }
  }, [from, jointApplications]);

  useEffect(() => {
    if (smartDetails?.length > 0) {
      const smartData = smartDetails[0];

      const fetchedFirstname = smartData?.name?.replace(/\./g, ' ');
      const spaceRemovedFirstName = fetchedFirstname?.replace(/ +(?= )/g, '');

      setValue('firstName', spaceRemovedFirstName);
      setValue('emailId', smartData.email);
      setValue('mobileNo', smartData.phoneNumber);
      setValue('whatsapp', smartData.whatsappNumber);
      setValue('documentNo', smartData.aadhaarNo);
      if (smartData.aadhaarNo) {
        setValue('documentType', 1);
      }
    }
  }, [smartDetails]);

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);
  useEffect(() => {
    if (inwardID) {
      fetchApplication(inwardID);
    }
  }, [inwardID]);

  useEffect(() => {
    if (userLocalBody?.districtId) {
      fetchPostOffice({ districtId: userLocalBody?.districtId });
    }
  }, [userLocalBody?.districtId]);

  const onDeleteActions = (data, index) => {
    if (data.id) {
      deleteApplicant({
        inwardId: params?.id ? params?.id : inwardID,
        applicantId: data.id
      });
    } else {
      const existingArray = JSON.parse(JSON.stringify(jointApplications));
      existingArray.splice(index, 1);
      setEditId('');
      setEditIndex(null);
      setEditState(false);
      setJointApplicant(existingArray);
    }
  };

  const formatAddressType = (data) => {
    if (data.addressType && Number(data.addressType) !== 3) {
      return Number(data.addressType);
    }
    return Number(data.localbodyType);
  };

  const onEditActions = (data, index) => {
    scrollToTop('applicant_details');
    setEditState(true);
    setEditIndex(index);
    setEditId(data.id);
    if (Number(data.addressType) === 1) {
      fetchPostOffice({ offLbCode: userInfo.id });
    }
    setValue('localbodyType', formatAddressType(data));
    setValue('countryId', Number(data.countryId));
    setValue('stateId', Number(data.stateId));
    setValue('districtId', Number(data.districtId));
    setValue('documentType', formateDocumentType(data));
    setValue('documentNo', setDocument(data));
    setValue('firstName', data.firstName);
    setValue('middleName', data.middleName);
    setValue('lastName', data.lastName);
    setValue('localFirstName', data.localFirstName);
    setValue('localMiddleName', data.localMiddleName);
    setValue('localLastName', data.localLastName);
    setValue('houseName', data.houseName);
    setValue('localHouseName', data.localHouseName);
    setValue('postOffice', parseInt(data.postOffice, 10));
    setValue('pincode', data.pincode);
    setValue('street', data.street);
    setValue('localPlace', data.localPlace);
    setValue('mainPlace', data.mainPlace);
    setValue('localStreet', data.localStreet);
    setValue('localLocalPlace', data.localLocalPlace);
    setValue('localMainPlace', data.localMainPlace);
    setValue('emailId', data.emailId);
    setValue('mobileNo', data.mobileNo);
    setValue('institutionName', data.institutionName);
    setValue('isWhatsappSame', data.isWhatsappSame);
    setValue('whatsapp', data.whatsappNo);
    setValue('landlineNo', data.landlineNo);
    setValue('referenceNo', data.referenceNo);
    setValue('institutionDate', data.institutionDate);
    setValue('officerName', data.officerName);
    setValue('designation', data.designation);
    setValue('postOfficeName', data.postOfficeName);
  };

  const tableActions = (row) => {
    return (
      <>
        <IconButton variant="unstyled" onClick={() => onDeleteActions(row?.row, row.rowIndex)} icon={<Delete />} />
        <IconButton variant="unstyled" onClick={() => onEditActions(row?.row, row.rowIndex)} icon={<Edit />} />
      </>
    );
  };

  const columns = [
    {
      header: t('slNo'),
      cell: ({ rowIndex }) => rowIndex + 1,
      alignment: 'left'
    },
    {
      header: t('name'),
      field: 'firstName',
      alignment: 'left',
      cell: applicantName
    },
    {
      header: t('type'),
      field: 'type',
      alignment: 'left',
      cell: checkType
    },
    {
      header: t('phoneNumber'),
      field: 'mobileNo',
      alignment: 'left'
    },
    {
      header: t('action'),
      alignment: 'left',
      cell: tableActions
    }
  ];

  const [joinStatus, setJoinStatus] = useState(false);

  const handleJointCheck = () => {
    setJoinStatus(!joinStatus);
    setValue('jointApplication', !joinStatus);
  };

  useEffect(() => {
    if (userLocalBody) {
      if (getValues('localbodyType') === 1 || getValues('localbodyType') === 2) {
        setValue('countryId', DEFAULT_COUNTRY.id);
        setValue('stateId', userLocalBody?.stateCode);
        setValue('districtId', userLocalBody?.districtId);
      }
    }
  }, [userLocalBody]);

  // const handleRegister = () => {
  //   setAlertAction(false);
  //   setRegisterOpen(true);
  // };

  // const fetchKsmartId = () => {
  //   setActionTriggered({ loading: true, id: 'fetchKsmartId' });
  //   const sendData = {
  //     phoneNumber: getValues('mobileNo'),
  //     project: 'KSMART',
  //     module: 'PENSION',
  //     userType: USER_TYPE.CITIZEN
  //   };
  //   fetchSmartProfile({ sendData, handleRegister });
  // };

  const verifyAadharOtpFuc = () => {
    const sendData = {
      aadhaarNo: getValues('documentNo'),
      otp: getValues('validateAadhar'),
      txn: generateAadharOtpRes?.txn,
      project: 'KSMART',
      module: 'PENSION'
    };
    setActionTriggered({ loading: true, id: 'validateAadharOtp' });
    verifyAadharOtp(sendData);
  };

  const checkAadhaarExist = () => {
    // const find = jointApplications.findIndex(((item) => item.documentNo === data));
  };

  const handleFieldChange = (field, data) => {
    const malayalamOnly = data?.target?.value?.replace(ML_ONLY, '');
    const emailOnly = data?.target?.value?.replace(EMAIL_ONLY, '');
    const mobileValidation = data?.target?.value?.replace(MOBILE_ONLY, '');
    const nameValidate = nameValidation(data?.target?.value);
    if (data) {
      switch (field) {
        case 'localbodyType':
          setValue('localbodyType', Number(data));
          if (Number(data) === 1) {
            fetchDistricts(DEFAULT_STATE.id);
            setValue('countryId', DEFAULT_COUNTRY.id);
            setValue('stateId', userLocalBody?.stateCode);
            setValue('districtId', userLocalBody?.districtId);
            fetchPostOffice({ offLbCode: userInfo.id });
            setValue('documentType', 1);
            setValue('mobileNo', null);
          }
          if (Number(data) === 2) {
            setValue('countryId', DEFAULT_COUNTRY.id);
            setValue('stateId', userLocalBody?.stateCode);
            setValue('districtId', userLocalBody?.districtId);
            fetchPostOffice({ districtId: userLocalBody?.districtId });
            setValue('documentType', 1);
            setValue('documentNo', '');
            setValue('pincode', null);
            setValue('mobileNo', null);
          }
          break;
        case 'countryId':
          setValue('countryCode', data.countryCode);
          setValue('stateId', null);
          setValue('districtId', null);
          setActionTriggered({ loading: true, id: 'counter-applicant-state' });
          if (data.id !== DEFAULT_COUNTRY.id) {
            setValue('documentType', 3);
            setValue('documentNo', '');
            setValue('pincode', null);
          } else {
            setValue('documentType', 1);
            setValue('documentNo', '');
            setValue('pincode', null);
          }
          break;
        case 'stateId':
          setActionTriggered({ loading: true, id: 'counter-applicant-district' });
          fetchDistricts(data.id);
          setValue('postOffice', '');
          setValue('wardName', '');
          break;
        case 'districtId':
          setActionTriggered({ loading: true, id: 'counter-applicant-postoffice' });
          fetchPostOffice({ districtId: data.id });
          setValue('postOffice', '');
          setValue('wardName', '');
          break;
        case 'documentType':
          setValue('documentType', Number(data));
          break;
        case 'documentNo':
          checkAadhaarExist(data);
          break;
        case 'mobileNo':
          if (mobileValidation) {
            setValue('mobileNo', mobileValidation);
            // if (mobileValidation?.toString().length === 10) {
            //   fetchKsmartId();
            // }
            if (isWhatsappSame) {
              setValue('whatsapp', mobileValidation);
            }
          } else {
            setValue('mobileNo', null);
          }
          break;
        case 'whatsappCheck':
          setValue('isWhatsappSame', data);
          setValue('whatsapp', data ? mobileNo : '');
          break;
        case 'postOffice':
          setValue('pincode', data.pinCode);
          setValue('postOfficeName', data.name);
          break;
        case 'pincode':
          if (data.length >= 6) {
            fetchPostOfficeByPin(data);
          }
          break;
        case 'isWhatsappSame':
          setValue('isWhatsappSame', data);
          setValue('whatsapp', data ? mobileValidation : '');
          break;
        case 'whatsapp':
          setValue('whatsapp', mobileValidation);
          if (mobileValidation !== whatsapp) {
            setValue('isWhatsappSame', false);
          }
          break;
        case 'localFirstName':
          setValue('localFirstName', malayalamOnly);
          break;
        case 'localMiddleName':
          setValue('localMiddleName', malayalamOnly);
          break;
        case 'localLastName':
          setValue('localLastName', malayalamOnly);
          break;
        case 'localHouseName':
          setValue('localHouseName', malayalamOnly);
          break;
        case 'localStreet':
          setValue('localStreet', malayalamOnly);
          break;
        case 'localLocalPlace':
          setValue('localLocalPlace', malayalamOnly);
          break;
        case 'localMainPlace':
          setValue('localMainPlace', malayalamOnly);
          break;
        case 'localInstitutionName':
          setValue('localInstitutionName', malayalamOnly);
          break;
        case 'localOfficeName':
          setValue('localOfficeName', malayalamOnly);
          break;
        case 'localDesignation':
          setValue('localDesignation', malayalamOnly);
          break;

        case 'firstName':
          setValue('firstName', nameValidate.replace(ENG_ONLY, ''));
          break;
        case 'middleName':
          if (nameValidate === '') {
            setValue('middleName', null);
          } else {
            setValue('middleName', nameValidate.replace(ENG_ONLY, ''));
          }
          break;
        case 'lastName':
          if (nameValidate === '') {
            setValue('lastName', null);
          } else {
            setValue('lastName', nameValidate.replace(ENG_ONLY, ''));
          }
          break;
        case 'houseName':
          setValue('houseName', nameValidate);
          break;
        case 'street':
          setValue('street', nameValidate);
          break;
        case 'localPlace':
          setValue('localPlace', nameValidate);
          break;
        case 'mainPlace':
          setValue('mainPlace', nameValidate);
          break;
        case 'emailId':
          setValue('emailId', emailOnly);
          break;

        default:
          break;
      }
    } else {
      switch (field) {
        case 'postOffice':
          setValue('pincode', null);
          break;
        case 'countryId':
          setValue('stateId', null);
          setValue('districtId', null);
          break;
        case 'stateId':
          setValue('districtId', null);
          break;
        default:
          break;
      }
    }
  };

  const onSubmitForm = (data) => {
    if (editState && (joinStatus || inwardID)) {
      const applicantArray = JSON.parse(JSON.stringify(jointApplicantData));
      applicantArray[editIndex] = formatApplicantDetails(data);
      applicantArray[editIndex].id = editId || null;
      setJointApplicant(applicantArray);
      setEditState(false);
      setEditIndex('');
      resetData();
    } else {
      let jointArray = JSON.parse(JSON.stringify(jointApplicantData));
      if (editState && !joinStatus) {
        const newData = data;
        newData.addressType = data.localBodyType ? data.localBodyType : data.addressType;
        setJointApplicant(newData);
        jointArray = [newData];
      } else if (!editState && (joinStatus || inwardID)) {
        const newData = data;
        newData.addressType = joinStatus ? 3 : data.localBodyType;
        newData.id = null;
        setEditId(null);
        jointArray.push(newData);
        setJointApplicant(jointArray);
        resetData();
      } else {
        const newData = data;
        newData.addressType = data.localBodyType ? data.localBodyType : data.addressType;
        setJointApplicant([newData]);
        jointArray = [newData];
      }

      if ((!joinStatus && !editState) || (!joinStatus && editState)) {
        const applicantDetailsIndividualRequests = [];
        const institutionDetails = [];

        // eslint-disable-next-line array-callback-return
        jointArray.map((item) => {
          if (item.localbodyType === 1) {
            applicantDetailsIndividualRequests.push(formatApplicantDetails(item, editId, from));
          } else if (item.localbodyType === 2) {
            institutionDetails.push(formatApplicantDetails(item, editId, from));
          }
        });

        setActionTriggered({ loading: true, id: 'counter-applicant' });
        const saveData = {
          inwardId: params.id ? params.id : inwardID,
          addressType: data.localbodyType,
          isPostal: formActiveData?.isPostal,
          userInfo: {
            officeId: userInfo?.id
          },
          applicantDetailsIndividualRequests:
            applicantDetailsIndividualRequests.length > 0 ? applicantDetailsIndividualRequests : null,
          institutionDetails: institutionDetails.length > 0 ? institutionDetails : null,
          from
        };
        updateApplication(saveData);
      }
    }
  };

  useEffect(() => {
    fetchCountry();
    setActionTriggered({ loading: false, id: 'counter-applicant-country' });
    fetchStates();
    fetchDistricts(DEFAULT_STATE.id);
  }, []);

  useEffect(() => {
    if (Object.keys(userInfo).length > 0) {
      fetchWard(userInfo.id);
      setActionTriggered({ loading: false, id: 'counter-applicant-ward' });
    }
  }, [JSON.stringify(userInfo)]);

  useEffect(() => {
    if (isWhatsappSame) {
      setValue('whatsapp', mobileNo);
    }
  }, [isWhatsappSame, mobileNo]);

  useEffect(() => {
    if (mobileNo !== whatsapp) {
      setValue('isWhatsappSame', false);
    }
  }, [whatsapp]);

  useEffect(() => {
    if (formActiveData) {
      setValue('isPostal', formActiveData?.isPostal);
      setIsPostal(formActiveData?.isPostal);
      if (Object.keys(formActiveData).length > 0) {
        const addressList = formActiveData?.applicantDetailsAddress;
        if (addressList) {
          setJointApplicant(formActiveData?.applicantDetailsAddress);
          if (addressList.length > 1) {
            setJoinStatus(true);
            setValue('jointApplication', true);
          } else if (!inwardID) {
            if (formActiveData.addressType === 4) {
              setJoinStatus(true);
              setValue('jointApplication', true);
            } else {
              setJoinStatus(false);
              setValue('jointApplication', false);
            }
            setEditState(true);
            const data = addressList[0];
            setEditIndex(0);
            setEditId(data.id);

            if (data.addressType === 1) {
              fetchPostOffice({ offLbCode: userInfo.id });
            } else {
              fetchPostOffice({ districtId: data.districtId });
            }
            if (data.stateId) {
              fetchDistricts(data.stateId);
            }
            setValue('localbodyType', Number(data.addressType));
            setValue('countryId', Number(data.countryId));
            setValue('stateId', Number(data.stateId));
            setValue('districtId', Number(data.districtId));
            setValue('documentType', formateDocumentType(data));
            setValue('documentNo', setDocument(data));
            setValue('firstName', data.firstName);
            setValue('middleName', data.middleName);
            setValue('lastName', data.lastName);
            setValue('localFirstName', data.localFirstName);
            setValue('localMiddleName', data.localMiddleName);
            setValue('localLastName', data.localLastName);
            setValue('houseName', data.houseName);
            setValue('localHouseName', data.localHouseName);
            setValue('postOffice', Number(data.postOffice));
            setValue('pincode', data.pincode);
            setValue('street', data.street);
            setValue('localPlace', data.localPlace);
            setValue('mainPlace', data.mainPlace);
            setValue('emailId', data.emailId);
            setValue('mobileNo', data.mobileNo);
            setValue('localInstitutionName', data.localInstitutionName);
            setValue('localOfficeName', data.localOfficeName);
            setValue('localDesignation', data.localDesignation);
            setValue('institutionName', data.institutionName);
            setValue('isWhatsappSame', data.isWhatsappSame);
            setValue('whatsapp', data.whatsappNo);
            setValue('landlineNo', data.landlineNo);
            setValue('referenceNo', data.referenceNo);
            setValue('institutionDate', data.institutionDate);
            setValue('officerName', data.officerName);
            setValue('designation', data.designation);
            setValue('postOfficeName', data.postOfficeName);
            setValue('localMainPlace', data.localMainPlace);
            setValue('localLocalPlace', data.localLocalPlace);
            setValue('landlineNo', data.landlineNo);
            setValue('localStreet', data.localStreet);
            setValue('institutionDate', data?.referenceDate ? reFormattedDate(data?.referenceDate) : '');
          }
        }
      }
    }
  }, [formActiveData]);

  useEffect(() => {
    if (jointApplicantData?.length > 0) {
      setJointApplications(jointApplicantData);
    } else {
      setJointApplications([]);
    }
  }, [jointApplicantData]);

  useEffect(() => {
    if (errors) {
      const getKeys = Object.keys(errors);
      if (getKeys?.length > 0) {
        scrollToTop(getKeys[0]);
      }
    }
  }, [errors]);

  const saveApplicant = () => {
    setActionTriggered({ loading: true, id: 'counter-applicant' });
    const applicantDetailsIndividualRequests = [];
    const institutionDetails = [];
    // eslint-disable-next-line array-callback-return
    jointApplications.map((item) => {
      if ((item.localbodyType ? item.localbodyType : item.addressType) === 1) {
        applicantDetailsIndividualRequests.push(formatApplicantDetails(item, editId, from));
      } else if ((item.localbodyType ? item.localbodyType : item.addressType) === 2) {
        institutionDetails.push(formatApplicantDetails(item, editId, from));
      }
    });

    const saveData = {
      inwardId: params.id ? params.id : inwardID,
      addressType: joinStatus ? 3 : localbodyType,
      isPostal: formActiveData?.isPostal,
      userInfo: {
        officeId: userInfo?.id
      },
      applicantDetailsIndividualRequests,
      institutionDetails,
      from
    };
    updateApplication(saveData);
    reset();
  };

  const allowStart = () => {
    let star = true;
    if (Number(countrySelected) === DEFAULT_COUNTRY.id && Number(localbodyType) === 1) {
      star = true;
    }
    if (
      Number(countrySelected) === DEFAULT_COUNTRY.id
      && Number(stateSelected) === DEFAULT_STATE.id
      && Number(localbodyType) === 2
    ) {
      star = true;
    }
    if (Number(countrySelected) !== DEFAULT_COUNTRY.id && Number(localbodyType) === 2) {
      star = false;
    }
    return star;
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmitForm)} id="applicant-form">
        <div id="applicant_details" />
        <FormWrapper>
          <div className="lg:col-span-4 md:col-span-6 col-span-12 mt-4">
            <Button
              variant="link"
              style={{ textDecoration: 'none' }}
              leftIcon={joinStatus ? <CheckedBox /> : <UnCheckedBox />}
              onClick={handleJointCheck}
            >
              {t('jointApplication')}
            </Button>
          </div>

          <div className="col-span-12">
            <FormController
              name="localbodyType"
              type="radio"
              control={control}
              errors={errors}
              options={localbodyRadioOptions}
              optionKey="id"
              // disabledIndex={joinStatus ? [2] : []}
              handleChange={(data) => handleFieldChange('localbodyType', data)}
            />
          </div>
          <div className="col-span-12" />
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="countryId"
              type="select"
              label={t('country')}
              control={control}
              errors={errors}
              options={_.get(countryDropdown, 'data', [])}
              handleChange={(data) => handleFieldChange('countryId', data)}
              isLoading={actionTriggered?.id === 'counter-applicant-country' && actionTriggered?.loading}
              optionKey="id"
              required
              isClearable
            />
          </div>
          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="stateId"
                  type="select"
                  label={t('state')}
                  control={control}
                  errors={errors}
                  optionKey="id"
                  options={_.get(stateDropdown, 'data', [])}
                  handleChange={(data) => handleFieldChange('stateId', data)}
                  isLoading={actionTriggered?.id === 'counter-applicant-state' && actionTriggered?.loading}
                  required
                  isClearable
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="districtId"
                  type="select"
                  label={t('district')}
                  control={control}
                  errors={errors}
                  optionKey="id"
                  options={_.get(districtDropdown, 'data', [])}
                  handleChange={(data) => handleFieldChange('districtId', data)}
                  isLoading={actionTriggered?.id === 'counter-applicant-district' && actionTriggered?.loading}
                  required
                  isClearable
                />
              </div>
            </>
          )}

          {Number(countrySelected) === DEFAULT_COUNTRY.id ? (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="mobileNo"
                type="text"
                label={t('concatLabel', { label: t('mobile'), type: t('number') })}
                control={control}
                errors={localbodyType === 1 && !isPostal ? errors : null}
                handleChange={(data) => handleFieldChange('mobileNo', data)}
                maxLength={10}
                rightContent={(
                  <>
                    {actionTriggered?.id === 'fetchKsmartId' && actionTriggered?.loading && (
                      <Spinner style={{ width: '20px', height: '20px' }} />
                    )}

                    <div className="w-[2px] mx-2 bg-gray-100 h-[54px]" />

                    <FormController
                      type="check"
                      control={control}
                      errors={errors}
                      name="isWhatsappSame"
                      label={<WhatsappIcon />}
                    />
                  </>
                )}
                required={localbodyType === 1 && !isPostal}
              />
            </div>
          ) : (
            <div className="lg:col-span-4 md:col-span-6 col-span-12 flex">
              <div className="border rounded-l-lg mr-[-5px] p-[15px] w-[100px]">+{getValues('countryCode')}</div>
              <FormController
                name="internationalMobileNo"
                type="text"
                label={t('concatLabel', { label: t('mobile'), type: t('number') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('internationalMobileNo', data)}
                required
              />
            </div>
          )}

          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="whatsapp"
                type="text"
                label={t('concatLabel', { label: t('whatsapp'), type: t('number') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('whatsapp', data)}
                maxLength={10}
              />
            </div>
          )}

          {localbodyType !== 2 && (
            <>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="documentNo"
                  type="text"
                  label={docNumber(documentType)}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('documentNo', data)}
                  maxlength={docLength(documentType)}
                  required={Number(countrySelected) !== DEFAULT_COUNTRY.id}
                />
              </div>
              {otpStatus && (
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="validateAadhar"
                    type="text"
                    label={t('otp')}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('validateAadhar', data)}
                    required
                    rightContent={
                      Number(documentType) === 1 ? (
                        <Button
                          isLoading={actionTriggered?.id === 'validateAadharOtp' && actionTriggered?.loading}
                          onClick={() => verifyAadharOtpFuc()}
                          variant="primary_outline"
                          size="md"
                        >
                          {t('validate')}
                        </Button>
                      ) : null
                    }
                  />
                </div>
              )}
            </>
          )}
          <div className="col-span-12" />
          {localbodyType !== 2 && (
            <>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="firstName"
                  type="text"
                  label={t('concatLabel', { label: t('first'), type: t('name') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('firstName', data)}
                  required
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="middleName"
                  type="text"
                  label={t('concatLabel', { label: t('middle'), type: t('name') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('middleName', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="lastName"
                  type="text"
                  label={t('concatLabel', { label: t('last'), type: t('name') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('lastName', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="localFirstName"
                  type="text"
                  label={t('concatLabel', { label: t('firstName'), type: t('local') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('localFirstName', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="localMiddleName"
                  type="text"
                  label={t('concatLabel', { label: t('middleName'), type: t('local') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('localMiddleName', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="localLastName"
                  type="text"
                  label={t('concatLabel', { label: t('lastName'), type: t('local') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('localLastName', data)}
                />
              </div>
              <>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="houseName"
                    type="text"
                    label={
                      localbodyType === 2 && Number(countrySelected) !== DEFAULT_COUNTRY.id
                        ? t('concatLabel', { label: t('house'), type: t('noName') })
                        : t('concatLabel', { label: t('house'), type: t('name') })
                    }
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('houseName', data)}
                    required={!(localbodyType === 2 && Number(countrySelected) !== DEFAULT_COUNTRY.id)}
                  />
                </div>
                <div className="lg:col-span-4 md:col-span-6 col-span-12">
                  <FormController
                    name="localHouseName"
                    type="text"
                    label={t('concatLabel', { label: t('houseName'), type: t('local') })}
                    control={control}
                    errors={errors}
                    handleChange={(data) => handleFieldChange('localHouseName', data)}
                  />
                </div>
              </>
            </>
          )}

          {localbodyType === 2 && (
            <>
              <div className="col-span-12">
                <h4>
                  <strong>
                    {t('institution')} {t('details')}
                  </strong>
                </h4>
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="referenceNo"
                  type="text"
                  label={t('concatLabel', { label: t('reference'), type: t('number') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('referenceNo', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="institutionDate"
                  type="date"
                  label={t('date')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('institutionDate', data)}
                  maxDate={new Date()}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="institutionName"
                  type="text"
                  label={t('concatLabel', { label: t('institution'), type: t('name') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('institutionName', data)}
                  required
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="officerName"
                  type="text"
                  label={t('concatLabel', { label: t('officer'), type: t('name') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('officerName', data)}
                  // required
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="designation"
                  type="text"
                  label={t('designation')}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('designation', data)}
                  required
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="localInstitutionName"
                  type="text"
                  label={t('concatLabel', { label: t('institution'), type: t('local') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('localInstitutionName', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="localOfficeName"
                  type="text"
                  label={t('concatLabel', { label: t('officer'), type: t('local') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('localOfficeName', data)}
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="localDesignation"
                  type="text"
                  label={t('concatLabel', { label: t('designation'), type: t('local') })}
                  control={control}
                  errors={errors}
                  handleChange={(data) => handleFieldChange('localDesignation', data)}
                />
              </div>
            </>
          )}
          {Number(countrySelected) === DEFAULT_COUNTRY.id && (
            <>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="postOffice"
                  type="select"
                  label={t('postOffice')}
                  optionKey="id"
                  control={control}
                  errors={errors}
                  options={_.get(postOfficeDropdown, 'data', [])}
                  handleChange={(data) => handleFieldChange('postOffice', data)}
                  isLoading={actionTriggered?.id === 'counter-applicant-postoffice' && actionTriggered?.loading}
                  required={allowStart()}
                  isClearable
                />
              </div>
              <div className="lg:col-span-4 md:col-span-6 col-span-12">
                <FormController
                  name="pincode"
                  type="text"
                  label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pinCode' : 'postZipCode')}
                  control={control}
                  errors={errors}
                  handleChange={(event) => handleFieldChange('pincode', event.target.value)}
                  required={Number(countrySelected) === DEFAULT_COUNTRY.id}
                  disabled
                />
              </div>
            </>
          )}

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="street"
              type="text"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'street' : 'streetNoName')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('street', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localStreet"
              type="text"
              label={
                Number(countrySelected) === DEFAULT_COUNTRY.id
                  ? t('concatLabel', { label: t('street'), type: t('local') })
                  : t('concatLabel', { label: t('streetNoName'), type: t('local') })
              }
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localStreet', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localPlace"
              type="text"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'localPlace' : 'locality')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localPlace', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localLocalPlace"
              type="text"
              label={
                Number(countrySelected) === DEFAULT_COUNTRY.id
                  ? t('concatLabel', { label: t('localPlace'), type: t('local') })
                  : t('concatLabel', { label: t('locality'), type: t('local') })
              }
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localLocalPlace', data)}
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="mainPlace"
              type="text"
              label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'mainPlace' : 'cityTown')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('mainPlace', data)}
              required
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="localMainPlace"
              type="text"
              label={
                Number(countrySelected) === DEFAULT_COUNTRY.id
                  ? t('concatLabel', { label: t('mainPlace'), type: t('local') })
                  : t('concatLabel', { label: t('cityTown'), type: t('local') })
              }
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('localMainPlace', data)}
            />
          </div>
          {Number(countrySelected) !== DEFAULT_COUNTRY.id && (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="pincode"
                type="text"
                label={t(Number(countrySelected) === DEFAULT_COUNTRY.id ? 'pincode' : 'postZipCode')}
                control={control}
                errors={errors}
                disabled={Number(countrySelected) === DEFAULT_COUNTRY.id}
                handleChange={(data) => handleFieldChange('pincode', data)}
                required={Number(countrySelected) === DEFAULT_COUNTRY.id}
              />
            </div>
          )}

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="emailId"
              type="text"
              label={t('emailId')}
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('emailId', data)}
            />
          </div>

          {localbodyType === 2 && (
            <div className="lg:col-span-4 md:col-span-6 col-span-12">
              <FormController
                name="landlineNo"
                type="text"
                label={t('concatLabel', { label: t('landLine'), type: t('number') })}
                control={control}
                errors={errors}
                handleChange={(data) => handleFieldChange('landLine', data)}
              />
            </div>
          )}
          <div className="col-span-12 text-right">
            {hideButton && (jointApplication || inwardID) && (
              <Button type="submit" variant="secondary_outline" className="shadow-md mr-4" form="applicant-form">
                {editState ? t('updateApplicant') : t('addApplicant')}
              </Button>
            )}
          </div>
          <div className="col-span-12">
            {jointApplications.length > 0 && localbodyType !== 3 && (jointApplication || inwardID) && (
              <CommonTable tableData={jointApplications} columns={columns} />
            )}
          </div>

          <div className="col-span-12 text-right">
            {!hideButton
              && (jointApplication || inwardID ? (
                <Button type="submit" variant="secondary_outline" className="shadow-md mr-4" form="applicant-form">
                  {editState ? t('updateApplicant') : t('addApplicant')}
                </Button>
              ) : (
                <Button
                  variant="secondary_outline"
                  className="shadow-md"
                  type="submit"
                  form="applicant-form"
                  isLoading={actionTriggered?.id === 'counter-applicant' && actionTriggered?.loading}
                >
                  {t('proceed')}
                </Button>
              ))}
            {(inwardID || (!inwardID && jointApplications.length > 1)) && (
              <Button
                variant="secondary_outline"
                className="shadow-md"
                onClick={saveApplicant}
                form="applicant-form"
                isLoading={actionTriggered?.id === 'counter-applicant' && actionTriggered?.loading}
              >
                {t('proceed')}
              </Button>
            )}
          </div>
        </FormWrapper>
      </form>
      <Registration />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  countryDropdown: getCountry,
  stateDropdown: getState,
  districtDropdown: getDistricts,
  wardDropdown: getWard,
  postOfficeDropdown: getPostOffice,
  jointApplicantData: getJointApplicant,
  actionTriggered: getActionTriggered,
  userLocalBody: getUserLocalBody,
  userInfo: getUserInfo,
  generateAadharOtpRes: getGenerateAadharOtp,
  otpStatus: getOtpStatus,
  smartDetails: getSmartDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchCountry: () => dispatch(commonActions.fetchCountry()),
  fetchStates: () => dispatch(commonActions.fetchState()),
  fetchDistricts: (data) => dispatch(commonActions.fetchDistricts(data)),
  fetchWard: (data) => dispatch(commonActions.fetchWardsByLocalBodyId(data)),
  fetchPostOffice: (data) => dispatch(commonActions.fetchPostOffice(data)),
  fetchPostOfficeByPin: (data) => dispatch(commonActions.fetchPostOfficeByPin(data)),
  updateApplication: (data) => dispatch(actions.updateApplicationService(data)),
  setJointApplicant: (data) => dispatch(sliceActions.setJointApplicant(data)),
  deleteApplicant: (data) => dispatch(actions.deleteApplicant(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  verifyAadharOtp: (data) => dispatch(commonActions.verifyAadharOtp(data)),
  fetchSmartProfile: (data) => dispatch(commonActions.fetchSmartProfile(data)),
  fetchApplication: (data) => dispatch(actions.fetchApplicationService(data)),
  setRegisterOpen: (data) => dispatch(commonSliceActions.setRegisterOpen(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ApplicantDetails);
