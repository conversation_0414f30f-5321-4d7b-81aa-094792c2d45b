import React, { useState, useEffect } from 'react';
import {
  t, To<PERSON>, Button, FormWrapper
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import AddCircle from 'assets/addCircle';
import MinusCircle from 'assets/MinusCircle';
import { useParams } from 'react-router-dom';
import { getActionTriggered, getDoorKey, getUserInfo } from 'pages/common/selectors';
import NoNotesIcon from 'assets/NoNotesIcon';
import RoutingKeys from 'pages/common/components/GeneralDetails/RoutingKeys';
import GeneralDetailsForm from 'pages/common/components/GeneralDetails/GeneralDetailsForm';
import {
  addGeneralSaveData,
  dataFact,
  existingGeneralData,
  generalRoutingKeys,
  generalSaveData
} from 'pages/common/components/GeneralDetails/helper';
import { IKM_FILE_COUNTER } from 'pages/file/details/constants';
import * as actions from '../actions';

const GeneralDetails = (props) => {
  const {
    updateGeneralDetails,
    formActiveData,
    userInfo,
    from,
    inwardID,
    save,
    setActionTriggered,
    doorkey,
    actionTriggered
  } = props;
  const params = useParams();
  const { errorTost } = Toast;
  const [activeIndex, setActiveIndex] = useState(0);
  const [existingData, setExistingData] = useState([]);
  const [existingGeneral, setExistingGeneral] = useState({});
  const [serviceInfo, setServiceInfo] = useState({});

  useEffect(() => {
    if (formActiveData?.generalDetailsResponses) {
      const generalDetailsResponses = formActiveData?.generalDetailsResponses;
      setExistingGeneral(existingGeneralData(generalDetailsResponses));
    }
  }, [formActiveData]);

  const resetData = () => {
    setExistingData(null);
    setActiveIndex(0);
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  useEffect(() => {
    if (!formActiveData) return;
    const { applicantDetailsAddress } = formActiveData;
    if (!applicantDetailsAddress || applicantDetailsAddress.length === 0) return;
    setExistingData(null);
    const newData = applicantDetailsAddress.map((applicant, index) => ({
      id: applicant.id,
      addressType: applicant.addressType,
      firstName:
        Number(applicant.addressType) === 3
          ? applicant.institutionName
          : `${applicant.firstName ? applicant.firstName : ''} ${applicant.middleName ? applicant.middleName : ''} ${
            applicant.lastName ? applicant.lastName : ''
          } `,
      data: formActiveData?.generalDetailsResponses?.details[index]
        ? dataFact(
          formActiveData?.generalDetailsResponses?.details,
          index,
          params.id ? params.id : inwardID,
          userInfo?.id,
          formActiveData?.applicantDetailsAddress
        )
        : {}
    }));
    setExistingData(newData);
    setServiceInfo({ code: formActiveData?.serviceCode, name: formActiveData?.serviceName });
  }, [formActiveData]);

  const handleOpenCard = (index) => {
    if (activeIndex === index) {
      setActiveIndex(null);
    } else {
      setActiveIndex(index);
    }
  };

  const addGeneralDetails = (data) => {
    const existingArray = JSON.parse(JSON.stringify(existingData || []));
    existingArray[activeIndex].data = addGeneralSaveData(data, existingData[activeIndex]);
    setExistingData(existingArray);

    if (activeIndex < existingData.length) {
      setActiveIndex(activeIndex + 1);
    }
  };

  const routingSave = (data) => {
    const inCompletedSteps = existingData.map((item, index) => {
      if (Object.keys(item.data).length === 0) {
        return index;
      }
      return null;
    });
    const steps = _.filter(inCompletedSteps, (el) => !_.isNull(el));
    if (steps.length > 0) {
      setActiveIndex(steps[0]);
      errorTost({
        title: t('incompleteDetails'),
        description: t('pleaseCompleteMissingDetails')
      });
    } else {
      setActionTriggered({ loading: true, id: 'counterGeneralDetailsCreate' });
      const keyData = generalRoutingKeys(data, doorkey);
      const rounteKeyData = _.omitBy(keyData, _.isNil);
      const saveData = generalSaveData(
        rounteKeyData,
        params.id ? params.id : inwardID,
        existingData,
        data,
        userInfo?.id,
        'counter'
      );
      updateGeneralDetails(_.omitBy(saveData, _.isNil));
      if (inwardID) {
        save('final');
      }
    }
  };

  return (
    <div className="pt-5">
      {existingData?.length === 0 && (
        <div className="p-10 text-center">
          <NoNotesIcon width="100px" height="100px" className="mx-auto" />
          <h4>Please Complete the Applicant Details</h4>
        </div>
      )}
      <div id="general_details" />
      {existingData.map((item, index) => {
        return (
          <>
            <FormWrapper py="2">
              <div className="col-span-12">
                <div
                  className="p-3 px-6 cursor-pointer bg-slate-200 rounded-full"
                  aria-hidden
                  onClick={() => handleOpenCard(index)}
                >
                  <div className="flex">
                    <div className="flex-none w-8 font-medium text-blue-900">{index + 1}.</div>
                    <div className="grow font-medium text-blue-900">{item.firstName}</div>
                    <div className="flex-none">
                      {activeIndex === index ? (
                        <MinusCircle width="24" height="24" />
                      ) : (
                        <AddCircle width="24" height="24" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </FormWrapper>
            {activeIndex === index && (
              <GeneralDetailsForm
                applicantData={item.data}
                addGeneralDetails={addGeneralDetails}
                from={from}
                serviceInfo={serviceInfo}
                addressType={item.addressType}
              />
            )}
          </>
        );
      })}
      {existingData?.length > 0 ? (
        <RoutingKeys
          from={IKM_FILE_COUNTER}
          routingSave={routingSave}
          activeIndex={activeIndex === existingData?.length}
          existingData={existingGeneral}
          serviceInfo={serviceInfo}
          officeCode={userInfo?.id}
        />
      ) : (
        <div className="pb-10 mt-4 text-right pr-24">
          <Button
            type="submit"
            className="shadow-md"
            variant="secondary_outline"
            onClick={routingSave}
            isLoading={actionTriggered?.id === 'counterGeneralDetailsCreate' && actionTriggered?.loading}
          >
            {t('proceed')}
          </Button>
        </div>
      )}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  doorkey: getDoorKey,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  updateGeneralDetails: (data) => dispatch(actions.saveGeneralDetails(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(GeneralDetails);
