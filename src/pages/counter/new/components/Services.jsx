import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Form<PERSON>rapper, FormController, t, Button
} from 'common/components';
import _ from 'lodash';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { createStructuredSelector } from 'reselect';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  getFileTypeDropdown,
  getSubModule,
  getUserInfo,
  getActionTriggered,
  getModuleById,
  getServiceDfmsDropdown
} from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import { actions as commonSliceActions } from 'pages/common/slice';
import { FILTER_TYPE } from 'pages/common/constants';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import * as actions from '../actions';
import { ServiceFormSchema } from '../validate';
import { getServiceByCodeDetails } from '../selectors';
import { serviceDefaultValues } from './helper';

const Services = (props) => {
  const {
    fetchDfmsServices,
    serviceDfmsDropdown,
    fetchSubModuleById,
    fetchModuleById,
    fetchServiceByServiceCode,
    fetchFileTypeOptions,
    fileTypeDropdown,
    moduleById,
    formActiveData,
    saveApplicationService,
    subModule,
    serviceByCodeDetails,
    userInfo,
    setActionTriggered,
    actionTriggered
  } = props;

  const params = useParams();

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset
  } = useForm({
    mode: 'all',
    defaultValues: serviceDefaultValues,
    resolver: yupResolver(ServiceFormSchema)
  });

  const [isPostal, setIsPostal] = useState(false);

  const onSubmitForm = (data) => {
    setActionTriggered({ loading: true, id: 'counterServiceCreate' });
    const saveData = {
      inwardId: params?.id || null,
      services: {
        serviceCode: data.services,
        module: data.modules,
        subModule: data.subModule,
        fileType: data.fileType,
        title: data.title,
        filingMode: 'counter',
        isPostal: data.isPostal
      },
      userInfo: {
        officeId: userInfo?.id
      }
    };
    saveApplicationService(saveData);
  };
  const resetData = () => {
    reset({ ...serviceDefaultValues }, { keepValues: false });
  };

  useEffect(() => {
    if (!params) {
      if (!params.id) {
        resetData();
      }
      resetData();
    }
  }, [params]);

  useEffect(() => {
    fetchDfmsServices('serviceTypes=1|1,2|1,3&inputTemplateType=1&eFiling=0&eFiling=1');
    setActionTriggered({ loading: true, id: 'counter-service-dropdown' });
    fetchFileTypeOptions();
  }, []);

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.SERVICES:
        if (data) {
          fetchSubModuleById(data.subModuleId);
          fetchModuleById(data.moduleId);
          setValue('serviceCode', data.serviceCode);
        } else {
          setValue('subModule', '');
          setValue('modules', '');
          setValue('serviceCode', null);
        }
        break;
      case 'serviceCode':
        setValue('serviceCode', data.target.value.toUpperCase());
        if (data.target.value.length > 4) {
          fetchServiceByServiceCode(data.target.value);
          setValue('subModule', '');
          setValue('modules', '');
          setValue('services', '');
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (serviceByCodeDetails?.length > 0) {
      fetchSubModuleById(serviceByCodeDetails[0]?.subModuleId);
      fetchModuleById(serviceByCodeDetails[0]?.moduleId);
      setValue('services', serviceByCodeDetails[0]?.code);
      setValue('serviceCode', serviceByCodeDetails[0]?.code);
    }
  }, [serviceByCodeDetails]);

  useEffect(() => {
    if (subModule) {
      if (Object.keys(subModule).length > 0) {
        setValue('subModule', subModule.name);
      }
    } else {
      setValue('subModule', '');
    }
  }, [subModule]);

  useEffect(() => {
    if (moduleById) {
      if (Object.keys(moduleById).length > 0) {
        setValue('modules', moduleById.name);
      }
    } else {
      setValue('modules', '');
    }
  }, [moduleById]);

  useEffect(() => {
    if (formActiveData) {
      if (formActiveData?.inwardId) {
        setValue('services', formActiveData?.serviceCode);
        fetchServiceByServiceCode(formActiveData?.serviceCode);
        setValue('title', formActiveData?.title);
        setValue('fileType', formActiveData?.fileType);
        setValue('isPostal', formActiveData.isPostal);
        setIsPostal(formActiveData.isPostal);
      }
    }
  }, [formActiveData]);

  const handleByPostal = () => {
    setIsPostal(!isPostal);
    setValue('isPostal', !isPostal);
  };

  return (
    <>
      <form id="service-form" onSubmit={handleSubmit(onSubmitForm)}>
        <div id="service_details" />
        <FormWrapper>
          <div className="col-span-12">
            <Button
              variant="link"
              style={{ textDecoration: 'none' }}
              leftIcon={isPostal ? <CheckedBox /> : <UnCheckedBox />}
              onClick={handleByPostal}
            >
              {t('byPostalOrEmail')}
            </Button>
          </div>

          <div className="lg:col-span-8 md:col-span-8 col-span-12">
            <FormController
              name="services"
              type="select"
              label={t('services')}
              placeholder={t('services')}
              control={control}
              errors={errors}
              optionKey="serviceCode"
              options={_.get(serviceDfmsDropdown, 'data', [])}
              handleChange={(data) => {
                handleFieldChange(FILTER_TYPE.SERVICES, data);
              }}
              isClearable
              isLoading={actionTriggered?.id === 'counter-service-dropdown' && actionTriggered?.loading}
              required
              disabled={params?.id}
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="serviceCode"
              type="text"
              label={t('serviceCode')}
              readOnly
              control={control}
              errors={errors}
              handleChange={(data) => handleFieldChange('serviceCode', data)}
              required
            />
          </div>

          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="modules"
              type="text"
              label={t('module')}
              control={control}
              errors={errors}
              readOnly
              handleChange={(data) => handleFieldChange('moduleId', data)}
              required
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="subModule"
              type="text"
              label={t('concatLabel', { label: t('sub'), type: t('module') })}
              control={control}
              errors={errors}
              readOnly
              handleChange={(data) => handleFieldChange('subModuleId', data)}
              required
            />
          </div>
          <div className="lg:col-span-4 md:col-span-6 col-span-12">
            <FormController
              name="fileType"
              type="select"
              label={t('priority')}
              placeholder={t('priority')}
              control={control}
              errors={errors}
              optionKey="id"
              options={_.get(fileTypeDropdown, 'data', [])}
              defaultValue="1"
              required
              isClearable
            />
          </div>
          <div className="col-span-12">
            <FormController
              name="title"
              type="text"
              label={t('title')}
              placeholder={t('title')}
              control={control}
              errors={errors}
            />
          </div>
        </FormWrapper>
      </form>

      <FormWrapper>
        <div className="col-span-12 text-right">
          <Button
            type="submit"
            variant="secondary_outline"
            className="shadow-md"
            form="service-form"
            isLoading={actionTriggered?.id === 'counterServiceCreate' && actionTriggered?.loading}
            loadingText="Proceeding.."
          >
            {t('proceed')}
          </Button>
        </div>
      </FormWrapper>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  serviceDfmsDropdown: getServiceDfmsDropdown,
  fileTypeDropdown: getFileTypeDropdown,
  moduleById: getModuleById,
  subModule: getSubModule,
  serviceByCodeDetails: getServiceByCodeDetails,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered
});

const mapDispatchToProps = (dispatch) => ({
  fetchDfmsServices: (data) => dispatch(commonActions.fetchDfmsServices(data)),
  fetchSubModuleById: (data) => dispatch(commonActions.fetchSubModuleById(data)),
  fetchModuleById: (data) => dispatch(commonActions.fetchModuleById(data)),
  fetchServiceByServiceCode: (data) => dispatch(actions.fetchServiceByServiceCode(data)),
  fetchFileTypeOptions: () => dispatch(commonActions.fetchFileTypeDetails()),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  saveApplicationService: (data) => dispatch(actions.saveApplicationService(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Services);
