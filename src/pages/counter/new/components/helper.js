import { t } from 'common/components';
import { DEFAULT_COUNTRY, DEFAULT_DISTRICT, DEFAULT_STATE } from 'common/constants';
import { convertToLocalDate } from 'utils/date';
import { DATE_FORMAT } from 'pages/common/constants';
import { DOCUMENT_TYPE, LOCAL_BODY_TYPE } from '../constants';

const serviceDefaultValues = {
  services: '',
  modules: '',
  subModule: '',
  fileType: 1,
  title: '',
  serviceCode: '',
  isPostal: false
};

const applicantDefaultValues = {
  localbodyType: 1,
  countryId: DEFAULT_COUNTRY.id,
  stateId: DEFAULT_STATE.id,
  districtId: DEFAULT_DISTRICT.id,
  jointApplication: false,
  mobileNo: null,
  documentType: 1,
  documentNo: null,
  firstName: '',
  middleName: null,
  lastName: null,
  localFirstName: null,
  localMiddleName: null,
  localLastName: null,
  validateAadhar: null,
  houseName: '',
  localHouseName: null,
  postOffice: null,
  pincode: '',
  street: null,
  localPlace: null,
  mainPlace: '',
  localStreet: null,
  localLocalPlace: null,
  localMainPlace: null,
  emailId: null,
  countryCode: '',
  institutionName: '',
  localInstitutionName: null,
  localOfficeName: null,
  localDesignation: null,
  isWhatsappSame: false,
  whatsapp: null,
  landlineNo: '',
  referenceNo: null,
  institutionDate: null,
  officerName: '',
  designation: '',
  internationalMobileNo: null,
  postOfficeName: null,
  isPostal: false
};

const generalDetailsDefaultValues = {
  // gender: null,
  category: null,
  financialStatus: null,
  ownership: null,
  accountNo: '',
  bank: null,
  bankName: '',
  branch: null,
  branchName: null,
  ifsc: null,
  // dateOfBirth: null,
  // income: null,
  ward: null,
  doorNo: null,
  subNo: null,
  // educationalQualification: null,
  description: null,
  fieldsValidation: null
};

const formatLocalBodyType = (data) => {
  let locType;
  switch (data) {
    case LOCAL_BODY_TYPE.JOINT_APPLICANT_ADDRESS_INSIDE_LOCAL_BODY:
      locType = 1;
      break;
    case LOCAL_BODY_TYPE.JOINT_APPLICANT_ADDRESS_OUTSIDE_LOCAL_BODY:
      locType = 2;
      break;
    default:
      locType = 3;
      break;
  }
  return locType;
};

const formatApplicantDetails = (data, editId, from) => {
  let docType;
  let dataSave;

  switch (Number(data.documentType)) {
    case 1:
      docType = DOCUMENT_TYPE.AADHAR;
      break;
    case 2:
      docType = DOCUMENT_TYPE.UUID;
      break;
    default:
      docType = DOCUMENT_TYPE.PASSPORT;
      break;
  }

  switch (Number(data.localbodyType) ? Number(data.localbodyType) : data.addressType) {
    case 1:
      dataSave = {
        ...data,
        id: data.id || editId,
        [docType]: data.documentNo,
        addressType: Number(data.localbodyType) ? Number(data.localbodyType) : data.addressType,
        firstName: (data.firstName?.replace(/^\s+|\s+$/gm, ''))?.replace(/\s*$/, ''),
        middleName: data.middleName?.replace(/\s*$/, ''),
        lastName: data.lastName === '' ? null : data.lastName?.replace(/\s*$/, ''),
        localFirstName: data.localFirstName,
        localMiddleName: data.localMiddleName,
        localLastName: data.localLastName,
        houseName: data.houseName === '' ? null : data.houseName,
        localHouseName: data.localHouseName,
        postOffice: data.postOffice === '' ? null : data.postOffice,
        postOfficeName: data.postOfficeName,
        pincode: data.pincode,
        street: data.street,
        localPlace: data.localPlace,
        localStreet: data.localStreet,
        localLocalPlace: data.localLocalPlace,
        localMainPlace: data.localMainPlace,
        mainPlace: data.mainPlace,
        emailId: data.emailId,
        mobileNo: data.mobileNo,
        whatsappNo: data.whatsapp,
        countryId: parseInt(data.countryId, 10),
        stateId: parseInt(data.stateId, 10),
        districtId: parseInt(data.districtId, 10),
        year: 0,
        beneficiaryFlag: from === 'beneficiary'
      };
      break;
    case 2:
      dataSave = {
        ...data,
        id: data.id || editId,
        [docType]: data.documentNo,
        addressType: Number(data.localbodyType) ? Number(data.localbodyType) : data.addressType,
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        institutionName: data.institutionName,
        referenceDate: convertToLocalDate(data.institutionDate, DATE_FORMAT.DATE_LOCAL),
        localFirstName: data.localFirstName,
        localMiddleName: data.localMiddleName,
        localLastName: data.localLastName,
        houseName: data.houseName === '' ? null : data.houseName,
        localHouseName: data.localHouseName ? data.localHouseName : null,
        postOffice: data.postOffice === '' ? null : data.postOffice,
        postOfficeName: data.postOfficeName,
        pincode: data.pincode,
        street: data.street,
        localPlace: data.localPlace,
        localStreet: data.localStreet,
        localLocalPlace: data.localLocalPlace,
        localMainPlace: data.localMainPlace,
        mainPlace: data.mainPlace,
        emailId: data.emailId,
        mobileNo: data.mobileNo ? data.mobileNo : null,
        whatsappNo: data.whatsapp,
        internationalMobileNo: data.internationalMobileNo ? `+${data.countryCode}${data.internationalMobileNo}` : null,
        countryId: parseInt(data.countryId, 10),
        stateId: parseInt(data.stateId, 10),
        districtId: parseInt(data.districtId, 10),
        year: 0,
        beneficiaryFlag: from === 'beneficiary'
      };
      break;
    default:
      dataSave = {
        // ...data,
        id: data.id || editId,
        referenceNo: data.referenceNo,
        addressType: Number(data.localbodyType) ? Number(data.localbodyType) : data.addressType,
        referenceDate: convertToLocalDate(data.institutionDate, DATE_FORMAT.DATE_LOCAL),
        institutionName: data.institutionName,
        officerName: data.officerName,
        designation: data.designation,
        postOffice: data.postOffice,
        postOfficeName: data.postOfficeName,
        pincode: data.pincode,
        street: data.street,
        localPlace: data.localPlace,
        mainPlace: data.mainPlace,
        localInstitutionName: data.localInstitutionName,
        localOfficeName: data.localOfficeName,
        localDesignation: data.localDesignation,
        localStreet: data.localStreet,
        localLocalPlace: data.localLocalPlace,
        localMainPlace: data.localMainPlace,
        emailId: data.emailId,
        mobileNo: data.mobileNo,
        whatsappNo: data.whatsapp,
        landlineNo: data.landlineNo,
        countryId: parseInt(data.countryId, 10),
        stateId: parseInt(data.stateId, 10),
        districtId: parseInt(data.districtId, 10),
        year: 0,
        beneficiaryFlag: from === 'beneficiary'
      };
      break;
  }
  return dataSave;
};

const formateDocumentType = (data) => {
  if (data.documentType) {
    return Number(data.documentType);
  }
  if (data.aadharNo) {
    return 1;
  }
  if (data.udid) {
    return 2;
  }
  if (data.countryId === DEFAULT_COUNTRY.id) {
    return 1;
  }
  return 3;
};

const formateDocumentTypeName = (data) => {
  if (data.aadharNo) {
    return DOCUMENT_TYPE.AADHAR;
  }
  if (data.udid) {
    return DOCUMENT_TYPE.UUID;
  }
  return DOCUMENT_TYPE.PASSPORT;
};

const setDocument = (data) => {
  if (data.documentType) {
    return data.documentNo;
  }
  if (data.aadharNo) {
    return data.aadharNo;
  }
  if (data.udid) {
    return data.udid;
  }
  return data.passport;
};

const docNumber = (type) => {
  switch (type) {
    case 1:
      return t('concatLabel', { label: t('aadhar'), type: t('number') });
    case 2:
      return t('concatLabel', { label: t('udid'), type: t('number') });
    case 3:
      return t('concatLabel', { label: t('passport'), type: t('number') });
    default:
      return t('concatLabel', { label: t('aadhar'), type: t('number') });
  }
};

const docLength = (type) => {
  switch (type) {
    case 1:
      return '12';
    case 2:
      return '18';
    case 3:
      return '10';
    default:
      return '12';
  }
};

export {
  applicantDefaultValues,
  generalDetailsDefaultValues,
  formatLocalBodyType,
  formatApplicantDetails,
  formateDocumentType,
  formateDocumentTypeName,
  serviceDefaultValues,
  docNumber,
  docLength,
  setDocument
};
