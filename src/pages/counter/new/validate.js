import * as yup from 'yup';

import {
  AADHAAR, NAME, NUMERIC, EN_NUMERIC, EN, EMAIL, MOBILE, ML, EN_SPACE, FILE_NO, INCOME_LIMIT, ENG_NUMBER,
  MOBILE_INTERNATIONAL,
  EN_SPECIAL
} from 'common/regex';

import { t } from 'common/components';
import { DEFAULT_COUNTRY, DEFAULT_STATE } from 'common/constants';

export const ServiceFormSchema = yup.object({
  services: yup.string().required(t('isRequired', { type: t('services') })),
  modules: yup.string().required(t('isRequired', { type: `${t('module')}` })),
  subModule: yup.string().required(t('isRequired', { type: `${t('sub')} ${t('module')}` })),
  fileType: yup.string().required(t('isRequired', { type: `${t('file')} ${t('type')}` })),
  title: yup.string().max(150, 'Only 150 characters can be entered in the Title')
}).required();

export const ApplicatDetailsFormSchema = yup.object().shape({
  localbodyType: yup.number()
    .required(t('isRequired', { type: t('localBodyType') })),
  countryId: yup.string()
    .required(t('isRequired', { type: t('country') })),
  stateId: yup.number().when(['localbodyType', 'countryId'], (data, schema) => {
    if (Number(data[1]) !== DEFAULT_COUNTRY.id) {
      return schema.notRequired();
    }
    return schema.required();
  }),
  // stateId: yup.number().required(),

  districtId: yup.number().when(['localbodyType', 'countryId'], (data, schema) => {
    if (Number(data[1]) !== DEFAULT_COUNTRY.id) {
      return schema.notRequired();
    }
    return schema.required();
  }),
  mobileNo: yup.string()
    .when(['countryId', 'localbodyType', 'isPostal'], (data, schema) => {
      if (Number(data[1]) === 2 || data[2]) {
        return schema.notRequired().nullable().transform((val) => val || null).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` }));
      }
      if (Number(data[0]) === DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: `${t('mobile')} ${t('number')}` })).matches(MOBILE, t('invalidType', { type: `${t('mobile')} ${t('number')}` }));
      }
      return schema.notRequired();
    }),

  documentNo: yup.string()
    .when(['documentType', 'localbodyType', 'countryId'], (data, schema) => {
      if (data[1] !== 3) {
        if (Number(data[0], 10) === 1 && data[1] !== 3) {
          return schema.notRequired(t('isRequired', { type: `${t('aadhar')} ${t('number')}` })).nullable().transform((val) => val || null).matches(AADHAAR, t('invalidType', { type: `${t('aadhar')} ${t('number')}` }));
        }
        if ((Number(data[0], 10) === 3 && data[2] !== 3 && Number(data[0])) === DEFAULT_COUNTRY.id) {
          return schema.required(t('isRequired', { type: `${t('passport')} ${t('number')}` })).length(9).matches(EN_NUMERIC, t('invalidType', { type: `${t('passport')} ${t('number')}` }));
        }
      }
      return schema.notRequired();
    }),
  firstName: yup.string()
    .when(['localbodyType'], (localbodyType, schema) => {
      if (Number(localbodyType[0]) !== 2) {
        return schema.required(t('isRequired', { type: `${t('first')} ${t('name')}` })).max(150);
        // .matches(NAME, `${t('first')} ${t('name')} ${t('inEnglishRequired')}`);
      }
      return schema.notRequired();
    }),
  middleName: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(NAME, `${t('middle')} ${t('name')} ${t('inEnglishRequired')}`),
  // lastName: yup.string()
  //   .max(150)
  //   .nullable()
  //   .transform((val) => val || null)
  //   .matches(NAME, `${t('last')} ${t('name')} ${t('inEnglishRequired')}`),
  localFirstName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('first')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localMiddleName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('middle')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localLastName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('last')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  houseName: yup.string().when(['localbodyType'], {
    is: (localbodyType, countryId) => {
      if (localbodyType === 2 && Number(countryId) !== DEFAULT_COUNTRY.id) {
        return false;
      }
      if (localbodyType === 1) {
        return true;
      }
      return false;
    },
    then: (schema) => schema
      .required(t('isRequired', { type: `${t('house')} ${t('name')}` })).max(150).matches(EN_SPACE, `${t('house')} ${t('name')} ${t('inEnglishRequired')}`)
  }),
  localHouseName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('house')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  // wardName: yup.string()
  //   .when(['localbodyType'], (localbodyType, schema) => {
  //     if (Number(localbodyType[0]) === 1) {
  //       return schema.required(t('isRequired', { type: `${t('ward')} ${t('name')}` }));
  //     }
  //     if (Number(localbodyType[0]) === 2) {
  //       return schema.notRequired().nullable().transform((val) => val || null);
  //     }
  //     return schema.notRequired();
  //   }),
  // doorNo: yup.string()
  //   .max(5, 'Door No must be five digits'),
  // postOffice: yup.string().when(['countryId', 'stateId', 'localbodyType'], {
  //   is: (countryId, stateId, localbodyType) => {
  //     if (Number(countryId) === DEFAULT_COUNTRY.id && Number(localbodyType) === 1) {
  //       return true;
  //     }
  //     if (Number(countryId) === DEFAULT_COUNTRY.id && Number(stateId) === DEFAULT_STATE.id && Number(localbodyType) === 2) {
  //       return true;
  //     }
  //     if (Number(countryId) === DEFAULT_COUNTRY.id && Number(stateId) !== DEFAULT_STATE.id && Number(localbodyType) === 2) {
  //       return true;
  //     }
  //     if (Number(countryId) !== DEFAULT_COUNTRY.id && (Number(localbodyType) === 2 || Number(localbodyType) === 1)) {
  //       return false;
  //     }
  //     return true;
  //   },
  //   then: (schema) => schema
  //     .required(t('isRequired', { type: t('postOffice') }))
  // }),
  postOffice: yup
    .string()
    .nullable() // Allows postOffice to be null if not required
    .when(['countryId', 'stateId', 'localbodyType'], {
      is: (countryId, stateId, localbodyType) => {
        if (Number(countryId) === DEFAULT_COUNTRY.id && Number(localbodyType) === 1) {
          return true;
        }
        if (Number(countryId) === DEFAULT_COUNTRY.id && Number(stateId) === DEFAULT_STATE.id && Number(localbodyType) === 2) {
          return true;
        }
        if (Number(countryId) === DEFAULT_COUNTRY.id && Number(stateId) !== DEFAULT_STATE.id && Number(localbodyType) === 2) {
          return true;
        }
        if (Number(countryId) !== DEFAULT_COUNTRY.id && (Number(localbodyType) === 2 || Number(localbodyType) === 1)) {
          return false;
        }
        return true;
      },
      then: (schema) => schema
        .required(t('isRequired', { type: t('postOffice') })), // Required if conditions match
      otherwise: (schema) => schema.notRequired() // Not required if conditions don't match
    }),
  pincode: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(NUMERIC, t('useNumbersOnly')),
  street: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('street')} ${t('inEnglishRequired')}`),
  localPlace: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EN, `${t('localPlace')} ${t('inEnglishRequired')}`),
  mainPlace: yup.string()
    .when(['countryId'], (countryId, schema) => {
      if (parseFloat(countryId[0], 10) !== DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: t('cityTown') })).max(150).matches(EN_NUMERIC, t('useAlphaAndNumOnly'));
      }
      return schema.required(t('isRequired', { type: t('mainPlace') })).max(150).matches(EN, `${t('mainPlace')} ${t('inEnglishRequired')}`);
    }),
  localStreet: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('street')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localLocalPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('localPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localMainPlace: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('mainPlace')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localInstitutionName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('institution')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localOfficeName: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('name')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),
  localDesignation: yup.string()
    .max(150)
    .nullable()
    .matches(ML, `${t('designation')} ${t('inMalayalamRequired')}`)
    .transform((val) => val || null),

  emailId: yup.string()
    .max(150)
    .nullable()
    .transform((val) => val || null)
    .matches(EMAIL, t('invalidType', { type: t('emailId') })),
  internationalMobileNo: yup.string()
    .when(['countryId'], (countryId, schema) => {
      if (parseInt(countryId[0], 10) === DEFAULT_COUNTRY.id) {
        return schema.notRequired().nullable().transform((val) => val || null);
      }
      if (parseInt(countryId[0], 10) !== DEFAULT_COUNTRY.id) {
        return schema.required(t('isRequired', { type: `${t('mobile')} ${t('number')}` })).matches(MOBILE_INTERNATIONAL, t('invalidType', { type: `${t('mobile')} ${t('number')}` }));
      }
      return schema.notRequired();
    }),
  whatsapp: yup.string()
    .nullable()
    .transform((val) => val || null)
    .matches(MOBILE, t('invalidType', { type: `${t('whatsapp')} ${t('number')}` })),
  referenceNo: yup.string()
    .max(50)
    .nullable()
    .transform((val) => val || null),
  // .matches(EN_NUMERIC, t('useAlphaAndNumOnly')),
  institutionDate: yup.string()
    .nullable()
    .transform((val) => val || null),
  institutionName: yup.string()
    .when(['localbodyType'], (localbodyType, schema) => {
      if (Number(localbodyType[0]) === 2) {
        return schema.required(t('isRequired', { type: `${t('institution')} ${t('name')}` })).max(150).matches(EN_SPECIAL, `${t('institution')} ${t('name')} ${t('inEnglishRequired')}`);
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  // officerName: yup.string()
  //   .when(['localbodyType'], (localbodyType, schema) => {
  //     if (Number(localbodyType[0]) === 2) {
  //       return schema.required(t('isRequired', { type: `${t('officer')} ${t('name')}` })).matches(EN, `${t('officer')} ${t('name')} ${t('inEnglishRequired')}`);
  //     }
  //     return schema.nullable().transform((val) => val || null).notRequired();
  //   }),
  designation: yup.string()
    .when(['localbodyType'], (localbodyType, schema) => {
      if (Number(localbodyType[0]) === 2) {
        return schema.required(t('isRequired', { type: `${t('designation')}` }));
        // .matches(EN, `${t('designation')} ${t('inEnglishRequired')}`);
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  landlineNo: yup.string().max(15)
}).required();

export const DocumentDetailsFormSchema = yup.object().shape({
  supportingDocuments: yup.string()
    .when(['activeIdEdit'], (data, schema) => {
      if (!data[0]) {
        return schema.required(t('isRequired', { type: t('supportingDocuments') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  supportDocumentsName: yup.string().required(t('isRequired', { type: t('documentName') })),
  supportDocumentType: yup.string().required(t('isRequired', { type: t('supportDocumentType') }))

}).required();

export const AdvanceSearchSchema = yup.object({
  services: yup.string().required(t('isRequired', { type: t('service') })),
  modules: yup.string().required(t('isRequired', { type: t('modules') })),
  subModule: yup.string().required(t('isRequired', { type: t('subModule') }))
}).required();

export const GeneralDetailSchema = yup.object({
  genderId: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.genderId === 1) {
        return schema.required(t('isRequired', { type: t('gender') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  financialStatusId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.financialStatusId === 1) {
        return schema.required(t('isRequired', { type: t('financialStatus') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),

  dateOfBirth: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfBirth === 1) {
        return schema.required(t('isRequired', { type: t('dateOfBirth') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  category: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.category === 1) {
        return schema.required(t('isRequired', { type: t('category') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  accountNo: yup.string().nullable()
    .transform((val) => val || null)
    .matches(ENG_NUMBER, `${t('accountNo')} ${t('charAndNumbers')}`)
    .min(9, `${t('accountNo')} ${t('minimumNineOnly')}`)
    .max(18, `${t('accountNo')} ${t('maximumOnly')}`),
  bankNameId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.bankNameId === 1) {
        return schema.required(t('isRequired', { type: t('bankName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  bankBranchId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.bankBranchId === 1) {
        return schema.required(t('isRequired', { type: t('bankBranch') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ifsc: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ifsc === 1) {
        return schema.required(t('isRequired', { type: t('ifsc') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  income: yup.string()
    .matches(INCOME_LIMIT, `${t('income')} ${t('greaterThanNine')}`)
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.income === 1) {
        return schema.required(t('isRequired', { type: t('income') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  educationalQualificationId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.educationalQualificationId === 1) {
        return schema.required(t('isRequired', { type: t('educationalQualification') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })
}).required();

export const RoutingKeySchema = yup.object({
  referenceNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.referenceNo === 1) {
        return schema.required(t('isRequired', { type: t('referenceNo') })).matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
      }
      return schema.nullable().transform((val) => val || null).notRequired().matches(FILE_NO, t('invalidType', { type: `${t('referenceNo')}` }));
    }),
  ward: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ward === 1) {
        return schema.required(t('isRequired', { type: t('ward') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  doorNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.doorNo === 1) {
        return schema
          .max(5, 'Door No must be five digits')
          .required(t('isRequired', { type: t('doorNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  subNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.subNo === 1) {
        return schema.required(t('isRequired', { type: t('subNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownershipId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownershipId === 1) {
        return schema.required(t('isRequired', { type: t('ownershipId') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  localBodyPropertyType: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.localBodyPropertyType === 1) {
        return schema.required(t('isRequired', { type: t('localBodyPropertyType') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingUsage: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingUsage === 1) {
        return schema.required(t('isRequired', { type: t('buildingUsage') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  buildingArea: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.buildingArea === 1) {
        return schema.required(t('isRequired', { type: t('buildingArea') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functionalGroup: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functionalGroup === 1) {
        return schema.required(t('isRequired', { type: t('functionalGroup') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  functions: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.functions === 1) {
        return schema.required(t('isRequired', { type: t('functions') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  description: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.description === 1) {
        return schema.required(t('isRequired', { type: t('description') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ownerName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ownerName === 1) {
        return schema.required(t('isRequired', { type: t('ownerName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  ksebPostNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.ksebPostNo === 1) {
        return schema.required(t('isRequired', { type: t('ksebPostNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  roadName: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.roadName === 1) {
        return schema.required(t('isRequired', { type: t('roadName') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  landMark: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.landmark === 1) {
        return schema.required(t('isRequired', { type: t('landmark') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  talukId: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.taluk === 1) {
        return schema.required(t('isRequired', { type: t('taluk') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  village: yup.number()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.village === 1) {
        return schema.required(t('isRequired', { type: t('village') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  surveyNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.surveyNumber === 1) {
        return schema.required(t('isRequired', { type: t('surveyNumber') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  dateOfEvent: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.dateOfEvent === 1) {
        return schema.required(t('isRequired', { type: t('dateOfEvent') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    }),
  receiptNo: yup.string()
    .when(['fieldsValidation'], (fieldsValidation, schema) => {
      if (fieldsValidation[0]?.receiptNo === 1) {
        return schema.required(t('isRequired', { type: t('receiptNo') }));
      }
      return schema.nullable().transform((val) => val || null).notRequired();
    })

}).required();
