import React, { useEffect, useState } from 'react';
import { CommonTable } from 'common/components/Table';
import {
  t
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { getFilterParams, getTableLoader } from 'pages/common/selectors';
import { convertToLocalDate } from 'utils/date';
import { DATE_FORMAT } from 'pages/common/constants';
import * as actions from '../actions';

const CashDeclarationReport = ({
  filterParams, fetchCashDeclarationReport, tableData, setFilterParams, setTableLoader, tableLoader,
  roleWiseEnableFlag, setPage, page, userInfo, setLoginWiseFromDate, setLoginWiseToDate
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableArray, setTableArray] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const handleEmployeeName = (fileData) => {
    let employeeName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      employeeName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.employeeName}</div>;
    }
    return <div className="block">{employeeName}</div>;
  };

  const handleDesignation = (fileData) => {
    let designation;
    if (fileData?.row) {
      const cellData = fileData?.row;
      designation = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.designation}</div>;
    }
    return <div className="block">{designation}</div>;
  };

  const handleDate = (fileData) => {
    let date;
    if (fileData?.row) {
      const cellData = fileData?.row;
      date = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.date}</div>;
    }
    return <div className="block">{date}</div>;
  };

  const handleAmount = (fileData) => {
    let amount;
    if (fileData?.row) {
      const cellData = fileData?.row;
      amount = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.amount}</div>;
    }
    return <div className="block">{amount}</div>;
  };

  const formatColumns = () => {
    const columns = [];
    if (roleWiseEnableFlag) {
      columns.push(

        {
          header: t('employeeName'),
          field: 'employeeName',
          alignment: 'left',
          cell: (field) => handleEmployeeName(field)
        },
        {
          header: t('designation'),
          field: 'designation',
          alignment: 'left',
          cell: (field) => handleDesignation(field)
        },
        {
          header: t('date'),
          field: 'date',
          alignment: 'left',
          cell: (field) => handleDate(field)
        },
        {
          header: t('personalCashInCustody'),
          field: 'amount',
          alignment: 'left',
          cell: (field) => handleAmount(field)
        }
      );
    } else {
      columns.push(

        {
          header: t('date'),
          field: 'date',
          alignment: 'left',
          cell: (field) => handleDate(field)
        },
        {
          header: t('personalCashInCustody'),
          field: 'amount',
          alignment: 'left',
          cell: (field) => handleAmount(field)
        }
      );
    }
    return columns;
  };

  useEffect(() => {
    if (filterParams?.search) {
      setTableLoader({ loading: true, id: 'cash-declaration-report' });
      fetchCashDeclarationReport();
    }
  }, [filterParams]);

  useEffect(() => {
    if (userInfo?.id) {
      if (roleWiseEnableFlag) {
        setFilterParams({
          ...filterParams, officeId: userInfo?.id, fromDate: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL), toDate: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL), isOutsideOffice: false, search: true
        });
        setLoginWiseFromDate(convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL));
        setLoginWiseToDate(convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL));
      } else {
        setFilterParams({
          ...filterParams, officeId: userInfo?.id, fromDate: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL), toDate: convertToLocalDate(new Date(), DATE_FORMAT.DATE_LOCAL), penNos: userInfo?.userDetails?.pen, search: true
        });
      }
    }
  }, [roleWiseEnableFlag, JSON.stringify(userInfo)]);

  const onPageClick = (data) => {
    setPage(data);
    setFilterParams({
      ...filterParams, page: data
    });
  };

  useEffect(() => {
    if (tableData) {
      if (Object.keys(tableData).length > 0) {
        setTableLoader({ loading: false, id: 'cash-declaration-report' });
        setTableArray(tableData.content);
        setTotalItems(parseInt(`${tableData.totalPages}0`, 10));
        setNumberOfElements(Number(tableData.numberOfElements));
      } else {
        setTableLoader({ loading: false, id: 'cash-declaration-report' });
        setTableArray([]);
        setTotalItems(0);
      }
    }
  }, [tableData]);

  return (
    <div className="col-span-12 pb-20">
      <CommonTable
        variant="dashboard"
        tableData={tableArray}
        columns={formatColumns()}
        activeRows={activeRows}
        onPageClick={onPageClick}
        itemsPerPage={10}
        totalItems={totalItems}
        currentPage={page}
        paginationEnabled
        tableLoader={tableLoader?.loading && tableLoader?.id === 'cash-declaration-report'}
        numberOfElements={numberOfElements}
      />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  filterParams: getFilterParams,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchCashDeclarationReport: (data) => dispatch(actions.fetchCashDeclarationReport(data)),
  setFilterParams: (data) => dispatch(sliceActions.setFilterParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(CashDeclarationReport);
