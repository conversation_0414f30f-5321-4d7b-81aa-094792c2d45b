import React, { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import Filters from 'pages/common/components/Filters';
import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { t } from 'common/components';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import { getUserInfo } from 'pages/common/selectors';
import DocumentDownload from 'pages/common/components/DocumentDownload';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import { getDataFromStorage } from 'utils/encryption';
import { getTableData } from '../selectors';
import CashDeclarationReport from './CashDeclarationReport';

const Index = ({ tableData, userInfo }) => {
  const [selectedEmployees, setSelectedEmployees] = useState('');
  const [selectedFromDate, setSelectedFromDate] = useState('');
  const [selectedToDate, setSelectedToDate] = useState('');
  const [loginWiseFromDate, setLoginWiseFromDate] = useState('');
  const [loginWiseToDate, setLoginWiseToDate] = useState('');
  const [page, setPage] = useState(0);

  const [isInsideOffice, setIsInsideOffice] = useState(true);

  const userDetails = getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true) || [];

  const filters = {
    fromDate: true,
    toDate: true,
    department: false,
    cashDepartment: userDetails?.isAdministrator,
    employeeName: userDetails?.isAdministrator,
    generateReportFlag: userDetails?.isAdministrator,
    cashDeclarationType: userDetails?.isAdministrator
  };

  const getDownloadUrl = () => {
    let downloadUrl = `${baseApiURL}/${API_URL.REPORTS.CASH_DECLARATION_GENERATE_REPORTS}?officeId=${userInfo.id}&template=${'cashdeclaration'}&penNos=${selectedEmployees}&isOutsideOffice=${!isInsideOffice}`;

    if (selectedFromDate) {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.CASH_DECLARATION_GENERATE_REPORTS}?officeId=${userInfo.id}&template=${'cashdeclaration'}&penNos=${selectedEmployees}&fromDate=${selectedFromDate}&isOutsideOffice=${!isInsideOffice}`;
    }
    if (selectedToDate) {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.CASH_DECLARATION_GENERATE_REPORTS}?officeId=${userInfo.id}&template=${'cashdeclaration'}&penNos=${selectedEmployees}&toDate=${selectedToDate}&isOutsideOffice=${!isInsideOffice}`;
    }
    if (selectedToDate && selectedFromDate) {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.CASH_DECLARATION_GENERATE_REPORTS}?officeId=${userInfo.id}&template=${'cashdeclaration'}&penNos=${selectedEmployees}&fromDate=${selectedFromDate}&toDate=${selectedToDate}&isOutsideOffice=${!isInsideOffice}`;
    }

    if (loginWiseToDate && loginWiseFromDate) {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.CASH_DECLARATION_GENERATE_REPORTS}?officeId=${userInfo.id}&template=${'cashdeclaration'}&fromDate=${loginWiseFromDate}&toDate=${loginWiseToDate}&isOutsideOffice=${!isInsideOffice}`;
    }
    return downloadUrl;
  };

  const generateReports = () => {
    DocumentDownload(getDownloadUrl(), localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN));
  };

  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('cashDeclarationReport')}
        </div>
      </div>
      <Filters
        filters={filters}
        tableData={tableData}
        download={generateReports}
        fileName="cashDeclaration"
        setSelectedEmployees={setSelectedEmployees}
        setSelectedFromDate={setSelectedFromDate}
        setSelectedToDate={setSelectedToDate}
        setPage={setPage}
        setLoginWiseFromDate={setLoginWiseFromDate}
        setLoginWiseToDate={setLoginWiseToDate}
        isInsideOffice={isInsideOffice}
        setIsInsideOffice={setIsInsideOffice}

      />
      <CashDeclarationReport
        tableData={tableData}
        roleWiseEnableFlag={userDetails?.isAdministrator}
        setPage={setPage}
        page={page}
        userInfo={userInfo}
        setLoginWiseFromDate={setLoginWiseFromDate}
        setLoginWiseToDate={setLoginWiseToDate}
        isInsideOffice={isInsideOffice}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  tableData: getTableData,
  userInfo: getUserInfo
});

const mapDispatchToProps = () => ({

});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
