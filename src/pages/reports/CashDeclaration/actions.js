import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_CASH_DECLARATION_REPORT: `${STATE_REDUCER_KEY}/FETCH_CASH_DECLARATION_REPORT`,
  FETCH_CASH_DECLARATION_REPORT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_CASH_DECLARATION_REPORT_REQUEST`,
  FETCH_CASH_DECLARATION_REPORT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_CASH_DECLARATION_REPORT_SUCCESS`,
  FETCH_CASH_DECLARATION_REPORT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_CASH_DECLARATION_REPORT_FAILURE`

};

export const fetchCashDeclarationReport = createAction(ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT);
