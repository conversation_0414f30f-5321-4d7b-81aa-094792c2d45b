import {
  all, fork, put, select, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { getFilterParams } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

export function* fetchCashDeclarationReport() {
  const apiParams = yield select(getFilterParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const finalParams = _.omit(updatedParams, ['search']);

  yield fork(handleAPIRequest, api.fetchCashDeclarationReport, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT_SUCCESS,
    ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT_FAILURE]);
  if (type === ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT_SUCCESS) {
    yield put(sliceActions.setTableData(_.get(responsePayLoad, 'data', {})));
    yield put(commonSliceActions.setActionTriggered(false));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'cash-declaration-report' }));
  }
}

export default function* reportsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT, fetchCashDeclarationReport)
  ]);
}
