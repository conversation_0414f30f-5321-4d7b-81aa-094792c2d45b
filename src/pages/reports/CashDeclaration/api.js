import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchCashDeclarationReport = (params) => {
  return {
    url: API_URL.REPORTS.FETCH_CASH_DECLARATION_REPORT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT_REQUEST,
        ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT_SUCCESS,
        ACTION_TYPES.FETCH_CASH_DECLARATION_REPORT_FAILURE
      ],
      params
    }
  };
};
