import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  Button, t, FormController
} from 'common/components';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import { useForm } from 'react-hook-form';
import { getCounterOperator, getSeats, getUserInfo } from 'pages/common/selectors';
import { createStructuredSelector } from 'reselect';
import * as actions from 'pages/common/actions';
import { DATE_FORMAT, FILTER_TYPE } from 'pages/common/constants';
import DocumentDownload from 'pages/common/components/DocumentDownload';
import { convertToLocalDate } from 'utils/date';

const Index = ({
  userInfo, seatsDropdown,
  fetchCounterOperator, fetchSeats, counterOperatorDropdown,
  setSeats
}) => {
  const [loader, setLoader] = useState(false);
  const [post, setPost] = useState([]);

  const {
    control,
    handleSubmit, watch,
    setValue
  } = useForm({
    mode: 'all'
  });

  const seatName = watch('seat');
  const departmentName = watch('department');
  const from = watch('fromDate');
  const to = watch('toDate');

  useEffect(() => {
    fetchCounterOperator();
  }, [JSON.stringify(userInfo)]);

  useEffect(() => {
    if (seatsDropdown?.length > 0) {
      const postArray = seatsDropdown?.filter((item) => item?.name !== null && item?.name !== '' && item?.name !== ' ');
      setPost(postArray);
    } else {
      setPost([]);
    }
  }, [seatsDropdown]);

  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };

  const onSubmitForm = () => {
    setLoader(true);
    const downloadUrl = `${baseApiURL}/${API_URL.REPORTS.PERSONAL_REGISTER_REPORT_PDF}?officeId=${userInfo.id}&postId=${seatName}&fromDate=${convertToLocalDate(from, DATE_FORMAT.DATE_LOCAL)}&toDate=${convertToLocalDate(to, DATE_FORMAT.DATE_LOCAL)}`;
    DocumentDownload(downloadUrl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), setLoader);
  };

  const handleFieldChange = (field, data) => {
    switch (field) {
      case FILTER_TYPE.DEPARTMENT:
        if (data) {
          fetchSeats({ functionalGroupId: data?.functionalGroupId, officeId: userInfo?.id });
        } else {
          setValue('department', '');
          setValue('seat', '');
          setPost([]);
          setSeats([]);
        }
        break;
      default:
        break;
    }
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('personalRegisterReport')}
        </div>
      </div>
      <form
        action="enter"
        id="file-abstract"
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div className="grid grid-cols-5 gap-4 bg-white py-5 px-10 rounded-[10px]">
          <FormController
            name="department"
            type="select"
            label={t('functionalGroup')}
            placeholder={t('searchHere')}
            control={control}
            options={counterOperatorDropdown}
            handleChange={(data) => {
              handleFieldChange(FILTER_TYPE.DEPARTMENT, data);
            }}
            optionKey="functionalGroupId"
            isClearable
          />
          <div>
            <FormController
              name="seat"
              type="select"
              label={t('seat')}
              placeholder={t('searchHere')}
              control={control}
              options={post}
              optionKey="postId"
              isClearable
            />

          </div>
          <FormController
            type="date"
            name="fromDate"
            label={t('concatLabel', { label: t('from'), type: t('date') })}
            control={control}
            dateFormat="dd-MM-yyyy"
            maxDate={new Date()}
          />

          <FormController
            type="date"
            name="toDate"
            label={t('concatLabel', { label: t('to'), type: t('date') })}
            control={control}
            dateFormat="dd-MM-yyyy"
            minDate={from}
            disabled={!from}
          />
          <div className="">
            <Button
              variant="secondary_outline"
              type="submit"
              isDisabled={!seatName || !departmentName || !to || !from}
              isLoading={loader}
            >
              {t('download')}
            </Button>
          </div>
        </div>
      </form>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  seatsDropdown: getSeats,
  counterOperatorDropdown: getCounterOperator
});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchCounterOperator: (data) => dispatch(actions.fetchCounterOperator(data)),
  fetchSeats: (data) => dispatch(actions.fetchSeats(data)),
  setSeats: (data) => dispatch(commonSliceActions.setSeats(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
