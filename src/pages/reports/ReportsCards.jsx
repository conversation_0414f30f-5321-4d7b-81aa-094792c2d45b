import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import TableView from 'assets/TableView';
import { t } from 'common/components';
import { REPORTS } from 'pages/common/constants';
import React from 'react';
import { dark } from 'utils/color';

import { Icons } from 'assets/icons';
import { Images } from 'assets/images';
import { AUDIT_APPLICATION_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { getUserInfo } from 'pages/common/selectors';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';

const backToHome = (val) => {
  window.location.href = val?.userDetails?.isAuditor ? AUDIT_APPLICATION_PATH : EMPLOYEE_SERVICE_PATH;
};

const images = {
  'File Tracking': Images.Reports['File Log'],
  'File Status': Images.Reports['File Status'],
  'Cash Declaration': Images.Reports['Cash Declaration'],
  'File Abstract': Images.Reports['File Abstract'],
  'Distribution Register': Images.Reports['Distribution Register'],
  'Personal Register': Images.Reports['Personal Register']
};

const icons = {
  'File Tracking': Icons.Reports['File Log'],
  'File Status': Icons.Reports['File Status'],
  'Cash Declaration': Icons.Reports['Cash Declaration'],
  'File Abstract': Icons.Reports['File Abstract'],
  'Distribution Register': Icons.Reports['Distribution Register'],
  'Personal Register': Icons.Reports['Personal Register']
};

const CardRender = ({ cardDetail }) => {
  return (

    <div
      className="bg-white w-full h-full flex flex-col p-3 rounded-lg"
      key={cardDetail?.id}
      role="button"
      tabIndex={0}
      onClick={() => {
        if (cardDetail?.route !== '') {
          window.location = `${window.location.origin}/${cardDetail?.route}`;
        }
      }}
      aria-hidden="true"
    >
      <div className="relative w-full transition-all ease-linear duration-500 rounded-lg">
        <img className="w-full h-full rounded-lg" src={images[cardDetail?.name]} alt="dummy" />
      </div>
      <div className="w-[60px] h-[60px] border-[1px] rounded-full mx-auto mt-[-30px] bg-white relative pt-[15px] pl-[19px]">
        <TableView />
      </div>
      <div
        key={cardDetail?.name}
        className="w-full relative  duration-200 flex justify-between items-center px-[13px] py-[15px] cursor-pointer bg-[#F2F6FF] rounded-[4px] mt-[10px]"
        aria-hidden="true"
      >
        <div className="flex justify-center items-center gap-2 w-full">
          {icons[cardDetail?.name]}
          <span className="text-[#09327B] text-base font-bold leading-4">{cardDetail?.name}</span>
        </div>
      </div>
    </div>

  );
};

const ReportsCards = ({ userInfo }) => {
  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={() => backToHome(userInfo)} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('reports')}
        </div>
      </div>
      <div className="grid grid-cols-12 gap-6 auto-rows-[1fr]">
        <div className="col-span-12 pb-20">
          <div className="grid grid-cols-5 gap-4">

            {
              REPORTS
                ?.map((cardDetail) => {
                  return (
                    <CardRender key={cardDetail?.id} cardDetail={cardDetail} />
                  );
                })
            }

          </div>
        </div>
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo
});

const mapDispatchToProps = () => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(ReportsCards);
