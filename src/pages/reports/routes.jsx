import { lazy } from 'react';

const FileStatusReportsListing = lazy(() => import('./fileStatusReports/components'));
const DistributionRegisterReportsListing = lazy(() => import('./DistributionRegisterReports/components'));
const PendingFileReportsListing = lazy(() => import('./PendingFileReports/components'));
const FileAbstractReportsListing = lazy(() => import('./FileAbstract/components'));
const CashDeclaration = lazy(() => import('./CashDeclaration/components'));
const ReportsCards = lazy(() => import('./ReportsCards'));
const FileLogReportsListing = lazy(() => import('./fileLogReports/components'));
const PersonalRegister = lazy(() => import('./PersonalRegister/components'));

const routes = [{
  path: 'reports',
  element: <ReportsCards />
},
{
  path: 'reports/file-status-report',
  element: <FileStatusReportsListing />
},
{
  path: 'reports/distribution-register-report',
  element: <DistributionRegisterReportsListing />
},
{
  path: 'reports/pending-file-report',
  element: <PendingFileReportsListing />
},
{
  path: 'reports/file-abstract-report',
  element: <FileAbstractReportsListing />
},
{
  path: 'reports/cash-declaration',
  element: <CashDeclaration />
},
{
  path: 'reports/file-tracking',
  element: <FileLogReportsListing />
},
{
  path: 'reports/personal-register',
  element: <PersonalRegister />
}
];

export { routes };
