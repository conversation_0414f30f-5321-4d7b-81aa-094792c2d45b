import {
  all, fork, put, select, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getFilterParams } from 'pages/common/selectors';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

export function* fetchDistributionRegisterReport() {
  const apiParams = yield select(getFilterParams);
  const splitedParams = _.omit(apiParams, ['search', 'penNos']);
  const {
    page, size, sortDirection, officeId, filingMode, fromDate, toDate, stageNotIn
  } = splitedParams;
  let stringfiedParams = '';

  if (stageNotIn?.length > 0 && !filingMode && !fromDate && !toDate) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&stageNotIn=${splitedParams?.stageNotIn[0]}&stageNotIn=${splitedParams?.stageNotIn[1]}`;
  } else if (filingMode && stageNotIn?.length > 0 && !fromDate && !toDate) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&filingMode=${filingMode}&stageNotIn=${splitedParams?.stageNotIn[0]}&stageNotIn=${splitedParams?.stageNotIn[1]}`;
  } else if (fromDate && toDate && stageNotIn?.length > 0 && !filingMode) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&stageNotIn=${splitedParams?.stageNotIn[0]}&stageNotIn=${splitedParams?.stageNotIn[1]}&fromDate=${fromDate}&toDate=${toDate}`;
  } else if (filingMode && fromDate && toDate && stageNotIn?.length > 0) {
    stringfiedParams = `?page=${page}&size=${size}&sortDirection=${sortDirection}&officeId=${officeId}&filingMode=${filingMode}&fromDate=${fromDate}&toDate=${toDate}&stageNotIn=${splitedParams?.stageNotIn[0]}&stageNotIn=${splitedParams?.stageNotIn[1]}`;
  }

  yield fork(handleAPIRequest, api.fetchDistributionRegisterReport, stringfiedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT_SUCCESS,
    ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT_FAILURE]);
  if (type === ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT_SUCCESS) {
    yield put(sliceActions.setTableData(_.get(responsePayLoad, 'data', {})));
    yield put(commonSliceActions.setActionTriggered(false));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'distribution-register-report' }));
  } else {
    yield put(sliceActions.setTableData([]));
    yield put(commonSliceActions.setActionTriggered(false));
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'distribution-register-report' }));
  }
}

export default function* reportsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT, fetchDistributionRegisterReport)
  ]);
}
