import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchDistributionRegisterReport = (params) => {
  return {
    url: API_URL.REPORTS.FETCH_DISTRICBUTION_REGISTER_REPORT.replace('?query', params),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT_REQUEST,
        ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT_SUCCESS,
        ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT_FAILURE
      ]
    }
  };
};
