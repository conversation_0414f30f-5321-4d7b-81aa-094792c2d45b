import React, { useEffect, useState } from 'react';
import { CommonTable } from 'common/components/Table';
import {
  t
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { getFilterParams, getTableLoader } from 'pages/common/selectors';
import { STATUS } from 'common/regex';
import _ from 'lodash';
import { convertToLocalDate } from 'utils/date';
import * as actions from '../actions';

const DistributionRegisterReportsList = ({
  filterParams, fetchDistributionRegisterReport, tableData, setFilterParams, setTableLoader, tableLoader, setPage, page, userInfo
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableArray, setTableArray] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const getCurrentUser = (val) => {
    let newVal;
    if (val?.row) {
      const cellData = val?.row;
      if (cellData.mappedUsers?.length > 0) {
        newVal = cellData.mappedUsers?.includes('Employee not mapped') || cellData.mappedUsers?.includes('Employee not mapped.') || cellData.mappedUsers?.includes('')
          ? (
            <div className="rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
              {t('employeeNotMapped')}
            </div>
          )
          : (
            <div className="text-[14px] font-[400] text-[#454545] max-w-[400px] break-keep">{cellData.mappedUsers?.filter((item) => item && item !== 'null(null)')?.join(', ')}</div>
          );
      }
    }
    return newVal;
  };

  const handleCurrentUser = (val) => {
    const currentUser = getCurrentUser(val);
    return <div className="inline-block">{currentUser}</div>;
  };

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      status = (
        <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
          {_.capitalize(cellData?.stage?.replace(STATUS, ' '))}
        </div>
      );
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData.row;
      applicantName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px]">
          {
            cellData.applicantNames?.filter((item) => item && item !== 'null')?.join(', ')
            || cellData.applicantInstitutionName?.filter((item) => item && item !== 'null')?.join(', ')
          }
        </div>
      );
    }

    return <div className="block">{applicantName}</div>;
  };

  const handleInwardNo = (fileData) => {
    let inwardNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      inwardNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.inwardNo}</div>;
    }
    return <div className="block">{inwardNo}</div>;
  };

  const handleInwardDate = (fileData) => {
    let inwardDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      inwardDate = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.inwardDate}</div>;
    }
    return <div className="block">{inwardDate}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const columns = [

    {
      header: t('inwardNumber'),
      field: 'inwardNo',
      cell: (field) => handleInwardNo(field)
    },
    {
      header: t('inwardDate'),
      field: 'inwardDate',
      cell: (field) => handleInwardDate(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantNames',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('services'),
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('custodian'),
      field: 'mappedUsers',
      cell: (field) => handleCurrentUser(field)
    },
    {
      header: t('status'),
      field: 'stage',
      cell: (field) => handleFileStatus(field)
    }
  ];

  useEffect(() => {
    setTableLoader({ loading: true, id: 'distribution-register-report' });
    setFilterParams({
      ...filterParams,
      search: true,
      officeId: userInfo?.id,
      stageNotIn: ['partial', 'discarded'],
      fromDate: convertToLocalDate(new Date()),
      toDate: convertToLocalDate(new Date())
    });
  }, [JSON.stringify(userInfo)]);

  useEffect(() => {
    if (filterParams?.search) {
      setTableLoader({ loading: true, id: 'distribution-register-report' });
      fetchDistributionRegisterReport();
    }
  }, [filterParams]);

  const onPageClick = (data) => {
    setPage(data);
    setFilterParams({
      ...filterParams, page: data
    });
  };

  useEffect(() => {
    if (tableData) {
      if (Object.keys(tableData).length > 0) {
        setTableLoader({ loading: false, id: 'distribution-register-report' });
        setTableArray(tableData.content);
        setTotalItems(parseInt(`${tableData.totalPages}0`, 10));
        setNumberOfElements(Number(tableData.numberOfElements));
      } else {
        setTableLoader({ loading: false, id: 'distribution-register-report' });
        setTableArray([]);
        setTotalItems(0);
      }
    }
  }, [tableData]);

  return (
    <div className="col-span-12 pb-20">
      <CommonTable
        variant="dashboard"
        tableData={tableArray}
        columns={columns}
        activeRows={activeRows}
        onPageClick={onPageClick}
        itemsPerPage={10}
        totalItems={totalItems}
        currentPage={page}
        paginationEnabled
        tableLoader={tableLoader?.loading && tableLoader?.id === 'distribution-register-report'}
        numberOfElements={numberOfElements}
      />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  filterParams: getFilterParams,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchDistributionRegisterReport: (data) => dispatch(actions.fetchDistributionRegisterReport(data)),
  setFilterParams: (data) => dispatch(sliceActions.setFilterParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DistributionRegisterReportsList);
