import React, { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import Filters from 'pages/common/components/Filters';
import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { t } from 'common/components';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import DocumentDownload from 'pages/common/components/DocumentDownload';
import { baseApiURL } from 'utils/http';
import { getUserInfo } from 'pages/common/selectors';
import { API_URL } from 'common';
import { convertToLocalDate } from 'utils/date';
import DistributionRegisterReportsList from './DistributionRegisterReportsList';
import { getTableData } from '../selectors';

const Index = ({ tableData, userInfo }) => {
  const [page, setPage] = useState(0);
  const [selectedFromDate, setSelectedFromDate] = useState(convertToLocalDate(new Date()));
  const [selectedToDate, setSelectedToDate] = useState(convertToLocalDate(new Date()));
  const [selectedCheckBoxValue, setSelectedCheckBoxValue] = useState('All');

  const filters = {
    modules: false,
    subModule: false,
    service: false,
    department: false,
    seat: false,
    status: false,
    fromDate: true,
    toDate: true,
    counterOperator: false,
    fileNo: false,
    generateReportFlag: true,
    moduleType: true
  };

  const getDownloadUrl = () => {
    let downloadUrl = '';

    if (selectedToDate && selectedFromDate && selectedCheckBoxValue === 'All') {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.DISTRIBUTION_REGISTAR_REPORT_PDF}?officeId=${userInfo.id}&fromDate=${selectedFromDate}&toDate=${selectedToDate}`;
    }

    if (selectedToDate && selectedFromDate && selectedCheckBoxValue === 'Counter') {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.DISTRIBUTION_REGISTAR_REPORT_PDF}?officeId=${userInfo.id}&fromDate=${selectedFromDate}&toDate=${selectedToDate}&filingMode=counter`;
    }

    if (selectedToDate && selectedFromDate && selectedCheckBoxValue === 'E-file') {
      downloadUrl = `${baseApiURL}/${API_URL.REPORTS.DISTRIBUTION_REGISTAR_REPORT_PDF}?officeId=${userInfo.id}&fromDate=${selectedFromDate}&toDate=${selectedToDate}&filingMode=efile`;
    }
    return downloadUrl;
  };

  const generateReports = () => {
    DocumentDownload(getDownloadUrl(), localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN));
  };

  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };
  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('distributionStatusReport')}
        </div>
      </div>
      <Filters
        filters={filters}
        tableData={tableData}
        download={generateReports}
        fileName="distributionRegister"
        setPage={setPage}
        setSelectedFromDate={setSelectedFromDate}
        setSelectedToDate={setSelectedToDate}
        setSelectedCheckBoxValue={setSelectedCheckBoxValue}
      />
      <DistributionRegisterReportsList
        tableData={tableData}
        setPage={setPage}
        page={page}
        userInfo={userInfo}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  tableData: getTableData,
  userInfo: getUserInfo
});

const mapDispatchToProps = () => ({

});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
