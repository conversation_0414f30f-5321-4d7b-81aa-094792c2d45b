import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_DISTRIBUTION_REGISTER_REPORT: `${STATE_REDUCER_KEY}/FETCH_DISTRIBUTION_REGISTER_REPORT`,
  FETCH_DISTRIBUTION_REGISTER_REPORT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_DISTRIBUTION_REGISTER_REPORT_REQUEST`,
  FETCH_DISTRIBUTION_REGISTER_REPORT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_DISTRIBUTION_REGISTER_REPORT_SUCCESS`,
  FETCH_DISTRIBUTION_REGISTER_REPORT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_DISTRIBUTION_REGISTER_REPORT_FAILURE`

};

export const fetchDistributionRegisterReport = createAction(ACTION_TYPES.FETCH_DISTRIBUTION_REGISTER_REPORT);
