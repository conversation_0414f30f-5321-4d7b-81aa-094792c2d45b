import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchStatusReport = (params) => {
  return {
    url: API_URL.REPORTS.FETCH_STATUS_REPORT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILE_STATUS_REPORT_REQUEST,
        ACTION_TYPES.FETCH_FILE_STATUS_REPORT_SUCCESS,
        ACTION_TYPES.FETCH_FILE_STATUS_REPORT_FAILURE
      ],
      params
    }
  };
};

export const generatePdf = (data) => {
  const {
    modules, subModule, service, department, seat, status, fromDate, toDate, page, size
  } = data;

  const paramData = `?modules=${modules}&subModule=${subModule}&service=${service}&department=${department}&seat=${seat}&status=${status}&fromDate=${fromDate}&toDate=${toDate}&page=${page}&size=${size}`;

  return {
    url: API_URL.COMMON.FETCH_FILES.replace('?query', paramData),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.GENERATE_PDF_REQUEST,
        ACTION_TYPES.GENERATE_PDF_SUCCESS,
        ACTION_TYPES.GENERATE_PDF_FAILURE
      ]
    },
    data
  };
};

export const fetchSearchFiles = (params) => {
  return {
    url: API_URL.SEARCH_FILES.FETCH_SEARCH_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SEARCH_FILES_REQUEST,
        ACTION_TYPES.FETCH_SEARCH_FILES_SUCCESS,
        ACTION_TYPES.FETCH_SEARCH_FILES_FAILURE
      ],
      params
    }
  };
};
