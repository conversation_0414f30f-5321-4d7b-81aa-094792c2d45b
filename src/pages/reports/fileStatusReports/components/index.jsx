import React, { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getSearchListParams } from 'pages/common/selectors';
import { IconButton, t } from 'common/components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { BASE_PATH } from 'common/constants';
import Filters from '../../../common/components/Filters';
import StatusReportsList from './StatusReportsList';
import * as actions from '../actions';
import { getSearchList } from '../selectors';

const Index = ({ generatePdf, tableData, filterParams }) => {
  const [page, setPage] = useState(0);

  const filters = {
    modules: true,
    subModule: true,
    service: true,
    department: true,
    cashDepartment: false,
    seat: true,
    status: true,
    fromDate: true,
    toDate: true,
    counterOperator: false,
    fileNo: false,
    generateReportFlag: false
  };
  const generateReports = () => {
    generatePdf(filterParams);
  };
  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };
  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('fileStatusReport')}
        </div>
      </div>
      <Filters filters={filters} download={generateReports} tableData={tableData} fileName="file-status-report" setPage={setPage} />
      <StatusReportsList filterParams={filterParams} files={tableData} setPage={setPage} page={page} />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  filterParams: getSearchListParams,
  tableData: getSearchList
});

const mapDispatchToProps = (dispatch) => ({
  generatePdf: (data) => dispatch(actions.generatePdf(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
