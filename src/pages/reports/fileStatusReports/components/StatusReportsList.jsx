import React, { useEffect, useState } from 'react';
import { CommonTable } from 'common/components/Table';
import {
  t
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { getTableLoader } from 'pages/common/selectors';
import _ from 'lodash';
import { STATUS } from 'common/regex';
import * as actions from '../actions';

const StatusReportsList = ({
  filterParams, fetchSearchFiles, setFilterParams, files, setTableLoader, tableLoader, setPage, page
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      if (cellData?.currentStage !== null) {
        status = (
          <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
            {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
          </div>
        );
      }
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.applicantName}</div>;
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleFileDate = (fileData) => {
    let fileDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileDate = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.fileDate}</div>;
    }
    return <div className="block">{fileDate}</div>;
  };

  const handleFileType = (fileData) => {
    let fileTypeInfo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileTypeInfo = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.fileTypeInfo}</div>;
    }
    return <div className="block">{fileTypeInfo}</div>;
  };

  const handleDepartmentName = (fileData) => {
    let departmentName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      departmentName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.departmentName}</div>;
    }
    return <div className="block">{departmentName}</div>;
  };

  const handleCurentSeat = (fileData) => {
    let currentSeat;
    if (fileData?.row) {
      const cellData = fileData?.row;
      currentSeat = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.currentSeat}</div>;
    }
    return <div className="block">{currentSeat}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[400px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantName',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('date'),
      field: 'fileDate',
      alignment: 'left',
      cell: (field) => handleFileDate(field)
    },
    {
      header: t('fileType'),
      alignment: 'left',
      field: 'fileTypeInfo',
      cell: (field) => handleFileType(field)
    },
    {
      header: t('department'),
      alignment: 'left',
      field: 'departmentName',
      cell: (field) => handleDepartmentName(field)
    },
    {
      header: t('currentSeat'),
      alignment: 'left',
      field: 'currentSeat',
      cell: (field) => handleCurentSeat(field)
    },
    {
      header: t('services'),
      alignment: 'left',
      field: 'serviceName',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('stage'),
      alignment: 'left',
      field: 'currentStage',
      cell: (field) => handleFileStatus(field)
    }
  ];

  useEffect(() => {
    if (filterParams?.search) {
      setTableLoader({ loading: true, id: 'file-status-report' });
      fetchSearchFiles();
    }
  }, [filterParams]);

  const onPageClick = (data) => {
    setPage(data);
    setFilterParams({
      ...filterParams, page: data
    });
  };

  useEffect(() => {
    if (files) {
      if (Object.keys(files?.content).length > 0) {
        setTableLoader({ loading: false, id: 'file-status-report' });
        setTableData(files?.content);
        setTotalItems(parseInt(`${files.totalPages}0`, 10));
        setNumberOfElements(Number(files.numberOfElements));
      } else {
        setTableLoader({ loading: false, id: 'file-status-report' });
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [files]);

  return (
    <div className="col-span-12 pb-20">

      <CommonTable
        variant="dashboard"
        tableData={tableData}
        columns={columns}
        activeRows={activeRows}
        onPageClick={onPageClick}
        itemsPerPage={10}
        totalItems={totalItems}
        currentPage={page}
        paginationEnabled
        tableLoader={tableLoader?.loading && tableLoader?.id === 'file-status-report'}
        numberOfElements={numberOfElements}
      />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  setFilterParams: (data) => dispatch(sliceActions.setSearchListParams(data)),
  fetchSearchFiles: (data) => dispatch(actions.fetchSearchFiles(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(StatusReportsList);
