import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {
  FETCH_SEARCH_FILES: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES`,
  FET<PERSON>_SEARCH_FILES_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES_REQUEST`,
  FETCH_SEARCH_FILES_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES_SUCCESS`,
  FETCH_SEARCH_FILES_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SEARCH_FILES_FAILURE`,

  FETCH_FILE_STATUS_REPORT: `${STATE_REDUCER_KEY}/FETCH_FILE_STATUS_REPORT`,
  FETCH_FILE_STATUS_REPORT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILE_STATUS_REPORT_REQUEST`,
  FETCH_FILE_STATUS_REPORT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILE_STATUS_REPORT_SUCCESS`,
  FETCH_FILE_STATUS_REPORT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILE_STATUS_REPORT_FAILURE`,

  GENERATE_PDF: `${STATE_REDUCER_KEY}/GENERATE_PDF`,
  GENERATE_PDF_REQUEST: `${STATE_REDUCER_KEY}/GENERATE_PDF_REQUEST`,
  GENERATE_PDF_SUCCESS: `${STATE_REDUCER_KEY}/GENERATE_PDF_SUCCESS`,
  GENERATE_PDF_FAILURE: `${STATE_REDUCER_KEY}/GENERATE_PDF_FAILURE`

};

export const fetchFileStatusReport = createAction(ACTION_TYPES.FETCH_FILE_STATUS_REPORT);
export const generatePdf = createAction(ACTION_TYPES.GENERATE_PDF);
export const fetchSearchFiles = createAction(ACTION_TYPES.FETCH_SEARCH_FILES);
