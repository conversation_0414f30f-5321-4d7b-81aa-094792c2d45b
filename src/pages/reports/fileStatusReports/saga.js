import {
  all, call, fork, put, select, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { Toast, t } from 'common/components';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getFilterParams, getSearchListParams } from 'pages/common/selectors';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

const { successTost, errorTost } = Toast;

export function* fetchStatusReport() {
  const apiParams = yield select(getFilterParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchStatusReport, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_FILE_STATUS_REPORT_SUCCESS,
    ACTION_TYPES.FETCH_FILE_STATUS_REPORT_FAILURE]);
  if (type === ACTION_TYPES.FETCH_FILE_STATUS_REPORT_SUCCESS) {
    yield put(sliceActions.setTableData(_.get(responsePayLoad, 'data', {})));
  }
}

export function* generatePdf({ payload = {} }) {
  yield fork(handleAPIRequest, api.generatePdf, payload);
  const { type } = yield take([
    ACTION_TYPES.GENERATE_PDF_SUCCESS,
    ACTION_TYPES.GENERATE_PDF_FAILURE]);
  if (type === ACTION_TYPES.GENERATE_PDF_SUCCESS) {
    yield call(successTost, { id: t('Download'), title: t('success') });
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export function* fetchSearchFiles() {
  const apiParams = yield select(getSearchListParams);
  const updatedParams = { ...apiParams, statusList: apiParams?.fileStatus };
  const finalParams = _.omit(updatedParams, ['search', 'fileStatus']);

  yield fork(handleAPIRequest, api.fetchSearchFiles, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_SEARCH_FILES_SUCCESS,
    ACTION_TYPES.FETCH_SEARCH_FILES_FAILURE]);
  if (type === ACTION_TYPES.FETCH_SEARCH_FILES_SUCCESS) {
    yield put(sliceActions.setSearchFileList(responsePayLoad?.data));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'file-status-report' }));
  }
}

export default function* reportsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_FILE_STATUS_REPORT, fetchStatusReport),
    takeLatest(ACTION_TYPES.GENERATE_PDF, generatePdf),
    takeLatest(ACTION_TYPES.FETCH_SEARCH_FILES, fetchSearchFiles)
  ]);
}
