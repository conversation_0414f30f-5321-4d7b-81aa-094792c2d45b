import React, { useState } from 'react';
import { connect } from 'react-redux';
import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { BASE_PATH } from 'common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import {
  Button, t, InputGroup, Input
} from 'common/components';
// import SearchIcon from 'assets/SearchIcon';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import { useForm } from 'react-hook-form';
import { getUserInfo } from 'pages/common/selectors';
import { createStructuredSelector } from 'reselect';
import AbstractPreview from './Preview';

const styles = {
  search: {
    input: {
      borderRadius: '20px',
      border: '1px solid #DEDEDE',
      height: '44px'
    },
    button: {
      background: 'none'
    }
  }
};

const Index = ({ userInfo, setAlertAction }) => {
  const [fileNumber, setFileNumber] = useState(null);
  const [openAck, setOpenAck] = useState(false);
  const [flag, setFlag] = useState(false);
  const [url, setUrl] = useState(null);
  const [loading, setLoading] = useState(false);

  const {
    handleSubmit
  } = useForm({
    mode: 'all'

  });
  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };

  const onSubmitForm = () => {
    setLoading(true);
    setFlag(!flag);
    setUrl(`${baseApiURL}/${API_URL.REPORTS.FETCH_FILE_ABSTRACT_REPORT.replace(':officeId', userInfo?.id).replace(':fileNo', fileNumber)}`);
  };

  const handleOpen = () => {
    setOpenAck(true);
  };

  const handleClose = () => {
    setOpenAck(false);
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('fileAbstractReport')}
        </div>
      </div>
      <form
        action="enter"
        id="file-abstract"
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div className="flex bg-white py-5 px-10 rounded-[10px]">
          <div className="grow">
            <div className="grid grid-cols-4 gap-4">
              <div>
                <InputGroup style={styles.search}>
                  <Input
                    name="fileNo"
                    placeholder={t('fileNo')}
                    style={styles.search.input}
                    value={fileNumber}
                    onChange={(event) => {
                      setFileNumber(event.target.value);
                    }}
                  />
                  {/* <InputRightElement>
                    <SearchIcon />
                  </InputRightElement> */}
                </InputGroup>
              </div>
            </div>
          </div>
          <div className="">
            <Button
              variant="secondary_outline"
              type="submit"
              isDisabled={!fileNumber}
              isLoading={loading}
            >
              {t('view')}
            </Button>
          </div>
        </div>
      </form>
      <div className="bg-white py-5 px-10 rounded-[10px] mt-5 mb-10">
        <AbstractPreview open={openAck} handleOpen={handleOpen} downloadUrl={url} handleClose={handleClose} flag={flag} setAlertAction={setAlertAction} setLoading={setLoading} loading={loading} />
      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
