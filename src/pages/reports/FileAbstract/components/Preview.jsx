import React, { useState, useEffect } from 'react';
import {
  t, Button
} from 'common/components';
import {
  Pdf<PERSON>ie<PERSON>, Spinner
} from '@ksmartikm/ui-components';

import { secondary } from 'utils/color';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';
import NoNotesIcon from 'assets/NoNotesIcon';

const AbstractPreview = (props) => {
  const {
    handleClose = () => { }, open, handleOpen = () => { }, downloadUrl, flag, setAlertAction = () => { }, setLoading, loading
  } = props;

  const [baseCode, setBaseCode] = useState('');

  function getDocument(url, token) {
    try {
      fetch(url, {
        method: 'GET',
        headers: {
          Accept: DOCUMENT_TYPES.PDF,
          Authorization: `Bear<PERSON> ${token}`
        }
      })
        .then((response) => response.arrayBuffer())
        .then((response) => {
          if (response?.byteLength > 0) {
            handleOpen();
            const arr = new Uint8Array(response);
            const blob = new Blob([arr], {
              type: DOCUMENT_TYPES.PDF
            });
            const urlBlob = window.URL.createObjectURL(blob);
            setLoading(false);
            setBaseCode(urlBlob);
          } else {
            setLoading(false);
            handleClose();
            setAlertAction({
              open: true, variant: 'error', title: t('failed'), message: t('fileNumberNotValid'), backwardActionText: t('ok')
            });
          }
        });
    } catch (error) {
      setLoading(false);
      handleClose();
      setAlertAction({
        open: true, variant: 'errro', title: t('failed'), message: t('fileNumberNotValid'), backwardActionText: t('ok')
      });
    }
  }

  useEffect(() => {
    if (downloadUrl) {
      getDocument(downloadUrl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN));
    }
  }, [flag]);

  const printAck = () => {
    printBlob(baseCode);
  };

  const downloadAck = () => {
    downloadBlob({ blob: baseCode, fileName: 'KSMART-FILE-ABSTRACT-REPORT.pdf' });
  };

  return (
    open ? (
      <>
        <div style={{ height: 'calc(100vh - 200px)', overflowY: 'auto', width: '100%' }}>
          {loading ? <Spinner style={{ margin: '100px auto', display: 'block' }} /> : <PdfViewer title="do" height="350px" width="600px" type="application/pdf" data={baseCode} />}
        </div>

        <div className="flex justify-between items-center w-full mt-5">
          <div className="flex items-center gap-6" />
          <div className="flex items-center gap-2 cursor-pointer">
            <Button variant="secondary_outline" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" color={secondary} />}>{t('print')}</Button>
            <Button variant="secondary_outline" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" color={secondary} />}>{t('download')}</Button>
          </div>
        </div>
      </>
    ) : (
      <div className="p-10 text-center bg-white rounded-lg">
        <NoNotesIcon width="100px" height="100px" className="mx-auto" />
        <h4>{t('previewNotAvailable')}</h4>
      </div>
    )
  );
};

export default AbstractPreview;
