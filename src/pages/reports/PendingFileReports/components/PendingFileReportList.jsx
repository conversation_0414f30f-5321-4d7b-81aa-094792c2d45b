import React, { useEffect, useState } from 'react';
import { CommonTable } from 'common/components/Table';
import {
  t
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { getFilterParams, getTableLoader } from 'pages/common/selectors';
import * as actions from '../actions';

const PendingFileReportList = ({
  filterParams, fetchPendingFileReport, tableData, setFilterParams, setTableLoader, tableLoader
}) => {
  const activeRows = [{}];
  const [page, setPage] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [tableArray, setTableArray] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  const columns = [

    {
      header: t('department'),
      field: 'department'
    },
    {
      header: t('seat'),
      field: 'seat'
    },
    {
      header: t('pendingFileCount'),
      field: 'pendingFileCount'
    }
  ];

  useEffect(() => {
    if (filterParams?.search) {
      setTableLoader({ loading: true, id: 'pending-file-report' });
      fetchPendingFileReport();
    }
  }, [filterParams]);

  const onPageClick = (data) => {
    setPage(data);
    setFilterParams({
      ...filterParams, page: data
    });
  };

  useEffect(() => {
    if (tableData) {
      if (Object.keys(tableData).length > 0) {
        setTableLoader({ loading: false, id: 'pending-file-report' });
        setTableArray(tableData.content);
        setTotalItems(parseInt(`${tableData.totalPages}0`, 10));
        setNumberOfElements(Number(tableData.numberOfElements));
      } else {
        setTableLoader({ loading: false, id: 'pending-file-report' });
        setTableArray([]);
        setTotalItems(0);
      }
    }
  }, [tableData]);

  return (
    <div className="col-span-12 pb-10">
      <CommonTable
        variant="dashboard"
        tableData={tableArray}
        columns={columns}
        activeRows={activeRows}
        onPageClick={onPageClick}
        itemsPerPage={10}
        totalItems={totalItems}
        currentPage={page}
        paginationEnabled
        tableLoader={tableLoader?.loading && tableLoader?.id === 'pending-file-report'}
        numberOfElements={numberOfElements}

      />

    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  filterParams: getFilterParams,
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  fetchPendingFileReport: (data) => dispatch(actions.fetchPendingFileReport(data)),
  setFilterParams: (data) => dispatch(sliceActions.setFilterParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PendingFileReportList);
