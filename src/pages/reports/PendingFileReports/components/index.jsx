import React from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import Filters from 'pages/common/components/Filters';
import { IconButton } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { t } from 'common/components';
import { BASE_PATH } from 'common/constants';
import * as actions from '../actions';
import { getTableData } from '../selectors';
import PendingFileReportList from './PendingFileReportList';

const index = ({ tableData, generatePdf }) => {
  const filters = {
    modules: false,
    subModule: false,
    service: false,
    department: true,
    cashDepartment: false,
    seat: true,
    status: false,
    fromDate: true,
    toDate: true,
    counterOperator: false,
    fileNo: false,
    generateReportFlag: false
  };

  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };
  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('pendingFileReport')}
        </div>
      </div>
      <Filters filters={filters} tableData={tableData} download={generatePdf} fileName="pendingFile" />
      <PendingFileReportList tableData={tableData} />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  tableData: getTableData
});

const mapDispatchToProps = (dispatch) => ({
  generatePdf: (data) => dispatch(actions.generatePdf(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(index);
