import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchPendingFileReport = (params) => {
  return {
    url: API_URL.REPORTS.FETCH_PENDING_FILE_REPORT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_PENDING_FILE_REPORT_REQUEST,
        ACTION_TYPES.FETCH_PENDING_FILE_REPORT_SUCCESS,
        ACTION_TYPES.FETCH_PENDING_FILE_REPORT_FAILURE
      ],
      params
    }
  };
};

export const generatePdf = (data) => {
  const {
    department, fromDate, toDate, page, size, seat
  } = data;

  const paramData = `?department=${department}&fromDate=${fromDate}&toDate=${toDate}&page=${page}&size=${size}&seat=${seat}`;

  return {
    url: API_URL.COMMON.FETCH_FILES.replace('?query', paramData),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.GENERATE_PDF_REQUEST,
        ACTION_TYPES.GENERATE_PDF_SUCCESS,
        ACTION_TYPES.GENERATE_PDF_FAILURE
      ]
    },
    data
  };
};
