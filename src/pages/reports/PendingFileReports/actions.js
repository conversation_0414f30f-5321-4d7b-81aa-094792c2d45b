import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_PENDING_FILE_REPORT: `${STATE_REDUCER_KEY}/FETCH_PENDING_FILE_REPORT`,
  FETCH_PENDING_FILE_REPORT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_PENDING_FILE_REPORT_REQUEST`,
  FETCH_PENDING_FILE_REPORT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_PENDING_FILE_REPORT_SUCCESS`,
  FETCH_PENDING_FILE_REPORT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_PENDING_FILE_REPORT_FAILURE`,

  GENERATE_PDF: `${STATE_REDUCER_KEY}/GENERATE_PDF`,
  GENERATE_PDF_REQUEST: `${STATE_REDUCER_KEY}/GENERATE_PDF_REQUEST`,
  GENERATE_PDF_SUCCESS: `${STATE_REDUCER_KEY}/GENERATE_PDF_SUCCESS`,
  GENERATE_PDF_FAILURE: `${STATE_REDUCER_KEY}/GENERATE_PDF_FAILURE`

};

export const fetchPendingFileReport = createAction(ACTION_TYPES.FETCH_PENDING_FILE_REPORT);
export const generatePdf = createAction(ACTION_TYPES.GENERATE_PDF);
