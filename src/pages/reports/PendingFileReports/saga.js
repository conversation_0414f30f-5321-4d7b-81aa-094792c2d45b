import {
  all, call, fork, put, select, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { Toast, t } from 'common/components';
import { getFilterParams } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

const { successTost, errorTost } = Toast;

export function* fetchPendingFileReport() {
  const apiParams = yield select(getFilterParams);
  const finalParams = _.omit(apiParams, ['search']);

  yield fork(handleAPIRequest, api.fetchPendingFileReport, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_PENDING_FILE_REPORT_SUCCESS,
    ACTION_TYPES.FETCH_PENDING_FILE_REPORT_FAILURE]);
  if (type === ACTION_TYPES.FETCH_PENDING_FILE_REPORT_SUCCESS) {
    yield put(sliceActions.setTableData(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'pending-file-report' }));
  }
}

export function* generatePdf({ payload = {} }) {
  yield fork(handleAPIRequest, api.generatePdf, payload);
  const { type } = yield take([
    ACTION_TYPES.GENERATE_PDF_SUCCESS,
    ACTION_TYPES.GENERATE_PDF_FAILURE]);
  if (type === ACTION_TYPES.GENERATE_PDF_SUCCESS) {
    yield call(successTost, { id: t('Download'), title: t('success') });
  } else {
    yield call(errorTost, { id: t('error'), title: t('error') });
  }
}

export default function* reportsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_PENDING_FILE_REPORT, fetchPendingFileReport),
    takeLatest(ACTION_TYPES.GENERATE_PDF, generatePdf)
  ]);
}
