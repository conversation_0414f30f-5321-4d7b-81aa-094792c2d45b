import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchFileLogReportList = (params) => {
  return {
    url: API_URL.REPORTS.FETCH_STATUS_LOG_REPORT,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_FILE_LOG_REPORT_REQUEST,
        ACTION_TYPES.FETCH_FILE_LOG_REPORT_SUCCESS,
        ACTION_TYPES.FETCH_FILE_LOG_REPORT_FAILURE
      ],
      params
    }
  };
};
