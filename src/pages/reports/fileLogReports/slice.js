import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {

};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setTableData: (state, { payload }) => {
      _.set(state, 'fileStatusLogReportTableList', payload);
    },
    setSearchFileList: (state, { payload }) => {
      _.set(state, 'searchFileList', payload);
    }
  }
});

export const { actions, reducer } = slice;
