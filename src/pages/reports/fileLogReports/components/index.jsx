import React, { useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getFilterParams, getUserInfo } from 'pages/common/selectors';
import { IconButton, t } from 'common/components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { BASE_PATH } from 'common/constants';
import { baseApiURL } from 'utils/http';
import Filters from '../../../common/components/Filters';
import { getTableData } from '../selectors';
import LogReportsList from './LogReportsList';

import { API_URL } from '../../../../common/urls';

const Index = ({
  tableData, filterParams, userInfo
}) => {
  const [page, setPage] = useState(0);
  const filters = {
    modules: false,
    subModule: false,
    service: false,
    department: false,
    cashDepartment: false,
    seat: false,
    status: false,
    fromDate: false,
    toDate: false,
    counterOperator: false,
    fileNo: true,
    generateReportFlag: false
  };

  const backToHome = () => {
    window.location.href = `${BASE_PATH}/services/reports`;
  };

  const downloadUrl = `${baseApiURL}/${API_URL.REPORTS.FILE_LOG_GENERATE_REPORTS}?fileNo=${filterParams.fileNo}&officeId=${userInfo.id}&template=${'filelog'}`;

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('fileTrackingReport')}
        </div>
      </div>
      <Filters filters={filters} tableData={tableData} fileName="file-log" setPage={setPage} />
      <LogReportsList filterParams={filterParams} files={tableData} downloadUrl={downloadUrl} setPage={setPage} page={page} />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  filterParams: getFilterParams,
  tableData: getTableData,
  userInfo: getUserInfo
});

const mapDispatchToProps = () => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(Index);
