import React, { useEffect, useState } from 'react';
import { CommonTable } from 'common/components/Table';
import {
  t
} from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { getTableLoader } from 'pages/common/selectors';
import _ from 'lodash';
import { STATUS } from 'common/regex';
import TableView from 'assets/TableView';
import { convertToLocalTimeZone } from 'utils/date';
import * as actions from '../actions';
import FileStatusModal from './FileStatusModal';

const LogReportsList = ({
  filterParams, fetchFileLogList, files, setTableLoader, tableLoader,
  downloadUrl
}) => {
  const [tableData, setTableData] = useState([]);
  const [openFileStatusModal, setOpenFileStatusModal] = useState(false);
  const [modalData, setModalData] = useState([]);

  const viewActions = (row) => {
    setOpenFileStatusModal(!openFileStatusModal);
    setModalData(row);
  };

  const handleCloseFileStatusModal = () => {
    setOpenFileStatusModal(false);
  };

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      if (cellData?.status !== null) {
        status = (
          <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
            {_.capitalize(cellData?.status?.replace(STATUS, ' '))}
          </div>
        );
      }
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleFileDate = (val) => {
    let date;
    if (val?.row) {
      const cellData = val?.row;
      date = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">
          {convertToLocalTimeZone(cellData?.fileDate)}
        </div>
      );
    }
    return <div className="inline-block">{date}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">{cellData.applicantName}</div>;
    }
    return <div className="block">{applicantName}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleInwardNo = (fileData) => {
    let inwardNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      inwardNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.inwardNo}</div>;
    }
    return <div className="block">{inwardNo}</div>;
  };

  const handleSla = (fileData) => {
    let sla;
    if (fileData?.row) {
      const cellData = fileData?.row;
      sla = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData.sla}</div>;
    }
    return <div className="block">{sla}</div>;
  };

  const columns = [

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('applicantName'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('serviceName'),
      field: 'serviceName',
      alignment: 'left',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('inwardNumber'),
      field: 'inwardNo',
      alignment: 'left',
      cell: (field) => handleInwardNo(field)
    },
    {
      header: t('fileDate'),
      field: 'fileDate',
      cell: (field) => handleFileDate(field),
      alignment: 'left'
    },

    {
      header: t('sla'),
      field: 'sla',
      alignment: 'left',
      cell: (field) => handleSla(field)
    },
    {
      header: t('status'),
      alignment: 'left',
      field: 'status',
      cell: (field) => handleFileStatus(field)
    },
    {
      header: t('view'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            viewActions(row);
          }
        }
      ]
    }
  ];
  useEffect(() => {
    if (filterParams?.search) {
      setTableLoader({ loading: true, id: 'file-log-report' });
      fetchFileLogList();
    }
  }, [filterParams]);

  useEffect(() => {
    if (files) {
      if (Object.keys(files).length > 0) {
        setTableLoader({ loading: false, id: 'file-log-report' });
        if (files[0].fileNo) {
          setTableData(files);
        } else {
          setTableData([]);
        }
      } else {
        setTableLoader({ loading: false, id: 'file-log-report' });
        setTableData([]);
      }
    }
  }, [files]);

  return (
    <div className="col-span-12 pb-10">

      <CommonTable
        variant="dashboard"
        tableData={tableData}
        columns={columns}
        tableLoader={tableLoader?.loading && tableLoader?.id === 'file-log-report'}
      />

      <FileStatusModal openFileStatusModal={openFileStatusModal} handleCloseFileStatusModal={handleCloseFileStatusModal} modalData={modalData} downloadUrl={downloadUrl} />
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  tableLoader: getTableLoader
});

const mapDispatchToProps = (dispatch) => ({
  setFilterParams: (data) => dispatch(sliceActions.setSearchListParams(data)),
  fetchFileLogList: () => dispatch(actions.fetchFileLogList()),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(LogReportsList);
