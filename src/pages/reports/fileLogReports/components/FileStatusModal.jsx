import React, { useState } from 'react';
import {
  t, FormModal
} from 'common/components';
import './FileLog.css';
import DocumentDownload from 'pages/common/components/DocumentDownload';
import { STATUS } from 'common/regex';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { TickIcon } from 'assets/TickIcon';
import { convertToLocalDate, convertToLocalTimeZone } from 'utils/date';
import FileLogDownload from 'assets/FileLogDownload';
import FileLogUserIcon from 'assets/FileLogUserIcon';
import FileLogTimeIcon from 'assets/FileLogTimeIcon';
import FileLogFileIcon from 'assets/FileLogFileIcon';
import FileLogCurrentStatusIcon from 'assets/FileLogCurrentStatusIcon';
import FileLogArrowEnd from 'assets/FileLogArrowEnd';
import NoNotesIcon from 'assets/NoNotesIcon';
import { CustomTable, Spinner } from '@ksmartikm/ui-components';
import { DATE_FORMAT, FILE_STATUS } from 'pages/common/constants';

const TimelineItem = ({
  data, isCurrentUser, val, index
}) => {
  // const showActionDetails = (statusVal) => {
  //   if (statusVal === 'REJECTED') {
  //     return 'and Rejected on';
  //   } if (statusVal === 'RETURNED') {
  //     return 'and Returned on';
  //   } if (statusVal === 'RETURN_TO_CITIZEN') {
  //     return 'and Return to citizen on';
  //   } if (statusVal === 'APPROVED') {
  //     return 'and Approved on';
  //   } if (statusVal === 'ROUTE_CHANGE') {
  //     return 'and Route changed on';
  //   } if (statusVal === 'RETURN_TO_CUSTODIAN') {
  //     return 'and Return to custodian on';
  //   } if (statusVal === 'ENQUIRY') {
  //     return 'and Forwarded for enquiry on';
  //   }
  //   return 'and forwarded on';
  // };

  const formatedDate = (statusVal, arrayVal) => {
    if (statusVal === 'RECEIVED') {
      return convertToLocalTimeZone(data?.fileReceivedDate);
    } if (data?.stage === 'APPLIED' && arrayVal?.fileActionLogs?.length === 1) {
      return convertToLocalTimeZone(val?.fileDate);
    }
    return convertToLocalTimeZone(data?.actionTakenDate);
  };

  const formatApplicantName = (allData, outSideData) => {
    if (isCurrentUser && outSideData?.stage === 'RETURN_TO_CITIZEN') {
      return outSideData?.applicantName;
    } if (allData?.stage === 'APPLIED') {
      return outSideData?.applicantName;
    }
    return allData?.employeeName?.replace(STATUS, ' ');
  };

  const formatDesignation = (allData, outSideData) => {
    if (isCurrentUser && outSideData?.stage === 'RETURN_TO_CITIZEN') {
      return <span className="p-[7px]">has</span>;
    } if (allData?.stage === 'APPLIED') {
      return <span className="p-[7px]">has</span>;
    }
    return <span className="p-[7px]">{data?.designation?.replace(STATUS, ' ')}(Seat {data?.postnameEng}) has</span>;
  };

  return (
    <li className="timeline-item relative">
      {(isCurrentUser || index === 0) && (
        <div
          className="absolute"
          style={{
            background: 'white', width: '110px', top: 0, height: 'calc(100% - 78px - 25%)', left: '-55px'
          }}
        />
      )}
      <div className="bg-white pt-[10px] pb-[10px] px-[10px] rounded-[20px] absolute log-circle w-[76px]">
        <div className="relative">
          {isCurrentUser
            ? (
              <FileLogCurrentStatusIcon style={{ margin: 'auto' }} />
            )
            : <TickIcon style={{ margin: 'auto' }} />}
          <div className="text-[#959595] not-italic leading-normal pt-[4px] text-center  bg-white pb-[10px] text-[10px]"> <span className="font-bold text-[12px]">{data?.stage === 'APPLIED' && val?.fileActionLogs?.length === 1 ? convertToLocalTimeZone(val?.fileDate) : convertToLocalTimeZone(data?.fileReceivedDate)}</span></div>

          {index !== 0 && (
            <FileLogArrowEnd style={{
              margin: 'auto', position: 'absolute', bottom: isCurrentUser && index ? '86px' : '73px', left: 'calc(50% - 5px)', transform: 'rotate(180deg)'
            }}
            />
          )}

        </div>

      </div>

      <div className="w-full rounded-[16px] border-[#EFF2F5] border-2 mb-[10px] px-[10px] py-[20px]" style={{ background: '#FBFCFE' }}>

        {data?.stage !== 'APPLIED' && data?.stage !== 'RECEIVED' && (
          <div className="flex gap-2 px-3 items-center mb-[20px]">
            <FileLogFileIcon />
            <div className="flex-grow text-[#454545] text-[13px] font-semibold">
              <div className="w-full">
                File received at this seat on  <span className="text-[#09327B]">{convertToLocalTimeZone(data?.fileReceivedDate)}</span>
                {/* {showActionDetails(data?.stage)}<span className="text-[#09327B]">{convertToLocalTimeZone(data?.actionTakenDate)}</span> */}
              </div>
            </div>
          </div>
        )}
        <div className="flex gap-2 px-3 mb-[20px] items-center">
          <FileLogUserIcon />
          <div className="flex-grow text-[#454545] text-[13px] font-semibold">
            <div className="w-full">
              <span className="text-[#09327B]">{formatApplicantName(data, val)}</span>,
              {formatDesignation(data, val)}
              <span className="text-[#00B2EC] text-[14px]">{_.capitalize(data?.stage?.replace(STATUS, ' '))}</span>
            </div>
            <div className="w-full text-[#454545] text-[13px] font-semibold">
              this file on  <span className="text-[#09327B]">{formatedDate(data?.stage, val)}</span>
            </div>
          </div>
        </div>

        {data?.stage !== 'APPLIED' && data?.stage !== 'RECEIVED' && (
          <div className="flex gap-2 px-3 items-center">
            <FileLogTimeIcon />
            <div className="flex-grow text-[#454545] text-[13px] font-semibold">
              <div className="w-full">
                The Total time taken at this seat is <span className="text-[#E82C78]">{data?.duration === '1 days' ? '1 day' : data?.duration}</span>
              </div>
            </div>
          </div>
        )}

      </div>
    </li>
  );
};
const Timeline = ({ val, downloadLog, loader }) => {
  const parsedVal = JSON.parse(JSON.stringify(val?.fileActionLogs)) || [];
  // const updatedVal = parsedVal?.reverse();
  // const updatedVal = parsedVal;
  let updatedVal = [];

  if (val?.applicationDetails?.length > 0 && val?.applicationDetails?.some((app) => app?.module === 'COUNTER')) {
    const newArr = parsedVal?.slice(1);
    updatedVal = newArr;
  } else {
    updatedVal = parsedVal;
  }

  const currentUserObj = {
    employeeName: val?.currentUser?.employeeName,
    fileReceivedDate: val?.currentUserFileReceivedAt,
    postnameEng: val?.currentUser?.postNameInEng,
    stage: 'RECEIVED',
    designation: val?.currentUser?.designation
  };

  const handleDate = (fileData) => {
    let applicationSubmittedDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicationSubmittedDate = (
        <div className="text-[15px] font-[400] text-[#3C4449] max-w-[150px] break-keep">{convertToLocalDate(cellData?.applicationSubmittedDate, DATE_FORMAT.DATE_LOCAL)}</div>
      );
    }
    return <div className="block">{applicationSubmittedDate}</div>;
  };

  const handleInwardNo = (fileData) => {
    let applicationNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicationNumber = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.applicationNumber}</div>
      );
    }
    return <div className="block">{applicationNumber}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData.row;
      applicantName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[200px] break-keep">
          {
            cellData.applicantNames?.filter((item) => item && item !== 'null')?.join(', ') || val?.createdByName
          }
        </div>
      );
    }

    return <div className="block">{applicantName}</div>;
  };

  const headers = [
    {
      header: t('slNo'),
      alignment: 'left',
      field: 'slNo',
      cell: (({ rowIndex }) => (rowIndex + 1))
    },
    {
      header: t('inwardNo'),
      alignment: 'left',
      field: 'applicationNumber',
      cell: (field) => handleInwardNo(field)
    },
    {
      header: t('date'),
      alignment: 'left',
      field: 'applicationSubmittedDate',
      cell: (field) => handleDate(field)
    },
    {
      header: t('applicantName'),
      alignment: 'left',
      field: 'applicantNames',
      cell: (field) => handleApplicantName(field)
    }
  ];

  const statusChange = (values) => {
    switch (values) {
      case values === FILE_STATUS.RUNNING:
        return 'bg-[#FEFCBF] pt-[3px] pr-[40px] pb-[3px] pl-[40px] rounded-[29px] text-[14px] font-[600] text-[#D69E2E] text-center inline-block';
      case values === FILE_STATUS.APPROVED:
        return 'bg-[#3DE83A33] pt-[3px] pr-[40px] pb-[3px] pl-[40px] rounded-[29px] text-[14px] font-[600] text-[#00C62C] text-center inline-block';
      case values === FILE_STATUS.PENDING:
        return 'bg-[#F0F3DE] pt-[3px] pr-[40px] pb-[3px] pl-[40px] rounded-[29px] text-[14px] font-[600] text-[#8F914B] text-center inline-block';
      case values === FILE_STATUS.DELAYED:
        return 'bg-[#BAEAFE] pt-[3px] pr-[40px] pb-[3px] pl-[40px] rounded-[29px] text-[14px] font-[600] text-[#00B2EB] text-center inline-block';
      default:
        return 'bg-[#FEFCBF] pt-[3px] pr-[40px] pb-[3px] pl-[40px] rounded-[29px] text-[14px] font-[600] text-[#D69E2E] text-center inline-block';
    }
  };

  return (
    <>
      <div className="flex px-5">
        <div className="flex-grow">
          <span className="text-[#878787] text-[20px] font-semibold">{t('fileNo')}</span> : <span className="text-[#E82C78] text-[20px] font-semibold">{val?.fileNo}</span> <span className="text-[#000000] text-[20px] font-semibold p-[5px]">/</span> <span className="text-[#09327B] text-[20px] font-semibold">{val?.localBodyName?.replace(/\bIKM\b/g, '')}</span>
        </div>
        {
          loader
            ? <Spinner style={{ height: '20px', width: '20px' }} />
            : (
              <div className="flex-none flex gap-3 pt-[7px] cursor-pointer" aria-hidden="true" onClick={downloadLog}>
                <FileLogDownload /><span className="text-[12px]">{t('download')}</span>
              </div>
            )
        }

      </div>
      <div className="flex pt-7 pb-2 pr-5 pl-4 text-[#09327B] text-[18px] font-semibold">
        {t('inwardApplicationDetails')}
      </div>
      <div className="pl-3">
        <CustomTable
          columns={headers}
          tableData={val?.applicationDetails?.length > 0 ? val?.applicationDetails : []}
          paginationEnabled
          paginationPosition="end"
          itemsPerPage={5}
        />
      </div>
      <div className="flex pt-4 pb-2 pr-5 pl-4 text-[#09327B] text-[18px] font-semibold">
        {t('fileDetails')}
      </div>
      <div className="border rounded-lg rounded-[15px] rounded-[#E8ECEE] mx-3">
        <table className="w-full">
          <tbody>
            <tr>
              <td className="text-[#153171] text-[14px] font-normal pl-[20px] pt-[10px]">{t('service')} :</td>
              <td className="text-[#3C4449] text-[16px] font-normal pl-[20px] pt-[10px]">{val?.serviceName}</td>
            </tr>
            <tr>
              <td className="text-[#153171] text-[14px] font-normal pl-[20px] pt-[10px]">{t('currentStatus')} :</td>
              <td className="pl-[20px] pt-[10px]">
                <div className={statusChange(val?.status)}>
                  {_.capitalize(val?.status?.replace(STATUS, ' '))}
                </div>
              </td>
            </tr>
            <tr>
              <td className="text-[#153171] text-[14px] font-normal pl-[20px] pt-[10px] pb-[14px]">{t('currentLocation')} :</td>
              <td className="text-[#3C4449] text-[16px] font-normal pl-[20px] pt-[10px] pb-[14px]">
                <span className="text-[#09327B]">{val?.currentUser?.employeeName}</span>, {val?.currentUser?.designation} (<span className="font-bold">{t('seat')} {val?.currentUser?.postNameInEng}</span>)
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className="flex pt-12 pr-5 pl-4 text-[#09327B] text-[18px] font-semibold">
        {t('fileHistory')}
      </div>
      <ul className="timeline-container h-[490px] overflow-y-auto">

        {updatedVal?.map((data, index) => (
          <TimelineItem data={data} key={updatedVal?.length} isCurrentStage={Number(updatedVal?.length) - 1} isCurrentUser={false} val={val} index={index} />
        ))}
        <TimelineItem data={currentUserObj} isCurrentStage={Number(updatedVal?.length) - 1} isCurrentUser val={val} index={Number(updatedVal?.length) - 1} />
      </ul>
    </>

  );
};

const FileStatusModal = ({
  openFileStatusModal, handleCloseFileStatusModal, modalData, downloadUrl
}) => {
  const [loader, setLoader] = useState(false);

  const downloadLog = () => {
    setLoader(true);
    DocumentDownload(downloadUrl, localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN), setLoader);
  };

  return (
    <FormModal
      modalTitle={t('fileTracking')}
      open={openFileStatusModal}
      close={() => {
        handleCloseFileStatusModal();
      }}
      modalSize="4xl"
      content={modalData?.fileActionLogs?.length > 0 ? (
        <div>
          <Timeline val={modalData} downloadLog={downloadLog} loader={loader} />
        </div>
      )
        : (
          <div className="p-10 text-center">
            <NoNotesIcon width="100px" height="100px" className="m-auto" />
            <h4>{t('noRecordsFound')}</h4>
          </div>
        )}
      formId="pull-form"
      type="button"
      closeButtonText={t('close')}
      closeOnOverlayClick={false}
      closeOnEsc={false}
      actionButtonText=""
    />
  );
};

export default FileStatusModal;
