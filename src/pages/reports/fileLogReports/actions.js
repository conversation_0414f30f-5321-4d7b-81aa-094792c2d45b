import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_FILE_LOG_REPORT: `${STATE_REDUCER_KEY}/FETCH_FILE_LOG_REPORT`,
  FETCH_FILE_LOG_REPORT_REQUEST: `${STATE_REDUCER_KEY}/FETCH_FILE_LOG_REPORT_REQUEST`,
  FETCH_FILE_LOG_REPORT_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_FILE_LOG_REPORT_SUCCESS`,
  FETCH_FILE_LOG_REPORT_FAILURE: `${STATE_REDUCER_KEY}/FETCH_FILE_LOG_REPORT_FAILURE`

};

export const fetchFileLogList = createAction(ACTION_TYPES.FETCH_FILE_LOG_REPORT);
