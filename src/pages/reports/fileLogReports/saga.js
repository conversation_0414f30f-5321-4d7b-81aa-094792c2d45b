import {
  all, fork, put, select, take, takeLatest
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { getFilterParams } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'common/components';
import { ACTION_TYPES } from './actions';
import * as api from './api';
import { actions as sliceActions } from './slice';

export function* fetchFileLogReportList() {
  const apiParams = yield select(getFilterParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);
  const finalParams = _.omit(updatedParams, ['search', 'page', 'size', 'sortDirection', 'penNos']);

  yield fork(handleAPIRequest, api.fetchFileLogReportList, finalParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_FILE_LOG_REPORT_SUCCESS,
    ACTION_TYPES.FETCH_FILE_LOG_REPORT_FAILURE]);
  if (type === ACTION_TYPES.FETCH_FILE_LOG_REPORT_SUCCESS) {
    if (responsePayLoad?.data?.message) {
      yield put(commonSliceActions.setAlertAction({
        open: true,
        variant: 'warning',
        message: responsePayLoad?.data?.message,
        title: t('warning'),
        backwardActionText: t('ok')
      }));
      yield put(sliceActions.setTableData([]));
      yield put(commonSliceActions.setTableLoader({ loading: false, id: 'file-log-report' }));
    } else {
      yield put(sliceActions.setTableData([_.get(responsePayLoad, 'data', {})]));
      yield put(commonSliceActions.setTableLoader({ loading: false, id: 'file-log-report' }));
    }
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 'file-log-report' }));
  }
}

export default function* reportsSaga() {
  yield all([
    takeLatest(ACTION_TYPES.FETCH_FILE_LOG_REPORT, fetchFileLogReportList)
  ]);
}
