import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_RE_ASSIGN_USER_LIST: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST`,
  FETCH_RE_ASSIGN_USER_LIST_REQUEST: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST_REQUEST`,
  FETCH_RE_ASSIGN_USER_LIST_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST_SUCCESS`,
  FETCH_RE_ASSIGN_USER_LIST_FAILURE: `${STATE_REDUCER_KEY}/FETCH_RE_ASSIGN_USER_LIST_FAILURE`,

  RE_ASSIGN_FILE: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE`,
  RE_ASSIGN_FILE_REQUEST: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE_REQUEST`,
  RE_ASSIGN_FILE_SUCCESS: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE_SUCCESS`,
  RE_ASSIGN_FILE_FAILURE: `${STATE_REDUCER_KEY}/RE_ASSIGN_FILE_FAILURE`

};
export const fetchReassignUserTableList = createAction(ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST);
export const reassignFileApi = createAction(ACTION_TYPES.RE_ASSIGN_FILE);
