import {
  t, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, InputGroup, Input, InputRightElement, FormController
} from 'common/components';
import { CommonTable } from 'common/components/Table';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getActionTriggered,
  getServicesDropdown,
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import TableView from 'assets/TableView';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { dark } from 'utils/color';
import BackArrow from 'assets/BackIcon';
import { BASE_UI_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { STATUS } from 'common/regex';
import GridIcon from 'assets/GridIcon';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon';
import SearchIcon from 'assets/SearchIcon';
import { FILTER_TYPE } from 'pages/common/constants';
import { useForm } from 'react-hook-form';
import * as commonActions from 'pages/common/actions';
import { getReassignFiles, getReassignUserListData, getReassignUserSearchListParams } from '../selectors';
import { actions as sliceActions } from '../slice';
import * as actions from '../actions';

const styles = {
  search: {
    width: '200px',
    input: {
      borderRadius: '20px',
      border: '1px solid #CBD5E0'
    },
    button: {
      background: 'none'
    }
  }
};

const List = ({
  userInfo, fetchReassignUserTableList, setReassignUserTableListParams, reassignUserTableList, reassignUserListParams,
  setTableLoader, tableLoader, setAlertAction, actionTriggered, setActionTriggered, reassignFileApi, setReassignFiles,
  reassignFiles, serviceDropdown, fetchServicesOptions
}) => {
  const {
    control
  } = useForm({
    mode: 'all'
  });

  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [search, setSearch] = useState('');

  const viewActions = (data) => {
    window.location = `${BASE_UI_PATH}${data?.url}`;
  };

  const handleFileStatus = (val) => {
    let status;
    if (val?.row) {
      const cellData = val?.row;
      status = (
        <div className="bg-[#FEFCBF] pt-[4px] pr-[10px] pb-[2px] pl-[10px] rounded-[10px] text-[14px] font-[600] text-[#D69E2E] text-center">
          {_.capitalize(cellData?.currentStage?.replace(STATUS, ' '))}
        </div>
      );
    }
    return <div className="inline-block">{status}</div>;
  };

  const handleFileNumber = (fileData) => {
    let fileNumber;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNumber = (
        <>
          <div className="text-[14px] font-[400] text-[#454545]">{cellData?.fileNo}</div>
          <div className="text-[14px] font-[700] text-[#B5B5B5]">{cellData?.fileDate}</div>
        </>
      );
    }
    return <div className="block">{fileNumber}</div>;
  };

  const handleServiceName = (fileData) => {
    let serviceName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      serviceName = <div className="text-[14px] font-[400] text-[#454545] max-w-[300px] break-keep">{cellData?.serviceName}</div>;
    }
    return <div className="block">{serviceName}</div>;
  };

  const handleApplicantName = (fileData) => {
    let applicantName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      applicantName = (
        <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.applicantName}</div>
      );
    }
    return <div className="block">{applicantName}</div>;
  };

  const actionsTable = (row) => {
    return (
      <div className="flex gap-3">
        <IconButton variant="ghost" onClick={() => viewActions(row?.row)} icon={<TableView />} />
      </div>
    );
  };

  const handleSelect = (data) => {
    const { row } = data;
    const selectedFile = reassignFiles?.length > 0 ? JSON.parse(JSON.stringify(reassignFiles)) : [];
    const find = selectedFile.findIndex((item) => item === row.fileNo);

    if (find > -1) {
      selectedFile.splice(find, 1);
    } else {
      selectedFile.push(row?.fileNo);
    }

    setReassignFiles(selectedFile);
  };

  function checkBoxCheck(data) {
    const find = reassignFiles?.findIndex((item) => item === data);
    if (find > -1) {
      return <CheckedBox />;
    } return <UnCheckedBox />;
  }

  const selectedFileRow = (data) => {
    return (
      <IconButton
        variant="unstyled"
        style={{ textDecoration: 'none' }}
        icon={checkBoxCheck(data?.row?.fileNo)}
        onClick={() => handleSelect(data)}
      />
    );
  };

  const columns = [
    {
      cell: (field) => selectedFileRow(field)
    },

    {
      header: t('fileNumber'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNumber(field)
    },
    {
      header: t('nameOfApplicant'),
      field: 'applicantName',
      alignment: 'left',
      cell: (field) => handleApplicantName(field)
    },
    {
      header: t('service'),
      field: 'serviceName',
      alignment: 'left',
      cell: (field) => handleServiceName(field)
    },
    {
      header: t('stage'),
      field: 'currentStage',
      cell: (field) => handleFileStatus(field)
    },
    {
      header: t('Actions'),
      alignment: 'left',
      cell: (field) => actionsTable(field)
    }

  ];

  useEffect(() => {
    if (reassignUserListParams) {
      setTableLoader({ loading: true, id: 're-assign-user-file-table' });
      fetchReassignUserTableList();
    }
  }, [reassignUserListParams]);

  const onPageClick = (data) => {
    setPage(data);
    setReassignUserTableListParams({
      ...reassignUserListParams,
      page: data,
      officeId: userInfo.id
    });
  };

  useEffect(() => {
    if (reassignUserTableList) {
      setTableLoader({ loading: false, id: 're-assign-user-file-table' });
      if (Object.keys(reassignUserTableList).length > 0) {
        setTableData(reassignUserTableList.content);
        setTotalItems(Number(`${reassignUserTableList.totalPages}0`));
        setNumberOfElements(Number(reassignUserTableList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [reassignUserTableList]);

  const backToHome = () => {
    window.location.href = EMPLOYEE_SERVICE_PATH;
  };

  const handleReassignFiles = () => {
    if (reassignFiles?.length > 0) {
      setActionTriggered({ loading: true, id: 're-assign-file' });
      reassignFileApi({ fileList: reassignFiles });
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('pleaseSelectFileNo'),
        title: t('fileNo'),
        backwardActionText: t('ok')
      });
    }
  };

  const triggerSearch = (field, data) => {
    setPage(0);
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setReassignUserTableListParams({
          ...reassignUserListParams,
          keyword: data || null,
          page: 0
        });
        break;
      case FILTER_TYPE.SERVICES:
        setReassignUserTableListParams({
          ...reassignUserListParams,
          serviceCode: data?.code || null,
          page: 0
        });
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    fetchServicesOptions();
  }, []);

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px] pr-2">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('suspendedFiles')}
        </div>
        <div className="flex-none">
          <InputGroup style={styles.search}>
            <Input
              placeholder={t('searchHere')}
              style={styles.search.input}
              value={search}
              onChange={(event) => {
                setSearch(event.target.value);
              }}
            />
            <InputRightElement>
              <IconButton onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)} icon={<SearchIcon color="#fff" />} colorScheme="pink" variant="solid" isRound />
            </InputRightElement>
          </InputGroup>
        </div>
        <div style={{ width: '300px' }}>
          <FormController
            name="services"
            type="select"
            placeholder={t('searchHere')}
            control={control}
            options={_.get(serviceDropdown, 'data', [])}
            handleChange={(data) => {
              triggerSearch(FILTER_TYPE.SERVICES, data);
            }}
            optionKey="code"
          />
        </div>
        <div>
          <Button
            variant="secondary_outline"
            leftIcon={<GridIcon color="#E82C78" />}
            onClick={handleReassignFiles}
            style={{ maxWidth: '200px' }}
            isLoading={actionTriggered?.id === 're-assign-file' && actionTriggered?.loading}
          >
            {t('reAssign')}
          </Button>
        </div>
      </div>
      <div className="col-span-12 pb-20">
        <CommonTable
          variant="dashboard"
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 're-assign-user-file-table'}
        />

      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  userInfo: getUserInfo,
  reassignUserTableList: getReassignUserListData,
  reassignUserListParams: getReassignUserSearchListParams,
  tableLoader: getTableLoader,
  actionTriggered: getActionTriggered,
  reassignFiles: getReassignFiles,
  serviceDropdown: getServicesDropdown
});

const mapDispatchToProps = (dispatch) => ({
  fetchReassignUserTableList: (data) => dispatch(actions.fetchReassignUserTableList(data)),
  setReassignUserTableListParams: (data) => dispatch(sliceActions.setReassignUserTableListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  reassignFileApi: (data) => dispatch(actions.reassignFileApi(data)),
  setReassignFiles: (data) => dispatch(sliceActions.setReassignFiles(data)),
  fetchServicesOptions: () => dispatch(commonActions.fetchServicesDetails())
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
