import { flow } from 'lodash';
import { STATE_REDUCER_KEY } from './constants';

const getReassignUserDetails = (state) => state[STATE_REDUCER_KEY];

const reassignUserListData = (state) => state?.reassignUserTableList;
export const getReassignUserListData = flow(getReassignUserDetails, reassignUserListData);

const reassignUserSearchListParams = (state) => state?.reassignUserTableListParams;
export const getReassignUserSearchListParams = flow(getReassignUserDetails, reassignUserSearchListParams);

const reassignFiles = (state) => state?.reassignFiles;
export const getReassignFiles = flow(getReassignUserDetails, reassignFiles);
