import {
  all, takeLatest, fork, put, take, select, call
} from 'redux-saga/effects';
import { handleAPIRequest } from 'utils/http';
import _ from 'lodash';
import { actions as commonSliceActions } from 'pages/common/slice';
import { t } from 'common/components';
import * as api from './api';
import {
  ACTION_TYPES
} from './actions';
import { actions as sliceActions } from './slice';
import { getReassignUserSearchListParams } from './selectors';
import { formatVariant, formateResposneMessage } from '../helper';

export function* fetchReassignUserTableList() {
  const apiParams = yield select(getReassignUserSearchListParams);
  const updatedParams = _.omitBy(apiParams, _.isNil);

  yield fork(handleAPIRequest, api.fetchReassignUserTableList, updatedParams);
  const { payload: responsePayLoad = {}, type } = yield take([
    ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_SUCCESS,
    ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_FAILURE]);
  if (type === ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_SUCCESS) {
    yield put(sliceActions.setReassignUserTableList(_.get(responsePayLoad, 'data', {})));
  } else {
    yield put(commonSliceActions.setTableLoader({ loading: false, id: 're-assign-user-file-table' }));
  }
}

export function* reassignFileApi({ payload }) {
  yield fork(handleAPIRequest, api.reassignFileApi, payload);
  const { type, payload: responsePayLoad = {} } = yield take([
    ACTION_TYPES.RE_ASSIGN_FILE_SUCCESS,
    ACTION_TYPES.RE_ASSIGN_FILE_FAILURE]);
  if (type === ACTION_TYPES.RE_ASSIGN_FILE_SUCCESS) {
    const { apiResponse: { data: { payload: responceData = '' } } = {} } = responsePayLoad;
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 're-assign-file' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: `${formatVariant(responceData)}`,
        message: `${formateResposneMessage(responceData)}`,
        title: `${formatVariant(responceData)}`,
        backwardActionText: t('ok')
      })
    );
    yield call(fetchReassignUserTableList);
    yield put(sliceActions.setReassignFiles([]));
  } else {
    yield put(commonSliceActions.setActionTriggered({ loading: false, id: 're-assign-file' }));
    yield put(
      commonSliceActions.setAlertAction({
        open: true,
        variant: 'error',
        message: t('reassignFailed'),
        title: t('error'),
        backwardActionText: t('ok')
      })
    );
    yield put(sliceActions.setReassignFiles([]));
  }
}

export default function* counterSaga() {
  yield all([

    takeLatest(ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST, fetchReassignUserTableList),
    takeLatest(ACTION_TYPES.RE_ASSIGN_FILE, reassignFileApi)
  ]);
}
