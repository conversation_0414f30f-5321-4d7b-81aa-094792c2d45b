import { createSlice } from '@reduxjs/toolkit';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { STATE_REDUCER_KEY } from './constants';

const initialState = {
  reassignUserTableListParams: {
    page: 0,
    size: 10,
    sortDirection: 'desc',
    officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
    statusList: FILE_STATUS_FOR_API_PARAMS.SUSPENDED
  },
  reassignFiles: []
};

const slice = createSlice({
  initialState,
  name: STATE_REDUCER_KEY,
  reducers: {
    setReassignUserTableList: (state, { payload }) => {
      _.set(state, 'reassignUserTableList', payload);
    },
    setReassignUserTableListParams: (state, { payload }) => {
      _.set(state, 'reassignUserTableListParams', payload);
    },
    setReassignFiles: (state, { payload }) => {
      _.set(state, 'reassignFiles', payload);
    }
  }
});

export const { actions, reducer } = slice;
