import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchReassignUserTableList = (params) => {
  return {
    url: API_URL.SEARCH_FILES.FETCH_SEARCH_FILES,
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_REQUEST,
        ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_SUCCESS,
        ACTION_TYPES.FETCH_RE_ASSIGN_USER_LIST_FAILURE
      ],
      params
    }
  };
};

export const reassignFileApi = (data) => {
  return {
    url: API_URL.RE_ASSIGN_FILES,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.RE_ASSIGN_FILE_REQUEST,
        ACTION_TYPES.RE_ASSIGN_FILE_SUCCESS,
        ACTION_TYPES.RE_ASSIGN_FILE_FAILURE
      ],
      data
    }
  };
};
