import { t } from 'common/components';

export const formateResposneMessage = (response) => {
  const { failure = [], success = [] } = response;
  if (failure?.length === 0 && success?.length > 0) {
    return t('reassignSuccessfully');
  } if (failure?.length > 0 && success?.length === 0) {
    return t('reassignFailed');
  }
  const displaySuccessString = success.join(', ');
  const displayFailureString = failure.join(', ');
  return `<div class="flex flex-col"><div>Success : ${displaySuccessString}</div><div>Failed : ${displayFailureString}</div></div>`;
};

export const formatVariant = (response) => {
  const { failure = [], success = [] } = response;
  if (failure?.length === 0 && success?.length > 0) {
    return 'success';
  } if (failure?.length > 0 && success?.length === 0) {
    return 'error';
  }
  return 'warning';
};
