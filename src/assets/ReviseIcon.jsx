import React from 'react';

const ReviseIcon = ({ h = '20', w = '20' }) => (

  <svg width={w} height={h} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13 4H7C3.686 4 1 6.686 1 10C1 11.912 1.897 13.611 3.29 14.71" stroke="#323232" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7 15.9991H13C16.314 15.9991 19 13.3131 19 9.99906C19 8.08706 18.103 6.38806 16.71 5.28906" stroke="#323232" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.5 13.5L7 16L9.5 18.5" stroke="#323232" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.5 6.5L13 4L10.5 1.5" stroke="#323232" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default ReviseIcon;
