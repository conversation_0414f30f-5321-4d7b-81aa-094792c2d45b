import React from 'react';

const DraftInward = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" fillRule="evenodd" clipRule="evenodd" d="M9.33332 28H23.3333C25.1743 28 26.6667 26.5076 26.6667 24.6667V11.1622C26.6667 10.1014 26.2452 9.08395 25.4951 8.3338L22.3329 5.17157C21.5827 4.42143 20.5653 4 19.5044 4H9.99999C8.15904 4 6.66666 5.49238 6.66666 7.33333V25.3333C6.66666 26.8061 7.86056 28 9.33332 28Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M26.6352 10.6667H22C20.8954 10.6667 20 9.77124 20 8.66667V4.03149" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M21.3333 16H16.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M17 21.5H16" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_5" d="M21 21.5H20" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_6" d="M12 15.8334C11.908 15.8334 11.8333 15.908 11.8333 16C11.8333 16.0921 11.908 16.1667 12 16.1667C12.0921 16.1667 12.1667 16.0921 12.1667 16C12.1667 15.908 12.0921 15.8334 12 15.8334" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_7" d="M12 21.1666C11.908 21.1666 11.8333 21.2412 11.8333 21.3333C11.8333 21.4253 11.908 21.5 12 21.5C12.0921 21.5 12.1667 21.4253 12.1667 21.3333C12.1667 21.2412 12.0921 21.1666 12 21.1666" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default DraftInward;
