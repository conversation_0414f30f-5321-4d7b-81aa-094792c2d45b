import React from 'react';

const Beneficiary = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M28.0013 17.5V24C28.0013 26.2091 26.2104 28 24.0013 28H22.668" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 12V8C4 5.79086 5.79086 4 8 4H14.5" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 26.6667L5.54133 27.4373C6.28146 27.8074 7.09757 28 7.92504 28H16.6667C17.7712 28 18.6667 27.1046 18.6667 26V26C18.6667 24.8954 17.7712 24 16.6667 24H11.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 17.332H7.88447C8.46145 17.332 9.02288 17.5192 9.48447 17.8654L12.4675 20.1026C12.9695 20.4791 13.2834 21.0549 13.3278 21.6808C13.3723 22.3066 13.143 22.921 12.6993 23.3647V23.3647C11.9377 24.1264 10.7319 24.2121 9.87013 23.5658L8 22.1632" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="23.5" cy="4.5" r="2.5" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M19 13C19 11.3429 20.2796 10 21.8587 10H25.1413C26.7204 10 28 11.3429 28 13" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Beneficiary;
