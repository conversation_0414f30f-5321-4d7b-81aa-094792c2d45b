import React from 'react';

const Zoom = ({ stroke = '#848484', w = '24', h = '24' }) => (

  <svg width={w} height={h} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M18.9352 11.4748V11.4748C18.9352 15.5988 15.5922 18.9418 11.4682 18.9418V18.9418C7.34422 18.9418 4.00122 15.5988 4.00122 11.4748V11.4748C4.00122 7.35081 7.34422 4.00781 11.4682 4.00781V4.00781C15.5922 4.00781 18.9352 7.35081 18.9352 11.4748Z" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.0007 20.0078L16.7507 16.7578" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.7895 11.4727H8.14746" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.4675 14.7943L11.4675 8.15234" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Zoom;
