import React from 'react';

const Claim = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" d="M28 21.3333V24C28 26.2091 26.2091 28 24 28H22.6666" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M4 12V8C4 5.79086 5.79086 4 8 4H10.6667" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M4 26.6667L5.54133 27.4373C6.28146 27.8074 7.09757 28 7.92504 28H16.6667C17.7712 28 18.6667 27.1046 18.6667 26V26C18.6667 24.8954 17.7712 24 16.6667 24H11.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M4 17.3333H7.88447C8.46145 17.3333 9.02288 17.5205 9.48447 17.8666L12.4675 20.1039C12.9695 20.4804 13.2834 21.0562 13.3278 21.682C13.3723 22.3079 13.143 22.9223 12.6993 23.366V23.366C11.9377 24.1277 10.7319 24.2133 9.87013 23.567L8 22.1644" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_5" d="M28 12.9758V14.6425C28 16.1153 26.8061 17.3092 25.3333 17.3092H17.3333C15.8605 17.3092 14.6666 16.1153 14.6666 14.6425V6.6425C14.6666 5.16974 15.8605 3.97583 17.3333 3.97583H25.3333C26.8061 3.97583 28 5.16974 28 6.6425V8.30916" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_6" fillRule="evenodd" clipRule="evenodd" d="M27 8.3092H28.6666C29.0348 8.3092 29.3333 8.60768 29.3333 8.97587V12.3092C29.3333 12.6774 29.0348 12.9759 28.6666 12.9759H27C25.7113 12.9759 24.6666 11.9312 24.6666 10.6425V10.6425C24.6666 9.35387 25.7113 8.3092 27 8.3092Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default Claim;
