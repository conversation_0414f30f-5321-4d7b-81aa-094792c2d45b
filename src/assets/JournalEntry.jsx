import React from 'react';

const JournalEntry = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M26.6184 19.981V8.714C26.6184 6.67306 24.9639 5.01855 22.923 5.01855H15.5747H14.9353" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M26.6189 23.4424V23.9672C26.6189 24.7985 26.2886 25.5957 25.7008 26.1834C25.1129 26.7712 24.3157 27.1013 23.4845 27.1013H16.2092" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.3811 14.9772V8.0325C5.38116 7.20124 5.71142 6.40405 6.29925 5.8163C6.88707 5.22855 7.68431 4.89838 8.51556 4.89844H10.6052" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15.5389 25.5332L13.9717 27.1004L15.5389 28.6677" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M21.6913 20.9561H18.1829" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M21.691 16.0293L16.2092 16.0293" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M21.6909 11.1025H10.6052" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.82202 6.46649L11.3893 4.89926L9.82202 3.33203" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.3013 18.8887H5.3811H6.54709C8.27882 18.8887 9.68267 20.2925 9.68267 22.0243V22.0243C9.68267 23.756 8.27882 25.1598 6.54709 25.1598H5.75109C5.62719 25.1598 5.57182 25.3154 5.66785 25.3937L9.68267 28.6675M5.3811 21.8138H8.1255H11.6388" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default JournalEntry;
