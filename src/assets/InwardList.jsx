import React from 'react';

const InwardList = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" fillRule="evenodd" clipRule="evenodd" d="M9.33332 28H23.3333C25.1743 28 26.6667 26.5076 26.6667 24.6667V11.1622C26.6667 10.1014 26.2452 9.08395 25.4951 8.3338L22.3329 5.17157C21.5827 4.42143 20.5653 4 19.5044 4H9.99999C8.15904 4 6.66666 5.49238 6.66666 7.33333V25.3333C6.66666 26.8061 7.86056 28 9.33332 28Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M26.6352 10.6667H22C20.8954 10.6667 20 9.77124 20 8.66667V4.03149" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M18.6667 21.3333H21.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M12 21.0051L12.8847 21.8022L14.6667 20.1984" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_5" d="M18.6667 16H21.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_6" d="M12 15.6718L12.885 16.4692L14.6667 14.8647" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default InwardList;
