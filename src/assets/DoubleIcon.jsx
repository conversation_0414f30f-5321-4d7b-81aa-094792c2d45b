import React from 'react';

const RightDoubleIcon = ({ color = '#9AA6AD', ...rest }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" {...rest} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="icon icon-tabler icons-tabler-outline icon-tabler-chevrons-right"><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M7 7l5 5l-5 5" /><path d="M13 7l5 5l-5 5" /></svg>
);

const LeftDoubleIcon = ({ color = '#9AA6AD', ...rest }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" {...rest} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="icon icon-tabler icons-tabler-outline icon-tabler-chevrons-left"><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M11 7l-5 5l5 5" /><path d="M17 7l-5 5l5 5" /></svg>
);
const DotIcon = ({ color = '#9AA6AD', ...rest }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" {...rest} viewBox="0 0 24 24" fill="currentColor" stroke={color} className="icon icon-tabler icons-tabler-filled icon-tabler-point"><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M12 7a5 5 0 1 1 -4.995 5.217l-.005 -.217l.005 -.217a5 5 0 0 1 4.995 -4.783z" /></svg>
);
export {
  RightDoubleIcon,
  LeftDoubleIcon,
  DotIcon
};
