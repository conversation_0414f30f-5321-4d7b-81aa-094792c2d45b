import React from 'react';

function NotesNewIcon({ color = '#5C6E93', width = '24', height = '24' }) {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.99811 21.0055H5.99686C4.33931 21.0055 2.99561 19.6618 2.99561 18.0043V5.9993C2.99561 4.34175 4.33931 2.99805 5.99686 2.99805H18.0019C19.6594 2.99805 21.0031 4.34175 21.0031 5.9993V9.00055" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M14.7231 20.7115C14.5356 20.8992 14.2811 21.0047 14.0158 21.0047H12V18.9888C12 18.7235 12.1055 18.4691 12.2931 18.2815L18.2196 12.3501C18.5425 12.0271 18.9804 11.8457 19.4371 11.8457C19.8938 11.8457 20.3317 12.0271 20.6546 12.3501V12.3501C20.9778 12.6728 21.1594 13.1108 21.1594 13.5676C21.1594 14.0243 20.9778 14.4623 20.6546 14.7851L14.7231 20.7115Z" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M16.0122 14.5645L18.4432 16.9955" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M20.1681 15.2695L21.0685 16.1699V16.1699C21.5424 16.6448 21.5424 17.4137 21.0685 17.8886L19.979 18.9791" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9.24854 11.0002H14.0005" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9.24854 7.12521H16.0013" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.12228 11.1253C6.19134 11.1253 6.24733 11.0693 6.24733 11.0002C6.24733 10.9311 6.19134 10.8752 6.12228 10.8752C6.05321 10.8752 5.99723 10.9311 5.99723 11.0002C5.99723 11.0693 6.05321 11.1253 6.12228 11.1253" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.12228 7.25026C6.19134 7.25026 6.24733 7.19427 6.24733 7.12521C6.24733 7.05614 6.19134 7.00016 6.12228 7.00016C6.05321 7.00016 5.99723 7.05614 5.99723 7.12521C5.99723 7.19427 6.05321 7.25026 6.12228 7.25026" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9.24854 14.8772H10.9993" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.12228 15.0022C6.19134 15.0022 6.24733 14.9462 6.24733 14.8772C6.24733 14.8081 6.19134 14.7521 6.12228 14.7521C6.05321 14.7521 5.99723 14.8081 5.99723 14.8772C5.99723 14.9462 6.05321 15.0022 6.12228 15.0022" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

  );
}

export default NotesNewIcon;
