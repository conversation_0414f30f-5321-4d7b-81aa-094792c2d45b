import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const Rotate = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
    <path d="M9 4.55a8 8 0 0 1 6 14.9m0 -4.45v5h5" stroke="currentColor" />
    <path d="M5.63 7.16l0 .01" stroke="currentColor" />
    <path d="M4.06 11l0 .01" stroke="currentColor" />
    <path d="M4.63 15.1l0 .01" stroke="currentColor" />
    <path d="M7.16 18.37l0 .01" stroke="currentColor" />
    <path d="M11 19.94l0 .01" stroke="currentColor" />
  </Icon>
);

export default Rotate;
