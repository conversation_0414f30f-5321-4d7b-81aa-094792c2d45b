import React from 'react';

const Paper = ({ color }) => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.73642 0.0184379C14.1087 0.0184379 19.4902 0.0184379 24.8625 0.0184379C24.1666 0.986592 23.9625 2.08383 23.9625 3.25484C23.981 11.5072 23.9625 19.7596 23.9625 28.0027C23.9625 28.1963 23.9625 28.39 23.9439 28.5744C23.8419 29.7085 23.248 30.483 22.1903 30.9164C22.0047 30.9901 21.8099 31.0362 21.615 31.0916H20.882C20.882 31.0916 20.8263 31.0547 20.7985 31.0547C19.2954 30.6951 18.5252 29.7269 18.516 28.1871C18.516 27.0991 18.516 26.0018 18.516 24.9138C18.516 24.1393 18.1819 23.8074 17.4025 23.8074C13.5612 23.8074 9.72921 23.8074 5.8879 23.8074H5.50751V23.374C5.50751 18.5148 5.50751 13.6648 5.50751 8.80559C5.50751 7.11823 5.50751 5.43088 5.50751 3.73431C5.50751 2.31435 6.11988 1.20788 7.35393 0.497905C7.76218 0.258172 8.2632 0.165969 8.71784 0L8.73642 0.0184379ZM18.4139 10.9632C18.4139 10.8157 18.4139 10.6958 18.4139 10.5759C18.4139 8.87935 18.4139 7.18278 18.4139 5.47698C18.4139 4.9883 18.2191 4.65636 17.7737 4.44429C15.7881 3.51301 13.7932 3.50379 11.7983 4.4074C11.288 4.63792 11.0746 4.99751 11.0839 5.55075C11.1024 7.25654 11.0839 8.97156 11.0931 10.6774C11.0931 10.8802 11.0561 10.9724 10.8241 11.0093C10.4158 11.0738 10.1375 11.4427 10.1189 11.8484C10.1004 12.2448 10.3508 12.6137 10.7313 12.7335C10.8704 12.7796 11.0282 12.7888 11.186 12.7888C13.5705 12.7888 15.9458 12.7888 18.3304 12.7888C18.4603 12.7888 18.5995 12.7796 18.7201 12.752C19.1191 12.6505 19.3974 12.3002 19.3974 11.8852C19.3974 11.4795 19.1283 11.1107 18.7386 11.0001C18.6551 10.9724 18.5624 10.9632 18.4325 10.9447L18.4139 10.9632ZM14.7489 14.633C13.1901 14.633 11.6313 14.633 10.0725 14.633C9.97047 14.633 9.86839 14.633 9.76632 14.633C9.26528 14.6606 8.90341 15.0294 8.88485 15.5089C8.86629 15.9699 9.20036 16.3664 9.68285 16.431C9.80347 16.4494 9.92405 16.4494 10.0447 16.4494C13.1808 16.4494 16.317 16.4494 19.4531 16.4494C19.6016 16.4494 19.7593 16.4494 19.9078 16.4125C20.3346 16.3203 20.6222 15.9423 20.6129 15.5089C20.5944 14.9833 20.2047 14.633 19.6108 14.633C17.9964 14.633 16.3726 14.633 14.7582 14.633H14.7489ZM14.7489 18.2751C13.1901 18.2751 11.6313 18.2751 10.0725 18.2751C9.96119 18.2751 9.84986 18.2751 9.73852 18.2751C9.27459 18.3119 8.92197 18.6623 8.89414 19.1233C8.8663 19.5751 9.17247 19.9716 9.62712 20.0638C9.75702 20.0915 9.88697 20.0915 10.0261 20.0915C13.1808 20.0915 16.3355 20.0915 19.4902 20.0915C19.6387 20.0915 19.7964 20.0915 19.9449 20.0454C20.3439 19.944 20.6129 19.5844 20.6129 19.1694C20.6129 18.773 20.3438 18.4134 19.9634 18.3119C19.815 18.2751 19.6572 18.2751 19.5088 18.2751C17.9222 18.2751 16.3355 18.2751 14.7489 18.2751Z" fill={color} />
    <path d="M0.00585938 26.4907C0.274937 25.7346 0.451254 25.6147 1.24921 25.6147C5.99982 25.6147 10.7504 25.6147 15.501 25.6147C15.6031 25.6147 15.7052 25.6147 15.8072 25.6147C16.3268 25.6424 16.6887 25.9836 16.7072 26.4999C16.7258 26.9702 16.7258 27.4496 16.7072 27.9291C16.6608 29.0817 16.9207 30.1512 17.6073 31.1009H3.25335C3.19767 31.0825 3.14201 31.0548 3.08634 31.0456C1.68528 30.7598 0.720315 29.9484 0.24711 28.6022C0.126489 28.261 0.0893752 27.883 0.015147 27.5142C0.015147 27.173 0.015147 26.8226 0.015147 26.4815L0.00585938 26.4907Z" fill={color} />
    <path d="M31.2846 4.62881C31.0156 5.34801 30.8207 5.4771 30.0506 5.4771C28.7609 5.4771 27.4712 5.4771 26.1814 5.4771H25.8196C25.8196 5.1175 25.8196 4.78556 25.8196 4.46284C25.8196 3.83585 25.801 3.20885 25.8381 2.58186C25.9124 1.30943 26.7475 0.350494 28.0093 0.073879C28.065 0.0646585 28.1207 0.0369957 28.1764 0.0185547C28.4176 0.0185547 28.6681 0.0185547 28.9093 0.0185547C28.9557 0.0369957 29.0021 0.055438 29.0485 0.073879C30.1991 0.332053 30.9135 1.03281 31.2011 2.16694C31.2475 2.34213 31.2661 2.51731 31.2939 2.6925V4.63803L31.2846 4.62881Z" fill={color} />
    <path d="M16.5494 10.9633H12.9308C12.9308 10.8527 12.9122 10.7513 12.9122 10.659C12.9122 9.17454 12.9122 7.69004 12.9122 6.19632C12.9122 5.97503 12.9772 5.8736 13.1906 5.80906C14.2576 5.47712 15.3153 5.4679 16.3638 5.83672C16.4566 5.8736 16.5773 6.00269 16.5773 6.09489C16.5865 7.6716 16.5773 9.24831 16.5773 10.825C16.5773 10.8619 16.5587 10.8988 16.5494 10.9541V10.9633Z" fill={color} />
  </svg>
);

export default Paper;
