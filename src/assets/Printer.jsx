import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const PrintIcon = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="currentColor" d="M17 17h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-14a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2" />
    <path stroke="currentColor" d="M17 9v-4a2 2 0 0 0 -2 -2h-6a2 2 0 0 0 -2 2v4" />
    <path stroke="currentColor" d="M7 13m0 2a2 2 0 0 1 2 -2h6a2 2 0 0 1 2 2v4a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2z" />
  </Icon>
);

export default PrintIcon;
