import React from 'react';

const Services = () => (

  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.17109 4.8241C6.17109 3.98123 6.17624 3.13836 6.16852 2.29548C6.16594 2.02493 6.24837 1.91047 6.53686 1.89746C7.61355 1.84023 8.56661 1.39278 9.48618 0.867282C9.88801 0.638353 10.2795 0.380808 10.6556 0.110256C10.8565 -0.032824 10.9956 -0.0406284 11.1991 0.110256C12.1135 0.773629 13.0795 1.35115 14.169 1.67113C14.5606 1.7856 14.9701 1.84803 15.3771 1.90006C15.5986 1.92868 15.6888 2.01453 15.6888 2.23045C15.6836 3.95782 15.7197 5.68519 15.663 7.41256C15.6321 8.3777 15.2045 9.19976 14.388 9.76168C13.3164 10.5031 12.2372 11.2289 11.1656 11.9677C11.0008 12.0822 10.8617 12.09 10.6891 11.9729C9.61755 11.2341 8.5357 10.5109 7.46673 9.76688C6.62701 9.18155 6.22776 8.33088 6.18139 7.32411C6.14533 6.49424 6.17367 5.65917 6.17367 4.82671C6.17367 4.82671 6.16852 4.82671 6.16594 4.82671L6.17109 4.8241ZM10.5577 6.33035C10.1688 5.93233 9.83134 5.56812 9.4733 5.22473C9.15647 4.92036 8.71085 4.93337 8.42236 5.22993C8.14675 5.51349 8.13387 5.96615 8.42494 6.27052C8.96071 6.82983 9.50936 7.38134 10.0632 7.91984C10.3414 8.1904 10.7715 8.1956 11.0446 7.92505C11.9487 7.03014 12.8425 6.12484 13.7286 5.21172C13.9655 4.96719 13.9758 4.59257 13.8033 4.31682C13.6307 4.04367 13.2829 3.90319 12.9635 4.01765C12.8167 4.06968 12.675 4.17894 12.5617 4.29081C11.8972 4.95418 11.2429 5.62796 10.5551 6.32775L10.5577 6.33035Z" fill="#454545" />
    <path d="M14.2694 15.667C14.1689 15.6071 14.0762 15.5317 13.9706 15.4927C13.3704 15.2741 12.7728 15.0452 12.165 14.8553C11.1269 14.5301 10.1404 14.0983 9.1976 13.5572C8.50213 13.1565 7.77575 12.7897 6.94891 12.9484C6.34874 13.0629 5.75888 13.2632 5.17932 13.4661C4.744 13.6196 4.33187 13.8433 3.90171 14.015C3.7214 14.0879 3.67504 14.1867 3.69822 14.3688C3.82701 15.3886 3.94807 16.411 4.07171 17.4333C4.09232 17.5998 4.1155 17.7299 4.34217 17.7169C5.73827 17.6311 7.04679 17.9692 8.33985 18.4843C9.00184 18.7471 9.7205 18.8902 10.4263 19.0228C11.2892 19.1841 12.1289 19.041 12.9197 18.6482C15.1452 17.5478 17.1724 16.156 18.9471 14.4026C19.1712 14.1815 19.3412 13.898 19.4983 13.6196C19.5576 13.5155 19.5112 13.3464 19.5138 13.2086C19.3876 13.2086 19.2382 13.1643 19.1351 13.2138C18.6818 13.4375 18.2336 13.6664 17.8034 13.9318C17.2496 14.27 16.7139 14.6446 16.1704 15.001C15.887 15.1883 15.6217 15.1545 15.4646 14.9229C15.3203 14.7044 15.377 14.4209 15.6423 14.2518C16.5567 13.6716 17.4712 13.0915 18.4036 12.54C18.6535 12.3917 18.9574 12.3111 19.2485 12.259C19.9955 12.1264 20.6137 12.7949 20.4823 13.5572C20.3818 14.1269 20.0547 14.5665 19.6812 14.9724C18.5813 16.1742 17.2574 17.0978 15.9154 17.9901C15.2714 18.4193 14.5914 18.7913 13.9268 19.1867C12.2448 20.1883 10.4855 20.1909 8.68244 19.5952C7.89166 19.335 7.11891 18.9994 6.31268 18.8069C5.69963 18.6612 5.04795 18.682 4.41172 18.6482C4.32157 18.643 4.17217 18.7341 4.13868 18.8199C3.94807 19.2908 3.58746 19.4989 3.11351 19.5535C2.8585 19.5822 2.60092 19.6056 2.34591 19.6368C1.66332 19.7226 1.14558 19.335 1.06057 18.6482C0.869962 17.1238 0.684503 15.5967 0.506771 14.0697C0.444951 13.5364 0.813294 13.0083 1.33104 12.912C1.73544 12.8366 2.14757 12.7741 2.55713 12.7793C2.7941 12.7845 3.06199 12.8912 3.2526 13.0369C3.43548 13.1774 3.55912 13.1774 3.74458 13.0941C4.4246 12.8001 5.09689 12.4776 5.79751 12.2382C6.97209 11.835 8.13894 11.8792 9.22594 12.527C10.586 13.336 12.0568 13.8589 13.5507 14.335C13.95 14.4625 14.3338 14.6498 14.7047 14.8501C14.8773 14.9437 15.0344 15.1102 15.1349 15.2819C15.4131 15.7502 15.2817 16.2705 14.7897 16.4942C14.4394 16.6529 14.0479 16.7544 13.6667 16.7934C11.8146 16.9833 10.0399 16.6399 8.3244 15.9349C8.03591 15.8152 7.90712 15.5681 7.99469 15.321C8.08485 15.0634 8.34243 14.975 8.66183 15.0738C9.45776 15.3158 10.2434 15.6565 11.0573 15.7658C11.9949 15.8933 12.9635 15.8204 13.9165 15.8256C14.0247 15.8256 14.1329 15.7762 14.2436 15.7502C14.2514 15.7216 14.2617 15.693 14.2694 15.6644V15.667ZM2.65758 14.6576C2.65243 14.322 2.42834 14.0775 2.12954 14.0905C1.85393 14.1009 1.64529 14.3402 1.65044 14.6368C1.65816 14.9698 1.88999 15.2169 2.18106 15.2039C2.46697 15.1909 2.66016 14.9698 2.65501 14.6576H2.65758Z" fill="#454545" />
  </svg>

);

export default Services;
