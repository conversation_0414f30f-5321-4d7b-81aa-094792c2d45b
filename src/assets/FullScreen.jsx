import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const FullScreenIcon = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path
      stroke="none"
      d="M0 0h24v24H0z"
      fill="none"
    />
    <path
      stroke="currentColor"
      d="M16 4l4 0l0 4"
    />
    <path
      stroke="currentColor"
      d="M14 10l6 -6"
    />
    <path
      stroke="currentColor"
      d="M8 20l-4 0l0 -4"
    />
    <path
      stroke="currentColor"
      d="M4 20l6 -6"
    />
    <path
      stroke="currentColor"
      d="M16 20l4 0l0 -4"
    />
    <path
      stroke="currentColor"
      d="M14 14l6 6"
    />
    <path
      stroke="currentColor"
      d="M8 4l-4 0l0 4"
    />
    <path
      stroke="currentColor"
      d="M4 4l6 6"
    />
  </Icon>
);

export default FullScreenIcon;
