import React from 'react';

const DistributionRegister = () => {
  return (

    <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21.832 28.4987H24.4987C26.7078 28.4987 28.4987 26.7078 28.4987 24.4987V21.832" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M4.5 11.1667V8.5C4.5 6.29086 6.29086 4.5 8.5 4.5H12.5" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <rect x="4.5" y="15.5" width="13.3333" height="13.3333" rx="2" fill="#E83A7A" fillOpacity="0.1" stroke="#E83A7A" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M13.8333 22.1667H8.5" stroke="#E83A7A" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M13.8333 26.1667H8.5" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M13.8333 18.1667H8.5" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <rect x="19.5" y="8.5" width="3" height="5" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <rect x="22.5" y="3.5" width="4" height="10" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <rect x="26.5" y="6.5" width="4" height="7" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

  );
};

export default DistributionRegister;
