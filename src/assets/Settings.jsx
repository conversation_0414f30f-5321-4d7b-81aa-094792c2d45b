import React from 'react';

const Settings = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <g clipPath="url(#clip0_59_9178)">
      <path fillRule="evenodd" clipRule="evenodd" d="M9.99998 6.66634C9.11592 6.66634 8.26808 7.01753 7.64296 7.64265C7.01783 8.26777 6.66665 9.11562 6.66665 9.99967C6.66665 10.8837 7.01783 11.7316 7.64296 12.3567C8.26808 12.9818 9.11592 13.333 9.99998 13.333C10.884 13.333 11.7319 12.9818 12.357 12.3567C12.9821 11.7316 13.3333 10.8837 13.3333 9.99967C13.3333 9.11562 12.9821 8.26777 12.357 7.64265C11.7319 7.01753 10.884 6.66634 9.99998 6.66634ZM8.82147 8.82116C9.13403 8.5086 9.55795 8.33301 9.99998 8.33301C10.442 8.33301 10.8659 8.5086 11.1785 8.82116C11.4911 9.13372 11.6666 9.55765 11.6666 9.99967C11.6666 10.4417 11.4911 10.8656 11.1785 11.1782C10.8659 11.4907 10.442 11.6663 9.99998 11.6663C9.55795 11.6663 9.13403 11.4907 8.82147 11.1782C8.50891 10.8656 8.33331 10.4417 8.33331 9.99967C8.33331 9.55765 8.50891 9.13372 8.82147 8.82116Z" fill="#757575" />
      <path fillRule="evenodd" clipRule="evenodd" d="M9.15081 0.833008C8.77227 0.833008 8.44129 1.08814 8.34493 1.45421L7.92263 3.05855L6.58093 3.62414L5.55389 2.71041C5.22413 2.41703 4.72282 2.43166 4.41072 2.74375L2.74406 4.41042C2.44214 4.71234 2.41727 5.19361 2.68645 5.52505L3.58656 6.6333L3.03459 7.99124L1.47719 8.35483C1.10008 8.44287 0.833313 8.77909 0.833313 9.16634V10.833C0.833313 11.2088 1.0848 11.5381 1.44733 11.637L3.05706 12.0761L3.62418 13.4214L2.71136 14.445C2.41735 14.7747 2.4317 15.2766 2.74406 15.5889L4.41072 17.2556C4.71305 17.5579 5.19512 17.5824 5.52652 17.3123L6.63438 16.4091L7.95894 16.954L8.35867 18.537C8.45211 18.9071 8.78499 19.1663 9.16665 19.1663H10.8333C11.2148 19.1663 11.5476 18.9073 11.6412 18.5374L12.0419 16.9537L13.4108 16.3868C13.5486 16.5022 13.7066 16.6379 13.8638 16.7748C14.02 16.9108 14.1652 17.039 14.2714 17.1333C14.3245 17.1804 14.3677 17.2189 14.3976 17.2456L14.4429 17.2861C14.7724 17.5817 15.2762 17.5686 15.5892 17.2556L17.2559 15.5889C17.5626 15.2822 17.5828 14.7915 17.3022 14.4607L16.3945 13.3902L16.9562 12.0335L18.5429 11.6212C18.9102 11.5258 19.1666 11.1942 19.1666 10.8147V9.16634C19.1666 8.78524 18.9081 8.4527 18.5388 8.35873L16.9619 7.95754L16.4016 6.60412L17.3031 5.53763C17.5828 5.20676 17.5623 4.71677 17.2559 4.41042L15.5892 2.74375C15.2816 2.43616 14.7892 2.4169 14.4585 2.69955L13.3999 3.6044L11.9988 3.02834L11.5851 1.45448C11.4888 1.08828 11.1578 0.833008 10.7791 0.833008H9.15081ZM15.5346 14.9532L14.9645 15.5233L14.9583 15.5179C14.6505 15.2499 14.254 14.9108 14.0305 14.7509C13.7965 14.5836 13.4927 14.5489 13.2269 14.6589L11.0178 15.5739C10.7746 15.6746 10.5933 15.8843 10.5288 16.1394L10.1846 17.4997H9.81571L9.47212 16.139C9.40748 15.883 9.22535 15.6728 8.98117 15.5723L6.8095 14.679C6.52672 14.5627 6.20294 14.6106 5.96594 14.8038L5.05693 15.5448L4.47907 14.9669L5.2186 14.1376C5.43269 13.8976 5.48949 13.5557 5.36454 13.2593L4.43537 11.0551C4.33596 10.8193 4.1337 10.6422 3.8868 10.5749L2.49998 10.1966V9.82753L3.8336 9.51619C4.09706 9.45468 4.31427 9.2691 4.41614 9.01847L5.31614 6.8043C5.43061 6.52269 5.38265 6.2011 5.19101 5.96514L4.45391 5.05759L5.03338 4.47812L5.86607 5.21894C6.10613 5.43252 6.4476 5.48905 6.74368 5.36423L8.94785 4.43507C9.18609 4.33464 9.36422 4.12932 9.43003 3.8793L9.79318 2.49967H10.1366L10.4924 3.8532C10.5588 4.10585 10.7398 4.31273 10.9814 4.41207L13.2373 5.33957C13.5265 5.45848 13.8579 5.40548 14.0956 5.2023L14.9556 4.46718L15.5355 5.04702L14.801 5.91588C14.5998 6.15399 14.5483 6.48452 14.6675 6.77258L15.5817 8.98091C15.6822 9.22378 15.8914 9.40497 16.1462 9.46978L17.5 9.81421V10.1702L16.1354 10.5248C15.8825 10.5905 15.675 10.7711 15.575 11.0126L14.66 13.2226C14.5406 13.5111 14.5924 13.8421 14.7944 14.0803L15.5346 14.9532Z" fill="#757575" />
    </g>
    <defs>
      <clipPath id="clip0_59_9178">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default Settings;
