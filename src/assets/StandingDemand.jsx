import React from 'react';

const StandingDemand = () => (

  <svg width="25" height="25" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M24.7933 24.1668H16.8733C16.1147 24.1668 15.5 23.5522 15.5 22.7935V16.2068C15.5 15.4482 16.1147 14.8335 16.8733 14.8335H24.7933C25.552 14.8335 26.1667 15.4482 26.1667 16.2068V22.7935C26.1667 23.5522 25.552 24.1668 24.7933 24.1668V24.1668Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.1258 17.3613L21.4058 18.9973C21.6004 19.2453 21.6018 19.5947 21.4098 19.844L20.1138 21.5347" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.5 23.8335H5.5C3.29086 23.8335 1.5 22.0426 1.5 19.8335V5.8335C1.5 3.62436 3.29086 1.8335 5.5 1.8335H21.5C23.7091 1.8335 25.5 3.62436 25.5 5.8335V10.5002" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.6893 14.1289L12.3912 13.3807C12.2403 13.0018 12.0064 12.6609 11.7071 12.3842C11.2 11.9139 10.5337 11.6538 9.84367 11.6538H7.22134C6.53013 11.6538 5.86504 11.9151 5.35792 12.3842C5.05863 12.6609 4.82466 13.0018 4.67383 13.3807L4.37573 14.1289" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0019 5.23768C10.8131 6.04885 10.8131 7.36476 10.0019 8.17711C9.19074 8.98828 7.87483 8.98828 7.06248 8.17711C6.25131 7.36595 6.25131 6.05003 7.06248 5.23768C7.87483 4.42533 9.19074 4.42533 10.0019 5.23768" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default StandingDemand;
