import React from 'react';

function NoDraftsIcon({ width = '228px', ...rest }) {
  return (
    <div>

      <svg width={width} height="228" {...rest} viewBox="0 0 228 228" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="228" height="228" rx="114" fill="#F4F8FC" />
        <path d="M66.8867 203.944C68.1025 208.482 72.7665 211.175 77.304 209.959L186.164 180.79C190.701 179.574 193.394 174.91 192.178 170.373L160.808 53.2974L143.88 43.524L121.743 30.7434L35.4769 53.8584C30.9395 55.0742 28.2467 59.7381 29.4625 64.2756L66.8867 203.944Z" fill="#B9EEFF" fillOpacity="0.75" />
        <g filter="url(#filter0_d_3759_6208)">
          <path d="M160.808 53.2976L121.743 30.7436L129.998 61.5529L160.808 53.2976Z" fill="white" />
        </g>
        <path d="M90.9338 38.9986L129.998 61.5526L121.743 30.7433L90.9338 38.9986Z" fill="#EEEEEE" />
        <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 78.9155 181.968)" fill="#29CBFF" fillOpacity="0.5" />
        <rect width="99.9413" height="4.25282" rx="2.12641" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 73.4121 162.571)" fill="#00B2EB" />
        <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 67.9082 147.946)" fill="#29CBFF" fillOpacity="0.5" />
        <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 62.405 113.923)" fill="#29CBFF" fillOpacity="0.5" />
        <rect width="57.4131" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 51.3977 80.4123)" fill="#29CBFF" fillOpacity="0.5" />
        <g filter="url(#filter1_d_3759_6208)">
          <path d="M69.7893 24.6144C69.7893 19.9169 73.5974 16.1087 78.2949 16.1087H190.995C195.692 16.1087 199.5 19.9169 199.5 24.6144V145.82L185.679 159.641L167.604 177.716H78.2949C73.5974 177.716 69.7893 173.908 69.7893 169.21V24.6144Z" fill="white" />
          <g filter="url(#filter2_d_3759_6208)">
            <path d="M199.5 145.82L167.604 177.716L167.604 145.82L199.5 145.82Z" fill="white" />
          </g>
          <path d="M135.708 177.716L167.604 145.82L167.604 177.716L135.708 177.716Z" fill="#EEEEEE" />
          <rect x="85.5002" y="47.0871" width="48.3261" height="7.43478" rx="3.71739" fill="#76DDFE" />
          <rect x="143.739" y="45.8479" width="42.1304" height="30.9783" rx="3.71739" fill="#76DDFE" />
          <rect x="85.5002" y="69.3914" width="48.3261" height="7.43478" rx="3.71739" fill="#76DDFE" />
          <rect x="85.5002" y="92.9349" width="99.9413" height="7.43478" rx="3.71739" fill="#76DDFE" />
          <rect x="85.5002" y="116.478" width="100.37" height="7.43478" rx="3.71739" fill="#76DDFE" />
          <rect x="85.5002" y="140.022" width="63.1957" height="7.43478" rx="3.71739" fill="#76DDFE" />
        </g>
        <defs>
          <filter id="filter0_d_3759_6208" x="108.347" y="17.3472" width="65.8575" height="57.6021" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset />
            <feGaussianBlur stdDeviation="6.69819" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3759_6208" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3759_6208" result="shape" />
          </filter>
          <filter id="filter1_d_3759_6208" x="62.6023" y="8.92179" width="144.085" height="175.982" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset />
            <feGaussianBlur stdDeviation="3.59348" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3759_6208" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3759_6208" result="shape" />
          </filter>
          <filter id="filter2_d_3759_6208" x="154.208" y="132.424" width="58.689" height="58.6889" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset />
            <feGaussianBlur stdDeviation="6.69819" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3759_6208" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3759_6208" result="shape" />
          </filter>
        </defs>
      </svg>

    </div>
  );
}

export default NoDraftsIcon;
