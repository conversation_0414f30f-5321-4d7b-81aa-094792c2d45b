import React from 'react';

const Share = () => {
  return (
    <svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="46" height="46" rx="23" fill="#00B2EC" />
      <g clipPath="url(#clip0_2137_78291)">
        <path d="M16.5 26C18.1569 26 19.5 24.6569 19.5 23C19.5 21.3431 18.1569 20 16.5 20C14.8431 20 13.5 21.3431 13.5 23C13.5 24.6569 14.8431 26 16.5 26Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M28.5 20C30.1569 20 31.5 18.6569 31.5 17C31.5 15.3431 30.1569 14 28.5 14C26.8431 14 25.5 15.3431 25.5 17C25.5 18.6569 26.8431 20 28.5 20Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M28.5 32C30.1569 32 31.5 30.6569 31.5 29C31.5 27.3431 30.1569 26 28.5 26C26.8431 26 25.5 27.3431 25.5 29C25.5 30.6569 26.8431 32 28.5 32Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M19.1992 21.7008L25.7992 18.3008" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M19.1992 24.3008L25.7992 27.7008" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_2137_78291">
          <rect width="24" height="24" fill="white" transform="translate(11 11)" />
        </clipPath>
      </defs>
    </svg>

  );
};

export default Share;
