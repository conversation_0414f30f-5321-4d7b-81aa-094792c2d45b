import React from 'react';

const Personal = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path d="M6.66667 7.50016C6.20643 7.50016 5.83333 7.87326 5.83333 8.3335C5.83333 8.79373 6.20643 9.16683 6.66667 9.16683H13.3333C13.7936 9.16683 14.1667 8.79373 14.1667 8.3335C14.1667 7.87326 13.7936 7.50016 13.3333 7.50016H6.66667Z" fill="#454545" />
    <path d="M6.66667 10.8335C6.20643 10.8335 5.83333 11.2066 5.83333 11.6668C5.83333 12.1271 6.20643 12.5002 6.66667 12.5002H10C10.4602 12.5002 10.8333 12.1271 10.8333 11.6668C10.8333 11.2066 10.4602 10.8335 10 10.8335H6.66667Z" fill="#454545" />
    <path d="M6.66667 14.1668C6.20643 14.1668 5.83333 14.5399 5.83333 15.0002C5.83333 15.4604 6.20643 15.8335 6.66667 15.8335H13.3333C13.7936 15.8335 14.1667 15.4604 14.1667 15.0002C14.1667 14.5399 13.7936 14.1668 13.3333 14.1668H6.66667Z" fill="#454545" />
    <path fillRule="evenodd" clipRule="evenodd" d="M17.399 4.28148C17.3319 4.11918 17.2335 3.97172 17.1093 3.84757L14.4863 1.22461C14.2365 0.974488 13.8976 0.833808 13.5441 0.833496H3.83333C3.47971 0.833496 3.14057 0.973972 2.89052 1.22402C2.64048 1.47407 2.5 1.81321 2.5 2.16683V17.8335C2.5 18.0086 2.53449 18.182 2.60149 18.3437C2.6685 18.5055 2.76671 18.6525 2.89052 18.7763C3.01433 18.9001 3.16132 18.9983 3.32309 19.0653C3.48486 19.1323 3.65824 19.1668 3.83333 19.1668H16.1667C16.3418 19.1668 16.5151 19.1323 16.6769 19.0653C16.8387 18.9983 16.9857 18.9001 17.1095 18.7763C17.2333 18.6525 17.3315 18.5055 17.3985 18.3437C17.4655 18.182 17.5 18.0086 17.5 17.8335V4.79209L16.6667 4.79183L17.5 4.79387L17.5 4.79209C17.5002 4.6169 17.4659 4.4434 17.399 4.28148ZM12.5 4.5835C12.5 5.27385 13.0596 5.8335 13.75 5.8335H15.8333V17.5002H4.16667V2.50016H12.5V4.5835ZM15.0715 4.16683L14.1667 3.26201V4.16683H15.0715Z" fill="#454545" />
  </svg>
);

export default Personal;
