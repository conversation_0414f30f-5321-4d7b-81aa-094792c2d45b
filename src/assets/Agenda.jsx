import React from 'react';

const Agenda = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.53791 10.0103H6.33145V10.0103C4.56057 10.0103 3.12499 11.4459 3.12499 13.2168V13.9616" stroke="#09327B" strokeWidth="2" strokeLinecap="round" />
    <path d="M20.2203 10.0103H23.2903H25.0283C25.7639 10.0103 26.3603 10.6067 26.3603 11.3424V11.3424L26.0139 15.1357" stroke="#09327B" strokeWidth="2" strokeLinecap="round" />
    <path d="M3.12499 13.7151V17.9697C3.12499 18.8486 3.83744 19.561 4.71629 19.561V19.561C5.59513 19.561 6.30758 18.8486 6.30758 17.9697V14.3267" stroke="#09327B" strokeWidth="2" strokeLinecap="round" />
    <path d="M6.17465 19.2373L6.50659 25.4166C6.55365 26.2928 7.27784 26.9791 8.1553 26.9791V26.9791C9.33662 26.9791 10.3535 26.1448 10.5842 24.9862L10.7623 24.0921" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M24.8887 22.4332L24.4144 24.9803V24.9803C24.1972 26.1394 23.1852 26.9793 22.006 26.9793H21.9679C21.105 26.9793 20.3852 26.3197 20.31 25.4601L20.223 24.4655" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="8.46847" cy="4.86765" r="1.83603" stroke="#09327B" strokeWidth="2" />
    <circle cx="22.3209" cy="4.86765" r="1.83603" stroke="#09327B" strokeWidth="2" />
    <path d="M28.875 16.743L25.9805 19.6375L24.2436 17.9006" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14 20L14.04 21.2732L14.2121 26.7519C14.234 27.4475 14.8041 28 15.5 28V28C16.1959 28 16.766 27.4475 16.7879 26.7519L16.96 21.2732L17 20" stroke="#09327B" strokeWidth="2" strokeLinecap="round" />
    <circle cx="15.5" cy="10.5" r="1.5" stroke="#09327B" strokeWidth="2" />
    <path d="M18.1169 21V21C19.1569 21 20 20.1569 20 19.1169V17.5C20 15.567 18.433 14 16.5 14H14.5C12.567 14 11 15.567 11 17.5V18.9785C11 20.0949 11.9051 21 13.0215 21V21" stroke="#09327B" strokeWidth="2" strokeLinecap="round" />
  </svg>

);

export default Agenda;
