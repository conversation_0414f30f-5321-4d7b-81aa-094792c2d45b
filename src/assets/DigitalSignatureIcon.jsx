import React from 'react';

const DigitalSignatureIcon = ({ color = '#323232', h = '20', w = '20' }) => (

  <svg width={w} height={h} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M7.51106 14.7839C7.32306 14.9719 7.06906 15.0769 6.80406 15.0769H4.78906V13.0619C4.78906 12.7969 4.89506 12.5429 5.08206 12.3549L12.0061 5.42588C12.6781 4.75388 13.7681 4.75388 14.4401 5.42588V5.42588C15.1131 6.09787 15.1121 7.18787 14.4401 7.85987L7.51106 14.7839V14.7839Z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.7998 7.64062L12.2298 10.0706" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.9538 8.34766L14.8558 9.24966V9.24966C15.3298 9.72466 15.3298 10.4937 14.8558 10.9677L13.2148 12.6087" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M16 19H4C2.343 19 1 17.657 1 16V4C1 2.343 2.343 1 4 1H16C17.657 1 19 2.343 19 4V16C19 17.657 17.657 19 16 19Z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default DigitalSignatureIcon;
