import React from 'react';

const Statistics = () => (
  <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_3230_16224)">
      <path d="M13.371 20.9999H3.50435C3.14709 20.8657 3.01104 20.5984 3.0255 20.2181C3.03996 19.8358 3.02872 19.4528 3.02872 19.0699C3.02872 18.8535 3.02872 18.6371 3.02872 18.402C2.93123 18.402 2.85892 18.402 2.78715 18.402C2.24564 18.402 1.70413 18.4053 1.16316 18.4009C0.748052 18.3977 0.501132 18.1451 0.501132 17.7222C0.50006 12.0421 0.50006 6.36205 0.500596 0.68199C0.500596 0.248644 0.749123 -7.73974e-05 1.17815 -7.73974e-05C5.52256 -0.000625243 9.86696 -0.000625243 14.2114 -7.73974e-05C14.6479 -7.73974e-05 14.8873 0.249192 14.8889 0.702808C14.8905 1.24298 14.8889 1.78261 14.8889 2.32279C14.8889 2.3951 14.8889 2.46796 14.8889 2.55617C14.9891 2.55617 15.0619 2.55617 15.1342 2.55617C15.6891 2.55617 16.2435 2.55507 16.7984 2.55617C17.2547 2.55781 17.4974 2.80051 17.4974 3.2596C17.4979 7.74317 17.4974 12.2273 17.4995 16.7108C17.4995 16.959 17.4224 17.1579 17.2451 17.3299C16.0908 18.4492 14.9414 19.5739 13.7861 20.6915C13.6629 20.8104 13.5097 20.8969 13.3704 20.9988L13.371 20.9999ZM16.2837 3.80032H4.24457V19.7607H12.6356C12.6356 19.6791 12.6356 19.6057 12.6356 19.5322C12.6356 18.6639 12.6345 17.7961 12.6356 16.9278C12.6361 16.4336 12.8637 16.203 13.349 16.2024C14.258 16.2019 15.1669 16.2024 16.0759 16.2024H16.2831V3.80032H16.2837ZM3.02872 17.1595V16.8752C3.02872 12.4053 3.03568 7.93601 3.02175 3.46614C3.01961 2.76161 3.29063 2.54685 3.90874 2.54959C7.07639 2.56439 10.244 2.55672 13.4117 2.55672H13.6752V1.24791H1.71484V17.1595H3.02872ZM13.852 18.9335C14.372 18.4267 14.8632 17.9484 15.3747 17.4504H13.852V18.9335Z" fill="#454545" />
      <path d="M8.28419 8.73962C8.28419 8.452 8.28205 8.18739 8.28419 7.92278C8.28794 7.4867 8.52843 7.23414 8.95585 7.23031C9.53646 7.22538 10.1165 7.22921 10.7261 7.22921C10.7261 7.15087 10.7261 7.08513 10.7261 7.01993C10.7261 6.62384 10.7213 6.2272 10.7277 5.83111C10.7336 5.4657 10.9783 5.20766 11.3329 5.20492C12.1412 5.19835 12.9494 5.1989 13.7577 5.20492C14.1246 5.20766 14.3704 5.4646 14.371 5.84261C14.3736 7.79623 14.3736 9.75039 14.371 11.704C14.3704 12.0886 14.1149 12.3455 13.7379 12.345C11.3265 12.3444 8.91461 12.3422 6.50326 12.3373C6.09833 12.3362 5.84927 12.0859 5.84605 11.6739C5.84016 10.9157 5.83963 10.1574 5.84605 9.39922C5.8498 8.98889 6.10208 8.74291 6.50701 8.74072C7.08762 8.73743 7.66876 8.74017 8.28365 8.74017L8.28419 8.73962ZM13.1572 11.1096V6.45017H11.9457V11.1096H13.1572ZM10.7148 8.47885H9.50058V11.097H10.7148V8.47885ZM8.27615 9.98487H7.06351V11.0932H8.27615V9.98487Z" fill="#454545" />
      <path d="M11.3923 13.9027C12.2466 13.9027 13.1015 13.9022 13.9558 13.9027C14.3618 13.9027 14.6392 14.1493 14.644 14.5103C14.6494 14.8763 14.3644 15.1327 13.9488 15.1327C12.2332 15.1327 10.5176 15.1338 8.80151 15.1327C8.42282 15.1327 8.17644 14.9223 8.14751 14.5854C8.11323 14.1893 8.36551 13.906 8.76883 13.9044C9.6435 13.9006 10.5176 13.9033 11.3923 13.9033V13.9027Z" fill="#454545" />
      <path d="M9.74216 17.8927C9.38865 17.8927 9.03407 17.9119 8.68217 17.8878C8.23975 17.8576 8.00086 17.3723 8.21725 16.9778C8.33295 16.7669 8.52041 16.6737 8.74805 16.6727C9.4149 16.6688 10.0823 16.6639 10.7491 16.6737C11.1192 16.6792 11.364 16.9395 11.3619 17.2884C11.3597 17.638 11.1155 17.8785 10.7427 17.8987C10.7293 17.8993 10.7159 17.8998 10.7025 17.8998C10.3822 17.8998 10.0619 17.8998 9.74216 17.8998C9.74216 17.8971 9.74216 17.8949 9.74216 17.8922V17.8927Z" fill="#454545" />
      <path d="M7.08166 14.5147C7.08166 14.8609 6.82884 15.125 6.49301 15.1299C6.1545 15.1348 5.88133 14.8576 5.88294 14.5108C5.88455 14.1827 6.16307 13.9033 6.48712 13.9038C6.81652 13.9044 7.08166 14.1772 7.08166 14.5147Z" fill="#454545" />
      <path d="M7.08221 17.2827C7.08168 17.6339 6.83154 17.8903 6.48821 17.8914C6.14649 17.8925 5.88939 17.6366 5.8835 17.2888C5.87814 16.9606 6.15666 16.6735 6.48286 16.6719C6.81333 16.6702 7.08328 16.9453 7.08275 17.2827H7.08221Z" fill="#454545" />
    </g>
    <defs>
      <clipPath id="clip0_3230_16224">
        <rect width="17" height="21" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export default Statistics;
