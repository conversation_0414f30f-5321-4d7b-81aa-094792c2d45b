import React from 'react';

const MinusCircle = ({ width, height }) => (
  <svg width={width} height={height} viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="vuesax/outline/minus-circle">
      <g id="minus-circle">
        <path id="Vector" d="M15.4999 29.3856C7.84034 29.3856 1.6145 23.1597 1.6145 15.5002C1.6145 7.84058 7.84034 1.61475 15.4999 1.61475C23.1595 1.61475 29.3853 7.84058 29.3853 15.5002C29.3853 23.1597 23.1595 29.3856 15.4999 29.3856ZM15.4999 3.55225C8.91242 3.55225 3.552 8.91266 3.552 15.5002C3.552 22.0877 8.91242 27.4481 15.4999 27.4481C22.0874 27.4481 27.4478 22.0877 27.4478 15.5002C27.4478 8.91266 22.0874 3.55225 15.4999 3.55225Z" fill="#E82C78" />
        <path id="Vector_2" d="M20.6666 16.4688H10.3333C9.80367 16.4688 9.3645 16.0296 9.3645 15.5C9.3645 14.9704 9.80367 14.5312 10.3333 14.5312H20.6666C21.1962 14.5312 21.6353 14.9704 21.6353 15.5C21.6353 16.0296 21.1962 16.4688 20.6666 16.4688Z" fill="#E82C78" />

      </g>
    </g>
  </svg>
);

export default MinusCircle;
