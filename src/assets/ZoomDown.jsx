import React from 'react';

const ZoomDown = ({ stroke = '#848484', w = '24', h = '24' }) => (

  <svg width={w} height={h} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M18.934 11.467V11.467C18.934 15.591 15.591 18.934 11.467 18.934V18.934C7.343 18.934 4 15.591 4 11.467V11.467C4 7.343 7.343 4 11.467 4V4C15.591 4 18.934 7.343 18.934 11.467Z" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20 20L16.75 16.75" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.788 11.4688H8.146" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default ZoomDown;
