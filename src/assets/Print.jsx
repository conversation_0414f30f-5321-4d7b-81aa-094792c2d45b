import React from 'react';

const Print = () => {
  return (
    <svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="46" height="46" rx="23" fill="#00B2EC" />
      <g clipPath="url(#clip0_2137_78284)">
        <path d="M27.5 28H29.5C30.0304 28 30.5391 27.7893 30.9142 27.4142C31.2893 27.0391 31.5 26.5304 31.5 26V22C31.5 21.4696 31.2893 20.9609 30.9142 20.5858C30.5391 20.2107 30.0304 20 29.5 20H15.5C14.9696 20 14.4609 20.2107 14.0858 20.5858C13.7107 20.9609 13.5 21.4696 13.5 22V26C13.5 26.5304 13.7107 27.0391 14.0858 27.4142C14.4609 27.7893 14.9696 28 15.5 28H17.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M27.5 20V16C27.5 15.4696 27.2893 14.9609 26.9142 14.5858C26.5391 14.2107 26.0304 14 25.5 14H19.5C18.9696 14 18.4609 14.2107 18.0858 14.5858C17.7107 14.9609 17.5 15.4696 17.5 16V20" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M25.5 24H19.5C18.3954 24 17.5 24.8954 17.5 26V30C17.5 31.1046 18.3954 32 19.5 32H25.5C26.6046 32 27.5 31.1046 27.5 30V26C27.5 24.8954 26.6046 24 25.5 24Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_2137_78284">
          <rect width="24" height="24" fill="white" transform="translate(11 11)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Print;
