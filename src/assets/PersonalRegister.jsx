import React from 'react';

const PersonalRegister = () => {
  return (

    <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.8195 28.6247H8.33006C6.09794 28.6247 4.28844 26.8152 4.28844 24.5831V11.7972C4.28845 10.7253 4.71426 9.69728 5.47221 8.93934L8.85278 5.55876C9.61072 4.80082 10.6387 4.375 11.7106 4.375H21.8021C24.0343 4.375 25.8438 6.18449 25.8438 8.41662V14.3591" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M4.29349 11.7846H9.00871C10.4968 11.7846 11.7031 10.5783 11.7031 9.09022V4.375" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M27.3549 28.6259H19.8797C19.5621 28.6259 19.3047 28.3684 19.3047 28.0509V27.584C19.3085 26.2555 20.3844 25.1796 21.7129 25.1758H25.5218C26.8502 25.1796 27.9262 26.2555 27.9299 27.584V28.0509C27.9299 28.3684 27.6725 28.6259 27.3549 28.6259Z" fill="#FFE1EC" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M23.6197 22.2994C22.3503 22.2968 21.323 21.2664 21.3242 19.997C21.3255 18.7276 22.3549 17.6992 23.6243 17.6992C24.8937 17.6992 25.9231 18.7276 25.9244 19.997C25.9256 21.2664 24.8983 22.2968 23.6289 22.2994H23.6197Z" fill="#FFE1EC" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10.3086 15.7149H17.3966" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10.3086 20.3203H14.9026" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

  );
};

export default PersonalRegister;
