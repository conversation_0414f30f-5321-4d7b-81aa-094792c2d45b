import React from 'react';

const Merge = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" d="M21.3334 24H24C25.4728 24 26.6667 22.8061 26.6667 21.3333V6.66667C26.6667 5.19391 25.4728 4 24 4H12C10.5273 4 9.33337 5.19391 9.33337 6.66667V9.33333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" fillRule="evenodd" clipRule="evenodd" d="M20.4791 12.6273L18.0395 10.1876C17.4925 9.64066 16.7507 9.33337 15.9771 9.33337H8.00004C6.52728 9.33337 5.33337 10.5273 5.33337 12V25.3334C5.33337 26.8061 6.52728 28 8.00004 28H18.6667C20.1395 28 21.3334 26.8061 21.3334 25.3334V14.6896C21.3334 13.9161 21.0261 13.1742 20.4791 12.6273Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M21.3334 15.3334H17.3334C16.2288 15.3334 15.3334 14.4379 15.3334 13.3334V9.33337" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default Merge;
