import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const ToggleLeft = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
    <path d="M8 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" stroke="currentColor" />
    <path d="M2 6m0 6a6 6 0 0 1 6 -6h8a6 6 0 0 1 6 6v0a6 6 0 0 1 -6 6h-8a6 6 0 0 1 -6 -6z" stroke="currentColor" />
  </Icon>
);

const ToggleRight = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
    <path d="M8 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" stroke="currentColor" />
    <path d="M2 6m0 6a6 6 0 0 1 6 -6h8a6 6 0 0 1 6 6v0a6 6 0 0 1 -6 6h-8a6 6 0 0 1 -6 -6z" stroke="currentColor" />
  </Icon>
);

export { ToggleLeft, ToggleRight };
