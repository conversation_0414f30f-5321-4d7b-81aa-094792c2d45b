const ToolbarIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.57957 17.0024H4.99708C3.89162 17.0024 2.99625 16.107 2.99625 15.0016V4.99742C2.99625 3.89195 3.89162 2.99658 4.99708 2.99658H15.0012C16.1067 2.99658 17.0021 3.89195 17.0021 4.99742V7.99867"
        stroke="#323232"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.49811 7.49826H12.5002"
        stroke="#323232"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.99914 12.5001V7.49805"
        stroke="#323232"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.6235 20.682L21.681 14.6245C22.1112 14.1943 22.1112 13.498 21.681 13.0688L19.9353 11.3231C19.5051 10.8929 18.8088 10.8929 18.3797 11.3231L12.3221 17.3806C12.115 17.5877 12 17.8668 12 18.158V21.0042H14.8462C15.1373 21.0042 15.4164 20.8891 15.6235 20.682Z"
        stroke="#323232"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ToolbarIcon;
