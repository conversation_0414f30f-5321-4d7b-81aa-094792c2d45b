import React from 'react';

const Notification = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
    <g clipPath="url(#clip0_3230_16239)">
      <path d="M8.83337 4.16667C8.83337 3.72464 9.00897 3.30072 9.32153 2.98816C9.63409 2.67559 10.058 2.5 10.5 2.5C10.9421 2.5 11.366 2.67559 11.6786 2.98816C11.9911 3.30072 12.1667 3.72464 12.1667 4.16667C13.1237 4.61919 13.9395 5.32361 14.5267 6.20442C15.114 7.08523 15.4504 8.10923 15.5 9.16667V11.6667C15.5628 12.1848 15.7462 12.6809 16.0357 13.1151C16.3252 13.5493 16.7126 13.9095 17.1667 14.1667H3.83337C4.28749 13.9095 4.67488 13.5493 4.96436 13.1151C5.25385 12.6809 5.43733 12.1848 5.50004 11.6667V9.16667C5.54967 8.10923 5.88612 7.08523 6.47333 6.20442C7.06054 5.32361 7.87637 4.61919 8.83337 4.16667" stroke="#454545" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8 14.1665V14.9998C8 15.6629 8.26339 16.2988 8.73223 16.7676C9.20107 17.2364 9.83696 17.4998 10.5 17.4998C11.163 17.4998 11.7989 17.2364 12.2678 16.7676C12.7366 16.2988 13 15.6629 13 14.9998V14.1665" stroke="#454545" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M18 5.60583C17.4534 4.41688 16.6596 3.35808 15.6716 2.5" stroke="#454545" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M3 5.60583C3.54612 4.41703 4.33929 3.35825 5.32667 2.5" stroke="#454545" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_3230_16239">
        <rect width="20" height="20" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export default Notification;
