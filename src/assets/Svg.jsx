import React from 'react';

export const Add = ({ fill = 'white' }) => (
  <svg
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.21721 0.5V5.39552H12V7.5806H7.21721V12.5H4.7582V7.5806H0V5.39552H4.7582V0.5H7.21721Z"
      fill={fill}
    />
  </svg>
);

export const Edit = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1624_53961)">
      <path
        d="M4 20H8L18.5 9.5C19.0304 8.96957 19.3284 8.25015 19.3284 7.5C19.3284 6.74986 19.0304 6.03043 18.5 5.5C17.9696 4.96957 17.2501 4.67157 16.5 4.67157C15.7499 4.67157 15.0304 4.96957 14.5 5.5L4 16V20Z"
        stroke="#ADADAD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 6.5L17.5 10.5"
        stroke="#ADADAD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1624_53961">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Trash = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 7H20"
      stroke="#E42E78"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 11V17"
      stroke="#E42E78"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 11V17"
      stroke="#E42E78"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 7L6 19C6 19.5304 6.21071 20.0391 6.58579 20.4142C6.96086 20.7893 7.46957 21 8 21H16C16.5304 21 17.0391 20.7893 17.4142 20.4142C17.7893 20.0391 18 19.5304 18 19L19 7"
      stroke="#E42E78"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 7V4C9 3.73478 9.10536 3.48043 9.29289 3.29289C9.48043 3.10536 9.73478 3 10 3H14C14.2652 3 14.5196 3.10536 14.7071 3.29289C14.8946 3.48043 15 3.73478 15 4V7"
      stroke="#E42E78"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Upload = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.293 10.707L12.475 7.525C13.06 6.94 14.011 6.94 14.596 7.525V7.525C15.181 8.11 15.181 9.061 14.596 9.646L10.707 13.535C9.535 14.707 7.636 14.707 6.464 13.535V13.535C5.292 12.363 5.292 10.464 6.464 9.292L10 5.757"
      stroke="#153171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 19H4C2.343 19 1 17.657 1 16V4C1 2.343 2.343 1 4 1H16C17.657 1 19 2.343 19 4V16C19 17.657 17.657 19 16 19Z"
      stroke="#153171"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Info = ({ color = 'E42E78' }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19Z"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 6H10.01"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 10H10V14H11"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InfoSolid = ({ color = 'E42E78', width = '24', height = '24' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 24 24" color={color} fill="currentColor"><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M12 2c5.523 0 10 4.477 10 10a10 10 0 0 1 -19.995 .324l-.005 -.324l.004 -.28c.148 -5.393 4.566 -9.72 9.996 -9.72zm0 9h-1l-.117 .007a1 1 0 0 0 0 1.986l.117 .007v3l.007 .117a1 1 0 0 0 .876 .876l.117 .007h1l.117 -.007a1 1 0 0 0 .876 -.876l.007 -.117l-.007 -.117a1 1 0 0 0 -.764 -.857l-.112 -.02l-.117 -.006v-3l-.007 -.117a1 1 0 0 0 -.876 -.876l-.117 -.007zm.01 -3l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007z" /></svg>
);

export const DownArrow = () => (
  <svg
    width="14"
    height="8"
    viewBox="0 0 14 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 1L7 7L13 1"
      stroke="#9AA6AD"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Clear = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 1L1 13"
      stroke="#153171"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1 1L13 13"
      stroke="#153171"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CheckMark = () => (
  <svg
    width="14"
    height="11"
    viewBox="0 0 14 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 5.08621L5.26667 9.5L13 1.5"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Calendar = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1216_11113)">
      <path
        d="M18 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H18C19.1046 21 20 20.1046 20 19V7C20 5.89543 19.1046 5 18 5Z"
        stroke="#9AA6AD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 3V7"
        stroke="#9AA6AD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 3V7"
        stroke="#9AA6AD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4 11H20"
        stroke="#9AA6AD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 15H8V17H10V15Z"
        stroke="#9AA6AD"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1216_11113">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const AddMore = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1_9)">
      <path
        d="M9 15.75C12.7279 15.75 15.75 12.7279 15.75 9C15.75 5.27208 12.7279 2.25 9 2.25C5.27208 2.25 2.25 5.27208 2.25 9C2.25 12.7279 5.27208 15.75 9 15.75Z"
        stroke="#09327B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.75 9H11.25"
        stroke="#09327B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 6.75V11.25"
        stroke="#09327B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1_9">
        <rect width="18" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const Delete = ({
  stroke = '#E42E78', w = '24', h = '24', strokeWidth = '1.2'
}) => (
  <svg
    width={w}
    height={h}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 7H20"
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 11V17"
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 11V17"
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 7L6 19C6 19.5304 6.21071 20.0391 6.58579 20.4142C6.96086 20.7893 7.46957 21 8 21H16C16.5304 21 17.0391 20.7893 17.4142 20.4142C17.7893 20.0391 18 19.5304 18 19L19 7"
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 7V4C9 3.73478 9.10536 3.48043 9.29289 3.29289C9.48043 3.10536 9.73478 3 10 3H14C14.2652 3 14.5196 3.10536 14.7071 3.29289C14.8946 3.48043 15 3.73478 15 4V7"
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Action = () => {
  return (
    <div title="Tooltip with a comment">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="5"
        height="16"
        viewBox="0 0 5 16"
        fill="none"
      >
        <ellipse opacity="0.5" cx="2.28366" cy="2" rx="1.80929" ry="2" fill="#09327B" />
        <ellipse opacity="0.5" cx="2.28366" cy="8" rx="1.80929" ry="2" fill="#09327B" />
        <ellipse opacity="0.5" cx="2.28366" cy="14" rx="1.80929" ry="2" fill="#09327B" />
      </svg>
    </div>
  );
};
export const View = () => (
  <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 576 512">
    <path d="M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z" />
  </svg>
);

export const MinusIcon = () => (
  <svg width="41" height="42" viewBox="0 0 41 42" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="20.5" cy="21" r="20.5" fill="#E82C78" />
    <path d="M22.2172 19.3955H27V21.5806H22.2172H19.7582H15V19.3955H19.7582H22.2172Z" fill="white" />
  </svg>
);

export const AddIconWithCircle = (props) => (

  <svg {...props} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19.9998 13.332V26.6654" stroke={props?.stroke || '#00B2EB'} strokeWidth={props?.strokeWidth || '2.5'} strokeLinecap="round" strokeLinejoin="round" />
    <path d="M26.6668 20.0013H13.3335" stroke={props?.stroke || '#00B2EB'} strokeWidth={props?.strokeWidth || '2.5'} strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M20 35V35C11.715 35 5 28.285 5 20V20C5 11.715 11.715 5 20 5V5C28.285 5 35 11.715 35 20V20C35 28.285 28.285 35 20 35Z" stroke={props?.stroke || '#00B2EB'} strokeWidth={props?.strokeWidth || '2.5'} strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export const RoundedCheck = ({ color }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="22" viewBox="0 0 21 22" fill="none">
    <rect x="0.5" y="1" width="20" height="20" rx="10" stroke={color} />
    <path d="M15.7775 8.7775L10.1075 14.4475C9.9675 14.5875 9.7775 14.6675 9.5775 14.6675C9.3775 14.6675 9.1875 14.5875 9.0475 14.4475L6.2175 11.6175C5.9275 11.3275 5.9275 10.8475 6.2175 10.5575C6.5075 10.2675 6.9875 10.2675 7.2775 10.5575L9.5775 12.8575L14.7175 7.7175C15.0075 7.4275 15.4875 7.4275 15.7775 7.7175C16.0675 8.0075 16.0675 8.4775 15.7775 8.7775Z" fill={color} />
  </svg>

);

export const SettingsFrame = ({ height = '40', width = '40' }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" rx="4" fill="#EEF0F2" />
      <path fillRule="evenodd" clipRule="evenodd" d="M22 22H24C24.552 22 25 22.448 25 23V27C25 27.552 24.552 28 24 28H22C21.448 28 21 27.552 21 27V23C21 22.448 21.448 22 22 22Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M29 25H25" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M21 25H11" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M18 18H16C15.448 18 15 17.552 15 17V13C15 12.448 15.448 12 16 12H18C18.552 12 19 12.448 19 13V17C19 17.552 18.552 18 18 18Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M11 15H15" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M19 15H29" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};
