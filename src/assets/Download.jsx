import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const DownloadIcon = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
    <path stroke="currentColor" d="M14 3v4a1 1 0 0 0 1 1h4" />
    <path stroke="currentColor" d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z" />
    <path stroke="currentColor" d="M12 17v-6" />
    <path stroke="currentColor" d="M9.5 14.5l2.5 2.5l2.5 -2.5" />
  </Icon>
);

export default DownloadIcon;
