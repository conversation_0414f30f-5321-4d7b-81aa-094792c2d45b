import React from 'react';

const NewInward = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <circle id="Oval" cx="8.66669" cy="22" r="6" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path" d="M17.3334 28H23.3334C25.1743 28 26.6667 26.5076 26.6667 24.6667V11.1622C26.6667 10.1014 26.2453 9.08395 25.4951 8.3338L22.3329 5.17157C21.5827 4.42143 20.5653 4 19.5045 4H10C8.15907 4 6.66669 5.49238 6.66669 7.33333V12" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M26.6352 10.6667H22C20.8954 10.6667 20 9.77124 20 8.66667V4.03149" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M10.3333 22H7" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M8.66667 20.3334V23.6667" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default NewInward;
