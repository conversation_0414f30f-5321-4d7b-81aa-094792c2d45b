import React from 'react';

const NoDocumentSelected = ({ width = '228px', height = '228', ...rest }) => (

  <svg width={width} height={height} {...rest} viewBox="0 0 228 228" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width={width} height={height} rx="114" fill="#F4F8FC" />
    <path d="M66.8865 203.944C68.1023 208.482 72.7662 211.175 77.3037 209.959L186.163 180.79C190.701 179.574 193.393 174.91 192.178 170.373L160.807 53.2973L143.879 43.5239L121.743 30.7433L35.4767 53.8582C30.9392 55.074 28.2465 59.738 29.4623 64.2754L66.8865 203.944Z" fill="#B9EEFF" fillOpacity="0.75" />
    <g filter="url(#filter0_d_12002_53643)">
      <path d="M160.808 53.2969L121.743 30.7429L129.998 61.5522L160.808 53.2969Z" fill="white" />
    </g>
    <path d="M90.9336 39L129.998 61.554L121.743 30.7447L90.9336 39Z" fill="#EEEEEE" />
    <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 78.915 181.969)" fill="#29CBFF" fillOpacity="0.5" />
    <rect width="99.9413" height="4.25282" rx="2.12641" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 73.4121 162.57)" fill="#00B2EB" />
    <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 67.9082 147.949)" fill="#29CBFF" fillOpacity="0.5" />
    <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 62.4043 113.922)" fill="#29CBFF" fillOpacity="0.5" />
    <rect width="57.4131" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 51.3975 80.4141)" fill="#29CBFF" fillOpacity="0.5" />
    <g filter="url(#filter1_d_12002_53643)">
      <path d="M69.7891 24.615C69.7891 19.9175 73.5972 16.1094 78.2947 16.1094H190.994C195.692 16.1094 199.5 19.9175 199.5 24.615V145.82L185.678 159.642L167.604 177.717H78.2947C73.5972 177.717 69.7891 173.908 69.7891 169.211V24.615Z" fill="white" />
      <g filter="url(#filter2_d_12002_53643)">
        <path d="M199.5 145.82L167.604 177.716L167.604 145.82L199.5 145.82Z" fill="white" />
      </g>
      <path d="M135.708 177.715L167.604 145.819L167.604 177.715L135.708 177.715Z" fill="#EEEEEE" />
      <rect x="85.5" y="47.0859" width="48.3261" height="7.43478" rx="3.71739" fill="#76DDFE" />
      <rect x="143.739" y="45.8477" width="42.1304" height="30.9783" rx="3.71739" fill="#76DDFE" />
      <rect x="85.5" y="69.3906" width="48.3261" height="7.43478" rx="3.71739" fill="#76DDFE" />
      <rect x="85.5" y="92.9336" width="99.9413" height="7.43478" rx="3.71739" fill="#76DDFE" />
      <rect x="85.5" y="116.477" width="100.37" height="7.43478" rx="3.71739" fill="#76DDFE" />
      <rect x="85.5" y="140.023" width="63.1957" height="7.43478" rx="3.71739" fill="#76DDFE" />
    </g>
    <defs>
      <filter id="filter0_d_12002_53643" x="108.347" y="17.3458" width="65.8572" height="57.6014" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset />
        <feGaussianBlur stdDeviation="6.69819" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12002_53643" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12002_53643" result="shape" />
      </filter>
      <filter id="filter1_d_12002_53643" x="62.6021" y="8.92242" width="144.085" height="175.979" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset />
        <feGaussianBlur stdDeviation="3.59348" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12002_53643" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12002_53643" result="shape" />
      </filter>
      <filter id="filter2_d_12002_53643" x="154.207" y="132.424" width="58.6893" height="58.6873" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset />
        <feGaussianBlur stdDeviation="6.69819" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12002_53643" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12002_53643" result="shape" />
      </filter>
    </defs>
  </svg>

);

export default NoDocumentSelected;
