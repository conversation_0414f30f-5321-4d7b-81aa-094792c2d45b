import React from 'react';

const AttachmentIcon = ({
  width = '24px', height = '24px', ...rest
}) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} {...rest} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="icon icon-tabler icons-tabler-outline icon-tabler-paperclip"><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5" /></svg>
  );
};

export default AttachmentIcon;
