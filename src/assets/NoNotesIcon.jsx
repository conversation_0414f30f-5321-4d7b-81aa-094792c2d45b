import React from 'react';

function NoNotesIcon({ width = '228px', height = '228', ...rest }) {
  return (

    <svg width={width} height={height} {...rest} viewBox="0 0 231 228" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="228" height="228" rx="114" fill="#F4F8FC" />
      <rect x="1" width="228" height="228" rx="114" fill="#F4F8FC" />
      <path d="M67.8865 203.944C69.1023 208.482 73.7662 211.175 78.3037 209.959L187.163 180.79C191.701 179.574 194.393 174.91 193.178 170.373L161.807 53.2974L144.879 43.524L122.743 30.7434L36.4767 53.8584C31.9392 55.0742 29.2465 59.7381 30.4623 64.2756L67.8865 203.944Z" fill="#B9EEFF" fillOpacity="0.75" />
      <g filter="url(#filter0_d_3763_7464)">
        <path d="M161.808 53.2976L122.743 30.7436L130.998 61.5529L161.808 53.2976Z" fill="white" />
      </g>
      <path d="M91.9336 38.9986L130.998 61.5526L122.743 30.7433L91.9336 38.9986Z" fill="#EEEEEE" />
      <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 79.915 181.968)" fill="#29CBFF" fillOpacity="0.5" />
      <rect width="99.9413" height="4.25282" rx="2.12641" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 74.4116 162.571)" fill="#00B2EB" />
      <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 68.9082 147.946)" fill="#29CBFF" fillOpacity="0.5" />
      <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 63.4048 113.923)" fill="#29CBFF" fillOpacity="0.5" />
      <rect width="57.4131" height="6.37923" rx="3.18962" transform="matrix(0.965926 -0.258819 -0.258819 -0.965926 52.3975 80.4123)" fill="#29CBFF" fillOpacity="0.5" />
      <g filter="url(#filter1_d_3763_7464)">
        <path d="M70.7891 24.6144C70.7891 19.9169 74.5972 16.1087 79.2947 16.1087H191.994C196.692 16.1087 200.5 19.9169 200.5 24.6144V145.82L186.678 159.641L168.604 177.716H79.2947C74.5972 177.716 70.7891 173.908 70.7891 169.21V24.6144Z" fill="white" />
        <g filter="url(#filter2_d_3763_7464)">
          <path d="M200.5 145.82L168.604 177.716L168.604 145.82L200.5 145.82Z" fill="white" />
        </g>
        <path d="M136.708 177.716L168.604 145.82L168.604 177.716L136.708 177.716Z" fill="#EEEEEE" />
      </g>
      <circle cx="136" cy="97.0001" r="37" fill="#F4F8FC" />
      <path d="M134.193 109.185C131.589 109.185 129.431 107.049 130.012 104.51V104.51C130.392 102.951 130.95 101.534 131.687 100.259C132.281 99.0546 132.922 98.11 133.611 97.4251C134.324 96.7167 135.132 95.9964 136.035 95.2644C136.961 94.5323 138.054 93.505 139.313 92.1826C140.287 91.1199 140.929 90.0809 141.238 89.0654C141.57 88.0264 141.737 86.8929 141.737 85.6649C141.737 85.0273 141.665 84.4133 141.523 83.8229C141.404 83.2326 141.166 82.713 140.81 82.2644C140.24 81.4851 139.479 80.9301 138.529 80.5995C137.603 80.2453 136.605 80.0682 135.536 80.0682C134.728 80.0682 133.932 80.1626 133.148 80.3515C132.388 80.5405 131.734 80.8475 131.188 81.2725C130.523 81.7684 130.024 82.3942 129.691 83.1499V83.1499C129.158 84.4535 128.102 85.6649 126.694 85.6649H124.354C121.5 85.6649 119.22 83.1288 120.318 80.4936C120.57 79.8872 120.856 79.2967 121.174 78.7221C122.338 76.6204 123.847 74.9674 125.7 73.763C127.054 72.8656 128.551 72.1808 130.19 71.7085C131.829 71.2362 133.469 71.0001 135.108 71.0001C137.816 71.0001 140.37 71.4251 142.77 72.2753C145.17 73.1018 147.153 74.4714 148.721 76.3842C149.767 77.5886 150.575 78.9937 151.145 80.5995C151.715 82.1817 152 83.7757 152 85.3815C152 87.9555 151.418 90.2934 150.254 92.3951C149.09 94.4969 147.652 96.4569 145.942 98.2753C145.205 99.0546 144.54 99.7394 143.946 100.33C143.376 100.897 142.853 101.44 142.378 101.959C141.927 102.455 141.523 102.975 141.166 103.518C140.501 104.604 140.097 105.49 139.955 106.174V106.174C139.614 107.809 138.387 109.185 136.718 109.185H134.193ZM134.645 123C131.83 123 129.549 120.714 129.549 117.899V117.899C129.549 115.085 131.83 112.798 134.645 112.798V112.798C137.459 112.798 139.741 115.085 139.741 117.899V117.899C139.741 120.714 137.459 123 134.645 123V123Z" fill="#78DEFF" />
      <circle cx="169.756" cy="134.895" r="26.0001" transform="rotate(-45 169.756 134.895)" fill="white" fillOpacity="0.5" stroke="#232F50" strokeWidth="8.46516" />
      <path d="M188.568 147.722L204.387 163.541C206.04 165.194 206.04 167.874 204.387 169.527C202.734 171.18 200.055 171.18 198.402 169.527L182.582 153.708L188.568 147.722Z" fill="#232F50" stroke="#232F50" strokeWidth="1.20931" />
      <path d="M159.293 118.827L158 119.667L160.414 119.612C174.458 119.291 185.816 130.97 185.102 145C196.456 128.046 176.405 107.711 159.293 118.827Z" fill="#DFEBF7" />
      <defs>
        <filter id="filter0_d_3763_7464" x="109.347" y="17.3472" width="65.8572" height="57.6021" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset />
          <feGaussianBlur stdDeviation="6.69819" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3763_7464" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3763_7464" result="shape" />
        </filter>
        <filter id="filter1_d_3763_7464" x="63.6021" y="8.92179" width="144.085" height="175.982" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset />
          <feGaussianBlur stdDeviation="3.59348" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3763_7464" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3763_7464" result="shape" />
        </filter>
        <filter id="filter2_d_3763_7464" x="155.208" y="132.424" width="58.6888" height="58.6889" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset />
          <feGaussianBlur stdDeviation="6.69819" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3763_7464" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3763_7464" result="shape" />
        </filter>
      </defs>
    </svg>

  );
}

export default NoNotesIcon;
