import React from 'react';

const Processed = () => (

  <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Property 1=Variant3">
      <path id="Path" d="M27.0419 8.87539C23.8061 4.26792 17.7988 2.535 12.6062 4.71117C7.41365 6.88734 4.43682 12.3854 5.45286 17.9232" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M7.15625 25.4344V22.7666H9.82403" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M7.48169 22.7665C10.7174 27.374 16.7247 29.1069 21.9173 26.9307C27.1099 24.7546 30.0867 19.2565 29.0707 13.7188" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M27.3672 6.20764V8.87542H24.6995" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_5" fillRule="evenodd" clipRule="evenodd" d="M15.3758 17.7072C16.4177 18.7487 18.1066 18.7484 19.1483 17.7066C20.19 16.6648 20.19 14.9759 19.1483 13.9341C18.1066 12.8923 16.4177 12.8921 15.3758 13.9336C14.8752 14.4339 14.594 15.1126 14.594 15.8204C14.594 16.5281 14.8752 17.2068 15.3758 17.7072Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_6" fillRule="evenodd" clipRule="evenodd" d="M11.4342 15.8203C11.4342 16.0768 11.4576 16.3332 11.4886 16.5819L11.0781 16.903C10.222 17.5713 9.9902 18.7693 10.5341 19.7095V19.7095C11.0768 20.6485 12.2256 21.0448 13.2319 20.6407L13.3303 20.6006C13.5725 20.5034 13.8431 20.5423 14.0607 20.6861C14.2511 20.8117 14.448 20.9282 14.6513 21.0318C14.8844 21.1497 15.0541 21.3621 15.0903 21.6211L15.1046 21.7247C15.2587 22.7958 16.1769 23.591 17.2584 23.591H17.2622C18.3449 23.591 19.2632 22.7958 19.4173 21.7234L19.4315 21.6211C19.4691 21.3621 19.6401 21.1484 19.8732 21.0318C20.0765 20.9295 20.2721 20.8156 20.4612 20.6899C20.68 20.5449 20.952 20.5034 21.1955 20.6019L21.2913 20.6407C22.2963 21.0448 23.4464 20.6472 23.9877 19.7095L23.989 19.7082C24.5317 18.768 24.2999 17.5726 23.4451 16.9043L23.0345 16.5832C23.0669 16.3332 23.0902 16.0768 23.0902 15.8203C23.0902 15.5639 23.0669 15.3075 23.0358 15.0588L23.4464 14.7376C24.3011 14.0693 24.533 12.8727 23.9903 11.9337L23.989 11.9324C23.4464 10.9935 22.2976 10.5972 21.2913 11.0012L21.1955 11.0401C20.952 11.1372 20.6813 11.0971 20.4612 10.952C20.2721 10.8264 20.0765 10.7124 19.8732 10.6101C19.6388 10.4923 19.4678 10.2799 19.4315 10.0208L19.4173 9.91982C19.2658 8.84617 18.3475 8.04968 17.2635 8.04968H17.2622C16.1795 8.04968 15.2613 8.84488 15.1072 9.91723L15.0929 10.0208C15.0554 10.2786 14.8857 10.491 14.6539 10.6101C14.4506 10.7137 14.2537 10.8303 14.0633 10.9559C13.8431 11.0997 13.5725 11.1385 13.3303 11.0401L13.2319 10.9999C12.2256 10.5972 11.0768 10.9935 10.5341 11.9324V11.9324C9.9902 12.8727 10.2233 14.0693 11.0781 14.7389L11.4886 15.0601C11.4576 15.3075 11.4342 15.5639 11.4342 15.8203V15.8203Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default Processed;
