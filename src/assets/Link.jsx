import React from 'react';

const Link = ({ color = '#5C6E93', width = '24', height = '24' }) => (

  <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <g id="Group_2">
        <path id="Path" d="M21.2853 8.04799C23.1267 6.20666 26.1107 6.20666 27.952 8.04799V8.04799C29.7933 9.88933 29.7933 12.8733 27.952 14.7147L20.1133 22.5533C18.272 24.3947 15.288 24.3947 13.4467 22.5533V22.5533C11.6053 20.712 11.6053 17.728 13.4467 15.8867L14.6187 14.7147" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path id="Path_2" d="M10.7147 25.2854C8.87333 27.1267 5.88933 27.1267 4.04799 25.2854V25.2854C2.20666 23.444 2.20666 20.46 4.04799 18.6187L11.8867 10.78C13.728 8.93871 16.712 8.93871 18.5533 10.78V10.78C20.3947 12.6214 20.3947 15.6054 18.5533 17.4467L17.3333 18.6667" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
    </g>
  </svg>

);

export default Link;
