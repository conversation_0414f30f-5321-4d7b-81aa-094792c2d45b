import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const MiniScreenIcon = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
    <path stroke="currentColor" d="M5 9l4 0l0 -4" />
    <path stroke="currentColor" d="M3 3l6 6" />
    <path stroke="currentColor" d="M5 15l4 0l0 4" />
    <path stroke="currentColor" d="M3 21l6 -6" />
    <path stroke="currentColor" d="M19 9l-4 0l0 -4" />
    <path stroke="currentColor" d="M15 9l6 -6" />
    <path stroke="currentColor" d="M19 15l-4 0l0 4" />
    <path stroke="currentColor" d="M15 15l6 6" />
  </Icon>
);

export default MiniScreenIcon;
