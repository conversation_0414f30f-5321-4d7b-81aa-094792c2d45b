import React from 'react';

const UnLink = ({ color = '#5C6E93', width = '24', height = '24' }) => (

  <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22.6 16.9427L26.3706 13.172C28.4533 11.0894 28.4533 7.71205 26.3706 5.62938V5.62938C24.288 3.54672 20.9106 3.54672 18.828 5.62938L15.0573 9.40005" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.39999 15.0574L5.62932 18.828C3.54666 20.9107 3.54666 24.288 5.62932 26.3707V26.3707C7.71199 28.4534 11.0893 28.4534 13.172 26.3707L16.9427 22.6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.52003 11.2799L4.69336 10.3466" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.28 7.52003L10.3467 4.69336" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M24.48 20.72L27.32 21.6534" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.72 24.48L21.6534 27.32" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.2267 19.7734L19.7734 12.2267" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default UnLink;
