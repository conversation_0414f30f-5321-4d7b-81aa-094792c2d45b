import React from 'react';

const ArrowView = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none">
      <g clipPath="url(#clip0_103_8323)">
        <path d="M10.5417 6.70801H5.74999C5.24166 6.70801 4.75415 6.90994 4.39471 7.26939C4.03526 7.62883 3.83333 8.11634 3.83333 8.62467V17.2497C3.83333 17.758 4.03526 18.2455 4.39471 18.605C4.75415 18.9644 5.24166 19.1663 5.74999 19.1663H14.375C14.8833 19.1663 15.3708 18.9644 15.7303 18.605C16.0897 18.2455 16.2917 17.758 16.2917 17.2497V12.458" stroke="#E93980" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M9.58333 13.4163L19.1667 3.83301" stroke="#E93980" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M14.375 3.83301H19.1667V8.62467" stroke="#E93980" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_103_8323">
          <rect width="23" height="23" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ArrowView;
