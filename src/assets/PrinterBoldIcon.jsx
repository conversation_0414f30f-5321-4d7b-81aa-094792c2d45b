import React from 'react';

const PrinterBoldIcon = ({ w = '18', h = '18', stroke = '#5C6E93' }) => (
  <svg
    width={w}
    height={h}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.66699 5.33333V2.66667C4.66699 2.29867 4.96566 2 5.33366 2H10.667C11.035 2 11.3337 2.29867 11.3337 2.66667V5.33333"
      stroke={stroke}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.66667 11.332H3.33333C2.59667 11.332 2 10.7354 2 9.9987V6.66536C2 5.9287 2.59667 5.33203 3.33333 5.33203H12.6667C13.4033 5.33203 14 5.9287 14 6.66536V9.9987C14 10.7354 13.4033 11.332 12.6667 11.332H11.3333"
      stroke={stroke}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.66699 9.19922H11.3337V13.3326C11.3337 13.7006 11.035 13.9992 10.667 13.9992H5.33366C4.96566 13.9992 4.66699 13.7006 4.66699 13.3326V9.19922Z"
      stroke={stroke}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.66699 7.33333H5.33366"
      stroke={stroke}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default PrinterBoldIcon;
