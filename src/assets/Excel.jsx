import React from 'react';

const Excel = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="33" viewBox="0 0 32 33" fill="none">
      <path d="M19.9998 31.0661H11.9998C4.75984 31.0661 1.6665 27.9727 1.6665 20.7327V12.7327C1.6665 5.49275 4.75984 2.39941 11.9998 2.39941H18.6665C19.2132 2.39941 19.6665 2.85275 19.6665 3.39941C19.6665 3.94608 19.2132 4.39941 18.6665 4.39941H11.9998C5.85317 4.39941 3.6665 6.58608 3.6665 12.7327V20.7327C3.6665 26.8794 5.85317 29.0661 11.9998 29.0661H19.9998C26.1465 29.0661 28.3332 26.8794 28.3332 20.7327V14.0661C28.3332 13.5194 28.7865 13.0661 29.3332 13.0661C29.8798 13.0661 30.3332 13.5194 30.3332 14.0661V20.7327C30.3332 27.9727 27.2398 31.0661 19.9998 31.0661Z" fill="#939393" />
      <path d="M29.3332 15.0661H23.9998C19.4398 15.0661 17.6665 13.2927 17.6665 8.73273V3.3994C17.6665 2.9994 17.9065 2.62607 18.2798 2.4794C18.6532 2.3194 19.0798 2.41273 19.3732 2.69273L30.0398 13.3594C30.3198 13.6394 30.4132 14.0794 30.2532 14.4527C30.0932 14.8261 29.7332 15.0661 29.3332 15.0661ZM19.6665 5.81273V8.73273C19.6665 12.1727 20.5598 13.0661 23.9998 13.0661H26.9198L19.6665 5.81273Z" fill="#939393" />
      <path d="M17.3335 19.0654H9.3335C8.78683 19.0654 8.3335 18.6121 8.3335 18.0654C8.3335 17.5188 8.78683 17.0654 9.3335 17.0654H17.3335C17.8802 17.0654 18.3335 17.5188 18.3335 18.0654C18.3335 18.6121 17.8802 19.0654 17.3335 19.0654Z" fill="#939393" />
      <path d="M14.6668 24.3994H9.3335C8.78683 24.3994 8.3335 23.9461 8.3335 23.3994C8.3335 22.8527 8.78683 22.3994 9.3335 22.3994H14.6668C15.2135 22.3994 15.6668 22.8527 15.6668 23.3994C15.6668 23.9461 15.2135 24.3994 14.6668 24.3994Z" fill="#939393" />
    </svg>
  );
};

export default Excel;
