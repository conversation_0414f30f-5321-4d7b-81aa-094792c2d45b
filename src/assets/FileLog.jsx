import React from 'react';

const FileLog = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
      <path d="M23.75 13.75V9.785C23.75 9.1225 23.4863 8.48625 23.0175 8.0175L19.4825 4.4825C19.0138 4.01375 18.3775 3.75 17.715 3.75H8.75C7.36875 3.75 6.25 4.86875 6.25 6.25V23.75C6.25 25.1313 7.36875 26.25 8.75 26.25H13.75" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M23.75 10H18.75C18.06 10 17.5 9.44 17.5 8.75V3.75" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M23.3591 21.6925V19.5038C23.3591 18.4525 22.4628 17.5 21.3553 17.5C20.2478 17.5 19.3516 18.3963 19.3516 19.5038V21.6925" fill="#E83A7A" fillOpacity="0.1" />
      <path d="M23.3591 21.6925V19.5038C23.3591 18.4525 22.4628 17.5 21.3553 17.5C20.2478 17.5 19.3516 18.3963 19.3516 19.5038V21.6925" stroke="#E83A7A" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M24.1795 26.2516H18.5283C18.0745 26.2516 17.707 25.8841 17.707 25.4316V22.5153C17.707 22.0616 18.0745 21.6953 18.527 21.6953H24.1783C24.632 21.6953 24.9983 22.0628 24.9983 22.5153V25.4316C24.9995 25.8841 24.632 26.2516 24.1795 26.2516Z" fill="#E83A7A" fillOpacity="0.1" stroke="#E83A7A" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default FileLog;
