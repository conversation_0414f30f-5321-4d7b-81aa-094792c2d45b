import React from 'react';

const CashDeclaration = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="27" viewBox="0 0 24 27" fill="none">
      <path d="M10.3333 24.4884L9.58832 24.8593C9.21256 25.0464 8.77065 25.0456 8.39557 24.8572L6.34104 23.8251L4.2444 24.8618C3.86921 25.0474 3.42868 25.046 3.05469 24.858L1.73469 24.1947C1.28429 23.9683 1.00001 23.5074 1 23.0033V2.99669C1.00001 2.49263 1.28427 2.03165 1.73467 1.80533L3.05467 1.14197C3.42865 0.954032 3.86919 0.952615 4.24437 1.13814L6.34101 2.17493L8.39555 1.14284C8.77062 0.954413 9.21253 0.953612 9.58829 1.14068L11.6667 2.17534L13.745 1.14068C14.1208 0.953612 14.5627 0.954413 14.9378 1.14284L16.9923 2.17493L19.0889 1.13814C19.4641 0.952615 19.9047 0.954032 20.2786 1.14197L21.5986 1.80533C22.049 2.03165 22.3333 2.49262 22.3333 2.99669L22.3334 11" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M22.668 16H15.668H16.566C18.4731 16 20.0192 17.546 20.0192 19.4532V19.4532C20.0192 21.3603 18.4731 22.9063 16.566 22.9063H16.0829C15.9543 22.9063 15.9017 23.0717 16.0068 23.1459L20.043 26M15.668 19.2214H18.444H21.9979" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.33594 9.0026H17.0026" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9.0026 17.0026H6.33594" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.33984 13.0026H11.6732" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default CashDeclaration;
