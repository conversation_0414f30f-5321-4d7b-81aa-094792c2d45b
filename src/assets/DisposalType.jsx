import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const DisposalTypeIcon = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path stroke="currentColor" d="M15 19h-10a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2h4l3 3h7a2 2 0 0 1 2 2v2.5" />
    <path stroke="currentColor" d="M19 22v.01" />
    <path stroke="currentColor" d="M19 19a2.003 2.003 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483" />
  </Icon>
);

export default DisposalTypeIcon;
