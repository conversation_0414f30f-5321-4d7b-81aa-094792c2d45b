import React from 'react';

const PayOrder = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" d="M14.6667 27.4884L13.9217 27.8593C13.5459 28.0464 13.104 28.0456 12.7289 27.8572L10.6744 26.8251L8.57777 27.8618C8.20259 28.0474 7.76205 28.046 7.38807 27.858L6.06807 27.1947C5.61766 26.9683 5.33339 26.5074 5.33337 26.0033V5.99669C5.33338 5.49263 5.61764 5.03165 6.06804 4.80533L7.38804 4.14197C7.76203 3.95403 8.20256 3.95262 8.57775 4.13814L10.6744 5.17493L12.7289 4.14284C13.104 3.95441 13.5459 3.95361 13.9217 4.14068L16 5.17534L18.0784 4.14068C18.4541 3.95361 18.8961 3.95441 19.2711 4.14284L21.3257 5.17493L23.4223 4.13814C23.7975 3.95262 24.238 3.95403 24.612 4.14197L25.932 4.80533C26.3824 5.03165 26.6667 5.49262 26.6667 5.99669L26.6668 14" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Vector 20" d="M27 19H20H20.898C22.8052 19 24.3512 20.546 24.3512 22.4532V22.4532C24.3512 24.3603 22.8052 25.9063 20.898 25.9063H20.4149C20.2863 25.9063 20.2338 26.0717 20.3388 26.1459L24.375 29M20 22.2214H22.7761H26.3299" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M10.6666 11.9999H21.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M13.3333 19.9999H10.6666" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M10.672 15.9999H16.0053" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default PayOrder;
