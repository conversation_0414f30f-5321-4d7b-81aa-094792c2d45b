import React from 'react';

const Oops = () => (

  <svg width="186" height="215" viewBox="0 0 186 215" fill="none" xmlns="http://www.w3.org/2000/svg">
    <ellipse cx="92.8786" cy="188.156" rx="91.2987" ry="21.6962" transform="rotate(-3.17504 92.8786 188.156)" fill="black" fillOpacity="0.09" />
    <path d="M184.761 183.561C184.761 194.716 147.601 203.76 101.761 203.76C55.9216 203.76 18.7613 194.716 18.7613 183.561C18.7613 172.405 55.9216 163.362 101.761 163.362C147.601 163.362 184.761 172.405 184.761 183.561Z" fill="#F2B167" />
    <path d="M18.7613 172.176H29.779V183.928H18.7613V172.176Z" fill="#F2B167" />
    <path d="M173.744 172.176H184.761V183.928H173.744V172.176Z" fill="#F2B167" />
    <ellipse cx="101.761" cy="171.808" rx="83" ry="20.1991" fill="#F09757" />
    <path d="M86.363 5.44121L38.5772 169.104C38.1393 170.604 38.6287 172.197 39.9738 172.992C45.475 176.245 62.6132 183.928 101.761 183.928C140.935 183.928 158.07 176.235 163.56 172.986C164.899 172.193 165.389 170.608 164.959 169.113L117.929 5.44121H86.363Z" fill="#F2B167" />
    <mask id="mask0_14694_26110" style={{ maskType: 'alpha' }} maskUnits="userSpaceOnUse" x="41" y="5" width="122" height="179">
      <path d="M81.9492 20.5581L41.8111 158.028C39.4023 166.278 42.3753 174.882 50.5479 177.541C60.2528 180.699 76.3818 183.928 101.761 183.928C127.196 183.928 143.34 180.685 153.038 177.521C161.176 174.865 164.152 166.303 161.788 158.075L122.298 20.6443C119.711 11.6426 111.477 5.44121 102.111 5.44121C92.7784 5.44121 84.565 11.5994 81.9492 20.5581Z" fill="#F2B167" />
    </mask>
    <g mask="url(#mask0_14694_26110)">
      <path d="M145.647 81.6795C115.179 92.4011 81.8391 91.5981 51.9219 79.4221L63.2517 51.584C86.3679 60.992 112.128 61.6125 135.671 53.3283L145.647 81.6795Z" fill="#FAFDFF" />
      <path d="M162.532 148.214C123.151 158.732 81.7076 158.795 42.2952 148.396L51.1072 114.998C84.7095 123.864 120.043 123.81 153.619 114.843L162.532 148.214Z" fill="#FAFDFF" />
    </g>
    <ellipse cx="102.129" cy="5.80847" rx="15.792" ry="5.50885" fill="#F09757" />
  </svg>

);

export default Oops;
