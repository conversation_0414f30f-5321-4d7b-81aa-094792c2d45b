import React from 'react';

const Child = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" d="M12 22.6667H6.66667C5.19391 22.6667 4 21.4728 4 20V4" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M12 9.33335H4" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" fillRule="evenodd" clipRule="evenodd" d="M28 11.3334V6.66669C28 5.56212 27.1046 4.66669 26 4.66669H23.2509C22.8094 4.66669 22.3966 4.44824 22.1484 4.08326L21.5817 3.25011C21.3335 2.88513 20.9206 2.66667 20.4792 2.66669H18C16.8954 2.66669 16 3.56212 16 4.66669V11.3334C16 12.4379 16.8954 13.3334 18 13.3334H26C27.1046 13.3334 28 12.4379 28 11.3334Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" fillRule="evenodd" clipRule="evenodd" d="M28 26V21.3333C28 20.2287 27.1046 19.3333 26 19.3333H23.2509C22.8094 19.3333 22.3966 19.1149 22.1484 18.7499L21.5817 17.9167C21.3335 17.5518 20.9206 17.3333 20.4792 17.3333H18C16.8954 17.3333 16 18.2287 16 19.3333V26C16 27.1045 16.8954 28 18 28H26C27.1046 28 28 27.1045 28 26Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default Child;
