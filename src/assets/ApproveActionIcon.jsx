import React from 'react';

const ApproveActionIcon = ({ w = '24', h = '24', allowHoverStyle = true }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={w}
    height={h}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={`icon-tabler icons-tabler-outline icon-tabler-checks ${
      allowHoverStyle ? 'icon' : ''
    }`}
  >
    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
    <path d="M7 12l5 5l10 -10" />
    <path d="M2 12l5 5m5 -5l5 -5" />
  </svg>
);

export default ApproveActionIcon;
