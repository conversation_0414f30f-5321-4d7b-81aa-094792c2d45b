import React from 'react';

const Payment = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" d="M4 21.3333L7.40072 15.6655C7.79211 15.0131 8.36141 14.4859 9.04184 14.1457L9.82209 13.7556C10.3775 13.4779 10.99 13.3333 11.6109 13.3333H13.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M20 4.66669H17.3334C15.1242 4.66669 13.3334 6.45755 13.3334 8.66669V20" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Vector 20" d="M25 8H20H20.6908C22.0258 8 23.108 9.08223 23.108 10.4172V10.4172C23.108 11.7522 22.0258 12.8344 20.6908 12.8344H20.3009C20.2087 12.8344 20.1715 12.9533 20.2473 13.0058L23.125 15M20 10.255H21.9829H24.5213" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M27.3334 12V21.3333C27.3334 23.5425 25.5425 25.3333 23.3334 25.3333H17.3334" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_4" d="M12 20H14.6667L17.5874 17.0804C18.3853 16.2829 19.6579 16.2203 20.5301 16.9356C21.4024 17.651 21.5902 18.9111 20.9644 19.8497L16.9739 25.8337C16.3414 26.7823 15.4215 27.5032 14.3492 27.8907L10.3566 29.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_5" d="M25 1.66663V2.99996" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_6" d="M29 7.00004H30.3333" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_7" d="M28.0572 3.94276L29 2.99995" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default Payment;
