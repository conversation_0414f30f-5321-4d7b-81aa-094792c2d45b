import React from 'react';

function NoNotesListIcon({ width = '228px' }) {
  return (
    <div>

      <svg width={width} height="235" viewBox="0 0 228 235" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="3" width="228" height="228" rx="114" fill="#F4F8FC" />
        <path d="M45.2266 191.851C45.2266 196.548 49.0347 200.356 53.7322 200.356H166.432C171.129 200.356 174.938 196.548 174.938 191.851V70.6454L161.116 56.8237L143.041 38.7492H53.7322C49.0347 38.7492 45.2266 42.5573 45.2266 47.2548V191.851Z" fill="#B9EEFF" fillOpacity="0.75" />
        <g filter="url(#filter0_d_3759_6432)">
          <path d="M174.938 70.6455L143.041 38.7494L143.041 70.6455L174.938 70.6455Z" fill="white" />
        </g>
        <path d="M111.145 38.749L143.041 70.6452L143.041 38.749L111.145 38.749Z" fill="#EEEEEE" />
        <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(1 0 0 -1 62.5332 173.737)" fill="#29CBFF" fillOpacity="0.5" />
        <rect width="99.9413" height="4.25282" rx="2.12641" transform="matrix(1 0 0 -1 62.2378 153.576)" fill="#00B2EB" />
        <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(1 0 0 -1 60.7068 138.025)" fill="#29CBFF" fillOpacity="0.5" />
        <rect width="99.9413" height="6.37923" rx="3.18962" transform="matrix(1 0 0 -1 64.1968 103.737)" fill="#29CBFF" fillOpacity="0.5" />
        <rect width="57.4131" height="6.37923" rx="3.18962" transform="matrix(1 0 0 -1 62.2378 68.519)" fill="#29CBFF" fillOpacity="0.5" />
        <g filter="url(#filter1_d_3759_6432)">
          <path d="M94.4441 19.3824C95.6599 14.8449 100.324 12.1522 104.861 13.368L213.721 42.5368C218.258 43.7526 220.951 48.4166 219.735 52.9541L188.365 170.029L182.226 192.757C180.998 197.305 176.308 199.988 171.766 198.743L149.3 192.583L63.0343 169.469C58.4968 168.253 55.8041 163.589 57.0199 159.051L94.4441 19.3824Z" fill="white" />
          <rect x="103.803" y="45.1556" width="48.3261" height="7.43478" rx="3.71739" transform="rotate(15 103.803 45.1556)" fill="#76DDFE" />
          <rect x="98.0305" y="66.7" width="48.3261" height="7.43478" rx="3.71739" transform="rotate(15 98.0305 66.7)" fill="#76DDFE" />
          <rect x="91.937" y="89.4412" width="99.9413" height="7.43478" rx="3.71739" transform="rotate(15 91.937 89.4412)" fill="#76DDFE" />
          <rect x="97.9243" y="67" width="99.9413" height="7.43478" rx="3.71739" transform="rotate(15 97.9243 67)" fill="#76DDFE" />
          <rect x="103.924" y="45" width="99.9413" height="7.43478" rx="3.71739" transform="rotate(15 103.924 45)" fill="#76DDFE" />
          <rect x="85.8435" y="112.182" width="100.37" height="7.43478" rx="3.71739" transform="rotate(15 85.8435 112.182)" fill="#76DDFE" />
          <rect x="79.75" y="134.924" width="98.935" height="7.43478" rx="3.71739" transform="rotate(15 79.75 134.924)" fill="#76DDFE" />
        </g>
        <g filter="url(#filter2_d_3759_6432)">
          <rect x="202.918" y="93.3171" width="12.4104" height="88.941" transform="rotate(32.7363 202.918 93.3171)" fill="#E97CB8" />
          <rect x="202.918" y="93.3171" width="4.13679" height="88.941" transform="rotate(32.7363 202.918 93.3171)" fill="#DF4399" />
          <path d="M208.511 84.6176C210.364 81.7349 214.204 80.9004 217.086 82.7536V82.7536C219.969 84.6069 220.804 88.4461 218.95 91.3288L214.476 98.2883L204.037 91.5771L208.511 84.6176Z" fill="#E97CB8" />
          <mask id="mask0_3759_6432" maskUnits="userSpaceOnUse" x="204" y="81" width="16" height="18">
            <path d="M208.511 84.6176C210.364 81.7349 214.204 80.9004 217.086 82.7536V82.7536C219.969 84.6069 220.804 88.4461 218.95 91.3288L214.476 98.2883L204.037 91.5771L208.511 84.6176Z" fill="#E97CB8" />
          </mask>
          <g mask="url(#mask0_3759_6432)">
            <rect x="211.867" y="79.398" width="4.13679" height="14.4788" transform="rotate(32.7363 211.867 79.398)" fill="#DF4399" />
          </g>
          <path d="M198.096 93.1678C198.838 92.0147 200.373 91.6809 201.526 92.4222L202.918 93.3171L185.022 121.155L183.35 120.081C182.352 119.438 182.062 118.108 182.705 117.109L198.096 93.1678Z" fill="#FFC34C" />
          <rect x="214.476" y="98.2884" width="6.20519" height="12.4104" transform="rotate(122.736 214.476 98.2884)" fill="#FFC34C" />
          <path d="M152.212 183.666L154.822 168.131L165.261 174.843L152.212 183.666Z" fill="#FFC34C" />
          <path d="M152.215 183.661L153.331 177.009L157.803 179.884L152.215 183.661Z" fill="#9E6900" />
        </g>
        <defs>
          <filter id="filter0_d_3759_6432" x="129.645" y="25.353" width="58.689" height="58.6889" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset />
            <feGaussianBlur stdDeviation="6.69819" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3759_6432" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3759_6432" result="shape" />
          </filter>
          <filter id="filter1_d_3759_6432" x="49.5411" y="5.88911" width="177.673" height="200.345" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset />
            <feGaussianBlur stdDeviation="3.59348" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3759_6432" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3759_6432" result="shape" />
          </filter>
          <filter id="filter2_d_3759_6432" x="134.912" y="70.5611" width="91.9939" height="123.061" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dx="-2" />
            <feGaussianBlur stdDeviation="3.3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3759_6432" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3759_6432" result="shape" />
          </filter>
        </defs>
      </svg>

    </div>
  );
}

export default NoNotesListIcon;
