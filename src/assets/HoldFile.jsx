import React from 'react';

const HoldFile = () => (

  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
      <path id="Path" fillRule="evenodd" clipRule="evenodd" d="M14.0235 20.7177C14.7729 20.103 15.8662 20.1577 16.5502 20.843L17.7075 21.999V17.335C17.7075 15.515 19.4902 14.2297 21.2169 14.8057L24.9542 16.051C26.0435 16.4137 26.7782 17.4337 26.7782 18.5803V24.0443C26.7782 26.5843 24.7182 28.6443 22.1782 28.6443H20.0102C18.5609 28.6443 17.1969 27.9617 16.3275 26.8017L13.7089 23.3043C13.1049 22.4963 13.2435 21.3563 14.0235 20.7177V20.7177V20.7177Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_2" d="M26.6667 11.3333H22C20.5272 11.3333 19.3333 10.1394 19.3333 8.66667V4" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path id="Path_3" d="M26.6667 14V11.3457C26.6667 10.2849 26.2452 9.26748 25.4951 8.51733L22.1493 5.17157C21.3992 4.42143 20.3818 4 19.3209 4H9.33333C7.12419 4 5.33333 5.79086 5.33333 8V24C5.33333 26.2091 7.12419 28 9.33333 28H12.6667" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </g>
  </svg>

);

export default HoldFile;
