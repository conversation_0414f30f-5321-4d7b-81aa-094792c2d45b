import React from 'react';

const ImprestDisbursement = () => (
  <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M21.35 12.7898L21.1851 12.9573L21.0146 12.7771C20.7253 12.4684 20.3224 12.2914 19.8994 12.2872C19.4763 12.2829 19.07 12.4518 18.7745 12.7546L18.7399 12.7898C18.1128 13.4342 18.1128 14.4608 18.7399 15.1052L20.4448 16.8363C20.64 17.0347 20.9067 17.1464 21.1851 17.1464C21.4635 17.1464 21.7302 17.0347 21.9254 16.8363L23.6303 15.1052C24.2574 14.4608 24.2574 13.4342 23.6303 12.7898V12.7898C23.3295 12.4843 22.9188 12.3123 22.4901 12.3123C22.0615 12.3123 21.6507 12.4843 21.35 12.7898V12.7898Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M26.4065 27.3375L28.1108 27.3375C28.5813 27.3373 28.9627 26.9559 28.9629 26.4854L28.9629 21.3726C28.9627 20.9021 28.5813 20.5207 28.1108 20.5205L26.4065 20.5205C25.936 20.5207 25.5546 20.9021 25.5544 21.3726L25.5544 26.4854C25.5546 26.9559 25.936 27.3373 26.4065 27.3375V27.3375Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.4419 23.927L18.7718 23.927C18.403 23.9272 18.0441 23.8076 17.7492 23.5862L15.6905 22.0421C15.1765 21.6569 14.4575 21.7081 14.0033 22.1623V22.1623C13.7611 22.4042 13.625 22.7325 13.625 23.0749C13.625 23.4173 13.7611 23.7456 14.0033 23.9875L15.7698 25.754C16.2457 26.2297 16.8517 26.554 17.5115 26.6862L20.0082 27.1855C20.5032 27.2845 21.014 27.2723 21.5037 27.1498L23.7618 26.5856C24.0318 26.5176 24.3091 26.4833 24.5875 26.4834L25.5547 26.4834" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.4419 23.929L19.1765 23.929C18.8341 23.9291 18.5057 23.7931 18.2636 23.551C18.0215 23.3089 17.8855 22.9804 17.8856 22.638L17.8856 22.3798C17.8854 21.7877 18.2885 21.2715 18.863 21.1281L20.816 20.6398C21.1335 20.5606 21.4594 20.5205 21.7866 20.5205V20.5205C22.5767 20.5203 23.3492 20.7542 24.0064 21.1928L25.5547 22.2248" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0928 9.67773V9.6932" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M5.57106 11.7436L6.35187 11.8631C6.92827 11.9514 7.3399 12.4674 7.29745 13.0489L7.23992 13.8369C7.22316 14.0665 7.34827 14.2826 7.55548 14.3826L8.133 14.6608C8.34021 14.7607 8.58763 14.7233 8.75686 14.5675L9.33717 14.0313C9.765 13.6359 10.4252 13.6359 10.8536 14.0313L11.4339 14.5675C11.6031 14.7239 11.85 14.7607 12.0577 14.6608L12.6364 14.3821C12.843 14.2826 12.9676 14.0671 12.9508 13.8381L12.8933 13.0489C12.8508 12.4674 13.2625 11.9514 13.8389 11.8631L14.6197 11.7436C14.847 11.709 15.0302 11.5386 15.0816 11.3141L15.224 10.6897C15.2754 10.4651 15.1843 10.2322 14.9944 10.1027L14.3426 9.65641C13.8618 9.32688 13.7149 8.68347 14.0053 8.178L14.3991 7.49326C14.5135 7.29386 14.4946 7.04421 14.351 6.86436L13.9517 6.36337C13.8081 6.18352 13.5691 6.10924 13.349 6.17682L12.5939 6.40805C12.0359 6.57896 11.4411 6.29244 11.2266 5.75011L10.9373 5.01677C10.8524 4.8023 10.6452 4.66155 10.4146 4.66211L9.77449 4.66379C9.54382 4.66435 9.33717 4.80621 9.25339 5.02124L8.97134 5.7462C8.7591 6.29188 8.16148 6.58063 7.60184 6.40861L6.81544 6.16733C6.59483 6.09919 6.35466 6.17403 6.21112 6.35499L5.81457 6.85654C5.67103 7.03806 5.65372 7.28828 5.77045 7.48767L6.17314 8.17409C6.46972 8.68012 6.3245 9.32968 5.84082 9.66088L5.19685 10.1021C5.00695 10.2322 4.91591 10.4652 4.9673 10.6891L5.10972 11.3135C5.16054 11.5386 5.34374 11.709 5.57106 11.7436V11.7436Z" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.6485 27.3374H7.23941C5.97703 27.3367 4.95386 26.3135 4.95312 25.0511L4.95313 17.5664" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M17.9404 4.66211L26.677 4.66211C27.9394 4.66285 28.9625 5.68602 28.9633 6.9484L28.9631 16.6057" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default ImprestDisbursement;
