import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { DATE_FORMAT } from 'pages/common/constants';

dayjs.extend(utc);
dayjs.extend(timezone);

export { dayjs };

export const dateDifference = (from = dayjs(), to = dayjs(), unit = 'day') => dayjs(from).diff(dayjs(to), unit);

export const convertToLocalDate = (date, format = DATE_FORMAT.DATE_LOCAL) => {
  if (date) {
    const parsedDate = dayjs(date, {
      format: DATE_FORMAT.DATE_TIME_GMT
    });
    const formattedDate = parsedDate.format(format);
    return formattedDate;
  } return null;
};

export const convertToLocalDateTime = (date) => {
  if (date) {
    const utcDate = new Date(date);
    const istDate = new Date(utcDate.getTime() + 0 * 60 * 60 * 1000); // UTC+5:30
    const converted = istDate.toLocaleString();
    return converted;
  } return null;
};

export const reFormattedDate = (dates = '') => {
  if (dates) {
    const startDate = dates;
    const splited = startDate?.split('-');
    const formatedStartDate = splited?.length > 0 ? new Date(parseInt(splited[2], 10), parseInt(splited[1], 10) - 1, parseInt(splited[0], 10)) : [];
    return formatedStartDate;
  }

  return null;
};

export const getDayTimePeriod = () => {
  const now = new Date();
  const currentHour = now.getHours();

  if (currentHour >= 5 && currentHour < 12) {
    return 'Good Morning';
  } if (currentHour >= 12 && currentHour < 17) {
    return 'Good Afternoon';
  }
  return 'Good Evening';
};

export const formatDate = (date) => {
  if (date) {
    return dayjs(date).format('DD/MM/YYYY');
  }
  return null;
};

export const formatDateYMD = (date) => {
  if (date) {
    return dayjs(date).format('YYYY/MM/DD');
  }
  return null;
};

export const formatDateYMDModify = (date) => {
  if (date) {
    return dayjs(date).format('YYYY-MM-DD');
  }
  return null;
};

export const convertToLocalTimeZone = (date, format = 'ddd, MMM DD YYYY, hh:mm A') => {
  return dayjs(date).utc('z').local().tz('Asia/Kolkata')
    .format(format);
};

export const dateTimeNow = () => dayjs().utc('z').format('ddd, MMM DD YYYY, hh:mm:ss A');

export const convertInputDate = (dateStr, format) => {
  if (!dateStr) {
    return '';
  }

  if (format === 'YYYY-MM-DD') {
    const [day, month, year] = dateStr.split('-');
    if (day && month && year) {
      const reformattedDate = `${year}-${month}-${day}`;
      return reformattedDate;
    }
  }

  return dateStr;
};

export const serverTimeToLocalTime = (dateTime) => {
  if (dateTime) {
    const [day, month, year, hours, minutes, seconds] = dateTime.split(/[-\s:]/);
    const date = new Date(Date.UTC(year, month - 1, day, hours, minutes, seconds));
    const options = {
      timeZone: 'Asia/Kolkata',
      hour12: false,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    };
    const formatter = new Intl.DateTimeFormat('en-GB', options);
    const formattedDate = formatter.format(date);
    const [datePart, timePart] = formattedDate.split(', ');
    const [dayPart, monthPart, yearPart] = datePart.split('/');
    const [hour, minute, second] = timePart.split(':');
    return `${dayPart}-${monthPart}-${yearPart} ${hour}:${minute}:${second}`;
  }
  return null;
};
