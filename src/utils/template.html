<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="Content-Style-Type" content="text/css" />
  <meta name="generator" content="Aspose.Words for .NET 23.9.0" />
  <title></title>

  <script type="text/javascript">
    var arrayData = ['Sree<PERSON>', 'Gokul', 'Array'];

    window.onload = function () {
      // Get the div element where the list will be appended
      var listContainer = document.getElementById('list-data');

      // Create a new unordered list (ul) element
      var ul = document.createElement('ul');

      // Iterate over the arrayData and create list items
      arrayData.forEach(function (item) {
        var li = document.createElement('li'); // Create a list item
        li.textContent = item; // Set the text content to the array item
        ul.appendChild(li); // Append the list item to the ul
      });

      // Append the ul to the div
      listContainer.appendChild(ul);
    };
  </script>

</head>

<body>
  <div id="list-data">
    <!-- List will be inserted here -->
  </div>
</body>

</html>
