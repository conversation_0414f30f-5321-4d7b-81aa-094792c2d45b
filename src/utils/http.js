import { createAction } from '@reduxjs/toolkit';
import {
  deleteRequest, getRequest, patchRequest, postRequest, putRequest
} from 'app/axios';
import { API_URL } from 'common';
import {
  EMPLOYEE_LOGIN_PATH,
  HTTP_HEADERS, REQUEST_METHOD, REQUEST_STATUS, STORAGE_KEYS
} from 'common/constants';
import _ from 'lodash';
import { getCommonConfigSelector } from 'pages/common/selectors';
import { call, put, select } from 'redux-saga/effects';
import queryString from 'query-string';
import { routeRedirect } from './common';

export const baseApiURL = import.meta.env.VITE_API_URL;
export const baseDSURL = import.meta.env.VITE_DIGITAL_SIGNATURE;

// const { errorTost } = Toast;

const getApiMethod = (method) => {
  switch (method) {
    case REQUEST_METHOD.DELETE:
      return deleteRequest;
    case REQUEST_METHOD.PUT:
      return putRequest;
    case REQUEST_METHOD.PATCH:
      return patchRequest;
    case REQUEST_METHOD.POST:
      return postRequest;
    case REQUEST_METHOD.MULTIPART:
      return postRequest;
    default:
      return getRequest;
  }
};

const getRequestParams = ({
  url, data: requestData = {}, params: requestParams = {}, method, commonConfig
}) => {
  let params = {};
  let data = {};
  const headers = { ...HTTP_HEADERS };
  if (method === REQUEST_METHOD.MULTIPART) {
    _.set(headers, 'Content-Type', 'multipart/form-data');
  }
  if (method === REQUEST_METHOD.POST) {
    _.set(headers, 'Content-Type', 'application/json');
  }
  if (method === REQUEST_METHOD.DELETE || method === REQUEST_METHOD.GET) {
    params = { ...requestData, ...requestParams };
    data = {};
  } else {
    params = requestParams;
    data = requestData;
  }
  let baseURL = import.meta.env.VITE_API_URL;
  let authHeaders = {};
  const bearerToken = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

  // TODO: Add Logic for fetching bearerToken
  if (bearerToken) {
    authHeaders = { Authorization: `Bearer ${bearerToken}` };
  }

  if (url === 'TEST') {
    // TODO: modify headers , baseURL, authHeaders , etc... based on url
  }

  if ([API_URL.E_SIGN.SIGN_PDF, API_URL.E_SIGN.LOCAL_ENROLLMENT].includes(url)) {
    baseURL = import.meta.env.VITE_DIGITAL_SIGNATURE;
  }

  const customHeaders = { 'X-STATE-CODE': commonConfig.stateId, 'X-LANGUAGE': commonConfig.locale };

  return {
    config: {
      headers: {
        ...headers,
        ...authHeaders,
        ...customHeaders
      },
      params,
      paramsSerializer: (paramsDetails) => queryString.stringify(paramsDetails)
    },
    baseURL,
    data,
    api: getApiMethod(method)
  };
};

function* invokeApi(method, url, payload) {
  const { types = ['REQUEST', 'SUCCESS', 'FAILURE'], data: payloadData, params = {} } = payload;
  const requestAction = createAction(types[0]);
  const successAction = createAction(types[1]);
  const failureAction = createAction(types[2]);

  yield put(requestAction({ isLoading: true, status: REQUEST_STATUS.PROGRESS }));

  const commonConfig = yield select(getCommonConfigSelector);

  const {
    api, config, baseURL, data
  } = getRequestParams({
    url, data: payloadData, params, method, commonConfig
  });

  const apiResponse = yield call(api, url, { config, baseURL, data });
  const { data: response, error } = apiResponse;
  if (error) {
    if (error?.response?.status === 401) {
      localStorage.clear();
      yield call(routeRedirect(`${EMPLOYEE_LOGIN_PATH}`));
    }
    yield put(failureAction({
      error, isLoading: false, status: REQUEST_STATUS.FAILED, apiResponse
    }));
    // const {
    //   code,
    //   title,
    //   message = 'Please try again after some time.',
    //   response: { data: customErrorResponse } = {}
    // } = error;
    // const errorMessage = { title: title || code || 'Error Occurred', description: _.isObject(customErrorResponse) ? customErrorResponse.error || message : customErrorResponse || message };
    // TODO: handle logout based on Invalid token  , token expired
    //  yield call(errorTost, { id: 'ERROR_PRIMARY', ...errorMessage });
  } else if (_.has(response, 'error')) {
    const customError = response.error || {};
    yield put(failureAction({
      error: customError,
      isLoading: false,
      status: REQUEST_STATUS.FAILED,
      apiResponse
    }));
    // yield call(errorTost, { id: 'ERROR_SECONDARY', title: 'CUSTOM_ERROR', message: JSON.stringify(customError) });
  } else {
    const responsePayLoad = _.has(response, 'payload') ? {
      data: response.payload,
      message: response.message,
      apiResponse
    } : { data: response };
    yield put(successAction({
      ...responsePayLoad,
      isLoading: false,
      status: REQUEST_STATUS.SUCCESS,
      apiResponse
    }));
  }

  return { response, error };
}

export function* handleAPIRequest(apiFn, ...rest) {
  const { method, url, payload } = apiFn(...rest);
  return yield call(invokeApi, method, url, payload);
}
