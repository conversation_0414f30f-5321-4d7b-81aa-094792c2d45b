import { InValidIcon, ValidIcon } from 'assets/ValidIcons';
import { t } from 'common/components';
import { FILE_NO } from 'common/regex';

export const validateFile = (data, file) => {
  const found = FILE_NO.test(file);
  let check;
  if (found) {
    if (Object.keys(data).length > 0) {
      if (data?.data?.length === 0) {
        check = false;
      } else if (data.data) {
        check = true;
      } else {
        check = null;
      }
    }
    switch (check) {
      case true:
        return <ValidIcon color="#E82C78" />;
      case false:
        return <InValidIcon color="#E82C78" />;
      default:
        return null;
    }
  } else {
    return null;
  }
};

export const validateFileText = (data, file) => {
  const found = FILE_NO.test(file);
  let check;
  if (found) {
    if (Object.keys(data).length > 0) {
      if (data?.data?.length === 0) {
        check = false;
      } else if (data.data) {
        check = true;
      } else {
        check = null;
      }
    }
    switch (check) {
      case false:
        return <p className="text-red-600 text-right text-xs">{t('fileNumberisNotValid')}</p>;
      case true:
        return <p className="text-green-600 text-right text-xs">{t('fileNumberisValid')}</p>;
      default:
        return null;
    }
  } return null;
};

export const nameValidation = (name) => {
  return name?.replace(/\d+/g, '').replace(/^\s+/, '').replace(/\b\w/g, (match) => match.toUpperCase());
};

export const removeDuplicates = (arr, key) => {
  return arr?.filter((item, index) => index === arr?.findIndex((ar) => ar[key] === item[key]));
};
