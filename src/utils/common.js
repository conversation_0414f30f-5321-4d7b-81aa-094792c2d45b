import { HOME_UI_PATH, MODULE_PATHS } from 'common/constants';

export const getProjectPaths = () => {
  const { VITE_PROJECT_URL } = import.meta.env;
  const projectPaths = {};

  Object.keys(MODULE_PATHS).forEach((key) => {
    projectPaths[key] = `${VITE_PROJECT_URL}/${HOME_UI_PATH}/${MODULE_PATHS[key]}`;
  });

  return projectPaths;
};

export const routeRedirect = (url) => {
  window.location.href = `${window.location.origin}/${url}`;
};

// const fetchCache = new Map();

export const blobUrlToBase64 = async (blobUrl) => {
  // if (fetchCache.has(blobUrl)) {
  //   return fetchCache.get(blobUrl);
  // }
  const promise = new Promise((resolve, reject) => {
    fetch(blobUrl)
      .then((response) => response.blob())
      .then((blob) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      })
      .catch(reject);
  });
  // fetchCache.set(blobUrl, promise);
  return promise;
};

export const handleContentType = (type) => {
  // if (type === 'application/pdf') return '.pdf';
  // if (type === 'image/jpeg') return '.jpeg';
  // if (type === 'image/png') return '.png';
  // return '.png';

  switch (type) {
    case 'application/pdf':
      return '.pdf';
    case 'image/jpeg':
      return '.jpeg';
    case 'image/png':
      return '.png';
    case 'application/msword':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return '.docx';
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
    case 'application/vnd.ms-excel':
    case 'application/vnd.oasis.opendocument.spreadsheet':
      return '.xlsx';
    case 'text/csv':
      return '.csv';
    default:
      return `.${type}`;
  }
};

export function maskPanNumber(panNumber) {
  if (typeof panNumber !== 'string' || panNumber.length !== 10) {
    throw new Error('Invalid PAN number format');
  }

  return panNumber.slice(0, 4) + panNumber.slice(4).replace(/./g, '*');
}
