@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== Scrollbar CSS ===== */

/* width */
::-webkit-scrollbar {
  width: 8px;
  height: 8px
}

/* Track */
::-webkit-scrollbar-track {
  border-radius: 10px;
  background: #e7eff5
}

/* Handle */
::-webkit-scrollbar-thumb {
  background-color: #00b2ec;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  cursor: pointer;
  background-clip: padding-box;
}

/* css over rydes */
.no-panel .KSmart-tab-panels {
  display: none !important;
}

body {
  overflow-x: hidden;
}

.customFileDatePicker input[type='date']::-webkit-calendar-picker-indicator {
  color: rgba(0, 0, 0, 0);
  opacity: 1;
  display: block;
  background: url(./assets/CalendarMoreDots.png) no-repeat;
  width: 20px;
  height: 20px;
  border-width: thin;
  margin-top: -3px;
}

.file-note-docs .KSmart-tabs .KSmart-tab-panels{
  padding-top: 5px
}
.tool-section{
  column-gap: 10px;
}
.input-rich-container.rich-editor .tiptap{
  padding: 10px !important;
  cursor: auto !important;
}
.drag-enabled .input-rich-container.rich-editor fieldset:is(:disabled, :read-only){
  border: 2px solid rgb(0, 178, 236) !important;
}

.KSmart__control--menu-is-open {
  z-index: 99 !important;
}

.dropdown-contain p.form-label {
  z-index: 100 !important;
}

.draft-rich-content p {
  line-height: 23px !important;
  text-align: justify !important;
  color: #323232;
  font-size: 14px !important;
}

.draft-rich-content span {
  line-height: 23px !important;
  text-align: justify !important;
  color: #323232;
  font-size: 14px !important;
}

.draft-rich-subject p {
  text-align: justify !important;
  color: #323232;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.draft-rich-subject span {
  text-align: justify !important;
  color: #323232;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.draft-update-group  {
  max-height: 40px;
  max-width: 300px;
}

.draft-update-group .KSmart__control {
  min-height: 40px;
}

.import-draft-note-modal-body > .KSmart-tabs .KSmart-tab-panels {
  padding: 20px 0 !important;
}

.import-draft-note-modal-body .input__container {
  height: 49px;
}

.import-note-card-rich-content {
  font-size: 14px;
}