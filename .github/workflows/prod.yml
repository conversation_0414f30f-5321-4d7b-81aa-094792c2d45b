name: PROD Deployment
on:
  push:
    branches:
      - main

jobs:
  call-build-and-deploy:
    uses: ksmartikm/ikm-devops-shared/.github/workflows/fe-build-and-deploy-prod.yml@main
    with:
      app-name: ikm-kfm-frontend
      artifacts-bucket: ikm-artifacts-prod
      app-bucket: ikm-webapp-prod
      env-conf: prod.json
      ui-path: file-management
    secrets:
      aws-access-key-id: ${{ secrets.IKM_PROD_AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.IKM_PROD_AWS_SECRET_ACCESS_KEY }}
      npm-auth-token: ${{ secrets.MAVEN_PASSWORD }}
      cloud-distribution-id: ${{ secrets.IKM_PROD_CLOUDFRONT_DIST_ID }}
      github-token: ${{ secrets.MAVEN_PASSWORD }}
