name: Continuous Deployment
on:
  push:
    branches:
      - develop

jobs:
  call-build-and-deploy:
    uses: ksmartikm/ikm-devops-shared/.github/workflows/fe-build-and-deploy.yml@main
    with:
      app-name: ikm-kfm-frontend
      artifacts-bucket: ikm-artifacts-dev
      app-bucket: ikm-webapp-dev
      env-conf: dev.json
      ui-path: file-management
    secrets:
      aws-access-key-id: ${{ secrets.IKM_AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.IKM_AWS_SECRET_ACCESS_KEY }}
      npm-auth-token: ${{ secrets.MAVEN_PASSWORD }}
      cloud-distribution-id: ${{ secrets.IKM_DEV_CLOUDFRONT_DIST_ID }}