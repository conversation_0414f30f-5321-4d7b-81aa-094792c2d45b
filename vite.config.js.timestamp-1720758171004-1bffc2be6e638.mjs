// vite.config.js
import { defineConfig, loadEnv } from "file:///E:/ikm/ksm-kfm-frontend/node_modules/.pnpm/vite@4.5.3/node_modules/vite/dist/node/index.js";
import react from "file:///E:/ikm/ksm-kfm-frontend/node_modules/.pnpm/@vitejs+plugin-react@3.1.0_vite@4.5.3/node_modules/@vitejs/plugin-react/dist/index.mjs";
import jsconfigPaths from "file:///E:/ikm/ksm-kfm-frontend/node_modules/.pnpm/vite-jsconfig-paths@2.0.1_vite@4.5.3/node_modules/vite-jsconfig-paths/dist/index.mjs";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  return {
    base: env.VITE_BASE_URL,
    experimental: {
      renderBuiltUrl: (filename) => `${env.VITE_BASE_URL}/${filename}`
    },
    plugins: [react(), jsconfigPaths()]
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxpa21cXFxca3NtLWtmbS1mcm9udGVuZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcaWttXFxcXGtzbS1rZm0tZnJvbnRlbmRcXFxcdml0ZS5jb25maWcuanNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L2lrbS9rc20ta2ZtLWZyb250ZW5kL3ZpdGUuY29uZmlnLmpzXCI7LyogZXNsaW50LWRpc2FibGUgaW1wb3J0L25vLWV4dHJhbmVvdXMtZGVwZW5kZW5jaWVzICovXHJcbmltcG9ydCB7IGRlZmluZUNvbmZpZywgbG9hZEVudiB9IGZyb20gJ3ZpdGUnO1xyXG5pbXBvcnQgcmVhY3QgZnJvbSAnQHZpdGVqcy9wbHVnaW4tcmVhY3QnO1xyXG5pbXBvcnQganNjb25maWdQYXRocyBmcm9tICd2aXRlLWpzY29uZmlnLXBhdGhzJztcclxuXHJcbi8vIGh0dHBzOi8vdml0ZWpzLmRldi9jb25maWcvXHJcbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZygoeyBtb2RlIH0pID0+IHtcclxuICBjb25zdCBlbnYgPSBsb2FkRW52KG1vZGUsIHByb2Nlc3MuY3dkKCksICcnKTtcclxuICByZXR1cm4ge1xyXG4gICAgYmFzZTogZW52LlZJVEVfQkFTRV9VUkwsXHJcbiAgICBleHBlcmltZW50YWw6IHtcclxuICAgICAgcmVuZGVyQnVpbHRVcmw6IChmaWxlbmFtZSkgPT4gYCR7ZW52LlZJVEVfQkFTRV9VUkx9LyR7ZmlsZW5hbWV9YFxyXG4gICAgfSxcclxuICAgIHBsdWdpbnM6IFtyZWFjdCgpLCBqc2NvbmZpZ1BhdGhzKCldXHJcbiAgfTtcclxufSk7XHJcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFDQSxTQUFTLGNBQWMsZUFBZTtBQUN0QyxPQUFPLFdBQVc7QUFDbEIsT0FBTyxtQkFBbUI7QUFHMUIsSUFBTyxzQkFBUSxhQUFhLENBQUMsRUFBRSxLQUFLLE1BQU07QUFDeEMsUUFBTSxNQUFNLFFBQVEsTUFBTSxRQUFRLElBQUksR0FBRyxFQUFFO0FBQzNDLFNBQU87QUFBQSxJQUNMLE1BQU0sSUFBSTtBQUFBLElBQ1YsY0FBYztBQUFBLE1BQ1osZ0JBQWdCLENBQUMsYUFBYSxHQUFHLElBQUksYUFBYSxJQUFJLFFBQVE7QUFBQSxJQUNoRTtBQUFBLElBQ0EsU0FBUyxDQUFDLE1BQU0sR0FBRyxjQUFjLENBQUM7QUFBQSxFQUNwQztBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
