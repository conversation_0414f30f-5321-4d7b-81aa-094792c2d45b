{"name": "ikm-file-management", "private": true, "version": "2.2.87", "type": "module", "scripts": {"eslint": "eslint src --ext .js,.jsx --max-warnings=0", "eslint:fix": "eslint src --ext .js,.jsx --fix", "dev": "vite --port 5015 --strictPort", "build": "vite build", "preview": "vite preview --port=5015 --strictPort", "prepare": "husky install", "mock:start": "cd mock && pnpm i && pnpm start", "mock:stop": "cd mock && pnpm delete"}, "lint-staged": {"**/*.{js,jsx}": ["pnpm run eslint"]}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@ksmartikm/ui-components": "^0.6.41", "@reduxjs/toolkit": "^1.9.7", "@tiptap/core": "^2.11.5", "@tiptap/extension-bubble-menu": "^2.1.12", "@tiptap/extension-color": "^2.1.12", "@tiptap/extension-floating-menu": "^2.1.12", "@tiptap/extension-font-family": "^2.1.12", "@tiptap/extension-heading": "^2.1.12", "@tiptap/extension-highlight": "^2.1.12", "@tiptap/extension-horizontal-rule": "^2.1.12", "@tiptap/extension-image": "^2.1.12", "@tiptap/extension-link": "^2.1.12", "@tiptap/extension-list-item": "^2.1.12", "@tiptap/extension-ordered-list": "^2.1.12", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-table": "^2.1.12", "@tiptap/extension-table-cell": "^2.1.12", "@tiptap/extension-table-header": "^2.1.12", "@tiptap/extension-table-row": "^2.1.12", "@tiptap/extension-text-align": "^2.1.12", "@tiptap/extension-text-style": "^2.1.12", "@tiptap/extension-typography": "^2.1.12", "@tiptap/extension-underline": "^2.1.12", "@tiptap/pm": "^2.1.12", "@tiptap/react": "^2.1.12", "@tiptap/starter-kit": "^2.1.12", "axios": "^1.7.2", "csvtojson": "^2.0.10", "dayjs": "^1.11.12", "framer-motion": "^10.18.0", "i18next": "^23.12.2", "lodash": "^4.17.21", "logrocket": "^8.1.1", "mammoth": "^1.9.0", "pnpm": "^8.15.9", "prop-types": "^15.8.1", "query-string": "^8.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-i18next": "^13.5.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^8.1.3", "react-router-dom": "^6.25.1", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "reselect": "^4.1.8", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/js": "^9.7.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-node": "^0.3.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "globals": "^15.8.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "postcss": "^8.4.39", "tailwindcss": "^3.4.6", "vite": "^4.5.3", "vite-jsconfig-paths": "^2.0.1"}}