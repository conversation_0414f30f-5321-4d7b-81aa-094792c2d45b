#!/bin/bash
# This script sets environment variables from a provided JSON file and then replaces specific environment variables in deployment files.
 
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <path_to_json_config>"
  exit 1
fi
 
JSON_CONFIG="$1"
 
# Check if the provided JSON file exists
if [[ ! -f "${JSON_CONFIG}" ]]; then
  echo "The provided JSON configuration file does not exist: ${JSON_CONFIG}"
  exit 1
fi
 
# Extract values from the provided JSON file and set them as environment variables
# VITE_BASE_URL
VALUE=$(jq -r '.VITE_BASE_URL' "${JSON_CONFIG}")
STATUS=$?
if [[ $STATUS -ne 0 ]]; then
  echo "Error extracting VITE_BASE_URL from ${JSON_CONFIG}"
  exit $STATUS
fi
export VITE_BASE_URL=$VALUE
 
# VITE_API_URL
VALUE=$(jq -r '.VITE_API_URL' "${JSON_CONFIG}")
STATUS=$?
if [[ $STATUS -ne 0 ]]; then
  echo "Error extracting VITE_API_URL from ${JSON_CONFIG}"
  exit $STATUS
fi
export VITE_API_URL=$VALUE
 
 # VITE_PROJECT_URL
VALUE=$(jq -r '.VITE_PROJECT_URL' "${JSON_CONFIG}")
STATUS=$?
if [[ $STATUS -ne 0 ]]; then
  echo "Error extracting VITE_PROJECT_URL from ${JSON_CONFIG}"
  exit $STATUS
fi
export VITE_PROJECT_URL=$VALUE

# VITE_SECRET
VALUE=$(jq -r '.VITE_SECRET' "${JSON_CONFIG}")
STATUS=$?
if [[ $STATUS -ne 0 ]]; then
  echo "Error extracting VITE_SECRET from ${JSON_CONFIG}"
  exit $STATUS
fi
export VITE_SECRET=$VALUE


# VITE_DIGITAL_SIGNATURE
VALUE=$(jq -r '.VITE_DIGITAL_SIGNATURE' "${JSON_CONFIG}")
STATUS=$?
if [[ $STATUS -ne 0 ]]; then
  echo "Error extracting VITE_DIGITAL_SIGNATURE from ${JSON_CONFIG}"
  exit $STATUS
fi
export VITE_DIGITAL_SIGNATURE=$VALUE

CURRENT_DIRECTORY=$(dirname $0)
DEPLOYMENT_DIRECTORY="${CURRENT_DIRECTORY}/../dist"
VARIABLES_TO_REPLACE='$VITE_BASE_URL:$VITE_API_URL:$VITE_PROJECT_URL:$VITE_SECRET:$VITE_DIGITAL_SIGNATURE'

if [[ -z "${VITE_BASE_URL}" ]] || [[ -z "${VITE_API_URL}" ]] || [[ -z "${VITE_PROJECT_URL}" ]] || [[ -z "${VITE_SECRET}" ]] || [[ -z "${VITE_DIGITAL_SIGNATURE}" ]]; then
  echo "Required environment variables are not set."
  exit 1
fi
 
# Handle index.html
if [[ -f "${DEPLOYMENT_DIRECTORY}/index.html" ]]; then
  envsubst $VARIABLES_TO_REPLACE <"${DEPLOYMENT_DIRECTORY}/index.html" >"${DEPLOYMENT_DIRECTORY}/index.html.replaced"
  mv "${DEPLOYMENT_DIRECTORY}/index.html.replaced" "${DEPLOYMENT_DIRECTORY}/index.html"
  echo "Processed ${DEPLOYMENT_DIRECTORY}/index.html"
else
  echo "File ${DEPLOYMENT_DIRECTORY}/index.html not found!"
  exit 1
fi
 
# Handle files in assets directory
find "${DEPLOYMENT_DIRECTORY}/assets" -maxdepth 1 -type f -print0 | while read -r -d '' file; do
  envsubst $VARIABLES_TO_REPLACE <"${file}" >"${file}.replaced"
  mv "${file}.replaced" "${file}"
  echo "Processed ${file}"
done