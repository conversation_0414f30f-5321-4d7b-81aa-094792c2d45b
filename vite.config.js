/// <reference types="vitest" />
/// <reference types="vite/client" />

/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import jsconfigPaths from 'vite-jsconfig-paths';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    base: env.VITE_BASE_URL,
    experimental: {
      renderBuiltUrl: (filename) => `${env.VITE_BASE_URL}/${filename}`
    },
    plugins: [
      react({
        jsxImportSource: 'react',
        jsxRuntime: 'automatic'
      }),
      jsconfigPaths()
    ],
    define: {
      global: 'globalThis'
    },
    optimizeDeps: {
      include: ['react', 'react-dom'],
      exclude: ['@ksmartikm/ui-components']
    },
    test: {
      globals: true,
      environment: 'jsdom',
      css: true,
      setUpFiles: './src/test/setup.js'
    }
  };
});
