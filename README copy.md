# Getting Started with Vite

This project was bootstrapped with [Vite]().
### Node version
Use `v18.16.0` or above.

### Package Manager
pnpm
[https://pnpm.io/installation] (installation) 

## Available Scripts

In the project directory, you can run:
### `pnpm start`

Runs the app in the development mode.\
Open [http://localhost:5002](http://localhost:5002) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

###### Dependencies and their references

1. https://vitejs.dev/guide/
2. https://prettier.io/docs/en/install.html
3. https://eslint.org/docs/latest/use/getting-started
4. https://www.npmjs.com/package/husky
